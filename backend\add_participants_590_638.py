#!/usr/bin/env python3

import os
import sys
import django
from datetime import datetime

# Add the project root to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'event_management.settings')
django.setup()

from participants.models import Participant, ParticipantType
from events.models import Event
from django.db import connection

def add_participants_590_638():
    """Add participants with IDs 590-638"""
    
    print("=== ADDING PARTICIPANTS 590-638 ===")
    
    # Get Event ID 1
    try:
        event = Event.objects.get(id=1)
    except Event.DoesNotExist:
        print("Error: Event with ID 1 not found")
        return
    
    # Get participant types
    participant_types = {}
    for pt in ParticipantType.objects.all():
        participant_types[pt.id] = pt
    
    # Additional participants data (IDs 590-638)
    additional_participants = [
        # (id, first_name, last_name, middle_name, email, phone, institution_name, participant_type_id, arrival_date, departure_date, profile_photo, remarks, status)
        (590, 'Belay', 'Ayenew', 'Mesfin', '<EMAIL>', '091 8805970', 'university of gondar', 24, '2025-08-01 20:31:00', '2025-08-04 20:31:00', 'event-registration/profiles/profile_2d6cdf95-d13b-4df8-bd9b-86dc9bc64cd7.jpg', '', 'active'),
        (591, 'Lemlem', 'Hailemariyam', 'Demissie', '<EMAIL>', '0911928439', 'MoE', 25, '2025-08-03 07:30:00', '2025-08-07 16:10:00', 'event-registration/profiles/profile_5317ef2c-7bcf-4bbf-a806-010e93c44ec4.jpeg', '', 'active'),
        (592, 'Hermela', 'Fekade', 'Belayhun', '<EMAIL>', '091 2179860', 'NBC Ethiopia', 24, '2025-08-03 01:40:00', '2025-08-07 10:40:00', None, '', 'active'),
        (593, 'Tesfahun', 'Yilma', 'Melese', '<EMAIL>', '091 8779820', 'University of Gondar', 25, '2025-08-01 22:32:00', '2025-08-02 22:32:00', 'event-registration/profiles/profile_b8a273e9-e268-437b-9e12-628a789f1c61.png', '', 'active'),
        (594, 'Sileshi', 'Assefa', 'Abbi', '<EMAIL>', '+251 930355379', 'Mekdela Amba university', 23, '2025-08-04 22:45:00', '2025-08-07 22:47:00', None, '', 'active'),
        (595, 'Tewodros', 'Chekol', 'Abebaw', '<EMAIL>', '091 0153087', 'Universtiy of Gondar', 32, '2025-08-03 08:00:00', '2025-08-07 18:00:00', 'event-registration/profiles/profile_2217eeef-85f5-4312-9baa-1cb48762a5fe.png', '', 'deleted'),
        (596, 'Reagan', 'Jennings', 'Nehru Underwood', '<EMAIL>', '094 3616975', 'Delaney Alvarez Associates', 4, '2025-08-03 08:00:00', '2025-08-07 18:00:00', None, 'Anim tempore neque', 'deleted'),
        (597, 'Solomon', 'Gebretsadik', 'Fantaw', '<EMAIL>', '093 7402246', 'University of Gondar', 25, '2025-08-03 08:00:00', '2025-08-07 18:00:00', 'event-registration/profiles/profile_bc54bbed-dc8f-44ac-bd89-d85ba6a1458e.jpg', '', 'active'),
        (598, 'Dr. Yitayal', 'Mengistu', 'Alemu', '<EMAIL>', '091 1601812', 'University of Gondar', 25, '2025-08-03 08:00:00', '2025-08-07 18:00:00', None, '', 'active'),
        (599, 'Chanie', 'Faris', 'Adefris', '<EMAIL>', '+251 911167250', 'Ministry of education', 25, '2025-08-03 08:00:00', '2025-08-07 18:00:00', None, '', 'active'),
        (600, 'Demiss', 'Geberu', 'Mulatu', '<EMAIL>', '092 4110714', 'University of Gondar', 25, '2025-08-03 08:00:00', '2025-08-07 18:00:00', 'event-registration/profiles/profile_328dcfd2-59e7-4b72-b419-07e4fb8aa855.jpg', '', 'active'),
        (601, 'Tadesse', 'Baymot', 'Weldegebreal', '<EMAIL>', '091 8778670', 'University of Gondar', 25, '2025-08-03 08:00:00', '2025-08-07 18:00:00', None, '', 'active'),
        (602, 'Getasew', 'Ayalew', 'Abebaw', '<EMAIL>', '913336448', 'UoG', 25, '2025-08-03 08:00:00', '2025-08-07 18:00:00', 'event-registration/profiles/profile_835e9bae-6366-4ba8-a9e7-e1c73e2f4921.jpg', '', 'active'),
        (603, 'Alemayehu', 'Heyie', 'Chala', '<EMAIL>', '912163096', 'Dilla university', 29, '2025-08-03 07:30:00', '2025-08-07 18:00:00', None, 'None', 'active'),
        (604, 'Yadelew', 'Shibabaw', 'Yimer', '<EMAIL>', '091 8464556', 'UoG', 25, '2025-08-03 08:00:00', '2025-08-07 18:00:00', None, '', 'active'),
        (605, 'Abebe Worku', 'Mekonnen', '', '<EMAIL>', '+251 911707979', 'University of Gondar', 25, '2025-08-03 08:00:00', '2025-08-07 18:00:00', 'event-registration/profiles/profile_34c79237-fe8d-42c0-9c8c-3483a971cb2c.jpg', '', 'active'),
        (606, 'Desta', 'Alemu', 'Kassa', '<EMAIL>', '914242914', 'Amhara Media Corporation', 24, '2025-08-03 08:00:00', '2025-08-07 18:00:00', 'event-registration/profiles/profile_0f7cfe61-062f-41e4-a7b1-817dd80b0bb8.jpg', '', 'active'),
        (607, 'Yeshiwas', 'Yohannes', 'Abunie', '<EMAIL>', '094 4700405', 'University of Gondar', 25, '2025-08-03 08:00:00', '2025-08-07 18:00:00', None, '', 'active'),
        (608, 'Mengistu', 'Letta', 'Urge', '<EMAIL>', '093 0374860', 'Haramaya University', 29, '2025-08-03 08:32:00', '2025-08-03 14:45:00', None, '', 'active'),
        (609, 'Yemane', 'Tequamwork', 'Berhanu', '<EMAIL>', '095 2827207', 'MCC/Reporter News Paper', 24, '2025-08-05 08:51:00', '2025-08-08 08:53:00', None, '', 'active'),
        (610, 'Prof. Yeshigeta', 'Birhanu', 'Gelaw', '<EMAIL>', '093 0415367', 'Bahir Dar University', 29, '2025-08-03 07:45:00', '2025-08-07 08:57:00', None, '', 'active'),
        (611, 'Dr. Berhanu', 'Robe', 'Lemma', '<EMAIL>', '929171672', 'Bule Hora University', 2, '2025-08-03 08:00:00', '2025-08-07 18:00:00', None, '', 'deleted'),
        (612, 'TEMESGEN', 'Wondimu', '', '<EMAIL>', '093 0097649', 'AASTU', 23, '2025-08-03 09:34:00', '2025-08-07 09:34:00', None, '', 'active'),
        (613, 'Prof. Yihenew', 'Mengesha', 'Gebreselassie', '<EMAIL>', '091 8765823', 'Hawassa University', 33, '2025-08-03 13:35:00', '2025-08-06 09:15:00', 'event-registration/profiles/profile_fcf13350-4dd9-48b7-845e-1971e7e614aa.jpg', 'Deputy Chair, Management Board of Hawassa University', 'active'),
        (614, 'Mebrat', 'Birhanu', 'Tilahun', '<EMAIL>', '093 3577374', 'University of Gondar', 25, '2025-08-03 08:00:00', '2025-08-07 18:00:00', None, '', 'active'),
        (615, 'SOLOMON GIRMA', 'YABOWORK', '', '<EMAIL>', '920570919', 'University of Gondar', 24, '2025-08-03 08:00:00', '2025-08-07 18:00:00', None, '', 'active'),
        (616, 'Abinet', 'Ergicho', 'Tamirat', '<EMAIL>', '+251 916187197', 'Arts tv', 24, '2025-08-03 08:00:00', '2025-08-07 18:00:00', 'event-registration/profiles/profile_a7a4aaee-4c65-4c31-b615-e4e43f243efa.jpg', '', 'active'),
        (617, 'Waltengus', 'Abineh', 'Tadesse', '<EMAIL>', '982169696', 'Editor', 24, '2025-08-03 08:00:00', '2025-08-07 18:00:00', 'event-registration/profiles/profile_95254fda-5e60-4f9c-b31b-1d74f89a8fb0.jpg', '', 'active'),
        (618, 'Elias', 'Bedasso', 'Alemu', '<EMAIL>', '+251 910515041', 'Dilla University', 2, '2025-08-03 08:00:00', '2025-08-07 18:00:00', None, '', 'active'),
        (619, 'Dr. Berhanu', 'Robe', 'Lemma', '<EMAIL>', '929171672', 'Bule Hora University', 2, '2025-08-03 08:00:00', '2025-08-07 18:00:00', None, '', 'deleted'),
        (620, 'Alene', 'Mekonnen', 'Ketema', '<EMAIL>', '092 1252233', 'University of Gondar', 25, '2025-08-03 08:00:00', '2025-08-07 18:00:00', None, '', 'active'),
        (621, 'Mengistu', 'Ayele', 'Mulu', '<EMAIL>', '091 8722142', 'UoG', 25, '2025-08-04 12:15:00', '2025-08-07 12:16:00', None, '', 'active'),
        (622, 'Amsalu', 'Hordofa', 'Tesso', '<EMAIL>', '091 7814778', 'Wollega University', 27, '2025-08-02 12:37:00', '2025-08-07 12:37:00', None, '', 'active'),
        (623, 'Genemi', 'Arti', 'Bukimi', '<EMAIL>', '095 3768053', 'Jika University', 27, '2025-08-02 07:23:00', '2025-08-03 06:20:00', None, '', 'active'),
        (624, 'Solomon', 'Derib', 'Aychew', '<EMAIL>', '+251 968199763', 'University of Gondar', 25, '2025-08-03 08:00:00', '2025-08-07 18:00:00', 'event-registration/profiles/profile_89a193cc-d408-475a-90f4-65f14e486f8a.jpg', '', 'active'),
        (625, 'Shimels', 'Feleke', 'Abebe', '<EMAIL>', '091 3830188', 'Ethiopian Teachers Association', 33, '2025-08-03 11:50:00', '2028-08-03 10:50:00', 'event-registration/profiles/profile_a867daaa-2cd1-4f22-b789-fc854baecd2a.jpg', '', 'active'),
        (626, 'Eyob', 'Feleke', 'Assegid', '<EMAIL>', '091 1468562', 'NBC ethiopia', 24, '2025-08-03 19:30:00', '2025-08-07 15:40:00', None, '', 'active'),
        (627, 'Guchie', 'Sulla', 'Gulie', '<EMAIL>', '091 1250668', 'Wolaita Sodo University', 2, '2025-08-04 07:20:00', '2025-08-05 20:30:00', 'event-registration/profiles/profile_f601a48e-dcbc-447e-8869-25e08b2aabd4.jpg', '', 'active'),
        (628, 'Fentie', 'Meketie', 'Biset', '<EMAIL>', '091 8770446', 'Debark University', 3, '2025-08-03 17:12:00', '2025-08-03 18:13:00', 'event-registration/profiles/profile_719ecf86-b469-4cc6-a0cc-73a4232926ea.jpg', '', 'active'),
        (629, 'Yenesew', 'Belew', 'Alene', '<EMAIL>', '+251 910040073', 'University of Gondar', 25, '2025-08-03 08:00:00', '2025-08-07 18:00:00', 'event-registration/profiles/profile_ed614822-2d12-431f-9fa9-458f7678ef01.jpeg', '', 'active'),
        (630, 'Biruk', 'Adera', 'Asefa', '<EMAIL>', '091 3307730', 'Moe', 8, '2025-08-03 20:05:00', '2025-08-03 21:30:00', None, 'No', 'active'),
        (631, 'Samuel', 'mitiku', 'asamenew', '<EMAIL>', '091 1430930', 'Moe', 8, '2025-08-03 20:04:00', '2025-08-03 21:35:00', None, 'No', 'active'),
        (632, 'Delil', 'Mussa', 'Kedir', '<EMAIL>', '091 1100348', 'Moe', 8, '2025-08-03 20:04:00', '2025-08-03 21:36:00', None, 'No', 'active'),
        (633, 'Eyob', 'Tesema', 'Tamiru', '<EMAIL>', '091 1586434', 'Moe', 8, '2025-08-03 20:04:00', '2025-08-03 21:36:00', None, 'No', 'active'),
        (634, 'Adisu', 'Degefa', 'Tesfaye', '<EMAIL>', '+251 922298556', 'Moe', 8, '2025-08-03 20:04:00', '2025-08-03 21:36:00', None, 'No', 'active'),
        (635, 'Ahmed', 'Asfaw', 'Abtew', '<EMAIL>', '+251 911211574', 'Education and training Authority', 1, '2025-08-03 16:00:00', '2025-08-07 13:00:00', None, '', 'active'),
        (636, 'Addisale', 'Meharie', 'Abathun', '<EMAIL>', '092 9436584', 'Addis Ababa university', 28, '2025-08-03 15:32:00', '2025-08-07 18:00:00', None, '', 'active'),
        (637, 'Fatuma', 'Selba', 'Muhammedaman', '<EMAIL>', '091 3355645', 'Made Walabu University', 27, '2025-08-03 08:00:00', '2025-08-07 18:00:00', None, '', 'active'),
        (638, 'Essa', 'Hassan', '', '<EMAIL>', '0930302781', 'Madda Walabu University', 23, '2025-08-03 09:10:00', '2025-08-06 10:20:00', '', '', 'active'),
    ]
    
    added_count = 0
    updated_count = 0
    
    with connection.cursor() as cursor:
        for p_data in additional_participants:
            # Check if participant already exists
            existing_participant = Participant.objects.filter(id=p_data[0]).first()
            
            if existing_participant:
                # Update existing participant with correct email if needed
                if existing_participant.email != p_data[4]:
                    print(f"Updating email for ID {p_data[0]}: {existing_participant.email} -> {p_data[4]}")
                    existing_participant.email = p_data[4]
                    existing_participant.save()
                    updated_count += 1
                else:
                    print(f"Participant with ID {p_data[0]} already exists with correct data, skipping...")
                continue
            
            # Get participant type
            participant_type = participant_types.get(p_data[7])
            if not participant_type:
                print(f"Warning: Participant type {p_data[7]} not found for {p_data[1]} {p_data[2]}")
                continue
            
            # Parse dates
            arrival_date = None
            departure_date = None
            
            if p_data[8]:  # arrival_date
                try:
                    arrival_date = datetime.strptime(p_data[8], "%Y-%m-%d %H:%M:%S")
                except:
                    arrival_date = None
            
            if p_data[9]:  # departure_date
                try:
                    departure_date = datetime.strptime(p_data[9], "%Y-%m-%d %H:%M:%S")
                except:
                    departure_date = None
            
            # Create participant
            try:
                participant = Participant(
                    first_name=p_data[1],
                    last_name=p_data[2],
                    middle_name=p_data[3] or "",
                    email=p_data[4],
                    phone=p_data[5],
                    institution_name=p_data[6],
                    position="Participant",
                    participant_type=participant_type,
                    arrival_date=arrival_date,
                    departure_date=departure_date,
                    profile_photo=p_data[10],
                    remarks=p_data[11] or "",
                    status=p_data[12],
                    event=event
                )
                participant.save()
                
                # Set the exact ID using raw SQL
                cursor.execute(
                    "UPDATE participants_participant SET id = %s WHERE id = %s",
                    [p_data[0], participant.id]
                )
                
                added_count += 1
                print(f"Added: {p_data[1]} {p_data[2]} with ID {p_data[0]}")
                
            except Exception as e:
                print(f"Error adding {p_data[1]} {p_data[2]}: {str(e)}")
    
    print(f"\nSummary:")
    print(f"Added {added_count} new participants")
    print(f"Updated {updated_count} existing participants")
    print(f"Total participants now: {Participant.objects.count()}")

if __name__ == "__main__":
    add_participants_590_638()
