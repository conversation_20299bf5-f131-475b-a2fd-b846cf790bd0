import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { Container, Row, Col, Card, Form, <PERSON><PERSON>, Al<PERSON>, Spinner, Tab, Nav } from 'react-bootstrap';
import { organizationService, Organization } from '../services/api';

interface OrganizationFormData {
  name: string;
  short_name: string;
  motto: string;
  description: string;
  email: string;
  phone: string;
  website: string;
  address_line_1: string;
  address_line_2: string;
  city: string;
  state_province: string;
  postal_code: string;
  country: string;
  facebook_url: string;
  twitter_url: string;
  linkedin_url: string;
  instagram_url: string;
  is_active: boolean;
  is_primary: boolean;
  logo?: File | null;
}

const OrganizationForm: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const isEdit = Boolean(id);

  const [formData, setFormData] = useState<OrganizationFormData>({
    name: '',
    short_name: '',
    motto: '',
    description: '',
    email: '',
    phone: '',
    website: '',
    address_line_1: '',
    address_line_2: '',
    city: '',
    state_province: '',
    postal_code: '',
    country: '',
    facebook_url: '',
    twitter_url: '',
    linkedin_url: '',
    instagram_url: '',
    is_active: true,
    is_primary: false,
    logo: null
  });

  const [loading, setLoading] = useState(false);
  const [submitLoading, setSubmitLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState('basic');

  useEffect(() => {
    if (isEdit && id) {
      fetchOrganization();
    }
  }, [isEdit, id]);

  const fetchOrganization = async () => {
    try {
      setLoading(true);
      const response = await organizationService.getOrganization(parseInt(id!));
      const org = response.data;
      
      setFormData({
        name: org.name || '',
        short_name: org.short_name || '',
        motto: org.motto || '',
        description: org.description || '',
        email: org.email || '',
        phone: org.phone || '',
        website: org.website || '',
        address_line_1: org.address_line_1 || '',
        address_line_2: org.address_line_2 || '',
        city: org.city || '',
        state_province: org.state_province || '',
        postal_code: org.postal_code || '',
        country: org.country || '',
        facebook_url: org.facebook_url || '',
        twitter_url: org.twitter_url || '',
        linkedin_url: org.linkedin_url || '',
        instagram_url: org.instagram_url || '',
        is_active: org.is_active ?? true,
        is_primary: org.is_primary ?? false,
        logo: null
      });
    } catch (err) {
      console.error('Error fetching organization:', err);
      setError('Failed to load organization details. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value, type } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? (e.target as HTMLInputElement).checked : value
    }));
  };

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0] || null;
    setFormData(prev => ({
      ...prev,
      logo: file
    }));
  };

  const validateForm = (): string | null => {
    if (!formData.name.trim()) {
      setActiveTab('basic');
      return 'Organization name is required';
    }
    if (!formData.email.trim()) {
      setActiveTab('contact');
      return 'Email is required';
    }

    // Email validation
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(formData.email)) {
      setActiveTab('contact');
      return 'Please enter a valid email address';
    }

    return null;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    const validationError = validateForm();
    if (validationError) {
      setError(validationError);
      return;
    }

    try {
      setSubmitLoading(true);
      setError(null);

      const submitData = new FormData();
      Object.entries(formData).forEach(([key, value]) => {
        if (key === 'logo' && value instanceof File) {
          submitData.append(key, value);
        } else if (key !== 'logo' && value !== null && value !== undefined) {
          submitData.append(key, value.toString());
        }
      });

      if (isEdit) {
        await organizationService.updateOrganization(parseInt(id!), submitData);
        setSuccess('Organization updated successfully!');
      } else {
        await organizationService.createOrganization(submitData);
        setSuccess('Organization created successfully!');
      }

      // Redirect after a short delay
      setTimeout(() => {
        navigate('/organizations');
      }, 2000);

    } catch (err) {
      console.error('Error saving organization:', err);
      setError('Failed to save organization. Please try again.');
    } finally {
      setSubmitLoading(false);
    }
  };

  if (loading) {
    return (
      <Container className="text-center mt-5">
        <Spinner animation="border" role="status">
          <span className="visually-hidden">Loading...</span>
        </Spinner>
        <p className="mt-3">Loading organization details...</p>
      </Container>
    );
  }

  return (
    <Container className="py-4">
      <Row className="justify-content-center">
        <Col lg={10}>
          <Card className="shadow">
            <Card.Header className="bg-primary text-white">
              <h3 className="mb-0">
                <i className={`fas fa-${isEdit ? 'edit' : 'plus'} me-2`}></i>
                {isEdit ? 'Edit Organization' : 'Add New Organization'}
              </h3>
            </Card.Header>
            <Card.Body className="p-4">
              {error && (
                <Alert variant="danger" className="mb-4">
                  <i className="fas fa-exclamation-triangle me-2"></i>
                  {error}
                </Alert>
              )}

              {success && (
                <Alert variant="success" className="mb-4">
                  <i className="fas fa-check-circle me-2"></i>
                  {success}
                </Alert>
              )}

              <Form onSubmit={handleSubmit}>
                <Tab.Container activeKey={activeTab} onSelect={(k) => setActiveTab(k || 'basic')}>
                  <Nav variant="tabs" className="mb-4">
                    <Nav.Item>
                      <Nav.Link eventKey="basic">
                        <i className="fas fa-info-circle me-2"></i>
                        Basic Information
                      </Nav.Link>
                    </Nav.Item>
                    <Nav.Item>
                      <Nav.Link eventKey="contact">
                        <i className="fas fa-address-book me-2"></i>
                        Contact & Address
                      </Nav.Link>
                    </Nav.Item>
                    <Nav.Item>
                      <Nav.Link eventKey="social">
                        <i className="fas fa-share-alt me-2"></i>
                        Social Media
                      </Nav.Link>
                    </Nav.Item>
                    <Nav.Item>
                      <Nav.Link eventKey="settings">
                        <i className="fas fa-cogs me-2"></i>
                        Settings
                      </Nav.Link>
                    </Nav.Item>
                  </Nav>

                  <Tab.Content>
                    {/* Basic Information Tab */}
                    <Tab.Pane eventKey="basic">
                      <Row>
                        <Col md={6}>
                          <Form.Group className="mb-3">
                            <Form.Label className="fw-semibold">
                              <i className="fas fa-building me-2"></i>
                              Organization Name *
                            </Form.Label>
                            <Form.Control
                              type="text"
                              name="name"
                              value={formData.name}
                              onChange={handleInputChange}
                              placeholder="Enter organization name"
                            />
                          </Form.Group>
                        </Col>
                        <Col md={6}>
                          <Form.Group className="mb-3">
                            <Form.Label className="fw-semibold">
                              <i className="fas fa-tag me-2"></i>
                              Short Name / Abbreviation
                            </Form.Label>
                            <Form.Control
                              type="text"
                              name="short_name"
                              value={formData.short_name}
                              onChange={handleInputChange}
                              placeholder="e.g., UOG, ACME Corp"
                            />
                          </Form.Group>
                        </Col>
                      </Row>

                      <Form.Group className="mb-3">
                        <Form.Label className="fw-semibold">
                          <i className="fas fa-quote-left me-2"></i>
                          Motto / Tagline
                        </Form.Label>
                        <Form.Control
                          type="text"
                          name="motto"
                          value={formData.motto}
                          onChange={handleInputChange}
                          placeholder="Enter organization motto or tagline"
                        />
                      </Form.Group>

                      <Form.Group className="mb-3">
                        <Form.Label className="fw-semibold">
                          <i className="fas fa-align-left me-2"></i>
                          Description
                        </Form.Label>
                        <Form.Control
                          as="textarea"
                          rows={4}
                          name="description"
                          value={formData.description}
                          onChange={handleInputChange}
                          placeholder="Brief description of the organization"
                        />
                      </Form.Group>

                      <Form.Group className="mb-3">
                        <Form.Label className="fw-semibold">
                          <i className="fas fa-image me-2"></i>
                          Organization Logo
                        </Form.Label>
                        <Form.Control
                          type="file"
                          name="logo"
                          onChange={handleFileChange}
                          accept="image/*"
                        />
                        <Form.Text className="text-muted">
                          Upload a logo for your organization (optional)
                        </Form.Text>
                      </Form.Group>
                    </Tab.Pane>

                    {/* Contact & Address Tab */}
                    <Tab.Pane eventKey="contact">
                      <h5 className="mb-3 text-primary">Contact Information</h5>
                      <Row>
                        <Col md={6}>
                          <Form.Group className="mb-3">
                            <Form.Label className="fw-semibold">
                              <i className="fas fa-envelope me-2"></i>
                              Email Address *
                            </Form.Label>
                            <Form.Control
                              type="email"
                              name="email"
                              value={formData.email}
                              onChange={handleInputChange}
                              placeholder="<EMAIL>"
                            />
                          </Form.Group>
                        </Col>
                        <Col md={6}>
                          <Form.Group className="mb-3">
                            <Form.Label className="fw-semibold">
                              <i className="fas fa-phone me-2"></i>
                              Phone Number
                            </Form.Label>
                            <Form.Control
                              type="tel"
                              name="phone"
                              value={formData.phone}
                              onChange={handleInputChange}
                              placeholder="+****************"
                            />
                          </Form.Group>
                        </Col>
                      </Row>

                      <Form.Group className="mb-4">
                        <Form.Label className="fw-semibold">
                          <i className="fas fa-globe me-2"></i>
                          Website
                        </Form.Label>
                        <Form.Control
                          type="url"
                          name="website"
                          value={formData.website}
                          onChange={handleInputChange}
                          placeholder="https://www.organization.com"
                        />
                      </Form.Group>

                      <h5 className="mb-3 text-primary">Address Information</h5>
                      <Form.Group className="mb-3">
                        <Form.Label className="fw-semibold">Address Line 1</Form.Label>
                        <Form.Control
                          type="text"
                          name="address_line_1"
                          value={formData.address_line_1}
                          onChange={handleInputChange}
                          placeholder="Street address"
                        />
                      </Form.Group>

                      <Form.Group className="mb-3">
                        <Form.Label className="fw-semibold">Address Line 2</Form.Label>
                        <Form.Control
                          type="text"
                          name="address_line_2"
                          value={formData.address_line_2}
                          onChange={handleInputChange}
                          placeholder="Apartment, suite, etc. (optional)"
                        />
                      </Form.Group>

                      <Row>
                        <Col md={6}>
                          <Form.Group className="mb-3">
                            <Form.Label className="fw-semibold">City</Form.Label>
                            <Form.Control
                              type="text"
                              name="city"
                              value={formData.city}
                              onChange={handleInputChange}
                              placeholder="City"
                            />
                          </Form.Group>
                        </Col>
                        <Col md={6}>
                          <Form.Group className="mb-3">
                            <Form.Label className="fw-semibold">State / Province</Form.Label>
                            <Form.Control
                              type="text"
                              name="state_province"
                              value={formData.state_province}
                              onChange={handleInputChange}
                              placeholder="State or Province"
                            />
                          </Form.Group>
                        </Col>
                      </Row>

                      <Row>
                        <Col md={6}>
                          <Form.Group className="mb-3">
                            <Form.Label className="fw-semibold">Postal Code</Form.Label>
                            <Form.Control
                              type="text"
                              name="postal_code"
                              value={formData.postal_code}
                              onChange={handleInputChange}
                              placeholder="Postal or ZIP code"
                            />
                          </Form.Group>
                        </Col>
                        <Col md={6}>
                          <Form.Group className="mb-3">
                            <Form.Label className="fw-semibold">Country</Form.Label>
                            <Form.Control
                              type="text"
                              name="country"
                              value={formData.country}
                              onChange={handleInputChange}
                              placeholder="Country"
                            />
                          </Form.Group>
                        </Col>
                      </Row>
                    </Tab.Pane>

                    {/* Social Media Tab */}
                    <Tab.Pane eventKey="social">
                      <h5 className="mb-3 text-primary">Social Media Links</h5>
                      <p className="text-muted mb-4">Add your organization's social media profiles (optional)</p>

                      <Row>
                        <Col md={6}>
                          <Form.Group className="mb-3">
                            <Form.Label className="fw-semibold">
                              <i className="fab fa-facebook me-2 text-primary"></i>
                              Facebook
                            </Form.Label>
                            <Form.Control
                              type="url"
                              name="facebook_url"
                              value={formData.facebook_url}
                              onChange={handleInputChange}
                              placeholder="https://facebook.com/yourorganization"
                            />
                          </Form.Group>
                        </Col>
                        <Col md={6}>
                          <Form.Group className="mb-3">
                            <Form.Label className="fw-semibold">
                              <i className="fab fa-twitter me-2 text-info"></i>
                              Twitter
                            </Form.Label>
                            <Form.Control
                              type="url"
                              name="twitter_url"
                              value={formData.twitter_url}
                              onChange={handleInputChange}
                              placeholder="https://twitter.com/yourorganization"
                            />
                          </Form.Group>
                        </Col>
                      </Row>

                      <Row>
                        <Col md={6}>
                          <Form.Group className="mb-3">
                            <Form.Label className="fw-semibold">
                              <i className="fab fa-linkedin me-2 text-primary"></i>
                              LinkedIn
                            </Form.Label>
                            <Form.Control
                              type="url"
                              name="linkedin_url"
                              value={formData.linkedin_url}
                              onChange={handleInputChange}
                              placeholder="https://linkedin.com/company/yourorganization"
                            />
                          </Form.Group>
                        </Col>
                        <Col md={6}>
                          <Form.Group className="mb-3">
                            <Form.Label className="fw-semibold">
                              <i className="fab fa-instagram me-2 text-danger"></i>
                              Instagram
                            </Form.Label>
                            <Form.Control
                              type="url"
                              name="instagram_url"
                              value={formData.instagram_url}
                              onChange={handleInputChange}
                              placeholder="https://instagram.com/yourorganization"
                            />
                          </Form.Group>
                        </Col>
                      </Row>
                    </Tab.Pane>

                    {/* Settings Tab */}
                    <Tab.Pane eventKey="settings">
                      <h5 className="mb-3 text-primary">Organization Settings</h5>

                      <Form.Group className="mb-3">
                        <Form.Check
                          type="checkbox"
                          name="is_active"
                          checked={formData.is_active}
                          onChange={handleInputChange}
                          label="Organization is active"
                          className="fs-6"
                        />
                        <Form.Text className="text-muted">
                          Active organizations can be used for events and other operations
                        </Form.Text>
                      </Form.Group>

                      <Form.Group className="mb-3">
                        <Form.Check
                          type="checkbox"
                          name="is_primary"
                          checked={formData.is_primary}
                          onChange={handleInputChange}
                          label="Set as primary organization"
                          className="fs-6"
                        />
                        <Form.Text className="text-muted">
                          The primary organization is used as the default for new events and communications
                        </Form.Text>
                      </Form.Group>
                    </Tab.Pane>
                  </Tab.Content>
                </Tab.Container>

                <div className="d-flex gap-3 mt-4">
                  <Button
                    variant="secondary"
                    onClick={() => navigate('/organizations')}
                    disabled={submitLoading}
                  >
                    <i className="fas fa-times me-2"></i>
                    Cancel
                  </Button>
                  <Button
                    type="submit"
                    variant="primary"
                    disabled={submitLoading}
                    className="flex-fill"
                  >
                    {submitLoading ? (
                      <>
                        <Spinner animation="border" size="sm" className="me-2" />
                        {isEdit ? 'Updating...' : 'Creating...'}
                      </>
                    ) : (
                      <>
                        <i className={`fas fa-${isEdit ? 'save' : 'plus'} me-2`}></i>
                        {isEdit ? 'Update Organization' : 'Create Organization'}
                      </>
                    )}
                  </Button>
                </div>
              </Form>
            </Card.Body>
          </Card>
        </Col>
      </Row>
    </Container>
  );
};

export default OrganizationForm;
