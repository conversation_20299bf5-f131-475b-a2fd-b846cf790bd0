# Use Python 3.11 slim image
FROM python:3.11-slim

# Set environment variables
ENV PYTHONDONTWRITEBYTECODE=1
ENV PYTHONUNBUFFERED=1

# Set work directory
WORKDIR /app

# Install system dependencies
RUN apt-get update \
    && apt-get install -y --no-install-recommends \
        postgresql-client \
        build-essential \
        libpq-dev \
        gettext \
        curl \
        netcat-openbsd \
        wait-for-it \
    && rm -rf /var/lib/apt/lists/*

# Install Python dependencies
COPY requirements.txt /app/
RUN pip install --no-cache-dir -r requirements.txt

# Copy project
COPY . /app/

# Create directories for media and static files with proper permissions
RUN mkdir -p /app/media /app/staticfiles

# Create health check directory and copy health check script
RUN mkdir -p /app/healthcheck

# Copy and set up entrypoint scripts
COPY docker-entrypoint.sh /usr/local/bin/
COPY celery-entrypoint.sh /usr/local/bin/
COPY healthcheck.py /app/healthcheck/
RUN chmod +x /usr/local/bin/docker-entrypoint.sh
RUN chmod +x /usr/local/bin/celery-entrypoint.sh
RUN chmod +x /app/healthcheck/healthcheck.py

# Create a non-root user
RUN adduser --disabled-password --gecos '' appuser

# Set proper ownership and permissions for all directories
RUN chown -R appuser:appuser /app
RUN chmod -R 755 /app/media /app/staticfiles

USER appuser

# Expose port
EXPOSE 8000

# Use entrypoint script
ENTRYPOINT ["docker-entrypoint.sh"]

# Run the application
CMD ["gunicorn", "--bind", "0.0.0.0:8000", "--workers", "3", "event_management.wsgi:application"]
