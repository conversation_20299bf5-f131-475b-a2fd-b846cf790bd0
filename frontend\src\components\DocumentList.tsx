import React, { useState, useEffect } from 'react';
import { <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, Spin<PERSON>, Row, Col, Form, InputGroup } from 'react-bootstrap';
import { documentService, EventDocument } from '../services/api';

interface DocumentListProps {
  eventId?: number;
  showUploadButton?: boolean;
  onUploadClick?: () => void;
  documents?: EventDocument[];
  loading?: boolean;
  error?: string;
  onRefresh?: () => void;
}

const DocumentList: React.FC<DocumentListProps> = ({
  eventId,
  showUploadButton = false,
  onUploadClick,
  documents: propDocuments,
  loading: propLoading,
  error: propError,
  onRefresh
}) => {
  const [documents, setDocuments] = useState<EventDocument[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [searchTerm, setSearchTerm] = useState('');
  const [filterType, setFilterType] = useState('all');

  // Use prop documents if provided, otherwise fetch them
  const displayDocuments = propDocuments || documents;
  const displayLoading = propLoading !== undefined ? propLoading : loading;
  const displayError = propError || error;

  useEffect(() => {
    if (!propDocuments && eventId) {
      fetchDocuments();
    }
  }, [eventId, propDocuments]);

  const fetchDocuments = async () => {
    if (!eventId) return;
    
    setLoading(true);
    setError('');
    try {
      const response = await documentService.getDocuments(eventId);
      setDocuments(response.data.results || []);
    } catch (error) {
      console.error('Error fetching documents:', error);
      setError('Failed to load documents');
    } finally {
      setLoading(false);
    }
  };

  const handleDocumentDownload = async (doc: EventDocument) => {
    try {
      const response = await documentService.downloadDocument(doc.id);

      // Create blob and download
      const blob = new Blob([response.data]);
      const url = window.URL.createObjectURL(blob);
      const link = window.document.createElement('a');
      link.href = url;
      link.download = doc.file.split('/').pop() || doc.title;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);
    } catch (error) {
      console.error('Error downloading document:', error);
      alert('Failed to download document');
    }
  };

  const getFileIcon = (extension: string): string => {
    switch (extension.toLowerCase()) {
      case 'pdf':
        return 'fas fa-file-pdf text-danger';
      case 'doc':
      case 'docx':
        return 'fas fa-file-word text-primary';
      case 'ppt':
      case 'pptx':
        return 'fas fa-file-powerpoint text-warning';
      case 'xls':
      case 'xlsx':
        return 'fas fa-file-excel text-success';
      case 'txt':
        return 'fas fa-file-alt text-secondary';
      default:
        return 'fas fa-file text-muted';
    }
  };

  const getTypeColor = (type: string): string => {
    switch (type.toLowerCase()) {
      case 'presentation': return 'primary';
      case 'schedule': return 'success';
      case 'agenda': return 'info';
      case 'brochure': return 'warning';
      case 'certificate': return 'danger';
      case 'report': return 'dark';
      default: return 'secondary';
    }
  };

  // Filter documents based on search term and type
  const filteredDocuments = (displayDocuments || []).filter(doc => {
    const matchesSearch = doc.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         doc.description.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesType = filterType === 'all' || doc.document_type === filterType;
    return matchesSearch && matchesType;
  });

  // Get unique document types for filter
  const documentTypes = Array.from(new Set((displayDocuments || []).map(doc => doc.document_type)));

  if (displayLoading) {
    return (
      <div className="text-center py-4">
        <Spinner animation="border" role="status">
          <span className="visually-hidden">Loading documents...</span>
        </Spinner>
        <p className="mt-3">Loading documents...</p>
      </div>
    );
  }

  if (displayError) {
    return (
      <Alert variant="danger" className="d-flex justify-content-between align-items-center">
        <span>
          <i className="fas fa-exclamation-triangle me-2"></i>
          {displayError}
        </span>
        {onRefresh && (
          <Button variant="outline-danger" size="sm" onClick={onRefresh}>
            <i className="fas fa-redo me-1"></i>
            Retry
          </Button>
        )}
      </Alert>
    );
  }

  return (
    <div>
      {/* Header with search and filters */}
      <div className="d-flex justify-content-between align-items-center mb-4">
        <h5 className="mb-0">
          <i className="fas fa-file-alt me-2"></i>
          Documents ({displayDocuments.length})
        </h5>
        {showUploadButton && onUploadClick && (
          <Button variant="primary" size="sm" onClick={onUploadClick}>
            <i className="fas fa-plus me-1"></i>
            Upload Documents
          </Button>
        )}
      </div>

      {/* Search and Filter */}
      {displayDocuments.length > 0 && (
        <Row className="mb-4">
          <Col md={8}>
            <InputGroup>
              <InputGroup.Text>
                <i className="fas fa-search"></i>
              </InputGroup.Text>
              <Form.Control
                type="text"
                placeholder="Search documents..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
            </InputGroup>
          </Col>
          <Col md={4}>
            <Form.Select
              value={filterType}
              onChange={(e) => setFilterType(e.target.value)}
            >
              <option value="all">All Types</option>
              {documentTypes.map(type => (
                <option key={type} value={type}>
                  {type.charAt(0).toUpperCase() + type.slice(1)}
                </option>
              ))}
            </Form.Select>
          </Col>
        </Row>
      )}

      {/* Documents Grid */}
      {filteredDocuments.length === 0 ? (
        <div className="text-center py-5">
          <i className="fas fa-file-alt text-muted fa-3x mb-3"></i>
          <h5 className="text-muted">
            {displayDocuments.length === 0 ? 'No Documents Available' : 'No Documents Match Your Search'}
          </h5>
          <p className="text-muted">
            {displayDocuments.length === 0 
              ? 'Event documents will be published here when available.'
              : 'Try adjusting your search terms or filters.'
            }
          </p>
          {searchTerm && (
            <Button variant="outline-secondary" onClick={() => setSearchTerm('')}>
              Clear Search
            </Button>
          )}
        </div>
      ) : (
        <Row>
          {filteredDocuments.map((doc) => (
            <Col key={doc.id} md={6} lg={4} className="mb-4">
              <Card className="h-100 border-0 shadow-sm hover-shadow">
                <Card.Body className="d-flex flex-column">
                  <div className="d-flex align-items-center mb-3">
                    <div className="me-3">
                      <i className={`${getFileIcon(doc.file_extension)} fa-2x`}></i>
                    </div>
                    <div className="flex-grow-1">
                      <h6 className="mb-1 fw-bold">{doc.title}</h6>
                      <div className="d-flex flex-wrap gap-1">
                        <Badge bg={getTypeColor(doc.document_type)}>
                          {doc.document_type_display}
                        </Badge>
                        <Badge bg="light" text="dark">
                          {doc.formatted_file_size}
                        </Badge>
                      </div>
                    </div>
                  </div>

                  {doc.description && (
                    <p className="text-muted small mb-3 flex-grow-1">
                      {doc.description}
                    </p>
                  )}

                  <div className="mt-auto">
                    <div className="d-flex justify-content-between align-items-center mb-2">
                      <small className="text-muted">
                        <i className="fas fa-download me-1"></i>
                        {doc.download_count} downloads
                      </small>
                      <small className="text-muted">
                        <i className="fas fa-clock me-1"></i>
                        {new Date(doc.uploaded_at).toLocaleDateString()}
                      </small>
                    </div>

                    <Button
                      variant="primary"
                      size="sm"
                      className="w-100"
                      onClick={() => handleDocumentDownload(doc)}
                    >
                      <i className="fas fa-download me-1"></i>
                      Download
                    </Button>
                  </div>
                </Card.Body>
              </Card>
            </Col>
          ))}
        </Row>
      )}
    </div>
  );
};

export default DocumentList;
