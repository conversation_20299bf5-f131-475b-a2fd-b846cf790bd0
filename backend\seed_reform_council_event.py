#!/usr/bin/env python
"""
Sc<PERSON><PERSON> to seed the Ethiopian Public Higher Education Institutions Reform Council Annual Meeting
Event ID: 6
Date: August 4-6, 2025
Location: University of Gondar, Ethiopia
"""
import os
import sys
import django
from datetime import datetime, timedelta
from django.utils import timezone

# Setup Django
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'event_management.settings')
django.setup()

from events.models import Event, EventSchedule

def create_reform_council_event():
    """Create the Ethiopian Public Higher Education Institutions Reform Council Annual Meeting"""
    
    print("🎓 Creating Ethiopian Public Higher Education Institutions Reform Council Annual Meeting...")
    
    # Set specific dates for August 4-6, 2025
    start_date = timezone.datetime(2025, 8, 4, 8, 30, 0)  # August 4, 2025, 8:30 AM
    end_date = timezone.datetime(2025, 8, 5, 17, 30, 0)   # August 5, 2025, 5:30 PM
    
    # Make timezone aware
    start_date = timezone.make_aware(start_date)
    end_date = timezone.make_aware(end_date)
    
    # Create or update the event with ID 6
    event_data = {
        'name': 'Ethiopian Public Higher Education Institutions Reform Council Annual Meeting',
        'description': '''The Ethiopian Public Higher Education Institutions Reform Council Annual Meeting is a prestigious gathering of university leaders, policy makers, and education stakeholders from across Ethiopia. This comprehensive three-day event focuses on advancing higher education reform initiatives, sharing best practices, and fostering collaboration among Ethiopian universities.

Key highlights include:
• Updates on Higher Education Reform initiatives
• Performance review and baseline data analysis
• Thematic research handbook presentations
• University cluster performance showcasing
• Policy discussions and strategic planning
• Networking opportunities with education leaders

This meeting serves as a crucial platform for shaping the future of higher education in Ethiopia, promoting excellence in teaching, research, and community service across all participating institutions.''',
        'start_date': start_date,
        'end_date': end_date,
        'location': 'University of Gondar Main Campus - Conference Center',
        'city': 'Gondar',
        'country': 'Ethiopia',
        'latitude': 12.6090,
        'longitude': 37.4692,
        'organizer_name': 'Ethiopian Ministry of Education - Higher Education Reform Council',
        'organizer_email': '<EMAIL>',
        'organizer_phone': '+251-11-155-0000',
        'is_active': True,
    }
    
    # Delete existing event with ID 6 if it exists
    Event.objects.filter(id=6).delete()
    
    # Create the event
    event = Event.objects.create(id=6, **event_data)
    
    print(f"✅ Created event: {event.name}")
    print(f"   Event ID: {event.id}")
    print(f"   Dates: {event.start_date.date()} to {event.end_date.date()}")
    
    # Create detailed schedule
    create_detailed_schedule(event)
    
    return event

def create_detailed_schedule(event):
    """Create the detailed schedule for the Reform Council meeting"""
    
    print("📅 Creating detailed schedule...")
    
    # Base dates
    day1 = timezone.datetime(2025, 8, 4)  # Monday, August 4, 2025
    day2 = timezone.datetime(2025, 8, 5)  # Tuesday, August 5, 2025
    
    # Monday, August 4, 2025 Schedule
    monday_schedule = [
        {
            'title': 'Registration',
            'description': 'Participant registration and welcome reception',
            'start_time': day1.replace(hour=8, minute=30),
            'end_time': day1.replace(hour=9, minute=0),
            'location': 'Conference Center Lobby',
            'speaker': '',
            'session_type': 'break',
            'is_break': True,
        },
        {
            'title': 'Welcoming Speech',
            'description': 'Official welcome address to all participants',
            'start_time': day1.replace(hour=9, minute=0),
            'end_time': day1.replace(hour=9, minute=25),
            'location': 'Main Conference Hall',
            'speaker': 'Dr. Asrat Haregwoin',
            'session_type': 'presentation',
            'is_break': False,
        },
        {
            'title': 'Opening Remarks',
            'description': 'Opening remarks and conference overview',
            'start_time': day1.replace(hour=9, minute=25),
            'end_time': day1.replace(hour=9, minute=40),
            'location': 'Main Conference Hall',
            'speaker': 'Ato Kora Tushune',
            'session_type': 'presentation',
            'is_break': False,
        },
        {
            'title': 'Updates on the Higher Education Reform',
            'description': 'Comprehensive overview of current higher education reform initiatives and progress',
            'start_time': day1.replace(hour=9, minute=40),
            'end_time': day1.replace(hour=10, minute=15),
            'location': 'Main Conference Hall',
            'speaker': 'Ato Kora Tushune',
            'session_type': 'presentation',
            'is_break': False,
        },
        {
            'title': 'Discussion on Policy Papers, Directives, and Guidelines',
            'description': 'Review and discussion of new policy frameworks and implementation guidelines',
            'start_time': day1.replace(hour=10, minute=15),
            'end_time': day1.replace(hour=10, minute=40),
            'location': 'Main Conference Hall',
            'speaker': 'Dr. Solomon Abrha',
            'session_type': 'presentation',
            'is_break': False,
        },
        {
            'title': 'Tea Break',
            'description': 'Networking break with refreshments',
            'start_time': day1.replace(hour=10, minute=40),
            'end_time': day1.replace(hour=11, minute=0),
            'location': 'Conference Center Terrace',
            'speaker': '',
            'session_type': 'break',
            'is_break': True,
        },
        {
            'title': 'Thematic Research Handbook and BTIC Guideline',
            'description': 'Presentation of thematic research frameworks and Business Technology Innovation Center guidelines',
            'start_time': day1.replace(hour=11, minute=0),
            'end_time': day1.replace(hour=11, minute=25),
            'location': 'Main Conference Hall',
            'speaker': 'Dr. Serawit Handiso',
            'session_type': 'presentation',
            'is_break': False,
        },
        {
            'title': 'Updates on Differentiation Roadmap and Progress So Far',
            'description': 'Progress report on university differentiation initiatives and future roadmap',
            'start_time': day1.replace(hour=11, minute=25),
            'end_time': day1.replace(hour=12, minute=0),
            'location': 'Main Conference Hall',
            'speaker': 'Dr. Eba Mijena',
            'session_type': 'presentation',
            'is_break': False,
        },
        {
            'title': 'Overview of the Findings from Performance Baseline Data Analysis',
            'description': 'Comprehensive analysis of university performance metrics and baseline data findings',
            'start_time': day1.replace(hour=12, minute=0),
            'end_time': day1.replace(hour=12, minute=30),
            'location': 'Main Conference Hall',
            'speaker': 'Ato Tesfaye Negewo',
            'session_type': 'presentation',
            'is_break': False,
        },
        {
            'title': 'Lunch Time',
            'description': 'Networking lunch for all participants',
            'start_time': day1.replace(hour=12, minute=30),
            'end_time': day1.replace(hour=14, minute=0),
            'location': 'University Dining Hall',
            'speaker': '',
            'session_type': 'break',
            'is_break': True,
        },
        {
            'title': 'Plenary Discussion',
            'description': 'Open floor discussion on morning presentations and reform initiatives',
            'start_time': day1.replace(hour=14, minute=0),
            'end_time': day1.replace(hour=16, minute=0),
            'location': 'Main Conference Hall',
            'speaker': 'All Participants',
            'session_type': 'workshop',
            'is_break': False,
        },
        {
            'title': 'Tea Break',
            'description': 'Afternoon refreshment break',
            'start_time': day1.replace(hour=16, minute=0),
            'end_time': day1.replace(hour=16, minute=20),
            'location': 'Conference Center Terrace',
            'speaker': '',
            'session_type': 'break',
            'is_break': True,
        },
        {
            'title': 'Discussion and Closing Remark',
            'description': 'Final discussions and closing remarks for Day 1',
            'start_time': day1.replace(hour=16, minute=20),
            'end_time': day1.replace(hour=17, minute=15),
            'location': 'Main Conference Hall',
            'speaker': 'Conference Organizers',
            'session_type': 'presentation',
            'is_break': False,
        },
        {
            'title': 'Announcements and Reminders',
            'description': 'Important announcements and reminders for Day 2',
            'start_time': day1.replace(hour=17, minute=15),
            'end_time': day1.replace(hour=17, minute=30),
            'location': 'Main Conference Hall',
            'speaker': 'Conference Secretariat',
            'session_type': 'other',
            'is_break': False,
        },
    ]
    
    # Tuesday, August 5, 2025 Schedule
    tuesday_schedule = [
        {
            'title': 'Briefing on Review of 2017 EC Performance (Showcasing)',
            'description': 'Introduction and briefing on the university performance review process for Ethiopian Calendar year 2017',
            'start_time': day2.replace(hour=8, minute=30),
            'end_time': day2.replace(hour=9, minute=0),
            'location': 'Main Conference Hall',
            'speaker': 'Review Committee',
            'session_type': 'presentation',
            'is_break': False,
        },
        {
            'title': 'Review of the 2017 EC Performance of Universities in Clusters (Session 1)',
            'description': 'Universities present their 2017 Ethiopian Calendar performance reports organized by clusters',
            'start_time': day2.replace(hour=9, minute=0),
            'end_time': day2.replace(hour=10, minute=40),
            'location': 'Main Conference Hall',
            'speaker': 'University Representatives',
            'session_type': 'presentation',
            'is_break': False,
        },
        {
            'title': 'Tea Break',
            'description': 'Morning refreshment break',
            'start_time': day2.replace(hour=10, minute=40),
            'end_time': day2.replace(hour=11, minute=0),
            'location': 'Conference Center Terrace',
            'speaker': '',
            'session_type': 'break',
            'is_break': True,
        },
        {
            'title': 'Review of the 2017 EC Performance of Universities in Clusters (Session 2)',
            'description': 'Continuation of university cluster performance presentations',
            'start_time': day2.replace(hour=11, minute=0),
            'end_time': day2.replace(hour=12, minute=30),
            'location': 'Main Conference Hall',
            'speaker': 'University Representatives',
            'session_type': 'presentation',
            'is_break': False,
        },
        {
            'title': 'Lunch Time',
            'description': 'Networking lunch for all participants',
            'start_time': day2.replace(hour=12, minute=30),
            'end_time': day2.replace(hour=14, minute=0),
            'location': 'University Dining Hall',
            'speaker': '',
            'session_type': 'break',
            'is_break': True,
        },
        {
            'title': 'Review of the 2017 EC Performance of Universities in Clusters (Session 3)',
            'description': 'Final session of university cluster performance presentations',
            'start_time': day2.replace(hour=14, minute=0),
            'end_time': day2.replace(hour=16, minute=0),
            'location': 'Main Conference Hall',
            'speaker': 'University Representatives',
            'session_type': 'presentation',
            'is_break': False,
        },
        {
            'title': 'Discussion',
            'description': 'Comprehensive discussion on university performance reviews and future directions',
            'start_time': day2.replace(hour=16, minute=0),
            'end_time': day2.replace(hour=17, minute=30),
            'location': 'Main Conference Hall',
            'speaker': 'HE Prof. Berhanu Nega',
            'session_type': 'workshop',
            'is_break': False,
        },
    ]
    
    # Combine all schedules
    all_schedules = monday_schedule + tuesday_schedule
    
    # Create schedule items
    for schedule_data in all_schedules:
        schedule_data['event'] = event
        schedule_data['start_time'] = timezone.make_aware(schedule_data['start_time'])
        schedule_data['end_time'] = timezone.make_aware(schedule_data['end_time'])
        
        schedule = EventSchedule.objects.create(**schedule_data)
        print(f"   ✓ {schedule.start_time.strftime('%b %d, %H:%M')} - {schedule.title}")
    
    print(f"✅ Created {len(all_schedules)} schedule items")

if __name__ == "__main__":
    create_reform_council_event()
    print("\n🎉 Ethiopian Public Higher Education Institutions Reform Council Annual Meeting seeded successfully!")
    print("   Event ID: 6")
    print("   Access URL: http://localhost/events/6")
