from django.core.management.base import BaseCommand
from authentication.models import UserRole

class Command(BaseCommand):
    help = 'Create default user roles with permissions'
    
    def handle(self, *args, **options):
        roles_data = [
            {
                'name': UserRole.ADMIN,
                'display_name': 'Administrator',
                'description': 'Full system access with all permissions',
                'color': '#dc3545',
                'permissions': {
                    'can_manage_users': True,
                    'can_manage_events': True,
                    'can_manage_participants': True,
                    'can_take_attendance': True,
                    'can_upload_gallery': True,
                    'can_manage_schedule': True,
                    'can_generate_badges': True,
                    'can_manage_hotels': True,
                    'can_manage_drivers': True,
                    'can_view_reports': True,
                    'can_export_data': True,
                }
            },
            {
                'name': UserRole.ATTENDANCE_TAKER,
                'display_name': 'Attendance Taker',
                'description': 'Can take attendance and view participant information',
                'color': '#28a745',
                'permissions': {
                    'can_take_attendance': True,
                    'can_view_reports': True,
                }
            },
            {
                'name': UserRole.GALLERY_UPLOADER,
                'display_name': 'Gallery Uploader',
                'description': 'Can upload and manage event photos',
                'color': '#17a2b8',
                'permissions': {
                    'can_upload_gallery': True,
                }
            },
            {
                'name': UserRole.EVENT_SCHEDULER,
                'display_name': 'Event Scheduler',
                'description': 'Can manage event schedules and sessions',
                'color': '#ffc107',
                'permissions': {
                    'can_manage_schedule': True,
                    'can_view_reports': True,
                }
            },
            {
                'name': UserRole.EVENT_ORGANIZER,
                'display_name': 'Event Organizer',
                'description': 'Can manage events, participants, and generate badges',
                'color': '#6f42c1',
                'permissions': {
                    'can_manage_events': True,
                    'can_manage_participants': True,
                    'can_generate_badges': True,
                    'can_manage_hotels': True,
                    'can_manage_drivers': True,
                    'can_view_reports': True,
                    'can_export_data': True,
                }
            },
            {
                'name': UserRole.PARTICIPANT,
                'display_name': 'Participant',
                'description': 'Basic participant access',
                'color': '#6c757d',
                'permissions': {}
            }
        ]
        
        created_count = 0
        updated_count = 0
        
        for role_data in roles_data:
            permissions = role_data.pop('permissions')
            role, created = UserRole.objects.get_or_create(
                name=role_data['name'],
                defaults=role_data
            )
            
            if created:
                created_count += 1
                self.stdout.write(
                    self.style.SUCCESS(f'Created role: {role.display_name}')
                )
            else:
                # Update existing role
                for key, value in role_data.items():
                    setattr(role, key, value)
                updated_count += 1
                self.stdout.write(
                    self.style.WARNING(f'Updated role: {role.display_name}')
                )
            
            # Set permissions
            for permission, value in permissions.items():
                setattr(role, permission, value)
            
            role.save()
        
        self.stdout.write(
            self.style.SUCCESS(
                f'Successfully processed {created_count} new roles and updated {updated_count} existing roles'
            )
        )
