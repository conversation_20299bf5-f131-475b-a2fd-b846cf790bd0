from django.core.management.base import BaseCommand
from participants.models import Participant, ParticipantType
from events.models import Event
from django.utils import timezone


class Command(BaseCommand):
    help = 'Test participant approval API functionality'

    def handle(self, *args, **options):
        self.stdout.write("🧪 Testing Participant Approval API...")
        
        # Get test data
        try:
            event = Event.objects.first()
            participant_type = ParticipantType.objects.first()
            
            if not event or not participant_type:
                self.stdout.write("❌ No test data available (need event and participant type)")
                return
                
            self.stdout.write(f"📅 Using event: {event.name}")
            self.stdout.write(f"👥 Using participant type: {participant_type.name}")
            
        except Exception as e:
            self.stdout.write(f"❌ Error getting test data: {e}")
            return
        
        # Create test participant for approval testing
        try:
            test_participant = Participant.objects.create(
                first_name="Test",
                last_name="Approval",
                email="<EMAIL>",
                phone="+251-911-000001",
                institution_name="Test Institution",
                position="Test Position",
                event=event,
                participant_type=participant_type,
                arrival_date=timezone.now() + timezone.timedelta(days=1),
                departure_date=timezone.now() + timezone.timedelta(days=3),
                status='pending'
            )
            
            self.stdout.write(f"✅ Created test participant: {test_participant.full_name}")
            self.stdout.write(f"   Status: {test_participant.status}")
            self.stdout.write(f"   Email: {test_participant.email}")
            
        except Exception as e:
            self.stdout.write(f"❌ Error creating test participant: {e}")
            return
        
        # Test approval process
        try:
            self.stdout.write("\n🔄 Testing approval process...")
            
            # Check initial status
            self.stdout.write(f"   Initial status: {test_participant.status}")
            self.stdout.write(f"   Initial approved_at: {test_participant.approved_at}")
            
            # Simulate approval (like the API would do)
            old_status = test_participant.status
            test_participant.status = 'approved'
            test_participant.approved_at = timezone.now()
            test_participant.save()  # This should trigger email signal
            
            self.stdout.write(f"   ✅ Status changed from '{old_status}' to '{test_participant.status}'")
            self.stdout.write(f"   ✅ Approved at: {test_participant.approved_at}")
            
            # Check if email was logged (should be triggered by signal)
            from events.models import EmailLog
            approval_email = EmailLog.objects.filter(
                recipient_email=test_participant.email,
                template_type='event_details'
            ).order_by('-created_at').first()
            
            if approval_email:
                self.stdout.write(f"   ✅ Approval email logged: {approval_email.status} at {approval_email.created_at}")
            else:
                self.stdout.write("   ⚠️ No approval email found in logs")
            
        except Exception as e:
            self.stdout.write(f"❌ Error in approval process: {e}")
            import traceback
            traceback.print_exc()
        
        # Test rejection process
        try:
            self.stdout.write("\n🔄 Testing rejection process...")
            
            # Reset to pending
            test_participant.status = 'pending'
            test_participant.approved_at = None
            test_participant.save()
            
            # Simulate rejection
            test_participant.status = 'rejected'
            test_participant.save()
            
            self.stdout.write(f"   ✅ Status changed to: {test_participant.status}")
            
        except Exception as e:
            self.stdout.write(f"❌ Error in rejection process: {e}")
        
        # Test API endpoint simulation
        try:
            self.stdout.write("\n🔗 Testing API endpoint simulation...")
            
            # Reset to pending for API test
            test_participant.status = 'pending'
            test_participant.approved_at = None
            test_participant.save()
            
            # Simulate what the API endpoint does
            from participants.views import ParticipantViewSet
            from rest_framework.test import APIRequestFactory
            from django.contrib.auth import get_user_model
            
            User = get_user_model()
            admin_user = User.objects.filter(is_superuser=True).first()
            
            if admin_user:
                factory = APIRequestFactory()
                request = factory.post(f'/api/participants/{test_participant.id}/approve/')
                request.user = admin_user
                
                viewset = ParticipantViewSet()
                viewset.request = request
                
                # Simulate the approve action
                response = viewset.approve(request, pk=test_participant.id)
                
                self.stdout.write(f"   ✅ API response status: {response.status_code}")
                self.stdout.write(f"   ✅ API response data: {response.data}")
                
                # Refresh participant from database
                test_participant.refresh_from_db()
                self.stdout.write(f"   ✅ Final status: {test_participant.status}")
                self.stdout.write(f"   ✅ Final approved_at: {test_participant.approved_at}")
                
            else:
                self.stdout.write("   ⚠️ No admin user found for API test")
            
        except Exception as e:
            self.stdout.write(f"❌ Error in API simulation: {e}")
            import traceback
            traceback.print_exc()
        
        # Cleanup
        try:
            test_participant.delete()
            self.stdout.write("\n🧹 Test participant cleaned up")
        except Exception as e:
            self.stdout.write(f"⚠️ Error cleaning up: {e}")
        
        self.stdout.write("\n✅ Participant approval API test completed!")
