#!/usr/bin/env python
"""
Test script for the simplified SVG-based badge generation
"""
import os
import sys
import django

# Add the project root to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'event_management.settings')
django.setup()

from badges.models import Badge
from participants.models import Participant

def test_simple_badge():
    """Test the new simplified badge generation"""
    print("🧪 Testing simplified SVG badge generation...")
    
    # Get a participant
    try:
        participant = Participant.objects.first()
        if not participant:
            print("❌ No participants found. Please create a participant first.")
            return
        
        print(f"📋 Using participant: {participant.first_name} {participant.last_name}")
        
        # Get or create badge
        badge, created = Badge.objects.get_or_create(participant=participant)
        
        if created:
            print("✅ Created new badge")
        else:
            print("🔄 Using existing badge")
        
        # Generate badge
        print("🎨 Generating badge...")
        badge_img = badge.generate_badge()
        
        print(f"✅ Badge generated successfully!")
        print(f"📁 Badge saved to: {badge.badge_image.path if badge.badge_image else 'Not saved'}")
        print(f"📏 Badge dimensions: {badge_img.size}")
        
        # Save a test copy
        test_path = os.path.join(os.path.dirname(__file__), 'test_badge_simple.png')
        badge_img.save(test_path)
        print(f"💾 Test copy saved to: {test_path}")
        
    except Exception as e:
        print(f"❌ Error: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_simple_badge()
