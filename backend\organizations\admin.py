from django.contrib import admin
from django.http import HttpResponse, JsonResponse
from django.shortcuts import render, redirect
from django.contrib import messages
from django.urls import path, reverse
from django.utils.html import format_html
from django.template.response import TemplateResponse
from django.contrib.admin.views.decorators import staff_member_required
from django.utils.decorators import method_decorator
from django.views.decorators.csrf import csrf_exempt
import json
from .models import Organization, OrganizationSettings


class OrganizationSettingsInline(admin.StackedInline):
    model = OrganizationSettings
    extra = 0
    fieldsets = (
        ('Event Settings', {
            'fields': ('default_event_duration_hours', 'default_registration_fee')
        }),
        ('Email Settings', {
            'fields': ('email_signature',)
        }),
        ('Branding Settings', {
            'fields': ('primary_color', 'secondary_color')
        }),
        ('Notification Settings', {
            'fields': ('send_welcome_emails', 'send_confirmation_emails', 'send_reminder_emails')
        }),
    )


@admin.register(Organization)
class OrganizationAdmin(admin.ModelAdmin):
    list_display = [
        'name', 'short_name', 'email', 'phone', 'city', 'country', 
        'is_active', 'is_primary', 'created_at'
    ]
    list_filter = ['is_active', 'is_primary', 'country', 'created_at']
    search_fields = ['name', 'short_name', 'email', 'city']
    readonly_fields = ['created_at', 'updated_at', 'full_address', 'display_name']
    inlines = [OrganizationSettingsInline]
    
    fieldsets = (
        ('Basic Information', {
            'fields': ('name', 'short_name', 'logo', 'motto', 'description')
        }),
        ('Contact Information', {
            'fields': ('email', 'phone', 'website')
        }),
        ('Address', {
            'fields': (
                'address_line_1', 'address_line_2', 'city', 
                'state_province', 'postal_code', 'country', 'full_address'
            )
        }),
        ('Social Media', {
            'fields': ('facebook_url', 'twitter_url', 'linkedin_url', 'instagram_url'),
            'classes': ('collapse',)
        }),
        ('Settings', {
            'fields': ('is_active', 'is_primary')
        }),
        ('Timestamps', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )
    
    def save_model(self, request, obj, form, change):
        """Ensure only one primary organization"""
        if obj.is_primary:
            Organization.objects.filter(is_primary=True).exclude(pk=obj.pk).update(is_primary=False)
        super().save_model(request, obj, form, change)


@admin.register(OrganizationSettings)
class OrganizationSettingsAdmin(admin.ModelAdmin):
    list_display = [
        'organization', 'default_event_duration_hours', 'default_registration_fee',
        'primary_color', 'secondary_color', 'updated_at'
    ]
    list_filter = ['send_welcome_emails', 'send_confirmation_emails', 'send_reminder_emails']
    search_fields = ['organization__name']
    readonly_fields = ['created_at', 'updated_at', 'bulk_email_link']

    fieldsets = (
        ('Organization', {
            'fields': ('organization',)
        }),
        ('Event Settings', {
            'fields': ('default_event_duration_hours', 'default_registration_fee')
        }),
        ('Email Settings', {
            'fields': ('email_signature',)
        }),
        ('Branding Settings', {
            'fields': ('primary_color', 'secondary_color')
        }),
        ('Notification Settings', {
            'fields': ('send_welcome_emails', 'send_confirmation_emails', 'send_reminder_emails')
        }),
        ('Bulk Email Management', {
            'description': 'Send bulk announcements to event participants',
            'fields': ('bulk_email_link',),
        }),
        ('Timestamps', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )

    def get_urls(self):
        urls = super().get_urls()
        custom_urls = [
            path('bulk-email/', self.admin_site.admin_view(self.bulk_email_view), name='organizations_bulk_email'),
            path('bulk-email/send/', self.admin_site.admin_view(self.send_bulk_email), name='organizations_send_bulk_email'),
        ]
        return custom_urls + urls

    def bulk_email_view(self, request):
        """Display bulk email form"""
        from events.models import Event
        from participants.models import ParticipantType

        context = {
            'title': 'Bulk Email Announcements',
            'events': Event.objects.filter(is_active=True).order_by('-created_at'),
            'participant_types': ParticipantType.objects.all().order_by('name'),
            'opts': self.model._meta,
            'has_change_permission': self.has_change_permission(request),
        }

        return TemplateResponse(request, 'admin/organizations/bulk_email.html', context)

    @method_decorator(csrf_exempt)
    def send_bulk_email(self, request):
        """Handle bulk email sending"""
        if request.method != 'POST':
            return JsonResponse({'error': 'Method not allowed'}, status=405)

        try:
            data = json.loads(request.body)
            subject = data.get('subject', '').strip()
            content = data.get('content', '').strip()
            event_id = data.get('event_id')
            recipient_type = data.get('recipient_type', 'all')
            participant_type_ids = data.get('participant_type_ids', [])

            if not subject or not content:
                return JsonResponse({'error': 'Subject and content are required'}, status=400)

            if not event_id:
                return JsonResponse({'error': 'Event selection is required'}, status=400)

            # Import here to avoid circular imports
            from events.models import Event
            from participants.models import Participant, ParticipantType
            from events.email_service import get_email_service

            try:
                event = Event.objects.get(id=event_id)
            except Event.DoesNotExist:
                return JsonResponse({'error': 'Event not found'}, status=404)

            # Build participant queryset based on filters
            participants_qs = Participant.objects.filter(event=event, is_confirmed=True)

            if recipient_type == 'participant_type' and participant_type_ids:
                participants_qs = participants_qs.filter(participant_type_id__in=participant_type_ids)

            participants = list(participants_qs)

            if not participants:
                return JsonResponse({'error': 'No participants found matching the criteria'}, status=400)

            # Send emails
            email_service = get_email_service()
            if not email_service.config:
                return JsonResponse({'error': 'Email configuration not found. Please configure email settings first.'}, status=400)

            success_count = 0
            failed_count = 0

            for participant in participants:
                try:
                    # Create context for email
                    context = {
                        'participant_name': participant.full_name,
                        'event_name': event.name,
                        'event_date': event.start_date.strftime('%B %d, %Y') if event.start_date else 'TBD',
                        'event_location': event.location or 'TBD',
                        'organization_name': 'University of Gondar',
                        'content': content,
                    }

                    # Render content with context
                    rendered_content = content.format(**context)
                    rendered_subject = subject.format(**context)

                    # Create HTML email content
                    html_content = f"""
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{rendered_subject}</title>
    <style>
        body {{ font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; margin: 0; padding: 0; background-color: #f8f9fa; }}
        .container {{ max-width: 600px; margin: 0 auto; background-color: #ffffff; }}
        .header {{ background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%); color: white; padding: 30px; text-align: center; }}
        .header h1 {{ margin: 0; font-size: 28px; font-weight: 300; }}
        .content {{ padding: 40px 30px; }}
        .content h2 {{ color: #1e3c72; margin-top: 0; }}
        .content p {{ line-height: 1.6; color: #333; margin-bottom: 15px; }}
        .event-info {{ background-color: #f8f9fa; padding: 20px; border-radius: 8px; margin: 20px 0; }}
        .event-info h3 {{ color: #1e3c72; margin-top: 0; }}
        .footer {{ background-color: #1e3c72; color: white; padding: 20px; text-align: center; font-size: 14px; }}
        .footer a {{ color: #ffffff; text-decoration: none; }}
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>📢 Event Announcement</h1>
        </div>
        <div class="content">
            <h2>Dear {participant.full_name},</h2>
            <div style="white-space: pre-line;">{rendered_content}</div>

            <div class="event-info">
                <h3>📅 Event Information</h3>
                <p><strong>Event:</strong> {event.name}</p>
                <p><strong>Date:</strong> {context['event_date']}</p>
                <p><strong>Location:</strong> {context['event_location']}</p>
            </div>

            <p>If you have any questions, please don't hesitate to contact our events team.</p>

            <p>Best regards,<br>
            <strong>University of Gondar Events Team</strong></p>
        </div>
        <div class="footer">
            <p>📧 <EMAIL> | 📞 +251-58-114-1240<br>
            🌐 <a href="https://www.uog.edu.et">www.uog.edu.et</a></p>
        </div>
    </div>
</body>
</html>
                    """

                    # Send email using the email service
                    if email_service._send_raw_email(
                        recipient_email=participant.email,
                        recipient_name=participant.full_name,
                        subject=rendered_subject,
                        html_content=html_content,
                        template_type='custom'
                    ):
                        success_count += 1
                    else:
                        failed_count += 1

                except Exception as e:
                    print(f"Failed to send email to {participant.email}: {e}")
                    failed_count += 1

            return JsonResponse({
                'success': True,
                'message': f'Bulk email sent successfully! {success_count} emails sent, {failed_count} failed.',
                'success_count': success_count,
                'failed_count': failed_count,
                'total_count': len(participants)
            })

        except json.JSONDecodeError:
            return JsonResponse({'error': 'Invalid JSON data'}, status=400)
        except Exception as e:
            return JsonResponse({'error': f'An error occurred: {str(e)}'}, status=500)

    def bulk_email_link(self, obj):
        """Display link to bulk email feature"""
        url = reverse('admin:organizations_bulk_email')
        return format_html(
            '<a href="{}" class="button" style="background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%); '
            'color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; '
            'display: inline-block; font-weight: bold;">📢 Send Bulk Email Announcement</a>',
            url
        )
    bulk_email_link.short_description = 'Bulk Email Management'
    bulk_email_link.allow_tags = True
