from rest_framework import viewsets, status
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated
from django.shortcuts import get_object_or_404
from django.utils import timezone
from datetime import datetime, timedelta
from .models import EmailConfiguration, EmailTemplate, EmailLog, Event
from .serializers import EmailConfigurationSerializer, EmailTemplateSerializer, EmailLogSerializer
from .email_service import get_email_service
from participants.models import Participant


class EmailConfigurationViewSet(viewsets.ModelViewSet):
    queryset = EmailConfiguration.objects.all()
    serializer_class = EmailConfigurationSerializer
    permission_classes = [IsAuthenticated]

    @action(detail=True, methods=['post'])
    def test_connection(self, request, pk=None):
        """Test email configuration"""
        config = self.get_object()
        test_email = request.data.get('test_email')

        if not test_email:
            return Response(
                {'error': 'test_email is required'},
                status=status.HTTP_400_BAD_REQUEST
            )

        try:
            # Temporarily activate this configuration for testing
            from django.conf import settings
            original_settings = {
                'EMAIL_BACKEND': getattr(settings, 'EMAIL_BACKEND', ''),
                'EMAIL_HOST': getattr(settings, 'EMAIL_HOST', ''),
                'EMAIL_PORT': getattr(settings, 'EMAIL_PORT', 587),
                'EMAIL_USE_TLS': getattr(settings, 'EMAIL_USE_TLS', True),
                'EMAIL_USE_SSL': getattr(settings, 'EMAIL_USE_SSL', False),
                'EMAIL_HOST_USER': getattr(settings, 'EMAIL_HOST_USER', ''),
                'EMAIL_HOST_PASSWORD': getattr(settings, 'EMAIL_HOST_PASSWORD', ''),
                'DEFAULT_FROM_EMAIL': getattr(settings, 'DEFAULT_FROM_EMAIL', ''),
            }

            # Apply test configuration
            settings.EMAIL_BACKEND = config.email_backend
            settings.EMAIL_HOST = config.email_host
            settings.EMAIL_PORT = config.email_port
            settings.EMAIL_USE_TLS = config.email_use_tls
            settings.EMAIL_USE_SSL = config.email_use_ssl
            settings.EMAIL_HOST_USER = config.email_host_user
            settings.EMAIL_HOST_PASSWORD = config.email_host_password
            settings.DEFAULT_FROM_EMAIL = config.default_from_email

            # Send test email
            from django.core.mail import send_mail
            send_mail(
                'Test Email - UoG Events',
                'This is a test email from University of Gondar Events system.',
                config.default_from_email,
                [test_email],
                fail_silently=False,
            )

            # Restore original settings
            for key, value in original_settings.items():
                setattr(settings, key, value)

            return Response({'message': f'Test email sent successfully to {test_email}'})

        except Exception as e:
            # Restore original settings on error
            for key, value in original_settings.items():
                setattr(settings, key, value)

            return Response(
                {'error': f'Failed to send test email: {str(e)}'},
                status=status.HTTP_400_BAD_REQUEST
            )


class EmailTemplateViewSet(viewsets.ModelViewSet):
    queryset = EmailTemplate.objects.all()
    serializer_class = EmailTemplateSerializer
    permission_classes = [IsAuthenticated]

    @action(detail=True, methods=['post'])
    def preview(self, request, pk=None):
        """Preview email template with sample data"""
        template = self.get_object()
        
        # Sample context data
        context = {
            'participant_name': 'John Doe',
            'event_name': 'Sample Conference 2024',
            'event_date': 'December 15, 2024',
            'event_location': 'University of Gondar Main Campus',
            'registration_date': 'November 20, 2024',
            'site_name': 'University of Gondar Events',
            'current_year': datetime.now().year,
        }
        
        try:
            subject = template.subject.format(**context)
            html_content = template.html_content.format(**context)
            text_content = template.text_content.format(**context) if template.text_content else None
            
            return Response({
                'subject': subject,
                'html_content': html_content,
                'text_content': text_content
            })
        except KeyError as e:
            return Response(
                {'error': f'Template error: Missing variable {e}'},
                status=status.HTTP_400_BAD_REQUEST
            )


class EmailLogViewSet(viewsets.ReadOnlyModelViewSet):
    queryset = EmailLog.objects.all()
    serializer_class = EmailLogSerializer
    permission_classes = [IsAuthenticated]

    def get_queryset(self):
        queryset = super().get_queryset()
        
        # Filter by status
        status_filter = self.request.query_params.get('status', None)
        if status_filter:
            queryset = queryset.filter(status=status_filter)
        
        # Filter by template type
        template_type = self.request.query_params.get('template_type', None)
        if template_type:
            queryset = queryset.filter(template_type=template_type)
        
        # Filter by date range
        date_from = self.request.query_params.get('date_from', None)
        date_to = self.request.query_params.get('date_to', None)
        
        if date_from:
            try:
                date_from = datetime.strptime(date_from, '%Y-%m-%d').date()
                queryset = queryset.filter(created_at__date__gte=date_from)
            except ValueError:
                pass
        
        if date_to:
            try:
                date_to = datetime.strptime(date_to, '%Y-%m-%d').date()
                queryset = queryset.filter(created_at__date__lte=date_to)
            except ValueError:
                pass
        
        return queryset.order_by('-created_at')

    @action(detail=False, methods=['get'])
    def stats(self, request):
        """Get email statistics"""
        queryset = self.get_queryset()
        
        total = queryset.count()
        sent = queryset.filter(status='sent').count()
        failed = queryset.filter(status='failed').count()
        pending = queryset.filter(status='pending').count()
        
        # Recent activity (last 7 days)
        week_ago = timezone.now() - timedelta(days=7)
        recent = queryset.filter(created_at__gte=week_ago).count()
        
        return Response({
            'total': total,
            'sent': sent,
            'failed': failed,
            'pending': pending,
            'recent_week': recent,
            'success_rate': round((sent / total * 100) if total > 0 else 0, 2)
        })


class EmailNotificationViewSet(viewsets.ViewSet):
    """ViewSet for sending email notifications"""
    permission_classes = [IsAuthenticated]

    @action(detail=False, methods=['post'])
    def send_event_details(self, request):
        """Send event details to participants"""
        event_id = request.data.get('event_id')
        participant_ids = request.data.get('participant_ids', [])
        
        if not event_id:
            return Response({'error': 'event_id is required'}, status=status.HTTP_400_BAD_REQUEST)
        
        try:
            event = Event.objects.get(id=event_id)
            email_service = get_email_service()
            
            if participant_ids:
                participants = Participant.objects.filter(id__in=participant_ids, event=event)
            else:
                participants = Participant.objects.filter(event=event)
            
            success_count = 0
            for participant in participants:
                if email_service.send_event_details(participant):
                    success_count += 1
            
            return Response({
                'message': f'Event details sent to {success_count} out of {participants.count()} participants'
            })
            
        except Event.DoesNotExist:
            return Response({'error': 'Event not found'}, status=status.HTTP_404_NOT_FOUND)
        except Exception as e:
            return Response({'error': str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    @action(detail=False, methods=['post'])
    def send_schedule_update(self, request):
        """Send schedule updates to participants"""
        event_id = request.data.get('event_id')
        participant_ids = request.data.get('participant_ids', [])
        
        if not event_id:
            return Response({'error': 'event_id is required'}, status=status.HTTP_400_BAD_REQUEST)
        
        try:
            event = Event.objects.get(id=event_id)
            email_service = get_email_service()
            
            if participant_ids:
                participants = Participant.objects.filter(id__in=participant_ids, event=event)
            else:
                participants = Participant.objects.filter(event=event)
            
            success_count = email_service.send_schedule_update(participants, event)
            
            return Response({
                'message': f'Schedule updates sent to {success_count} out of {participants.count()} participants'
            })
            
        except Event.DoesNotExist:
            return Response({'error': 'Event not found'}, status=status.HTTP_404_NOT_FOUND)
        except Exception as e:
            return Response({'error': str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    @action(detail=False, methods=['post'])
    def send_daily_gallery(self, request):
        """Send daily gallery images to participants"""
        event_id = request.data.get('event_id')
        date_str = request.data.get('date')  # Format: YYYY-MM-DD
        participant_ids = request.data.get('participant_ids', [])
        
        if not event_id:
            return Response({'error': 'event_id is required'}, status=status.HTTP_400_BAD_REQUEST)
        
        try:
            event = Event.objects.get(id=event_id)
            email_service = get_email_service()
            
            # Parse date
            if date_str:
                try:
                    date = datetime.strptime(date_str, '%Y-%m-%d').date()
                except ValueError:
                    return Response({'error': 'Invalid date format. Use YYYY-MM-DD'}, 
                                  status=status.HTTP_400_BAD_REQUEST)
            else:
                date = timezone.now().date()
            
            if participant_ids:
                participants = Participant.objects.filter(id__in=participant_ids, event=event)
            else:
                participants = Participant.objects.filter(event=event)
            
            success_count = email_service.send_daily_gallery(participants, event, date)
            
            return Response({
                'message': f'Daily gallery sent to {success_count} out of {participants.count()} participants'
            })
            
        except Event.DoesNotExist:
            return Response({'error': 'Event not found'}, status=status.HTTP_404_NOT_FOUND)
        except Exception as e:
            return Response({'error': str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    @action(detail=False, methods=['post'])
    def send_event_reminder(self, request):
        """Send event reminders to participants"""
        event_id = request.data.get('event_id')
        days_before = request.data.get('days_before', 1)
        participant_ids = request.data.get('participant_ids', [])
        
        if not event_id:
            return Response({'error': 'event_id is required'}, status=status.HTTP_400_BAD_REQUEST)
        
        try:
            event = Event.objects.get(id=event_id)
            email_service = get_email_service()
            
            if participant_ids:
                participants = Participant.objects.filter(id__in=participant_ids, event=event)
            else:
                participants = Participant.objects.filter(event=event)
            
            success_count = email_service.send_event_reminder(participants, event, days_before)
            
            return Response({
                'message': f'Event reminders sent to {success_count} out of {participants.count()} participants'
            })
            
        except Event.DoesNotExist:
            return Response({'error': 'Event not found'}, status=status.HTTP_404_NOT_FOUND)
        except Exception as e:
            return Response({'error': str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
