# Generated by Django 5.2.3 on 2025-07-26 21:22

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("events", "0001_initial"),
    ]

    operations = [
        migrations.CreateModel(
            name="EmailConfiguration",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "name",
                    models.CharField(
                        help_text="Configuration name (e.g., 'Gmail', 'Outlook')",
                        max_length=100,
                        unique=True,
                    ),
                ),
                (
                    "email_backend",
                    models.Char<PERSON>ield(
                        default="django.core.mail.backends.smtp.EmailBackend",
                        help_text="Django email backend class",
                        max_length=200,
                    ),
                ),
                (
                    "email_host",
                    models.Char<PERSON>ield(help_text="SMTP server host", max_length=100),
                ),
                (
                    "email_port",
                    models.Integer<PERSON>ield(default=587, help_text="SMTP server port"),
                ),
                (
                    "email_use_tls",
                    models.<PERSON><PERSON>anField(default=True, help_text="Use TLS encryption"),
                ),
                (
                    "email_use_ssl",
                    models.<PERSON><PERSON>an<PERSON>ield(default=False, help_text="Use SSL encryption"),
                ),
                (
                    "email_host_user",
                    models.EmailField(help_text="SMTP username/email", max_length=254),
                ),
                (
                    "email_host_password",
                    models.CharField(
                        help_text="SMTP password or app password", max_length=200
                    ),
                ),
                (
                    "default_from_email",
                    models.EmailField(
                        help_text="Default sender email address", max_length=254
                    ),
                ),
                (
                    "is_active",
                    models.BooleanField(
                        default=False, help_text="Use this configuration"
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
            ],
            options={
                "verbose_name": "Email Configuration",
                "verbose_name_plural": "Email Configurations",
                "db_table": "email_configurations",
            },
        ),
        migrations.CreateModel(
            name="EmailLog",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("recipient_email", models.EmailField(max_length=254)),
                ("recipient_name", models.CharField(blank=True, max_length=100)),
                ("subject", models.CharField(max_length=200)),
                (
                    "template_type",
                    models.CharField(
                        choices=[
                            ("registration_confirmation", "Registration Confirmation"),
                            ("event_approval", "Event Approval"),
                            ("event_details", "Event Details"),
                            ("badge_notification", "Badge Notification"),
                            ("schedule_update", "Schedule Update"),
                            ("daily_gallery", "Daily Gallery"),
                            ("event_reminder", "Event Reminder"),
                            ("welcome", "Welcome Email"),
                            ("custom", "Custom Template"),
                        ],
                        max_length=50,
                    ),
                ),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("pending", "Pending"),
                            ("sent", "Sent"),
                            ("failed", "Failed"),
                            ("bounced", "Bounced"),
                        ],
                        default="pending",
                        max_length=20,
                    ),
                ),
                ("error_message", models.TextField(blank=True)),
                ("sent_at", models.DateTimeField(blank=True, null=True)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
            ],
            options={
                "verbose_name": "Email Log",
                "verbose_name_plural": "Email Logs",
                "db_table": "email_logs",
                "ordering": ["-created_at"],
            },
        ),
        migrations.CreateModel(
            name="EmailTemplate",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("name", models.CharField(max_length=100)),
                (
                    "template_type",
                    models.CharField(
                        choices=[
                            ("registration_confirmation", "Registration Confirmation"),
                            ("event_approval", "Event Approval"),
                            ("event_details", "Event Details"),
                            ("badge_notification", "Badge Notification"),
                            ("schedule_update", "Schedule Update"),
                            ("daily_gallery", "Daily Gallery"),
                            ("event_reminder", "Event Reminder"),
                            ("welcome", "Welcome Email"),
                            ("custom", "Custom Template"),
                        ],
                        max_length=50,
                    ),
                ),
                (
                    "subject",
                    models.CharField(help_text="Email subject line", max_length=200),
                ),
                ("html_content", models.TextField(help_text="HTML email content")),
                (
                    "text_content",
                    models.TextField(
                        blank=True, help_text="Plain text email content (optional)"
                    ),
                ),
                ("is_active", models.BooleanField(default=True)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
            ],
            options={
                "verbose_name": "Email Template",
                "verbose_name_plural": "Email Templates",
                "db_table": "email_templates",
            },
        ),
    ]
