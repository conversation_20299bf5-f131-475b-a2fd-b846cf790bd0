#!/bin/sh
set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}🌐 Starting University of Gondar Event Management Frontend...${NC}"

# Wait for backend to be ready
if [ -n "$BACKEND_HOST" ] && [ -n "$BACKEND_PORT" ]; then
    echo -e "${YELLOW}⏳ Waiting for backend to be ready...${NC}"
    while ! nc -z $BACKEND_HOST $BACKEND_PORT; do
        echo -e "${YELLOW}⏳ Backend is unavailable - sleeping${NC}"
        sleep 2
    done
    echo -e "${GREEN}✅ Backend is ready!${NC}"
fi

# Replace environment variables in nginx config if needed
if [ -n "$REACT_APP_API_BASE_URL" ]; then
    echo -e "${BLUE}🔧 Configuring API base URL: $REACT_APP_API_BASE_URL${NC}"
fi

if [ -n "$REACT_APP_BACKEND_URL" ]; then
    echo -e "${BLUE}🔧 Configuring backend URL: $REACT_APP_BACKEND_URL${NC}"
fi

echo -e "${GREEN}✅ Frontend configuration complete!${NC}"
echo -e "${BLUE}🚀 Starting Nginx server...${NC}"

# Execute the main command
exec "$@"
