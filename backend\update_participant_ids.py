#!/usr/bin/env python3

import os
import sys
import django

# Add the project root to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'event_management.settings')
django.setup()

from participants.models import Participant

def update_participant_ids():
    """Update existing participants to have the exact IDs specified"""
    
    print("=== UPDATING PARTICIPANT IDS ===")
    
    # Mapping of (first_name, last_name) to desired ID
    name_to_id_mapping = {
        ('<PERSON><PERSON>', '<PERSON>'): 337,
        ('<PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>'): 338,
        ('Andargachew', 'De<PERSON>'): 339,
        ('Pal', 'Dol'): 340,
        ('<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>'): 342,
        ('<PERSON><PERSON><PERSON><PERSON>', 'Arba'): 343,
        ('<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>'): 344,
        ('<PERSON><PERSON><PERSON>', '<PERSON><PERSON>'): 346,
        ('<PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON>'): 347,
        ('<PERSON><PERSON>', '<PERSON>'): 348,
        ('<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>'): 349,
        ('<PERSON><PERSON><PERSON>w', '<PERSON><PERSON>'): 350,
        ('<PERSON><PERSON><PERSON><PERSON>', '<PERSON>yi<PERSON>'): 351,
        ('<PERSON><PERSON>', '<PERSON><PERSON><PERSON>'): 352,
        ('<PERSON>. <PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>'): 353,
        ('<PERSON><PERSON> <PERSON><PERSON><PERSON>', '<PERSON><PERSON>'): 354,
        ('<PERSON>. <PERSON>', '<PERSON><PERSON>'): 355,
        ('<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>'): 356,
        ('<PERSON>', '<PERSON><PERSON>'): 511,
        ('Adem', 'Kabo'): 512,
        ('Matebu', 'Jabessa'): 513,
        ('Asmamaw', 'Workneh'): 514,
        ('Dr.Megersa', 'Hussen'): 515,
        ('LAMESGIN', 'TIZAZU'): 516,
        ('Woldeamlak', 'Alemayehu'): 517,
        ('Derbew', 'Yohannes'): 518,
        ('Bizunesh', 'Borena'): 519,
        ('LIDETU', 'GOBENA'): 520,
        ('Alemayehu', 'Mekonnen'): 521,  # First one
        ('Asnake', 'Ede'): 523,
        ('Ochan', 'Agwa'): 524,
        ('Dr Abdiselam', 'Mohamed'): 525,
        ('Shelleme', 'Jiru'): 526,
        ('Nega', 'Tessemma'): 527,
        ('Dawit', 'Bezabih'): 528,
        ('Aynishet', 'Gebremariam'): 529,
        ('Lijalem', 'Abate'): 530,
        ('John', 'Firrisa'): 531,
        ('Adane', 'Tesega'): 532,
        ('Biniyam', 'Jimma'): 533,
        ('Serawit', 'Melkato'): 534,
        ('Hana', 'Kumera'): 535,
        ('Dr. Getinet', 'Ashenafi'): 536,
        ('KEBEDE', 'SHIFERAW'): 537,
        ('Zaid', 'Zewde'): 538,
        ('Dunkana', 'Kenie'): 539,
        ('Birhan', 'Miheretu'): 540,
        ('Dr Alemu', 'Ayano'): 541,
        ('Fana Hagos', 'Berhane'): 542,
        ('Ataklti', 'Gebrehiwot'): 543,
        ('Asalf', 'Wondemgezahu'): 544,
        ('Tesfaye', 'Jimma'): 545,
        ('Nebiyu', 'Teklie'): 546,
        ('Henoke', 'demese'): 547,
        ('Zewdu', 'Worku'): 548,
        ('Lediya', 'Negussie'): 549,
        ('Walelign', 'Tilahun'): 550,
    }
    
    updated_count = 0
    
    for (first_name, last_name), desired_id in name_to_id_mapping.items():
        # Find the participant by name
        participants = Participant.objects.filter(first_name=first_name, last_name=last_name)
        
        if not participants.exists():
            print(f"Warning: Participant {first_name} {last_name} not found")
            continue
            
        if participants.count() > 1:
            print(f"Warning: Multiple participants found for {first_name} {last_name}, using first one")
        
        participant = participants.first()
        old_id = participant.id
        
        # Check if desired ID is already taken
        if Participant.objects.filter(id=desired_id).exists():
            existing = Participant.objects.get(id=desired_id)
            if existing.id != participant.id:
                print(f"Warning: ID {desired_id} already taken by {existing.first_name} {existing.last_name}")
                continue
        
        # Update the ID
        try:
            participant.id = desired_id
            participant.save()
            updated_count += 1
            print(f"Updated: {first_name} {last_name} from ID {old_id} to ID {desired_id}")
        except Exception as e:
            print(f"Error updating {first_name} {last_name}: {str(e)}")
    
    print(f"\nUpdated {updated_count} participant IDs successfully.")
    
    # Handle the duplicate Alemayehu Mekonnen case (ID 522)
    alemayehu_participants = Participant.objects.filter(first_name='Alemayehu', last_name='Mekonnen')
    if alemayehu_participants.count() > 1:
        print(f"\nFound {alemayehu_participants.count()} Alemayehu Mekonnen participants")
        second_alemayehu = alemayehu_participants[1]  # Get the second one
        try:
            second_alemayehu.id = 522
            second_alemayehu.save()
            print(f"Updated second Alemayehu Mekonnen to ID 522")
            updated_count += 1
        except Exception as e:
            print(f"Error updating second Alemayehu Mekonnen: {str(e)}")

if __name__ == "__main__":
    update_participant_ids()
