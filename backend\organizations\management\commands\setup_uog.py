"""
Management command to set up University of Gondar organization
"""
import os
from django.core.management.base import BaseCommand
from django.core.files import File
from django.conf import settings
from organizations.models import Organization, OrganizationSettings


class Command(BaseCommand):
    help = 'Set up University of Gondar organization with default data'

    def add_arguments(self, parser):
        parser.add_argument(
            '--logo-path',
            type=str,
            help='Path to the logo file',
        )

    def handle(self, *args, **options):
        self.stdout.write('Setting up University of Gondar organization...')
        
        # Check if primary organization already exists
        existing_org = Organization.objects.filter(is_primary=True).first()
        if existing_org:
            self.stdout.write(
                self.style.WARNING(f'Primary organization already exists: {existing_org.name}')
            )
            return
        
        # Create University of Gondar organization
        org_data = {
            'name': 'University of Gondar',
            'short_name': 'UoG',
            'motto': 'Excellence in Education, Research and Community Service',
            'description': 'The University of Gondar is a premier public university in Ethiopia, committed to providing quality education, conducting innovative research, and serving the community.',
            'email': '<EMAIL>',
            'phone': '+251-58-114-1240',
            'website': 'https://www.uog.edu.et',
            'address_line_1': 'University of Gondar',
            'city': 'Gondar',
            'state_province': 'Amhara Region',
            'country': 'Ethiopia',
            'postal_code': '196',
            'is_active': True,
            'is_primary': True,
        }
        
        # Create the organization
        organization = Organization.objects.create(**org_data)
        
        # Handle logo if provided
        logo_path = options.get('logo_path')
        if logo_path and os.path.exists(logo_path):
            try:
                with open(logo_path, 'rb') as logo_file:
                    organization.logo.save(
                        os.path.basename(logo_path),
                        File(logo_file),
                        save=True
                    )
                self.stdout.write(
                    self.style.SUCCESS(f'Logo uploaded from: {logo_path}')
                )
            except Exception as e:
                self.stdout.write(
                    self.style.ERROR(f'Failed to upload logo: {e}')
                )
        
        # Create organization settings
        settings_data = {
            'organization': organization,
            'default_event_duration_hours': 8,
            'default_registration_fee': 0.00,
            'primary_color': '#1e40af',  # UoG Blue
            'email_signature': f"""
Best regards,
{organization.name}
{organization.email}
{organization.phone}
{organization.website}
            """.strip()
        }
        
        OrganizationSettings.objects.create(**settings_data)
        
        self.stdout.write(
            self.style.SUCCESS(f'Successfully created organization: {organization.name}')
        )
        self.stdout.write(
            self.style.SUCCESS(f'Organization ID: {organization.id}')
        )
        self.stdout.write(
            self.style.SUCCESS('Organization settings created successfully')
        )
