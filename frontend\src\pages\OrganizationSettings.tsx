import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, Spinner } from 'react-bootstrap';
import { useAuth } from '../contexts/AuthContext';

interface OrganizationData {
  id: number;
  name: string;
  description: string;
  logo: string;
  primary_color: string;
  secondary_color: string;
  contact_email: string;
  contact_phone: string;
  address: string;
  website: string;
  default_event_duration: number;
  default_max_attendees: number;
  email_notifications_enabled: boolean;
  sms_notifications_enabled: boolean;
}

const OrganizationSettings: React.FC = () => {
  const { user } = useAuth();
  const [organization, setOrganization] = useState<OrganizationData | null>(null);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [message, setMessage] = useState<{ type: 'success' | 'error'; text: string } | null>(null);

  useEffect(() => {
    fetchOrganizationSettings();
  }, []);

  const fetchOrganizationSettings = async () => {
    try {
      const token = localStorage.getItem('token');
      const response = await fetch('/api/organizations/settings/', {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
      });

      if (response.ok) {
        const data = await response.json();
        setOrganization(data);
      } else {
        setMessage({ type: 'error', text: 'Failed to load organization settings' });
      }
    } catch (error) {
      setMessage({ type: 'error', text: 'Error loading organization settings' });
    } finally {
      setLoading(false);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!organization) return;

    setSaving(true);
    setMessage(null);

    try {
      const token = localStorage.getItem('token');
      const response = await fetch(`/api/organizations/${organization.id}/`, {
        method: 'PUT',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(organization),
      });

      if (response.ok) {
        setMessage({ type: 'success', text: 'Organization settings updated successfully!' });
      } else {
        setMessage({ type: 'error', text: 'Failed to update organization settings' });
      }
    } catch (error) {
      setMessage({ type: 'error', text: 'Error updating organization settings' });
    } finally {
      setSaving(false);
    }
  };

  const handleInputChange = (field: keyof OrganizationData, value: any) => {
    if (organization) {
      setOrganization({ ...organization, [field]: value });
    }
  };

  if (loading) {
    return (
      <Container className="py-4">
        <div className="text-center">
          <Spinner animation="border" role="status">
            <span className="visually-hidden">Loading...</span>
          </Spinner>
        </div>
      </Container>
    );
  }

  if (!organization) {
    return (
      <Container className="py-4">
        <Alert variant="warning">
          No organization settings found. Please contact your administrator.
        </Alert>
      </Container>
    );
  }

  return (
    <Container className="py-4">
      <Row>
        <Col lg={8} className="mx-auto">
          <Card>
            <Card.Header>
              <h3 className="mb-0">Organization Settings</h3>
            </Card.Header>
            <Card.Body>
              {message && (
                <Alert variant={message.type} dismissible onClose={() => setMessage(null)}>
                  {message.text}
                </Alert>
              )}

              <Form onSubmit={handleSubmit}>
                <Row>
                  <Col md={6}>
                    <Form.Group className="mb-3">
                      <Form.Label>Organization Name</Form.Label>
                      <Form.Control
                        type="text"
                        value={organization.name}
                        onChange={(e) => handleInputChange('name', e.target.value)}
                        required
                      />
                    </Form.Group>
                  </Col>
                  <Col md={6}>
                    <Form.Group className="mb-3">
                      <Form.Label>Contact Email</Form.Label>
                      <Form.Control
                        type="email"
                        value={organization.contact_email}
                        onChange={(e) => handleInputChange('contact_email', e.target.value)}
                      />
                    </Form.Group>
                  </Col>
                </Row>

                <Form.Group className="mb-3">
                  <Form.Label>Description</Form.Label>
                  <Form.Control
                    as="textarea"
                    rows={3}
                    value={organization.description}
                    onChange={(e) => handleInputChange('description', e.target.value)}
                  />
                </Form.Group>

                <Row>
                  <Col md={6}>
                    <Form.Group className="mb-3">
                      <Form.Label>Primary Color</Form.Label>
                      <Form.Control
                        type="color"
                        value={organization.primary_color}
                        onChange={(e) => handleInputChange('primary_color', e.target.value)}
                      />
                    </Form.Group>
                  </Col>
                  <Col md={6}>
                    <Form.Group className="mb-3">
                      <Form.Label>Secondary Color</Form.Label>
                      <Form.Control
                        type="color"
                        value={organization.secondary_color}
                        onChange={(e) => handleInputChange('secondary_color', e.target.value)}
                      />
                    </Form.Group>
                  </Col>
                </Row>

                <Row>
                  <Col md={6}>
                    <Form.Group className="mb-3">
                      <Form.Label>Contact Phone</Form.Label>
                      <Form.Control
                        type="tel"
                        value={organization.contact_phone}
                        onChange={(e) => handleInputChange('contact_phone', e.target.value)}
                      />
                    </Form.Group>
                  </Col>
                  <Col md={6}>
                    <Form.Group className="mb-3">
                      <Form.Label>Website</Form.Label>
                      <Form.Control
                        type="url"
                        value={organization.website}
                        onChange={(e) => handleInputChange('website', e.target.value)}
                      />
                    </Form.Group>
                  </Col>
                </Row>

                <Form.Group className="mb-3">
                  <Form.Label>Address</Form.Label>
                  <Form.Control
                    as="textarea"
                    rows={2}
                    value={organization.address}
                    onChange={(e) => handleInputChange('address', e.target.value)}
                  />
                </Form.Group>

                <Row>
                  <Col md={6}>
                    <Form.Group className="mb-3">
                      <Form.Label>Default Event Duration (hours)</Form.Label>
                      <Form.Control
                        type="number"
                        min="1"
                        max="24"
                        value={organization.default_event_duration}
                        onChange={(e) => handleInputChange('default_event_duration', parseInt(e.target.value))}
                      />
                    </Form.Group>
                  </Col>
                  <Col md={6}>
                    <Form.Group className="mb-3">
                      <Form.Label>Default Max Attendees</Form.Label>
                      <Form.Control
                        type="number"
                        min="1"
                        value={organization.default_max_attendees}
                        onChange={(e) => handleInputChange('default_max_attendees', parseInt(e.target.value))}
                      />
                    </Form.Group>
                  </Col>
                </Row>

                <Row>
                  <Col md={6}>
                    <Form.Group className="mb-3">
                      <Form.Check
                        type="checkbox"
                        label="Enable Email Notifications"
                        checked={organization.email_notifications_enabled}
                        onChange={(e) => handleInputChange('email_notifications_enabled', e.target.checked)}
                      />
                    </Form.Group>
                  </Col>
                  <Col md={6}>
                    <Form.Group className="mb-3">
                      <Form.Check
                        type="checkbox"
                        label="Enable SMS Notifications"
                        checked={organization.sms_notifications_enabled}
                        onChange={(e) => handleInputChange('sms_notifications_enabled', e.target.checked)}
                      />
                    </Form.Group>
                  </Col>
                </Row>

                <div className="d-flex justify-content-end">
                  <Button type="submit" variant="primary" disabled={saving}>
                    {saving ? (
                      <>
                        <Spinner animation="border" size="sm" className="me-2" />
                        Saving...
                      </>
                    ) : (
                      'Save Settings'
                    )}
                  </Button>
                </div>
              </Form>
            </Card.Body>
          </Card>
        </Col>
      </Row>
    </Container>
  );
};

export default OrganizationSettings;
