import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext';
import {
  eventService, galleryService, organizationService, publicService, developerService,
  Event, GalleryImage, Organization, PublicStatistics, Developer, getMediaUrl
} from '../services/api';
import CampusLifeSlider from '../components/CampusLifeSlider';
import ModernLayout from '../components/ModernLayout';
import '../styles/modern-landing.css';
import '../styles/compact-hero.css';
import '../styles/myevents.css';
import '../styles/gondar-sections.css';

interface HeroSlide {
  id: number;
  title: string;
  subtitle: string;
  description: string;
  image: string;
  cta: string;
  ctaLink: string;
  stats: {
    [key: string]: string;
  };
  eventData?: Event;
}

const NewHome: React.FC = () => {
  const { isAuthenticated } = useAuth();
  const [currentSlide, setCurrentSlide] = useState(0);
  const [events, setEvents] = useState<Event[]>([]);
  const [galleryImages, setGalleryImages] = useState<GalleryImage[]>([]);
  const [campusImages, setCampusImages] = useState<GalleryImage[]>([]);
  const [organization, setOrganization] = useState<Organization | null>(null);
  const [statistics, setStatistics] = useState<PublicStatistics | null>(null);
  const [developers, setDevelopers] = useState<Developer[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedEventId, setSelectedEventId] = useState<number | null>(null);
  const [featuredEvent, setFeaturedEvent] = useState<Event | null>(null);
  const [lookupLoading, setLookupLoading] = useState(false);
  const [lookupResults, setLookupResults] = useState<any>(null);
  const [lookupError, setLookupError] = useState<string>('');

  // Hero slides data - now using dynamic events from backend
  const getHeroSlides = (): HeroSlide[] => {
    const defaultStats = {
      participants: statistics?.participants.total || "500+",
      countries: statistics?.countries.total || "25+",
      organizations: statistics?.organizations.total || "50+"
    };

    // If we have events from backend, use them for hero slides
    if (events && events.length > 0) {
      return events.slice(0, 3).map((event, index) => {
        const eventDate = new Date(event.start_date);
        const isUpcoming = eventDate > new Date();
        const daysUntil = Math.ceil((eventDate.getTime() - new Date().getTime()) / (1000 * 60 * 60 * 24));

        return {
          id: event.id,
          title: event.name,
          subtitle: isUpcoming
            ? `${daysUntil > 0 ? `Starting in ${daysUntil} days` : 'Starting soon'} • ${event.location}`
            : `${event.location} • ${eventDate.getFullYear()}`,
          description: event.description || "Join us for this amazing event and be part of an unforgettable experience.",
          image: event.banner
            ? getMediaUrl(event.banner)
            : `https://images.unsplash.com/photo-${
                index === 0 ? '1540575467063-178a50c2df87' :
                index === 1 ? '1559757148-5c350d0d3c56' :
                '1578662996442-48f60103fc96'
              }?ixlib=rb-4.0.3&auto=format&fit=crop&w=2070&q=80`,
          cta: isUpcoming ? "Register Now" : "Learn More",
          ctaLink: isUpcoming ? "/participant-register" : `/events/${event.id}`,
          stats: {
            participants: `${event.participant_count || 0}`,
            duration: `${Math.ceil((new Date(event.end_date).getTime() - new Date(event.start_date).getTime()) / (1000 * 60 * 60 * 24))} Days`,
            location: event.city || "TBA"
          },
          eventData: event
        };
      });
    }

    // Fallback slides if no events available
    return [
      {
        id: 1,
        title: organization?.name ? `${organization.name} Conference 2024` : "International Academic Conference 2024",
        subtitle: "Shaping the Future of Higher Education",
        description: "Join world-renowned scholars, researchers, and educators in a groundbreaking conference that will define the next decade of academic excellence.",
        image: "https://images.unsplash.com/photo-1540575467063-178a50c2df87?ixlib=rb-4.0.3&auto=format&fit=crop&w=2070&q=80",
        cta: "Register Now",
        ctaLink: "/participant-register",
        stats: defaultStats
      },
      {
        id: 2,
        title: "Research Innovation Summit",
        subtitle: "Breakthrough Discoveries & Collaborative Research",
        description: "Discover cutting-edge research, innovative methodologies, and collaborative opportunities that are transforming academic landscapes globally.",
        image: "https://images.unsplash.com/photo-1559757148-5c350d0d3c56?ixlib=rb-4.0.3&auto=format&fit=crop&w=2070&q=80",
        cta: "Explore Research",
        ctaLink: "/events",
        stats: defaultStats
      },
      {
        id: 3,
        title: organization?.name ? `${organization.name} Heritage & Education` : "Ethiopian Heritage & Education",
        subtitle: "Preserving Culture Through Modern Education",
        description: "Celebrating rich cultural heritage while embracing innovative educational approaches for sustainable development.",
        image: "https://images.unsplash.com/photo-1578662996442-48f60103fc96?ixlib=rb-4.0.3&auto=format&fit=crop&w=2070&q=80",
        cta: "Learn More",
        ctaLink: "/events",
        stats: defaultStats
      }
    ];
  };

  const heroSlides = getHeroSlides();

  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true);

        // Fetch organization data
        const orgResponse = await organizationService.getPrimaryOrganization();
        setOrganization(orgResponse.data);

        // Fetch events for hero slider and events section
        const eventsResponse = await eventService.getPublicEvents();
        const allEvents = eventsResponse.data.results || [];

        // Sort events: upcoming first, then by start date, prioritize those with banners for hero
        const sortedEvents = allEvents.sort((a: Event, b: Event) => {
          const now = new Date();
          const aStart = new Date(a.start_date);
          const bStart = new Date(b.start_date);
          const aEnd = new Date(a.end_date);
          const bEnd = new Date(b.end_date);

          // Prioritize upcoming events
          const aUpcoming = aEnd >= now;
          const bUpcoming = bEnd >= now;

          if (aUpcoming && !bUpcoming) return -1;
          if (!aUpcoming && bUpcoming) return 1;

          // Among upcoming events, prioritize those with banners (for hero slider)
          if (aUpcoming && bUpcoming) {
            if (a.banner && !b.banner) return -1;
            if (!a.banner && b.banner) return 1;
          }

          // Sort by start date
          return aStart.getTime() - bStart.getTime();
        });

        setEvents(sortedEvents.slice(0, 6));

        // Set featured event (first upcoming event or first event)
        if (sortedEvents.length > 0) {
          const upcomingEvent = sortedEvents.find(event => new Date(event.start_date) > new Date());
          setFeaturedEvent(upcomingEvent || sortedEvents[0]);
        }

        // Fetch featured gallery images
        try {
          const galleryResponse = await galleryService.getFeaturedImages({ limit: 8 });
          setGalleryImages(galleryResponse.data || []);
        } catch (error) {
          console.warn('Gallery not available, using fallback');
          setGalleryImages([]);
        }

        // Fetch campus images
        try {
          const campusResponse = await galleryService.getCampusImages({ limit: 12 });
          setCampusImages(campusResponse.data || []);
        } catch (error) {
          console.warn('Campus images not available, using fallback');
          setCampusImages([]);
        }

        // Fetch public statistics
        try {
          const statsResponse = await publicService.getStatistics();
          setStatistics(statsResponse.data);
        } catch (error) {
          console.warn('Statistics not available, using defaults');
          setStatistics(null);
        }

        // Fetch developers
        try {
          const developersResponse = await developerService.getDevelopers();
          setDevelopers(developersResponse.data.results || []);
        } catch (error) {
          console.warn('Developers not available');
          setDevelopers([]);
        }

      } catch (error) {
        console.error('Error fetching data:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, []);

  // Auto-slide functionality
  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentSlide((prev) => (prev + 1) % heroSlides.length);
    }, 6000);
    return () => clearInterval(interval);
  }, [heroSlides.length]);

  // Participant lookup function
  const handleParticipantLookup = async (e: React.FormEvent) => {
    e.preventDefault();
    setLookupLoading(true);
    setLookupError('');
    setLookupResults(null);

    const formData = new FormData(e.target as HTMLFormElement);
    const lastName = formData.get('lastName') as string;
    const phoneNumber = formData.get('phoneNumber') as string;

    try {
      // Simulate API call - replace with actual API endpoint
      await new Promise(resolve => setTimeout(resolve, 1500)); // Simulate loading

      // Mock data - replace with actual API response
      const mockResults = {
        participant: {
          name: `John ${lastName}`,
          phone: phoneNumber,
          email: `john.${lastName.toLowerCase()}@email.com`
        },
        event: {
          name: "International Conference on Technology",
          date: "March 15-17, 2024",
          location: "University of Gondar Main Campus"
        },
        hotel: {
          name: "Gondar Hills Resort",
          room: "Room 205",
          checkIn: "March 14, 2024"
        },
        contact: {
          name: "Dr. Sarah Johnson",
          phone: "+251-911-123-456",
          email: "<EMAIL>"
        },
        transportation: {
          driver: "Ahmed Mohammed",
          vehicle: "Toyota Hiace - Plate: AA-123-456",
          driverPhone: "+251-911-789-012"
        }
      };

      setLookupResults(mockResults);

      // Show results
      const resultsDiv = document.querySelector('.lookup-results') as HTMLElement;
      if (resultsDiv) {
        resultsDiv.style.display = 'block';
        resultsDiv.classList.add('show');

        // Populate results
        document.getElementById('eventName')!.textContent = mockResults.event.name;
        document.getElementById('eventDate')!.textContent = mockResults.event.date;
        document.getElementById('eventLocation')!.textContent = mockResults.event.location;
        document.getElementById('hotelName')!.textContent = mockResults.hotel.name;
        document.getElementById('roomNumber')!.textContent = mockResults.hotel.room;
        document.getElementById('checkInDate')!.textContent = mockResults.hotel.checkIn;
        document.getElementById('contactName')!.textContent = mockResults.contact.name;
        document.getElementById('contactPhone')!.textContent = mockResults.contact.phone;
        document.getElementById('contactEmail')!.textContent = mockResults.contact.email;
        document.getElementById('driverName')!.textContent = mockResults.transportation.driver;
        document.getElementById('vehicleInfo')!.textContent = mockResults.transportation.vehicle;
        document.getElementById('driverPhone')!.textContent = mockResults.transportation.driverPhone;
      }

    } catch (error) {
      setLookupError('No registration found with the provided details. Please check your information and try again.');
    } finally {
      setLookupLoading(false);
    }
  };

  if (isAuthenticated) {
    return (
      <div className="authenticated-home">
        <div className="auth-hero">
          <div className="container">
            <div className="row align-items-center min-vh-100">
              <div className="col-lg-6">
                <h1 className="display-3 fw-bold text-white mb-4">
                  Welcome Back!
                </h1>
                <p className="lead text-light mb-5">
                  Continue managing your events and participants with our comprehensive platform.
                </p>
                <div className="d-flex gap-3">
                  <Link to="/dashboard" className="btn btn-light btn-lg px-5 py-3">
                    <i className="fas fa-tachometer-alt me-2"></i>
                    Dashboard
                  </Link>
                  <Link to="/events" className="btn btn-outline-light btn-lg px-5 py-3">
                    <i className="fas fa-calendar me-2"></i>
                    Events
                  </Link>
                </div>
              </div>
              <div className="col-lg-6">
                <div className="auth-stats">
                  <div className="row g-4">
                    <div className="col-6">
                      <div className="stat-card">
                        <h3 className="text-primary">150+</h3>
                        <p>Events Managed</p>
                      </div>
                    </div>
                    <div className="col-6">
                      <div className="stat-card">
                        <h3 className="text-success">5,000+</h3>
                        <p>Participants</p>
                      </div>
                    </div>
                    <div className="col-6">
                      <div className="stat-card">
                        <h3 className="text-warning">25+</h3>
                        <p>Countries</p>
                      </div>
                    </div>
                    <div className="col-6">
                      <div className="stat-card">
                        <h3 className="text-info">98%</h3>
                        <p>Success Rate</p>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <ModernLayout
      showHero={false}
      className="modern-landing"
    >

      {/* Professional Full-Width Hero Section */}
      <section id="home" className="hero-section-fullwidth">
        {/* Shooting Stars */}
        <div className="shooting-star"></div>
        <div className="shooting-star"></div>
        <div className="shooting-star"></div>
        <div className="shooting-star"></div>

        {/* Background with Featured Event */}
        <div className="hero-background-fullwidth">
          {featuredEvent && featuredEvent.banner && (
            <div
              className="hero-bg-image"
              style={{ backgroundImage: `url(${getMediaUrl(featuredEvent.banner)})` }}
            />
          )}
          <div className="hero-overlay-professional"></div>
        </div>

        {/* Professional Hero Content */}
        <div className="hero-container-fullwidth">
          <div className="hero-content-wrapper">
            <div className="hero-main-content">
              <div className="hero-text-professional">
                {/* Events-Focused Title */}
                <div className="hero-title-wrapper">
                  <h1 className="hero-title-professional">
                    Discover Amazing Events
                  </h1>
                  <div className="hero-tagline">
                    Join Academic Conferences • Cultural Celebrations • Research Symposiums
                  </div>
                </div>

                {/* Featured Event Display */}
                {featuredEvent && (
                  <div className="featured-event-professional">
                    <div className="event-status-professional">
                      {(() => {
                        const now = new Date();
                        const startDate = new Date(featuredEvent.start_date);
                        const endDate = new Date(featuredEvent.end_date);

                        if (now < startDate) {
                          return <span className="status upcoming">Upcoming Event</span>;
                        } else if (now >= startDate && now <= endDate) {
                          return <span className="status live">Live Event</span>;
                        } else {
                          return <span className="status past">Past Event</span>;
                        }
                      })()}
                    </div>
                    <h2 className="event-title-professional">{featuredEvent.name}</h2>
                    <p className="event-description-professional">{featuredEvent.description}</p>
                    <div className="event-meta-professional">
                      <span><i className="fas fa-calendar"></i> {new Date(featuredEvent.start_date).toLocaleDateString()}</span>
                      <span><i className="fas fa-map-marker-alt"></i> {featuredEvent.location}</span>
                      <span><i className="fas fa-users"></i> {featuredEvent.participant_count} Participants</span>
                    </div>
                  </div>
                )}

                {/* Action Buttons */}
                <div className="hero-actions-professional">
                  <a href="http://event.uog.edu.et/events/1" target="_blank" rel="noopener noreferrer" className="btn-professional btn-primary-professional">
                    <i className="fas fa-calendar-alt"></i>
                    <span>Explore Events</span>
                  </a>
                  <a href="#gondar-city" className="btn-professional btn-secondary-professional">
                    <i className="fas fa-city"></i>
                    <span>Discover Gondar</span>
                  </a>
                </div>
              </div>
            </div>

            <div className="hero-side-content">
              <div className="hero-events-grid">
                <div className="event-stat-item">
                  <div className="event-stat-number">{events.length}</div>
                  <div className="event-stat-label">Total Events</div>
                </div>
                <div className="event-stat-item">
                  <div className="event-stat-number">
                    {events.filter(event => {
                      const now = new Date();
                      const startDate = new Date(event.start_date);
                      return startDate > now;
                    }).length}
                  </div>
                  <div className="event-stat-label">Upcoming</div>
                </div>
                <div className="event-stat-item">
                  <div className="event-stat-number">
                    {events.reduce((total, event) => total + (event.participant_count || 0), 0)}
                  </div>
                  <div className="event-stat-label">Participants</div>
                </div>
                <div className="event-stat-item">
                  <div className="event-stat-number">
                    {events.filter(event => {
                      const now = new Date();
                      const startDate = new Date(event.start_date);
                      const endDate = new Date(event.end_date);
                      return now >= startDate && now <= endDate;
                    }).length}
                  </div>
                  <div className="event-stat-label">Live Now</div>
                </div>
              </div>
            </div>
          </div>
        </div>

      </section>



      {/* Gondar City Section */}
      <section id="gondar-city" className="gondar-city-section">
        <div className="container">
          <div className="section-header">
            <div className="row">
              <div className="col-lg-8 mx-auto text-center">
                <h2 className="section-title">Discover Gondar City</h2>
                <p className="section-subtitle">
                  Explore the historic royal city of Ethiopia, home to magnificent castles, ancient churches, and rich cultural heritage.
                </p>
              </div>
            </div>
          </div>

          <div className="row g-4 mb-5">
            <div className="col-lg-6">
              <div className="city-hero-image">
                <img
                  src="https://z-p3-scontent.fadd1-1.fna.fbcdn.net/v/t39.30808-6/525305752_784114047283035_8755840366625413316_n.jpg?_nc_cat=100&ccb=1-7&_nc_sid=833d8c&_nc_eui2=AeEaCM5uKZ_I8fI5JK2T99IqryuR1bT0zIWvK5HVtPTMhT_CH4unxoswibl4geA9SSgXcG_SZ6OLT7aQEE9Xla-M&_nc_ohc=lIC0Krph8iwQ7kNvwHVV-DY&_nc_oc=AdmsYraUiPK1HPWjbBm0uu-XGpw0AFb-90DPuRJ0S9fF_z53ltTvlO_SdZHTGvCHxUU&_nc_zt=23&_nc_ht=z-p3-scontent.fadd1-1.fna&_nc_gid=9dAEbBQAYtB7iRPq5gh6Ig&oh=00_AfQInlyYRS7m0bA9oRxRBCW8xwIwqCiGtIT_0xd6ibrdRQ&oe=6893DA6F"
                  alt="Gondar City"
                  className="img-fluid rounded-4 shadow-lg"
                />
              </div>
            </div>
            <div className="col-lg-6">
              <div className="city-content">
                <h3 className="city-title">The Royal City of Ethiopia</h3>
                <p className="city-description">
                  Gondar, founded in 1636 by Emperor Fasilides, served as the capital of Ethiopia for over 200 years.
                  Known as the "Camelot of Africa," this UNESCO World Heritage site boasts magnificent royal castles,
                  ancient churches, and a rich tapestry of Ethiopian history and culture.
                </p>
                <div className="city-highlights">
                  <div className="highlight-item">
                    <i className="fas fa-crown text-warning"></i>
                    <span>Royal Enclosure (Fasil Ghebbi)</span>
                  </div>
                  <div className="highlight-item">
                    <i className="fas fa-church text-primary"></i>
                    <span>Debre Berhan Selassie Church</span>
                  </div>
                  <div className="highlight-item">
                    <i className="fas fa-swimming-pool text-info"></i>
                    <span>Fasilides' Bath</span>
                  </div>
                  <div className="highlight-item">
                    <i className="fas fa-mountain text-success"></i>
                    <span>Simien Mountains Gateway</span>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div className="attractions-grid">
            <div className="row g-4">
              <div className="col-lg-4 col-md-6">
                <div className="attraction-card">
                  <div className="attraction-image">
                    <img
                      src="https://i0.wp.com/thinkafrica.net/wp-content/uploads/2019/02/fasil-ghebbi-featued-gondar10.jpg?resize=780%2C278&ssl=1"
                      alt="Fasil Ghebbi Royal Enclosure"
                    />
                    <div className="attraction-overlay">
                      <i className="fas fa-crown"></i>
                    </div>
                  </div>
                  <div className="attraction-content">
                    <h4>Fasil Ghebbi</h4>
                    <p>The royal enclosure containing six castles and several other buildings, showcasing unique Ethiopian architecture.</p>
                    <div className="attraction-meta">
                      <span><i className="fas fa-clock"></i> 2-3 hours</span>
                      <span><i className="fas fa-star"></i> UNESCO Site</span>
                    </div>
                  </div>
                </div>
              </div>
              <div className="col-lg-4 col-md-6">
                <div className="attraction-card">
                  <div className="attraction-image">
                    <img
                      src="https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcSK44gzJXIJoVNnlM4nMeEAI4UIzi3bAtggsg&s"
                      alt="Debre Berhan Selassie Church"
                    />
                    <div className="attraction-overlay">
                      <i className="fas fa-church"></i>
                    </div>
                  </div>
                  <div className="attraction-content">
                    <h4>Debre Berhan Selassie</h4>
                    <p>Famous church known for its beautiful ceiling decorated with angelic faces and religious paintings.</p>
                    <div className="attraction-meta">
                      <span><i className="fas fa-clock"></i> 1-2 hours</span>
                      <span><i className="fas fa-palette"></i> Art & Culture</span>
                    </div>
                  </div>
                </div>
              </div>
              <div className="col-lg-4 col-md-6">
                <div className="attraction-card">
                  <div className="attraction-image">
                    <img
                      src="https://blogger.googleusercontent.com/img/b/R29vZ2xl/AVvXsEj5laguI2fO9Q8qFEowdNsETtW__a2rFAxmQxUiqz0YaTlKJ-5El8g5qGDV_8uHcHq7_mcxvbtWlYkBvsE4KZ9Ro0LB3w2C4ns6NzcQDDaeMF0dWZXxh1W-tSlhCKsPa2tz4vpLYoyHQpSl/s1600/fasiledas+bath-1.jpg"
                      alt="Fasilides Bath Historic Pool"
                    />
                    <div className="attraction-overlay">
                      <i className="fas fa-swimming-pool"></i>
                    </div>
                  </div>
                  <div className="attraction-content">
                    <h4>Fasilides' Bath</h4>
                    <p>Historic bathing complex used for the annual Timkat (Epiphany) celebrations, surrounded by beautiful gardens.</p>
                    <div className="attraction-meta">
                      <span><i className="fas fa-clock"></i> 1 hour</span>
                      <span><i className="fas fa-water"></i> Historic Site</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* University of Gondar Section */}
      <section id="university" className="university-section">
        <div className="container">
          <div className="section-header">
            <div className="row">
              <div className="col-lg-8 mx-auto text-center">
                <h2 className="section-title">University of Gondar</h2>
                <p className="section-subtitle">
                  Leading higher education institution in Ethiopia, fostering excellence in teaching, research, and community service since 1954.
                </p>
              </div>
            </div>
          </div>

          <div className="university-stats">
            <div className="row g-3 mb-5 justify-content-center">
              <div className="col-lg-2 col-md-4 col-sm-6">
                <div className="stat-card animated-stat" data-aos="fade-up" data-aos-delay="100">
                  <div className="stat-icon">
                    <i className="fas fa-graduation-cap"></i>
                  </div>
                  <div className="stat-number" data-count="21800">21,800+</div>
                  <div className="stat-label">Enrolled Students</div>
                </div>
              </div>
              <div className="col-lg-2 col-md-4 col-sm-6">
                <div className="stat-card animated-stat" data-aos="fade-up" data-aos-delay="200">
                  <div className="stat-icon">
                    <i className="fas fa-chalkboard-teacher"></i>
                  </div>
                  <div className="stat-number" data-count="2400">2,400+</div>
                  <div className="stat-label">Faculty Members</div>
                </div>
              </div>
              <div className="col-lg-2 col-md-4 col-sm-6">
                <div className="stat-card animated-stat" data-aos="fade-up" data-aos-delay="300">
                  <div className="stat-icon">
                    <i className="fas fa-users-cog"></i>
                  </div>
                  <div className="stat-number" data-count="5400">5,400+</div>
                  <div className="stat-label">Supportive Staff</div>
                </div>
              </div>
              <div className="col-lg-2 col-md-4 col-sm-6">
                <div className="stat-card animated-stat" data-aos="fade-up" data-aos-delay="400">
                  <div className="stat-icon">
                    <i className="fas fa-award"></i>
                  </div>
                  <div className="stat-number" data-count="345">345</div>
                  <div className="stat-label">Academic Programs</div>
                </div>
              </div>
              <div className="col-lg-2 col-md-4 col-sm-6">
                <div className="stat-card animated-stat" data-aos="fade-up" data-aos-delay="500">
                  <div className="stat-icon">
                    <i className="fas fa-building"></i>
                  </div>
                  <div className="stat-number" data-count="7">7</div>
                  <div className="stat-label">Campuses</div>
                </div>
              </div>
            </div>
          </div>

          <div className="university-highlights">
            <div className="row g-4">
              <div className="col-lg-6">
                <div className="highlight-card">
                  <div className="highlight-image">
                    <img
                      src="https://z-p3-scontent.fadd1-1.fna.fbcdn.net/v/t39.30808-6/503210590_1114142577413910_5401089730644483341_n.jpg?_nc_cat=105&ccb=1-7&_nc_sid=833d8c&_nc_eui2=AeFG_MB66LfF6cGSDlucvLH260SFeMk0TSfrRIV4yTRNJ24xWvQQMnbKzbNovBHIshKkixqRTjETHl1YLkGJG7z9&_nc_ohc=GjX61I_2KrUQ7kNvwGwyAmY&_nc_oc=AdnEsLvM0Ny377tqxbpCBqyB1X-lMcNQ5ReOYkpRZyayW-2zxLrrsIbhrHQjTOEVkbQ&_nc_zt=23&_nc_ht=z-p3-scontent.fadd1-1.fna&_nc_gid=nKRQvljeKe7R6w6ltDhR4w&oh=00_AfQ12wjslSPUPbmALkjUtISrWNQHrbMNMPJa3Jw6kXg79A&oe=6893DDBD"
                      alt="University of Gondar Modern Campus"
                    />
                  </div>
                  <div className="highlight-content">
                    <h4>Modern Campus</h4>
                    <p>State-of-the-art facilities including modern laboratories, libraries, and research centers spread across multiple campuses.</p>
                  </div>
                </div>
              </div>
              <div className="col-lg-6">
                <div className="highlight-card">
                  <div className="highlight-image">
                    <img
                      src="https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcRLJeaWLWdHOoy-fvfC2YRGz8Ou5Xh1m1dff2540xpbb4GZEna8-R8D55UhjtLGF_iYvWM&usqp=CAU"
                      alt="University of Gondar Medical Excellence"
                    />
                  </div>
                  <div className="highlight-content">
                    <h4>Medical Excellence</h4>
                    <p>Renowned College of Medicine and Health Sciences, training healthcare professionals for Ethiopia and beyond.</p>
                  </div>
                </div>
              </div>
              <div className="col-lg-6">
                <div className="highlight-card">
                  <div className="highlight-image">
                    <img
                      src="https://z-p3-scontent.fadd2-1.fna.fbcdn.net/v/t39.30808-6/520439805_1150718623756305_9138878659435941121_n.jpg?_nc_cat=101&ccb=1-7&_nc_sid=833d8c&_nc_eui2=AeHleshyFKs6smaJPF8jXy-ZXXR0hP2xJztddHSE_bEnOwC-pyqQ_xFV-2IQzpIyimMgLSrGJ-9Jhi_w7ZKPNfzS&_nc_ohc=mIhFrtV5go8Q7kNvwHADZHe&_nc_oc=AdnvULEQxP0_ZQ6UjHTNOvKlYoo-IbwsMBsJyehVGRzggPfuKppXEES-Dh1ueccq4Qs&_nc_zt=23&_nc_ht=z-p3-scontent.fadd2-1.fna&_nc_gid=bf32mBXsQL9tjNjuLsO30A&oh=00_AfQvSNdxrvOzZmAG8Sh7oGjvE9wovXZL8XDP7PpmE25kCQ&oe=6893ABEA"
                      alt="University of Gondar Research Innovation"
                    />
                  </div>
                  <div className="highlight-content">
                    <h4>Research Innovation</h4>
                    <p>Leading research initiatives in health sciences, agriculture, engineering, and social sciences.</p>
                  </div>
                </div>
              </div>
              <div className="col-lg-6">
                <div className="highlight-card">
                  <div className="highlight-image">
                    <img
                      src="https://uog.edu.et/wp-content/uploads/2023/03/338686826_742137260748137_110050104499553600_n.jpg"
                      alt="University of Gondar Global Partnerships"
                    />
                  </div>
                  <div className="highlight-content">
                    <h4>Global Partnerships</h4>
                    <p>International collaborations and exchange programs with universities worldwide, fostering global education.</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Gallery Section */}
      <section id="gallery" className="gallery-section">
        <div className="container">
          <div className="section-header">
            <div className="row">
              <div className="col-lg-8 mx-auto text-center">
                <h2 className="section-title">Event Gallery</h2>
                <p className="section-subtitle">
                  Explore memorable moments from our events, conferences, and academic gatherings that showcase the vibrant community at University of Gondar.
                </p>
              </div>
            </div>
          </div>

          <div className="gallery-grid">
            <div className="row g-4">
              {galleryImages.length > 0 ? galleryImages.slice(0, 6).map((item, index) => (
                <div key={item.id} className={`col-lg-${index === 0 ? '6' : '3'} col-md-6`}>
                  <div className="gallery-item">
                    <div className="gallery-image">
                      <img src={getMediaUrl(item.image)} alt={item.title} />
                      <div className="gallery-overlay">
                        <div className="gallery-content">
                          <h4>{item.title}</h4>
                          <p>{item.description}</p>
                          <span className="gallery-category">{item.category_name || 'University Event'}</span>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              )) : (
                // Fallback content when no gallery items
                <>
                  <div className="col-lg-6 col-md-6">
                    <div className="gallery-item">
                      <div className="gallery-image">
                        <div className="gallery-placeholder">
                          <i className="fas fa-graduation-cap"></i>
                          <span>Academic Conference</span>
                        </div>
                        <div className="gallery-overlay">
                          <div className="gallery-content">
                            <h4>Academic Conference 2024</h4>
                            <p>International conference bringing together scholars and researchers</p>
                            <span className="gallery-category">Conference</span>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div className="col-lg-3 col-md-6">
                    <div className="gallery-item">
                      <div className="gallery-image">
                        <div className="gallery-placeholder">
                          <i className="fas fa-chalkboard-teacher"></i>
                          <span>Workshop</span>
                        </div>
                        <div className="gallery-overlay">
                          <div className="gallery-content">
                            <h4>Research Workshop</h4>
                            <p>Hands-on research methodology workshop</p>
                            <span className="gallery-category">Workshop</span>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div className="col-lg-3 col-md-6">
                    <div className="gallery-item">
                      <div className="gallery-image">
                        <div className="gallery-placeholder">
                          <i className="fas fa-microphone"></i>
                          <span>Seminar</span>
                        </div>
                        <div className="gallery-overlay">
                          <div className="gallery-content">
                            <h4>Guest Lecture</h4>
                            <p>Distinguished speaker series</p>
                            <span className="gallery-category">Seminar</span>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div className="col-lg-3 col-md-6">
                    <div className="gallery-item">
                      <div className="gallery-image">
                        <div className="gallery-placeholder">
                          <i className="fas fa-user-graduate"></i>
                          <span>Graduation</span>
                        </div>
                        <div className="gallery-overlay">
                          <div className="gallery-content">
                            <h4>Graduation Ceremony</h4>
                            <p>Celebrating academic achievements</p>
                            <span className="gallery-category">Ceremony</span>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div className="col-lg-3 col-md-6">
                    <div className="gallery-item">
                      <div className="gallery-image">
                        <div className="gallery-placeholder">
                          <i className="fas fa-music"></i>
                          <span>Cultural</span>
                        </div>
                        <div className="gallery-overlay">
                          <div className="gallery-content">
                            <h4>Cultural Festival</h4>
                            <p>Celebrating diversity and culture</p>
                            <span className="gallery-category">Cultural</span>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div className="col-lg-3 col-md-6">
                    <div className="gallery-item">
                      <div className="gallery-image">
                        <div className="gallery-placeholder">
                          <i className="fas fa-trophy"></i>
                          <span>Sports</span>
                        </div>
                        <div className="gallery-overlay">
                          <div className="gallery-content">
                            <h4>Sports Competition</h4>
                            <p>Inter-university sports championship</p>
                            <span className="gallery-category">Sports</span>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </>
              )}
            </div>
          </div>

          <div className="text-center mt-5">
            <a href="/gallery-demo" className="btn btn-outline-primary btn-lg">
              <i className="fas fa-images me-2"></i>
              View Full Gallery
            </a>
          </div>
        </div>
      </section>
      {/* Events Section */}
      <section id="events" className="events-section py-5">
        <div className="container">
          <div className="row">
            <div className="col-lg-12 text-center mb-5">
              <h2 className="section-title">Upcoming Events</h2>
              <p className="section-subtitle">
                Discover amazing events happening at University of Gondar
              </p>
            </div>
          </div>

          <div className="row">
            {events && events.length > 0 ? (
              events.slice(0, 6).map((event) => {
                const startDate = new Date(event.start_date);
                const endDate = new Date(event.end_date);
                const now = new Date();
                const isUpcoming = startDate > now;
                const isOngoing = now >= startDate && now <= endDate;

                return (
                  <div key={event.id} className="col-lg-4 col-md-6 mb-4">
                    <div className="event-card">
                      {event.banner && (
                        <div className="event-image">
                          <img
                            src={getMediaUrl(event.banner)}
                            alt={event.name}
                            className="img-fluid"
                          />
                          <div className="event-status">
                            {isOngoing ? (
                              <span className="badge bg-success">Live Now</span>
                            ) : isUpcoming ? (
                              <span className="badge bg-primary">Upcoming</span>
                            ) : (
                              <span className="badge bg-secondary">Past</span>
                            )}
                          </div>
                        </div>
                      )}

                      <div className="event-content">
                        <div className="event-date">
                          <i className="fas fa-calendar-alt me-2"></i>
                          {startDate.toLocaleDateString('en-US', {
                            month: 'short',
                            day: 'numeric',
                            year: 'numeric'
                          })}
                        </div>

                        <h4 className="event-title">{event.name}</h4>

                        {event.description && (
                          <p className="event-description">
                            {event.description.length > 100
                              ? `${event.description.substring(0, 100)}...`
                              : event.description
                            }
                          </p>
                        )}

                        <div className="event-meta">
                          <div className="event-location">
                            <i className="fas fa-map-marker-alt me-2"></i>
                            {event.location || 'University of Gondar'}
                          </div>

                          {event.participant_count && (
                            <div className="event-participants">
                              <i className="fas fa-users me-2"></i>
                              {event.participant_count} participants
                            </div>
                          )}
                        </div>

                        <div className="event-actions mt-3">
                          <Link
                            to={`/events/${event.id}`}
                            className="btn btn-primary btn-sm"
                          >
                            View Details
                          </Link>
                          {isUpcoming && (
                            <Link
                              to="/participant-register"
                              className="btn btn-outline-primary btn-sm ms-2"
                            >
                              Register
                            </Link>
                          )}
                        </div>
                      </div>
                    </div>
                  </div>
                );
              })
            ) : (
              <div className="col-12 text-center">
                <div className="no-events">
                  <i className="fas fa-calendar-times fa-3x text-muted mb-3"></i>
                  <h4 className="text-muted">No Events Available</h4>
                  <p className="text-muted">Check back later for upcoming events.</p>
                </div>
              </div>
            )}
          </div>

          {events && events.length > 6 && (
            <div className="row mt-4">
              <div className="col-12 text-center">
                <Link to="/events" className="btn btn-primary btn-lg">
                  View All Events
                  <i className="fas fa-arrow-right ms-2"></i>
                </Link>
              </div>
            </div>
          )}
        </div>
      </section>













    </ModernLayout>
  );
};

export default NewHome;
