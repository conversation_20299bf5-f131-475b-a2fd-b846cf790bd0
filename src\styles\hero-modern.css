/* Modern Hero Slider Styles */

.hero-slide-modern {
  min-height: 100vh;
  display: flex;
  align-items: center;
  position: relative;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  overflow: hidden;
}

.hero-slide-modern.active {
  animation: slideIn 0.8s ease-out;
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateX(-30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

/* Left Content Section */
.hero-content-modern {
  height: 100vh;
  display: flex;
  align-items: center;
  padding: 0 3rem;
  background: linear-gradient(135deg, rgba(102, 126, 234, 0.95) 0%, rgba(118, 75, 162, 0.95) 100%);
  position: relative;
}

.hero-content-modern::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="75" cy="75" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="50" cy="10" r="0.5" fill="rgba(255,255,255,0.05)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
  opacity: 0.3;
  z-index: 1;
}

.hero-content-inner {
  position: relative;
  z-index: 2;
  max-width: 600px;
}

/* Event Status Badge */
.event-status-modern {
  margin-bottom: 2rem;
}

.status-badge {
  display: flex;
  align-items: center;
  background: rgba(255, 255, 255, 0.15);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 50px;
  padding: 1rem 1.5rem;
  max-width: fit-content;
  transition: all 0.3s ease;
}

.status-badge:hover {
  background: rgba(255, 255, 255, 0.25);
  transform: translateY(-2px);
}

.status-badge.upcoming {
  border-left: 4px solid #ffc107;
}

.status-badge.live {
  border-left: 4px solid #28a745;
  animation: pulse 2s infinite;
}

.status-badge.past {
  border-left: 4px solid #6c757d;
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.7; }
}

.status-icon {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 1rem;
  color: white;
  font-size: 1.1rem;
}

.status-text {
  display: flex;
  flex-direction: column;
}

.status-label {
  color: white;
  font-weight: 600;
  font-size: 1rem;
  margin-bottom: 0.25rem;
}

.status-date {
  color: rgba(255, 255, 255, 0.8);
  font-size: 0.875rem;
}

/* Title Section */
.hero-title-modern {
  margin-bottom: 1.5rem;
}

.hero-title-modern h1 {
  font-size: 3.5rem;
  font-weight: 800;
  color: white;
  line-height: 1.1;
  margin-bottom: 1rem;
  text-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
}

.title-decoration {
  width: 80px;
  height: 4px;
  background: linear-gradient(90deg, #ffc107, #fd7e14);
  border-radius: 2px;
  position: relative;
}

.title-decoration::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 40px;
  height: 4px;
  background: rgba(255, 255, 255, 0.5);
  border-radius: 2px;
  animation: shimmer 2s infinite;
}

@keyframes shimmer {
  0% { transform: translateX(-40px); }
  100% { transform: translateX(120px); }
}

.hero-subtitle-modern {
  font-size: 1.5rem;
  color: rgba(255, 255, 255, 0.9);
  font-weight: 400;
  margin-bottom: 1.5rem;
  line-height: 1.4;
}

.hero-description-modern {
  font-size: 1.125rem;
  color: rgba(255, 255, 255, 0.8);
  line-height: 1.6;
  margin-bottom: 2.5rem;
}

/* Stats Section */
.hero-stats-modern {
  display: flex;
  gap: 2rem;
  margin-bottom: 3rem;
  flex-wrap: wrap;
}

.stat-item-modern {
  display: flex;
  align-items: center;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border-radius: 15px;
  padding: 1rem 1.5rem;
  border: 1px solid rgba(255, 255, 255, 0.2);
  transition: all 0.3s ease;
}

.stat-item-modern:hover {
  background: rgba(255, 255, 255, 0.2);
  transform: translateY(-3px);
}

.stat-icon {
  width: 50px;
  height: 50px;
  border-radius: 12px;
  background: rgba(255, 255, 255, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 1rem;
  color: white;
  font-size: 1.2rem;
}

.stat-content {
  display: flex;
  flex-direction: column;
}

.stat-number {
  font-size: 1.5rem;
  font-weight: 700;
  color: white;
  line-height: 1;
}

.stat-label {
  font-size: 0.875rem;
  color: rgba(255, 255, 255, 0.7);
  margin-top: 0.25rem;
}

/* Action Buttons */
.hero-actions-modern {
  display: flex;
  gap: 1rem;
  flex-wrap: wrap;
}

.btn-modern {
  display: inline-flex;
  align-items: center;
  padding: 1rem 2rem;
  border-radius: 50px;
  text-decoration: none;
  font-weight: 600;
  font-size: 1rem;
  transition: all 0.3s ease;
  border: 2px solid transparent;
  position: relative;
  overflow: hidden;
}

.btn-modern::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s ease;
}

.btn-modern:hover::before {
  left: 100%;
}

.btn-primary-modern {
  background: linear-gradient(135deg, #ffc107, #fd7e14);
  color: #212529;
  box-shadow: 0 8px 25px rgba(255, 193, 7, 0.4);
}

.btn-primary-modern:hover {
  transform: translateY(-3px);
  box-shadow: 0 12px 35px rgba(255, 193, 7, 0.6);
  color: #212529;
}

.btn-secondary-modern {
  background: transparent;
  color: white;
  border: 2px solid rgba(255, 255, 255, 0.3);
}

.btn-secondary-modern:hover {
  background: rgba(255, 255, 255, 0.1);
  border-color: rgba(255, 255, 255, 0.5);
  transform: translateY(-3px);
  color: white;
}

.btn-icon {
  margin-right: 0.75rem;
  font-size: 1.1rem;
}

.btn-text {
  position: relative;
  z-index: 2;
}

/* Right Image Section */
.hero-image-modern {
  height: 100vh;
  position: relative;
  overflow: hidden;
}

.image-container {
  position: relative;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.image-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(45deg, rgba(102, 126, 234, 0.3), rgba(118, 75, 162, 0.3));
  z-index: 2;
}

.hero-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  object-position: center;
  transition: transform 0.8s ease;
}

.hero-slide-modern.active .hero-image {
  animation: imageZoom 0.8s ease-out;
}

@keyframes imageZoom {
  from {
    transform: scale(1.1);
  }
  to {
    transform: scale(1);
  }
}

/* Decorative Elements */
.image-decoration {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 3;
  pointer-events: none;
}

.decoration-circle {
  position: absolute;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
}

.circle-1 {
  width: 120px;
  height: 120px;
  top: 10%;
  right: 10%;
  animation: float 6s ease-in-out infinite;
}

.circle-2 {
  width: 80px;
  height: 80px;
  bottom: 20%;
  right: 20%;
  animation: float 4s ease-in-out infinite reverse;
}

.circle-3 {
  width: 60px;
  height: 60px;
  top: 50%;
  right: 5%;
  animation: float 5s ease-in-out infinite;
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-20px);
  }
}

/* Responsive Design */
@media (max-width: 991.98px) {
  .hero-slide-modern {
    min-height: auto;
  }
  
  .hero-content-modern {
    height: auto;
    padding: 3rem 2rem;
  }
  
  .hero-image-modern {
    height: 50vh;
    order: -1;
  }
  
  .hero-title-modern h1 {
    font-size: 2.5rem;
  }
  
  .hero-stats-modern {
    gap: 1rem;
  }
  
  .stat-item-modern {
    flex: 1;
    min-width: 200px;
  }
  
  .hero-actions-modern {
    justify-content: center;
  }
}

@media (max-width: 767.98px) {
  .hero-content-modern {
    padding: 2rem 1rem;
  }
  
  .hero-title-modern h1 {
    font-size: 2rem;
  }
  
  .hero-subtitle-modern {
    font-size: 1.25rem;
  }
  
  .hero-description-modern {
    font-size: 1rem;
  }
  
  .hero-stats-modern {
    flex-direction: column;
    gap: 0.75rem;
  }
  
  .stat-item-modern {
    min-width: auto;
  }
  
  .btn-modern {
    padding: 0.875rem 1.5rem;
    font-size: 0.9rem;
  }
}
