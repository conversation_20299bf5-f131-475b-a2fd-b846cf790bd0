from rest_framework import viewsets, status
from rest_framework.decorators import action, api_view, permission_classes
from rest_framework.permissions import AllowAny
from rest_framework.response import Response
from django.http import HttpResponse
from django.shortcuts import render
from django.views.decorators.csrf import csrf_exempt
from .models import Badge, BadgeTemplate, DriverBadge, ContactPersonBadge
from .serializers import BadgeSerializer, BadgeTemplateSerializer, DriverBadgeSerializer, ContactPersonBadgeSerializer
from PIL import Image, ImageDraw, ImageFont
import qrcode
from io import BytesIO


class BadgeTemplateViewSet(viewsets.ModelViewSet):
    queryset = BadgeTemplate.objects.all()
    serializer_class = BadgeTemplateSerializer

    @action(detail=True, methods=['post'])
    def set_default(self, request, pk=None):
        """Set this template as default"""
        template = self.get_object()
        template.is_default = True
        template.save()

        serializer = self.get_serializer(template)
        return Response(serializer.data)


class BadgeViewSet(viewsets.ModelViewSet):
    queryset = Badge.objects.all()
    serializer_class = BadgeSerializer
    permission_classes = [AllowAny]  # Allow public access for badge preview

    def get_queryset(self):
        queryset = super().get_queryset()
        participant_id = self.request.query_params.get('participant', None)
        event_id = self.request.query_params.get('event', None)
        is_generated = self.request.query_params.get('generated', None)

        if participant_id is not None:
            queryset = queryset.filter(participant=participant_id)
        if event_id is not None:
            queryset = queryset.filter(participant__event=event_id)
        if is_generated is not None:
            queryset = queryset.filter(is_generated=is_generated.lower() == 'true')

        return queryset

    @action(detail=True, methods=['post'])
    def regenerate(self, request, pk=None):
        """Regenerate badge"""
        badge = self.get_object()
        try:
            badge.generate_badge()
            serializer = self.get_serializer(badge)
            return Response(serializer.data)
        except Exception as e:
            return Response({'error': str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    @action(detail=True, methods=['get'])
    def download(self, request, pk=None):
        """Download badge image"""
        badge = self.get_object()
        if badge.badge_image:
            response = HttpResponse(badge.badge_image.read(), content_type='image/png')
            response['Content-Disposition'] = f'attachment; filename="badge_{badge.participant.uuid}.png"'
            return response
        else:
            return Response({'error': 'Badge not generated'}, status=status.HTTP_404_NOT_FOUND)

    @action(detail=False, methods=['post'])
    def bulk_generate(self, request):
        """Generate badges for multiple participants"""
        participant_ids = request.data.get('participant_ids', [])
        if not participant_ids:
            return Response({'error': 'No participant IDs provided'},
                          status=status.HTTP_400_BAD_REQUEST)

        generated_badges = []
        errors = []

        for participant_id in participant_ids:
            try:
                from participants.models import Participant
                participant = Participant.objects.get(id=participant_id)
                badge, created = Badge.objects.get_or_create(participant=participant)
                badge.generate_badge()
                generated_badges.append(badge.id)
            except Exception as e:
                errors.append(f"Participant {participant_id}: {str(e)}")

        return Response({
            'generated_badges': generated_badges,
            'errors': errors,
            'total_generated': len(generated_badges),
            'total_errors': len(errors)
        })


class DriverBadgeViewSet(viewsets.ModelViewSet):
    queryset = DriverBadge.objects.all()
    serializer_class = DriverBadgeSerializer

    @action(detail=True, methods=['post'])
    def generate(self, request, pk=None):
        """Generate driver badge"""
        badge = self.get_object()
        try:
            badge.generate_badge()
            serializer = self.get_serializer(badge)
            return Response(serializer.data)
        except Exception as e:
            return Response({'error': str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    @action(detail=True, methods=['post'])
    def regenerate(self, request, pk=None):
        """Regenerate driver badge"""
        badge = self.get_object()
        try:
            badge.generate_badge()
            serializer = self.get_serializer(badge)
            return Response(serializer.data)
        except Exception as e:
            return Response({'error': str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    @action(detail=True, methods=['get'])
    def download(self, request, pk=None):
        """Download driver badge image"""
        badge = self.get_object()
        if badge.badge_image:
            response = HttpResponse(badge.badge_image.read(), content_type='image/png')
            response['Content-Disposition'] = f'attachment; filename="badge_driver_{badge.driver.id}.png"'
            return response
        else:
            return Response({'error': 'Badge not generated'}, status=status.HTTP_404_NOT_FOUND)

    @action(detail=False, methods=['post'])
    def bulk_generate(self, request):
        """Generate badges for multiple drivers"""
        driver_ids = request.data.get('driver_ids', [])
        if not driver_ids:
            return Response({'error': 'No driver IDs provided'},
                          status=status.HTTP_400_BAD_REQUEST)

        generated_badges = []
        errors = []

        for driver_id in driver_ids:
            try:
                from drivers.models import Driver
                driver = Driver.objects.get(id=driver_id)
                badge, created = DriverBadge.objects.get_or_create(driver=driver)
                badge.generate_badge()
                generated_badges.append(badge.id)
            except Exception as e:
                errors.append(f"Driver {driver_id}: {str(e)}")

        return Response({
            'generated_badges': generated_badges,
            'errors': errors,
            'total_generated': len(generated_badges),
            'total_errors': len(errors)
        })


class ContactPersonBadgeViewSet(viewsets.ModelViewSet):
    queryset = ContactPersonBadge.objects.all()
    serializer_class = ContactPersonBadgeSerializer

    @action(detail=True, methods=['post'])
    def generate(self, request, pk=None):
        """Generate contact person badge"""
        badge = self.get_object()
        try:
            badge.generate_badge()
            serializer = self.get_serializer(badge)
            return Response(serializer.data)
        except Exception as e:
            return Response({'error': str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    @action(detail=True, methods=['post'])
    def regenerate(self, request, pk=None):
        """Regenerate contact person badge"""
        badge = self.get_object()
        try:
            badge.generate_badge()
            serializer = self.get_serializer(badge)
            return Response(serializer.data)
        except Exception as e:
            return Response({'error': str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    @action(detail=True, methods=['get'])
    def download(self, request, pk=None):
        """Download contact person badge image"""
        badge = self.get_object()
        if badge.badge_image:
            response = HttpResponse(badge.badge_image.read(), content_type='image/png')
            response['Content-Disposition'] = f'attachment; filename="badge_contact_person_{badge.contact_person.id}.png"'
            return response
        else:
            return Response({'error': 'Badge not generated'}, status=status.HTTP_404_NOT_FOUND)

    @action(detail=False, methods=['post'])
    def bulk_generate(self, request):
        """Generate badges for multiple contact persons"""
        contact_person_ids = request.data.get('contact_person_ids', [])
        if not contact_person_ids:
            return Response({'error': 'No contact person IDs provided'},
                          status=status.HTTP_400_BAD_REQUEST)

        generated_badges = []
        errors = []

        for contact_person_id in contact_person_ids:
            try:
                from contact_persons.models import ContactPerson
                contact_person = ContactPerson.objects.get(id=contact_person_id)
                badge, created = ContactPersonBadge.objects.get_or_create(contact_person=contact_person)
                badge.generate_badge()
                generated_badges.append(badge.id)
            except Exception as e:
                errors.append(f"Contact Person {contact_person_id}: {str(e)}")

        return Response({
            'generated_badges': generated_badges,
            'errors': errors,
            'total_generated': len(generated_badges),
            'total_errors': len(errors)
        })


@api_view(['GET'])
@permission_classes([AllowAny])
def test_svg_badge(request):
    """Generate a test SVG-style badge"""
    try:
        # Create a test badge image
        width = 600
        height = 1200

        # Create badge with gradient background
        badge_img = Image.new('RGB', (width, height), color='#002D72')
        draw = ImageDraw.Draw(badge_img)

        # Add gradient background
        for y in range(height):
            progress = y / height
            r = int(0 + (2 - 0) * progress)
            g = int(45 + (27 - 45) * progress)
            b = int(114 + (58 - 114) * progress)
            draw.line([(0, y), (width, y)], fill=(r, g, b))

        # Add ministry logo circle
        center_x, center_y = 300, 90
        radius = 50
        draw.ellipse([center_x-radius, center_y-radius, center_x+radius, center_y+radius],
                    fill='white')

        # Add title
        try:
            title_font = ImageFont.truetype("arial.ttf", 24)
        except:
            title_font = ImageFont.load_default()

        draw.text((width//2, 165), "MINISTRY OF EDUCATION OF FDRE",
                 fill='white', font=title_font, anchor='mm')

        # Add participant info
        try:
            name_font = ImageFont.truetype("arial.ttf", 28)
        except:
            name_font = ImageFont.load_default()

        draw.text((width//2, 560), "Test Participant",
                 fill='white', font=name_font, anchor='mm')

        # Add QR code
        qr = qrcode.QRCode(version=1, box_size=10, border=5)
        qr.add_data("https://example.com/verify/test")
        qr.make(fit=True)
        qr_img = qr.make_image(fill_color="black", back_color="white")

        # QR code background
        draw.rectangle([200, 745, 400, 945], fill='white')
        qr_resized = qr_img.resize((200, 200), Image.Resampling.LANCZOS)
        badge_img.paste(qr_resized, (200, 745))

        # Convert to HTTP response
        buffer = BytesIO()
        badge_img.save(buffer, format='PNG')
        buffer.seek(0)

        response = HttpResponse(buffer.getvalue(), content_type='image/png')
        response['Content-Disposition'] = 'inline; filename="test_badge.png"'
        return response

    except Exception as e:
        return Response({'error': str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['POST'])
@permission_classes([AllowAny])
def force_regenerate_badge(request, participant_id):
    """Force regenerate badge with new SVG design"""
    try:
        from participants.models import Participant

        # Get participant
        try:
            participant = Participant.objects.get(id=participant_id)
        except Participant.DoesNotExist:
            return Response({'error': 'Participant not found'}, status=status.HTTP_404_NOT_FOUND)

        # Get or create badge
        badge, created = Badge.objects.get_or_create(participant=participant)

        # Clear existing badge files to force regeneration
        if badge.badge_image:
            badge.badge_image.delete(save=False)
        if badge.qr_code_image:
            badge.qr_code_image.delete(save=False)

        # Reset generation status
        badge.is_generated = False
        badge.generated_at = None
        badge.save()

        # Generate new SVG badge
        badge_img = badge.generate_badge()

        return Response({
            'success': True,
            'message': 'Badge regenerated with new SVG design',
            'participant': participant.full_name,
            'badge_dimensions': badge_img.size,
            'svg_template': badge_img.size == (600, 1200),
            'badge_url': badge.badge_image.url if badge.badge_image else None,
            'generated_at': badge.generated_at
        })

    except Exception as e:
        return Response({'error': str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['POST'])
@permission_classes([AllowAny])
def regenerate_all_badges(request):
    """Regenerate all badges with new SVG design"""
    try:
        from participants.models import Participant

        # Get participants (limit to first 10 for testing)
        participants = Participant.objects.all()[:10]

        if not participants:
            return Response({'error': 'No participants found'}, status=status.HTTP_404_NOT_FOUND)

        regenerated_count = 0
        results = []

        for participant in participants:
            try:
                # Get or create badge
                badge, created = Badge.objects.get_or_create(participant=participant)

                # Clear existing badge files to force regeneration
                if badge.badge_image:
                    badge.badge_image.delete(save=False)
                if badge.qr_code_image:
                    badge.qr_code_image.delete(save=False)

                # Reset generation status
                badge.is_generated = False
                badge.generated_at = None
                badge.save()

                # Generate new SVG badge
                badge_img = badge.generate_badge()

                regenerated_count += 1
                results.append({
                    'participant': participant.full_name,
                    'status': 'success',
                    'dimensions': badge_img.size,
                    'svg_template': badge_img.size == (600, 1200),
                    'badge_url': badge.badge_image.url if badge.badge_image else None
                })

            except Exception as e:
                results.append({
                    'participant': participant.full_name,
                    'status': 'error',
                    'error': str(e)
                })

        return Response({
            'success': True,
            'message': f'Regenerated {regenerated_count} badges with new SVG design',
            'total_processed': len(participants),
            'regenerated_count': regenerated_count,
            'results': results
        })

    except Exception as e:
        return Response({'error': str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@csrf_exempt
def badge_demo(request):
    """Demo page for the simplified badge generation"""
    html_content = """
    <!DOCTYPE html>
    <html lang="en">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Simplified SVG Badge Generation Demo</title>
        <style>
            body { font-family: Arial, sans-serif; max-width: 1200px; margin: 0 auto; padding: 20px; background-color: #f5f5f5; }
            .header { text-align: center; margin-bottom: 30px; padding: 20px; background: linear-gradient(135deg, #002D72, #021B3A); color: white; border-radius: 10px; }
            .demo-section { background: white; padding: 30px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); margin-bottom: 20px; }
            .badge-preview { text-align: center; margin: 20px 0; }
            .badge-preview img { max-width: 300px; border: 2px solid #ddd; border-radius: 10px; box-shadow: 0 4px 8px rgba(0,0,0,0.1); }
            .btn { background: #002D72; color: white; padding: 12px 24px; border: none; border-radius: 5px; cursor: pointer; font-size: 16px; margin: 10px; text-decoration: none; display: inline-block; }
            .btn:hover { background: #021B3A; }
        </style>
    </head>
    <body>
        <div class="header">
            <h1>🎨 Simplified SVG Badge Generation</h1>
            <p>Professional badge generation matching the exact SVG template design</p>
        </div>

        <div class="demo-section">
            <h2>📋 Badge Preview</h2>
            <p>This is a live preview of the new simplified badge generation system that matches the exact SVG template.</p>

            <div class="badge-preview">
                <img src="/api/badges/test-svg-badge/" alt="Test Badge" id="badgePreview">
            </div>

            <div style="text-align: center;">
                <button class="btn" onclick="refreshBadge()">🔄 Refresh Badge</button>
                <a href="/api/badges/test-svg-badge/" class="btn" download="test_badge.png">💾 Download Badge</a>
            </div>
        </div>

        <div class="demo-section">
            <h2>✨ Key Features</h2>
            <ul>
                <li>🎯 Exact SVG template match (600x1200 dimensions)</li>
                <li>🎨 Gradient background (#002D72 to #021B3A)</li>
                <li>🏛️ Ministry logo with white circle</li>
                <li>📋 Professional typography and spacing</li>
                <li>🔗 QR code for verification</li>
                <li>🤝 Sponsor acknowledgment sections</li>
                <li>📞 Contact information footer</li>
            </ul>
        </div>

        <div class="demo-section">
            <h2>🔧 Implementation</h2>
            <p>The badge generation has been completely simplified:</p>
            <ul>
                <li>✅ Modular helper methods for each section</li>
                <li>✅ Exact SVG template matching</li>
                <li>✅ Clean, maintainable code</li>
                <li>✅ Professional output quality</li>
                <li>✅ Backward compatible with existing system</li>
            </ul>
        </div>

        <script>
            function refreshBadge() {
                const img = document.getElementById('badgePreview');
                const timestamp = new Date().getTime();
                img.src = `/api/badges/test-svg-badge/?t=${timestamp}`;
            }
        </script>
    </body>
    </html>
    """
    return HttpResponse(html_content, content_type='text/html')


@api_view(['GET'])
@permission_classes([AllowAny])
def badge_status(request):
    """Check badge generation system status"""
    try:
        from participants.models import Participant

        # Check if we can access the database
        participant_count = Participant.objects.count()
        badge_count = Badge.objects.count()

        # Test basic badge generation components
        test_results = {
            'database_accessible': True,
            'participant_count': participant_count,
            'badge_count': badge_count,
            'pil_available': True,
            'qrcode_available': True,
            'svg_methods_available': True
        }

        # Test PIL
        try:
            test_img = Image.new('RGB', (100, 100), color='red')
            test_results['pil_available'] = True
        except:
            test_results['pil_available'] = False

        # Test QR code
        try:
            qr = qrcode.QRCode(version=1, box_size=10, border=5)
            qr.add_data("test")
            qr.make(fit=True)
            test_results['qrcode_available'] = True
        except:
            test_results['qrcode_available'] = False

        # Check if SVG methods exist
        try:
            badge = Badge()
            has_svg_methods = (
                hasattr(badge, '_add_svg_gradient_background') and
                hasattr(badge, '_add_svg_ministry_logo') and
                hasattr(badge, '_add_svg_participant_info')
            )
            test_results['svg_methods_available'] = has_svg_methods
        except:
            test_results['svg_methods_available'] = False

        return Response({
            'status': 'success',
            'message': 'Badge generation system is operational',
            'simplified_svg_generation': True,
            'test_results': test_results
        })

    except Exception as e:
        return Response({
            'status': 'error',
            'message': f'Badge generation system error: {str(e)}',
            'simplified_svg_generation': False
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


def api_test(request):
    """API test page"""
    return render(request, 'api_test.html')
