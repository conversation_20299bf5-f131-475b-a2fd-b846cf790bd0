# Generated by Django 4.2.7 on 2025-07-31 17:43

import django.core.validators
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('events', '0006_merge_20250729_0840'),
    ]

    operations = [
        migrations.CreateModel(
            name='GalleryCategory',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, unique=True)),
                ('description', models.TextField(blank=True)),
                ('slug', models.SlugField(unique=True)),
                ('icon', models.CharField(blank=True, help_text='FontAwesome icon class (e.g., fas fa-camera)', max_length=50)),
                ('color', models.CharField(default='#667eea', help_text='Hex color code for category theme', max_length=7)),
                ('is_active', models.BooleanField(default=True)),
                ('order', models.PositiveIntegerField(default=0, help_text='Display order (lower numbers first)')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'verbose_name': 'Gallery Category',
                'verbose_name_plural': 'Gallery Categories',
                'ordering': ['order', 'name'],
            },
        ),
        migrations.CreateModel(
            name='GalleryImage',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(max_length=200)),
                ('description', models.TextField(blank=True)),
                ('image', models.ImageField(upload_to='gallery/%Y/%m/', validators=[django.core.validators.FileExtensionValidator(allowed_extensions=['jpg', 'jpeg', 'png', 'webp'])])),
                ('thumbnail', models.ImageField(blank=True, upload_to='gallery/thumbnails/%Y/%m/')),
                ('tags', models.CharField(blank=True, help_text='Comma-separated tags', max_length=500)),
                ('is_featured', models.BooleanField(default=False, help_text='Show in featured sections')),
                ('is_slider', models.BooleanField(default=False, help_text='Include in homepage slider')),
                ('is_campus', models.BooleanField(default=False, help_text='Campus/University related')),
                ('is_active', models.BooleanField(default=True)),
                ('photographer', models.CharField(blank=True, max_length=100)),
                ('date_taken', models.DateField(blank=True, null=True)),
                ('location', models.CharField(blank=True, max_length=200)),
                ('alt_text', models.CharField(blank=True, help_text='Alt text for accessibility', max_length=200)),
                ('order', models.PositiveIntegerField(default=0, help_text='Display order within category')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('category', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='images', to='gallery.gallerycategory')),
            ],
            options={
                'verbose_name': 'Gallery Image',
                'verbose_name_plural': 'Gallery Images',
                'ordering': ['-is_featured', 'order', '-created_at'],
            },
        ),
        migrations.CreateModel(
            name='EventSlider',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('slider_title', models.CharField(blank=True, help_text='Custom title for slider (optional)', max_length=200)),
                ('slider_subtitle', models.CharField(blank=True, help_text='Custom subtitle for slider (optional)', max_length=200)),
                ('slider_description', models.TextField(blank=True, help_text='Custom description for slider (optional)')),
                ('background_image', models.ImageField(blank=True, upload_to='slider/backgrounds/%Y/%m/', validators=[django.core.validators.FileExtensionValidator(allowed_extensions=['jpg', 'jpeg', 'png', 'webp'])])),
                ('background_color', models.CharField(default='#667eea', help_text='Hex color code for gradient start', max_length=7)),
                ('background_color_end', models.CharField(default='#764ba2', help_text='Hex color code for gradient end', max_length=7)),
                ('is_active', models.BooleanField(default=True, help_text='Show in homepage slider')),
                ('order', models.PositiveIntegerField(default=0, help_text='Display order in slider')),
                ('cta_text', models.CharField(default='Register Now', help_text='Call-to-action button text', max_length=50)),
                ('cta_url', models.URLField(blank=True, help_text='Custom CTA URL (optional)')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('event', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='slider_config', to='events.event')),
            ],
            options={
                'verbose_name': 'Event Slider',
                'verbose_name_plural': 'Event Sliders',
                'ordering': ['order', '-created_at'],
            },
        ),
    ]
