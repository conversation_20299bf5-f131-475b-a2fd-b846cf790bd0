<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>QR Code Decoder</title>
    <script src="https://unpkg.com/html5-qrcode@2.3.8/html5-qrcode.min.js"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        #reader {
            width: 100%;
            max-width: 500px;
            margin: 20px auto;
        }
        .result {
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
            border-left: 4px solid #007bff;
        }
        .error {
            background-color: #f8d7da;
            border-left-color: #dc3545;
        }
        .success {
            background-color: #d4edda;
            border-left-color: #28a745;
        }
        pre {
            background-color: #f8f9fa;
            padding: 10px;
            border-radius: 3px;
            overflow-x: auto;
            white-space: pre-wrap;
            word-break: break-all;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
    </style>
</head>
<body>
    <h1>QR Code Decoder</h1>
    <p>This tool will help us see exactly what data is in your QR code.</p>
    
    <div>
        <button onclick="startScanning()">Start Camera</button>
        <button onclick="stopScanning()">Stop Camera</button>
    </div>
    
    <div id="reader"></div>
    
    <div id="results"></div>

    <script>
        let html5QrcodeScanner = null;
        let isScanning = false;

        function startScanning() {
            if (isScanning) return;
            
            const resultsDiv = document.getElementById('results');
            resultsDiv.innerHTML = '<div class="result">Starting camera...</div>';
            
            html5QrcodeScanner = new Html5QrcodeScanner(
                "reader",
                { 
                    fps: 10, 
                    qrbox: { width: 250, height: 250 },
                    aspectRatio: 1.0
                },
                false
            );
            
            html5QrcodeScanner.render(onScanSuccess, onScanFailure);
            isScanning = true;
        }

        function stopScanning() {
            if (html5QrcodeScanner && isScanning) {
                html5QrcodeScanner.clear();
                isScanning = false;
                document.getElementById('results').innerHTML += '<div class="result">Camera stopped.</div>';
            }
        }

        function onScanSuccess(decodedText, decodedResult) {
            console.log(`Code matched = ${decodedText}`, decodedResult);
            
            // Stop scanning after successful read
            stopScanning();
            
            // Display results
            displayResults(decodedText);
        }

        function onScanFailure(error) {
            // Handle scan failure - usually just means no QR code in view
            // Don't log these as they're very frequent
        }

        function displayResults(decodedText) {
            const resultsDiv = document.getElementById('results');
            
            // Parse the QR code data
            const analysis = analyzeQRData(decodedText);
            
            resultsDiv.innerHTML = `
                <div class="result success">
                    <h3>✅ QR Code Successfully Decoded!</h3>
                    <h4>Raw Data:</h4>
                    <pre>${decodedText}</pre>
                    
                    <h4>Analysis:</h4>
                    <pre>${JSON.stringify(analysis, null, 2)}</pre>
                    
                    <h4>Recommended Fix:</h4>
                    <pre>${analysis.recommendedFix}</pre>
                </div>
            `;
        }

        function analyzeQRData(decodedText) {
            const analysis = {
                rawData: decodedText,
                dataType: 'unknown',
                extractedEmail: null,
                extractedName: null,
                isValidFormat: false,
                recommendedFix: 'Unknown format'
            };

            // Check if it's JSON
            try {
                const jsonData = JSON.parse(decodedText);
                analysis.dataType = 'JSON';
                analysis.extractedEmail = jsonData.email || jsonData.participant_email;
                analysis.extractedName = jsonData.name || jsonData.full_name || 
                                       (jsonData.first_name && jsonData.last_name ? 
                                        `${jsonData.first_name} ${jsonData.last_name}` : null);
                analysis.isValidFormat = !!analysis.extractedEmail;
                analysis.recommendedFix = analysis.isValidFormat ? 
                    'JSON format is good - use email field' : 
                    'JSON format but missing email field';
                return analysis;
            } catch (e) {
                // Not JSON, continue with other checks
            }

            // Check if it's a URL
            if (decodedText.includes('http') || decodedText.includes('www')) {
                analysis.dataType = 'URL';
                try {
                    const url = new URL(decodedText);
                    analysis.extractedEmail = url.searchParams.get('email');
                    analysis.extractedName = url.searchParams.get('name') || 
                                           url.searchParams.get('firstname') + ' ' + url.searchParams.get('lastname');
                } catch (e) {
                    // Manual parsing
                    const emailMatch = decodedText.match(/email=([^&]+)/);
                    analysis.extractedEmail = emailMatch ? decodeURIComponent(emailMatch[1]) : null;
                }
                analysis.isValidFormat = !!analysis.extractedEmail;
                analysis.recommendedFix = analysis.isValidFormat ? 
                    'URL format is good - extract email parameter' : 
                    'URL format but missing email parameter';
                return analysis;
            }

            // Check if it contains an email pattern
            const emailPattern = /([a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,})/;
            const emailMatch = decodedText.match(emailPattern);
            
            if (emailMatch) {
                analysis.dataType = 'Mixed Text with Email';
                analysis.extractedEmail = emailMatch[1];
                
                // Try to extract name (everything before or after email)
                const parts = decodedText.split(emailMatch[1]);
                const beforeEmail = parts[0].trim();
                const afterEmail = parts[1] ? parts[1].trim() : '';
                
                // Assume name is the longer part or the part that looks like a name
                if (beforeEmail && beforeEmail.length > 2) {
                    analysis.extractedName = beforeEmail.replace(/[^a-zA-Z\s]/g, '').trim();
                } else if (afterEmail && afterEmail.length > 2) {
                    analysis.extractedName = afterEmail.replace(/[^a-zA-Z\s]/g, '').trim();
                }
                
                analysis.isValidFormat = true;
                analysis.recommendedFix = `Extract email: "${analysis.extractedEmail}" from mixed text`;
                return analysis;
            }

            // Check if it's just an email
            if (emailPattern.test(decodedText.trim())) {
                analysis.dataType = 'Pure Email';
                analysis.extractedEmail = decodedText.trim();
                analysis.isValidFormat = true;
                analysis.recommendedFix = 'Pure email format - use directly';
                return analysis;
            }

            // Unknown format
            analysis.recommendedFix = 'Unknown format - need to implement custom parsing';
            return analysis;
        }
    </script>
</body>
</html>
