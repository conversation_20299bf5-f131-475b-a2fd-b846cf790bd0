#!/usr/bin/env python3

import os
import sys
import django

# Add the project root to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'event_management.settings')
django.setup()

from participants.models import Participant
from django.db import transaction

def clean_duplicates_and_update_ids():
    """Remove duplicates and update IDs to exact values"""
    
    print("=== CLEANING DUPLICATES AND UPDATING IDS ===")
    
    # Mapping of (first_name, last_name) to desired ID
    name_to_id_mapping = {
        ('<PERSON><PERSON>', '<PERSON>'): 337,
        ('<PERSON><PERSON><PERSON><PERSON>', '<PERSON>ress<PERSON>'): 338,
        ('Andargachew', 'De<PERSON>'): 339,
        ('Pa<PERSON>', 'Do<PERSON>'): 340,
        ('<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>'): 342,
        ('<PERSON><PERSON><PERSON><PERSON>', '<PERSON>rb<PERSON>'): 343,
        ('<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>'): 344,
        ('<PERSON><PERSON><PERSON>', '<PERSON><PERSON>'): 346,
        ('<PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON>'): 347,
        ('<PERSON><PERSON>', '<PERSON>'): 348,
        ('<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>'): 349,
        ('En<PERSON><PERSON>w', '<PERSON><PERSON>'): 350,
        ('<PERSON>s<PERSON><PERSON>', '<PERSON>yi<PERSON>'): 351,
        ('<PERSON><PERSON>', '<PERSON><PERSON><PERSON>'): 352,
        ('<PERSON>. <PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>'): 353,
        ('<PERSON><PERSON> <PERSON><PERSON><PERSON>', '<PERSON><PERSON>'): 354,
        ('<PERSON>. <PERSON>', '<PERSON><PERSON>'): 355,
        ('<PERSON><PERSON>e', 'Gerbi'): 356,
        ('Carly', 'Gentry'): 511,
        ('Adem', 'Kabo'): 512,
        ('Matebu', 'Jabessa'): 513,
        ('Asmamaw', 'Workneh'): 514,
        ('Dr.Megersa', 'Hussen'): 515,
        ('LAMESGIN', 'TIZAZU'): 516,
        ('Woldeamlak', 'Alemayehu'): 517,
        ('Derbew', 'Yohannes'): 518,
        ('Bizunesh', 'Borena'): 519,
        ('LIDETU', 'GOBENA'): 520,
        ('Alemayehu', 'Mekonnen'): 521,  # First one will get 521
        ('Asnake', 'Ede'): 523,
        ('Ochan', 'Agwa'): 524,
        ('Dr Abdiselam', 'Mohamed'): 525,
        ('Shelleme', 'Jiru'): 526,
        ('Nega', 'Tessemma'): 527,
        ('Dawit', 'Bezabih'): 528,
        ('Aynishet', 'Gebremariam'): 529,
        ('Lijalem', 'Abate'): 530,
        ('John', 'Firrisa'): 531,
        ('Adane', 'Tesega'): 532,
        ('Biniyam', 'Jimma'): 533,
        ('Serawit', 'Melkato'): 534,
        ('Hana', 'Kumera'): 535,
        ('Dr. Getinet', 'Ashenafi'): 536,
        ('KEBEDE', 'SHIFERAW'): 537,
        ('Zaid', 'Zewde'): 538,
        ('Dunkana', 'Kenie'): 539,
        ('Birhan', 'Miheretu'): 540,
        ('Dr Alemu', 'Ayano'): 541,
        ('Fana Hagos', 'Berhane'): 542,
        ('Ataklti', 'Gebrehiwot'): 543,
        ('Asalf', 'Wondemgezahu'): 544,
        ('Tesfaye', 'Jimma'): 545,
        ('Nebiyu', 'Teklie'): 546,
        ('Henoke', 'demese'): 547,
        ('Zewdu', 'Worku'): 548,
        ('Lediya', 'Negussie'): 549,
        ('Walelign', 'Tilahun'): 550,
    }
    
    updated_count = 0
    deleted_count = 0
    
    with transaction.atomic():
        for (first_name, last_name), desired_id in name_to_id_mapping.items():
            # Find all participants with this name
            participants = Participant.objects.filter(first_name=first_name, last_name=last_name)
            
            if not participants.exists():
                print(f"Warning: Participant {first_name} {last_name} not found")
                continue
            
            if participants.count() > 1:
                print(f"Found {participants.count()} duplicates for {first_name} {last_name}")
                
                # Keep the first one, delete the rest
                keeper = participants.first()
                duplicates = participants.exclude(id=keeper.id)
                
                for duplicate in duplicates:
                    print(f"  Deleting duplicate: ID {duplicate.id}, Email: {duplicate.email}")
                    duplicate.delete()
                    deleted_count += 1
                
                # Update the keeper's ID
                old_id = keeper.id
                keeper.id = desired_id
                keeper.save()
                updated_count += 1
                print(f"  Updated keeper: {first_name} {last_name} from ID {old_id} to ID {desired_id}")
                
            else:
                # Single participant, just update ID
                participant = participants.first()
                old_id = participant.id
                
                # Check if desired ID is already taken by someone else
                if Participant.objects.filter(id=desired_id).exclude(id=participant.id).exists():
                    existing = Participant.objects.get(id=desired_id)
                    print(f"Warning: ID {desired_id} already taken by {existing.first_name} {existing.last_name}")
                    continue
                
                participant.id = desired_id
                participant.save()
                updated_count += 1
                print(f"Updated: {first_name} {last_name} from ID {old_id} to ID {desired_id}")
        
        # Handle the second Alemayehu Mekonnen for ID 522
        alemayehu_participants = Participant.objects.filter(first_name='Alemayehu', last_name='Mekonnen')
        if alemayehu_participants.count() > 1:
            print(f"\nHandling additional Alemayehu Mekonnen participants...")
            # The first one should already have ID 521, assign 522 to the second one
            remaining = alemayehu_participants.exclude(id=521)
            if remaining.exists():
                second_alemayehu = remaining.first()
                old_id = second_alemayehu.id
                second_alemayehu.id = 522
                second_alemayehu.save()
                print(f"Updated second Alemayehu Mekonnen from ID {old_id} to ID 522")
                updated_count += 1
                
                # Delete any additional duplicates
                additional_duplicates = remaining.exclude(id=522)
                for duplicate in additional_duplicates:
                    print(f"  Deleting additional Alemayehu duplicate: ID {duplicate.id}")
                    duplicate.delete()
                    deleted_count += 1
    
    print(f"\nSummary:")
    print(f"Updated {updated_count} participant IDs")
    print(f"Deleted {deleted_count} duplicate participants")
    print(f"Total participants now: {Participant.objects.count()}")

if __name__ == "__main__":
    clean_duplicates_and_update_ids()
