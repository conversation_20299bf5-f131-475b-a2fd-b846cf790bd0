# Generated by Django 4.2.7 on 2025-08-05 03:40

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('events', '0007_add_map_embed'),
    ]

    operations = [
        migrations.CreateModel(
            name='EventDocument',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(max_length=200)),
                ('description', models.TextField(blank=True)),
                ('document_type', models.CharField(choices=[('presentation', 'Presentation'), ('schedule', 'Schedule'), ('agenda', 'Agenda'), ('brochure', 'Brochure'), ('certificate', 'Certificate'), ('report', 'Report'), ('other', 'Other')], default='other', max_length=20)),
                ('file', models.FileField(upload_to='event_documents/')),
                ('file_size', models.PositiveIntegerField(blank=True, null=True)),
                ('uploaded_at', models.DateTimeField(auto_now_add=True)),
                ('is_public', models.BooleanField(default=True, help_text='Whether this document is publicly downloadable')),
                ('download_count', models.PositiveIntegerField(default=0)),
                ('is_active', models.BooleanField(default=True)),
                ('event', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='documents', to='events.event')),
                ('uploaded_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name_plural': 'Event Documents',
                'ordering': ['-uploaded_at'],
            },
        ),
    ]
