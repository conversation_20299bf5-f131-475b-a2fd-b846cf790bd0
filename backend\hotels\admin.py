from django.contrib import admin
from django.http import HttpResponse
from django.shortcuts import render, redirect
from django.contrib import messages
from django.urls import path, reverse
from django.utils.html import format_html
import csv
import io
from .models import Hotel, HotelRoom, HotelReservation


@admin.register(Hotel)
class HotelAdmin(admin.ModelAdmin):
    list_display = ['name', 'contact_person', 'star_rating', 'event', 'is_active', 'assigned_participants_count', 'total_rooms']
    list_filter = ['star_rating', 'event', 'is_active', 'created_at']
    search_fields = ['name', 'address', 'contact_person', 'email']
    readonly_fields = ['created_at', 'updated_at']

    fieldsets = (
        ('Hotel Information', {
            'fields': ('name', 'address', 'phone', 'email', 'contact_person')
        }),
        ('Location & Rating', {
            'fields': ('latitude', 'longitude', 'star_rating', 'website')
        }),
        ('Event & Status', {
            'fields': ('event', 'is_active', 'description')
        }),
        ('System Information', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        })
    )

    actions = ['export_to_csv', 'mark_active', 'mark_inactive']

    def assigned_participants_count(self, obj):
        count = obj.assigned_participants.count()
        if count > 0:
            url = reverse('admin:participants_participant_changelist') + f'?assigned_hotel__id__exact={obj.id}'
            return format_html('<a href="{}">{} participants</a>', url, count)
        return '0 participants'
    assigned_participants_count.short_description = 'Assigned Participants'

    def total_rooms(self, obj):
        count = obj.rooms.count()
        if count > 0:
            url = reverse('admin:hotels_hotelroom_changelist') + f'?hotel__id__exact={obj.id}'
            return format_html('<a href="{}">{} rooms</a>', url, count)
        return '0 rooms'
    total_rooms.short_description = 'Total Rooms'

    def mark_active(self, request, queryset):
        updated = queryset.update(is_active=True)
        self.message_user(request, f'{updated} hotels marked as active.')
    mark_active.short_description = "Mark selected hotels as active"

    def mark_inactive(self, request, queryset):
        updated = queryset.update(is_active=False)
        self.message_user(request, f'{updated} hotels marked as inactive.')
    mark_inactive.short_description = "Mark selected hotels as inactive"

    def export_to_csv(self, request, queryset):
        response = HttpResponse(content_type='text/csv')
        response['Content-Disposition'] = 'attachment; filename="hotels.csv"'

        writer = csv.writer(response)
        writer.writerow([
            'Name', 'Address', 'Phone', 'Email', 'Contact Person', 'Star Rating',
            'Website', 'Event', 'Active', 'Description', 'Latitude', 'Longitude'
        ])

        for hotel in queryset:
            writer.writerow([
                hotel.name,
                hotel.address,
                hotel.phone,
                hotel.email,
                hotel.contact_person,
                hotel.star_rating or '',
                hotel.website,
                hotel.event.name,
                'Yes' if hotel.is_active else 'No',
                hotel.description,
                hotel.latitude or '',
                hotel.longitude or ''
            ])

        return response
    export_to_csv.short_description = "Export selected hotels to CSV"

    def get_urls(self):
        urls = super().get_urls()
        custom_urls = [
            path('import-csv/', self.admin_site.admin_view(self.import_csv), name='hotels_hotel_import_csv'),
        ]
        return custom_urls + urls

    def import_csv(self, request):
        if request.method == 'POST':
            csv_file = request.FILES.get('csv_file')
            if not csv_file:
                messages.error(request, 'Please select a CSV file.')
                return redirect('admin:hotels_hotel_import_csv')

            try:
                # Read and process CSV
                csv_content = csv_file.read().decode('utf-8')
                csv_reader = csv.DictReader(io.StringIO(csv_content))

                created_count = 0
                errors = []

                for row_num, row in enumerate(csv_reader, start=2):
                    try:
                        # Get event
                        from events.models import Event
                        event_name = row.get('event', '').strip()
                        if event_name:
                            event = Event.objects.filter(name__icontains=event_name).first()
                            if not event:
                                errors.append(f"Row {row_num}: Event '{event_name}' not found")
                                continue
                        else:
                            errors.append(f"Row {row_num}: Event name is required")
                            continue

                        # Parse star rating
                        star_rating = None
                        if row.get('star_rating', '').strip():
                            try:
                                star_rating = int(row['star_rating'])
                                if star_rating < 1 or star_rating > 5:
                                    star_rating = None
                            except ValueError:
                                pass

                        # Parse coordinates
                        latitude = None
                        longitude = None
                        if row.get('latitude', '').strip():
                            try:
                                latitude = float(row['latitude'])
                            except ValueError:
                                pass
                        if row.get('longitude', '').strip():
                            try:
                                longitude = float(row['longitude'])
                            except ValueError:
                                pass

                        # Create hotel
                        hotel = Hotel.objects.create(
                            name=row['name'].strip(),
                            address=row['address'].strip(),
                            phone=row['phone'].strip(),
                            email=row['email'].strip(),
                            contact_person=row['contact_person'].strip(),
                            star_rating=star_rating,
                            website=row.get('website', '').strip(),
                            description=row.get('description', '').strip(),
                            latitude=latitude,
                            longitude=longitude,
                            event=event,
                            is_active=row.get('active', 'yes').lower() in ['yes', 'true', '1']
                        )
                        created_count += 1

                    except Exception as e:
                        errors.append(f"Row {row_num}: {str(e)}")

                if created_count > 0:
                    messages.success(request, f'Successfully imported {created_count} hotels.')

                if errors:
                    for error in errors[:10]:  # Show first 10 errors
                        messages.error(request, error)
                    if len(errors) > 10:
                        messages.error(request, f'... and {len(errors) - 10} more errors.')

            except Exception as e:
                messages.error(request, f'Error processing CSV file: {str(e)}')

            return redirect('admin:hotels_hotel_changelist')

        return render(request, 'admin/hotels/import_csv.html')


@admin.register(HotelRoom)
class HotelRoomAdmin(admin.ModelAdmin):
    list_display = ['hotel', 'room_number', 'room_type', 'capacity', 'price_per_night', 'is_available', 'assigned_participant']
    list_filter = ['hotel', 'room_type', 'is_available', 'created_at']
    search_fields = ['hotel__name', 'room_number', 'room_type']
    readonly_fields = ['created_at', 'updated_at']

    def assigned_participant(self, obj):
        participant = obj.assigned_participants.first()
        if participant:
            url = reverse('admin:participants_participant_change', args=[participant.id])
            return format_html('<a href="{}">{}</a>', url, participant.full_name)
        return 'Not assigned'
    assigned_participant.short_description = 'Assigned Participant'


@admin.register(HotelReservation)
class HotelReservationAdmin(admin.ModelAdmin):
    list_display = ['participant', 'hotel', 'room', 'check_in_date', 'check_out_date', 'status']
    list_filter = ['hotel', 'status', 'check_in_date', 'created_at']
    search_fields = ['participant__first_name', 'participant__last_name', 'hotel__name']
    readonly_fields = ['created_at', 'updated_at']
    date_hierarchy = 'check_in_date'
