# Internal DNS Setup Guide for Windows Server
## University of Gondar Event Management System

## 🌐 Overview

This guide explains how to set up DNS records on your Windows Active Directory server for the UoG Event Management System to work properly in your internal network while still allowing Let's Encrypt SSL certificates to be issued.

## 📋 Current Network Configuration

- **Internal Server IP**: `************`
- **Domain**: `event.uog.edu.et`
- **Subdomain**: `www.event.uog.edu.et`
- **Environment**: Windows Server with Active Directory DNS

## 🔧 DNS Configuration Steps

### Step 1: Access DNS Manager on Windows Server

1. **Open DNS Manager**:
   ```
   Start Menu → Administrative Tools → DNS
   ```
   Or run: `dnsmgmt.msc`

2. **Connect to your DNS Server** (if not already connected)

### Step 2: Create Forward Lookup Zone (if needed)

If `uog.edu.et` zone doesn't exist:

1. Right-click **Forward Lookup Zones**
2. Select **New Zone...**
3. Choose **Primary zone**
4. Enter zone name: `uog.edu.et`
5. Accept default settings and finish

### Step 3: Add DNS Records

#### Option A: Create A Records (Recommended)

1. **Navigate to the zone**:
   - Expand **Forward Lookup Zones**
   - Click on `uog.edu.et`

2. **Add A Record for event.uog.edu.et**:
   - Right-click in the zone → **New Host (A or AAAA)...**
   - Name: `event`
   - IP Address: `************`
   - ✅ Check "Create associated pointer (PTR) record"
   - Click **Add Host**

3. **Add A Record for www.event.uog.edu.et**:
   - Right-click in the zone → **New Host (A or AAAA)...**
   - Name: `www.event`
   - IP Address: `************`
   - ✅ Check "Create associated pointer (PTR) record"
   - Click **Add Host**

#### Option B: Create CNAME Records (Alternative)

If you prefer CNAME records:

1. **Add A Record for event.uog.edu.et** (as above)
2. **Add CNAME for www**:
   - Right-click in zone → **New Alias (CNAME)...**
   - Alias name: `www.event`
   - Target host: `event.uog.edu.et`
   - Click **OK**

### Step 4: Verify DNS Resolution

1. **Open Command Prompt** on the server:
   ```cmd
   nslookup event.uog.edu.et
   nslookup www.event.uog.edu.et
   ```

2. **Expected Output**:
   ```
   Server:  [Your DNS Server]
   Address: [DNS Server IP]

   Name:    event.uog.edu.et
   Address: ************
   ```

## 🔒 Let's Encrypt Considerations

### Internal vs External DNS

For Let's Encrypt to work properly, you need:

1. **Internal DNS** (what you're setting up):
   - Points `event.uog.edu.et` to `************`
   - Used by internal clients and the server itself

2. **External DNS** (public DNS):
   - Points `event.uog.edu.et` to your **public IP address**
   - Used by Let's Encrypt for domain validation
   - Must be configured with your domain registrar or DNS provider

### Firewall Configuration

Ensure your firewall allows:

1. **Inbound Rules**:
   - Port 80 (HTTP) from internet → server
   - Port 443 (HTTPS) from internet → server
   - Port 80/443 from internal network → server

2. **Port Forwarding** (if behind NAT):
   - External Port 80 → Internal ************:80
   - External Port 443 → Internal ************:443

## 🧪 Testing Internal DNS

### Test from Server

```cmd
# Test DNS resolution
nslookup event.uog.edu.et
ping event.uog.edu.et

# Test HTTP access (after starting services)
curl http://localhost
curl http://event.uog.edu.et
```

### Test from Client Machines

```cmd
# From domain-joined computers
nslookup event.uog.edu.et
ping event.uog.edu.et

# Test web access
# Open browser: http://event.uog.edu.et
```

## 🚀 Deploy Let's Encrypt After DNS Setup

Once DNS is configured:

1. **Verify DNS Resolution**:
   ```bash
   nslookup event.uog.edu.et
   ```

2. **Run Let's Encrypt Setup**:
   ```bash
   ./init-letsencrypt.sh
   ```

3. **The script will now**:
   - Skip the ping check (normal for internal networks)
   - Use external DNS for Let's Encrypt validation
   - Configure SSL certificates properly

## 🔧 Troubleshooting

### Common Issues

1. **DNS Not Resolving Internally**:
   ```cmd
   # Check DNS server configuration
   ipconfig /all
   
   # Flush DNS cache
   ipconfig /flushdns
   
   # Test specific DNS server
   nslookup event.uog.edu.et [DNS-Server-IP]
   ```

2. **Let's Encrypt Validation Fails**:
   - Verify external DNS points to public IP
   - Check firewall allows port 80 from internet
   - Ensure port forwarding is configured

3. **Internal Access Issues**:
   - Verify A records are correct
   - Check Windows Firewall on server
   - Test with `telnet event.uog.edu.et 80`

### DNS Cache Issues

If changes don't take effect immediately:

```cmd
# On DNS Server
dnscmd /clearcache

# On Client Machines
ipconfig /flushdns
```

## 📝 DNS Record Summary

After setup, you should have:

```
Zone: uog.edu.et
├── event          A    ************
└── www.event      A    ************
```

Or with CNAME:
```
Zone: uog.edu.et
├── event          A       ************
└── www.event      CNAME   event.uog.edu.et
```

## 🌍 External DNS Requirements

**Important**: You also need external DNS records with your domain registrar:

```
Type    Name           Value
A       event          [Your Public IP]
A       www.event      [Your Public IP]
```

## ✅ Verification Checklist

- [ ] Internal DNS A record for `event.uog.edu.et` → `************`
- [ ] Internal DNS A record for `www.event.uog.edu.et` → `************`
- [ ] External DNS A record for `event.uog.edu.et` → `[Public IP]`
- [ ] External DNS A record for `www.event.uog.edu.et` → `[Public IP]`
- [ ] Firewall allows ports 80/443 from internet
- [ ] Port forwarding configured (if behind NAT)
- [ ] DNS resolution works from server: `nslookup event.uog.edu.et`
- [ ] DNS resolution works from clients
- [ ] Let's Encrypt initialization completes successfully

## 🔄 Next Steps

1. **Set up internal DNS records** (this guide)
2. **Verify external DNS** with your domain registrar
3. **Configure firewall** and port forwarding
4. **Run Let's Encrypt setup**: `./init-letsencrypt.sh`
5. **Test HTTPS access**: `https://event.uog.edu.et`

---

**Note**: The modified `init-letsencrypt.sh` script now handles internal network environments properly and will continue even if the domain isn't accessible from the server itself.
