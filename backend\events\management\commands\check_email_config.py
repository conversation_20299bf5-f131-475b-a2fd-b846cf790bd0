from django.core.management.base import BaseCommand
from events.models import EmailConfiguration, EmailTemplate, EmailLog
from django.utils import timezone
from datetime import timedelta


class Command(BaseCommand):
    help = 'Check email configuration status'
    
    def handle(self, *args, **options):
        self.stdout.write(
            self.style.SUCCESS('📧 Email System Status Check')
        )
        self.stdout.write('=' * 50)
        
        # Check active configuration
        try:
            active_config = EmailConfiguration.objects.get(is_active=True)
            self.stdout.write(
                self.style.SUCCESS('✓ Active SMTP Configuration Found')
            )
            self.stdout.write(f'  Name: {active_config.name}')
            self.stdout.write(f'  Host: {active_config.email_host}:{active_config.email_port}')
            self.stdout.write(f'  User: {active_config.email_host_user}')
            self.stdout.write(f'  TLS: {active_config.email_use_tls}')
            self.stdout.write(f'  From: {active_config.default_from_email}')
            
        except EmailConfiguration.DoesNotExist:
            self.stdout.write(
                self.style.ERROR('✗ No active SMTP configuration found')
            )
            return
        
        # Check email templates
        templates = EmailTemplate.objects.filter(is_active=True)
        self.stdout.write(f'\n✓ Email Templates: {templates.count()} active')
        for template in templates:
            self.stdout.write(f'  - {template.get_template_type_display()}')
        
        # Check recent email logs
        recent_logs = EmailLog.objects.filter(
            created_at__gte=timezone.now() - timedelta(hours=24)
        )
        
        self.stdout.write(f'\n📊 Email Statistics (Last 24 hours):')
        total = recent_logs.count()
        sent = recent_logs.filter(status='sent').count()
        failed = recent_logs.filter(status='failed').count()
        pending = recent_logs.filter(status='pending').count()
        
        self.stdout.write(f'  Total: {total}')
        self.stdout.write(f'  Sent: {sent}')
        self.stdout.write(f'  Failed: {failed}')
        self.stdout.write(f'  Pending: {pending}')
        
        if total > 0:
            success_rate = (sent / total) * 100
            self.stdout.write(f'  Success Rate: {success_rate:.1f}%')
        
        # Show recent email activity
        if recent_logs.exists():
            self.stdout.write(f'\n📋 Recent Email Activity:')
            for log in recent_logs.order_by('-created_at')[:5]:
                status_icon = '✓' if log.status == 'sent' else '✗' if log.status == 'failed' else '⏳'
                self.stdout.write(
                    f'  {status_icon} {log.recipient_email} - {log.template_type} ({log.created_at.strftime("%H:%M")})'
                )
        
        self.stdout.write('\n' + '=' * 50)
        self.stdout.write(
            self.style.SUCCESS('✓ Email system is configured and ready!')
        )
        
        self.stdout.write('\n🔗 Access Points:')
        self.stdout.write('  - Django Admin: /admin/events/emailconfiguration/')
        self.stdout.write('  - Email Management: /email-management (frontend)')
        self.stdout.write('  - Email Logs: /admin/events/emaillog/')
        
        self.stdout.write('\n🧪 Test Commands:')
        self.stdout.write('  - Basic test: python manage.py test_email_system --to <EMAIL>')
        self.stdout.write('  - Template test: python manage.py test_email_system --to <EMAIL> --template-test')
