#!/usr/bin/env python3
"""
Test script to verify that the system is accessible from IP ************
"""

import requests
import json
import sys
from datetime import datetime

def test_backend_access(base_url):
    """Test backend API access"""
    print(f"🔗 Testing Backend API access at {base_url}")
    
    try:
        # Test health check endpoint
        response = requests.get(f"{base_url}/health/", timeout=10)
        if response.status_code == 200:
            print(f"   ✅ Health check: {response.status_code}")
        else:
            print(f"   ❌ Health check failed: {response.status_code}")
            return False
    except requests.exceptions.RequestException as e:
        print(f"   ❌ Health check error: {e}")
        return False
    
    try:
        # Test API root endpoint
        response = requests.get(f"{base_url}/api/", timeout=10)
        if response.status_code in [200, 404]:  # 404 is ok if no root API view
            print(f"   ✅ API root: {response.status_code}")
        else:
            print(f"   ❌ API root failed: {response.status_code}")
    except requests.exceptions.RequestException as e:
        print(f"   ⚠️ API root error: {e}")
    
    try:
        # Test events endpoint (should require auth but should respond)
        response = requests.get(f"{base_url}/api/events/", timeout=10)
        if response.status_code in [200, 401, 403]:  # Auth required is ok
            print(f"   ✅ Events API: {response.status_code}")
        else:
            print(f"   ❌ Events API failed: {response.status_code}")
    except requests.exceptions.RequestException as e:
        print(f"   ⚠️ Events API error: {e}")
    
    return True

def test_frontend_access(base_url):
    """Test frontend access"""
    print(f"🌐 Testing Frontend access at {base_url}")
    
    try:
        response = requests.get(base_url, timeout=10)
        if response.status_code == 200:
            print(f"   ✅ Frontend: {response.status_code}")
            # Check if it's actually React content
            if 'react' in response.text.lower() or 'root' in response.text:
                print(f"   ✅ React app detected")
            else:
                print(f"   ⚠️ Response doesn't look like React app")
            return True
        else:
            print(f"   ❌ Frontend failed: {response.status_code}")
            return False
    except requests.exceptions.RequestException as e:
        print(f"   ❌ Frontend error: {e}")
        return False

def test_cors_headers(base_url, origin):
    """Test CORS headers"""
    print(f"🔒 Testing CORS headers from origin {origin}")
    
    try:
        headers = {
            'Origin': origin,
            'Access-Control-Request-Method': 'GET',
            'Access-Control-Request-Headers': 'Content-Type'
        }
        
        response = requests.options(f"{base_url}/api/events/", headers=headers, timeout=10)
        
        cors_headers = {
            'Access-Control-Allow-Origin': response.headers.get('Access-Control-Allow-Origin'),
            'Access-Control-Allow-Methods': response.headers.get('Access-Control-Allow-Methods'),
            'Access-Control-Allow-Headers': response.headers.get('Access-Control-Allow-Headers'),
        }
        
        print(f"   Status: {response.status_code}")
        for header, value in cors_headers.items():
            if value:
                print(f"   ✅ {header}: {value}")
            else:
                print(f"   ❌ {header}: Not set")
        
        return response.status_code in [200, 204]
        
    except requests.exceptions.RequestException as e:
        print(f"   ❌ CORS test error: {e}")
        return False

def main():
    print("🧪 Testing IP Access Configuration")
    print("=" * 50)
    print(f"📅 Test time: {datetime.now()}")
    print(f"🎯 Target IP: ************")
    print()
    
    # Test different possible configurations
    test_configs = [
        {
            'name': 'Local Development (localhost)',
            'backend': 'http://localhost:8000',
            'frontend': 'http://localhost:3000',
            'origin': 'http://localhost:3000'
        },
        {
            'name': 'Target IP Configuration',
            'backend': 'http://************:8000',
            'frontend': 'http://************:3000',
            'origin': 'http://************:3000'
        },
        {
            'name': 'Alternative Port Configuration',
            'backend': 'http://************:8000',
            'frontend': 'http://************:8080',
            'origin': 'http://************:8080'
        }
    ]
    
    results = []
    
    for config in test_configs:
        print(f"🔍 Testing: {config['name']}")
        print("-" * 30)
        
        backend_ok = test_backend_access(config['backend'])
        frontend_ok = test_frontend_access(config['frontend'])
        cors_ok = test_cors_headers(config['backend'], config['origin'])
        
        results.append({
            'name': config['name'],
            'backend': backend_ok,
            'frontend': frontend_ok,
            'cors': cors_ok,
            'overall': backend_ok and frontend_ok and cors_ok
        })
        
        print()
    
    # Summary
    print("📊 TEST SUMMARY")
    print("=" * 50)
    
    for result in results:
        status = "✅ PASS" if result['overall'] else "❌ FAIL"
        print(f"{result['name']}: {status}")
        print(f"   Backend: {'✅' if result['backend'] else '❌'}")
        print(f"   Frontend: {'✅' if result['frontend'] else '❌'}")
        print(f"   CORS: {'✅' if result['cors'] else '❌'}")
        print()
    
    # Recommendations
    print("💡 RECOMMENDATIONS")
    print("=" * 50)
    
    working_configs = [r for r in results if r['overall']]
    if working_configs:
        print("✅ Working configurations found:")
        for config in working_configs:
            print(f"   • {config['name']}")
    else:
        print("❌ No fully working configurations found.")
        print("\n🔧 Troubleshooting steps:")
        print("   1. Ensure Docker containers are running")
        print("   2. Check firewall settings on the host")
        print("   3. Verify network connectivity to ************")
        print("   4. Restart Docker services after configuration changes")
        print("   5. Check Docker logs for any errors")
    
    print("\n🚀 Next steps:")
    print("   1. Run: docker-compose -f docker-compose.local.yml down")
    print("   2. Run: docker-compose -f docker-compose.local.yml up -d")
    print("   3. Wait for services to start (2-3 minutes)")
    print("   4. Test access from ************")

if __name__ == "__main__":
    main()
