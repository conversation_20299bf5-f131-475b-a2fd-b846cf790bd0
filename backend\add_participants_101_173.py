#!/usr/bin/env python3

import os
import sys
import django
from datetime import datetime

# Add the project root to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'event_management.settings')
django.setup()

from participants.models import Participant, ParticipantType
from events.models import Event
from django.db import connection

def add_participants_101_173():
    """Add participants with IDs 101-173"""
    
    print("=== ADDING PARTICIPANTS 101-173 ===")
    
    # Get Event ID 1
    try:
        event = Event.objects.get(id=1)
    except Event.DoesNotExist:
        print("Error: Event with ID 1 not found")
        return
    
    # Get participant types
    participant_types = {}
    for pt in ParticipantType.objects.all():
        participant_types[pt.id] = pt
    
    # Participants data (IDs 101-173)
    participants_data = [
        # (id, first_name, last_name, middle_name, email, phone, institution_name, participant_type_id, arrival_date, departure_date, profile_photo, remarks, status)
        (101, 'YOHANNES', 'AEMRO', 'GEBRU', '<EMAIL>', '091 1790592', 'Wolkite University', 23, '2025-08-03 10:17:00', '2025-08-07 10:17:00', None, '', 'active'),
        (102, 'Nigussu', 'Kassie', 'Bitew', '<EMAIL>', '091 9470087', 'BHU', 4, '2025-08-05 17:28:00', '2025-08-07 10:28:00', None, '', 'active'),
        (103, 'Dr Sewagegne', 'Admassu', 'Asrat', '<EMAIL>', '093 0355380', 'Mekdela Amba university', 23, '2025-08-03 15:55:00', '2025-08-07 02:30:00', 'event-registration/profile_68887b9bd388e.jpg', '', 'active'),
        (104, 'Tigabu', 'Bekele', 'Mekonnen', '<EMAIL>', '091 5716392', 'Mekdela Amba University', 8, '2025-07-29 11:27:00', '2025-08-07 12:26:00', None, '', 'active'),
        (105, 'Kifle', 'Hajito', 'Woldemichael', '<EMAIL>', '935123168', 'Bonga University', 29, '2025-08-03 11:25:00', '2025-08-06 11:26:00', None, '', 'active'),
        (106, 'Dereje', 'Woldemichael', 'Engida', '<EMAIL>', '+251 912502995', 'AASTU', 2, '2025-08-03 13:35:00', '2025-08-07 08:30:00', None, '', 'active'),
        (107, 'Elyas', 'Mohamued', 'Abdulahi', '<EMAIL>', '+251 941070300', 'Jigjiga University', 23, '2025-08-03 11:30:00', '2025-08-06 13:30:00', None, '', 'active'),
        (108, 'Tesfaye', 'Tefera', 'Lemma', '<EMAIL>', '+251 932178954', 'Wollega University', 2, '2025-08-03 12:15:00', '2025-08-07 10:20:00', 'event-registration/profile_6888946f616a8.jpeg', '', 'active'),
        (109, 'Solomon', 'Werta', 'Zerihun', '<EMAIL>', '+251 939290200', 'Dire Dawa University', 23, '2025-08-03 13:00:00', '2025-08-06 16:00:00', 'event-registration/profile_6888963617371.jpg', 'The time set above are boarding time, not check in', 'active'),
        (110, 'Bayissa', 'Danno', 'Leta', '<EMAIL>', '+251 961132431', 'Ambo University', 2, '2025-08-03 15:55:00', '2025-08-07 14:05:00', 'event-registration/profile_6888963e4c117.jpg', '', 'active'),
        (111, 'Eyob', 'Goa', 'Asrat', '<EMAIL>', '091 3936435', 'Samara University', 9, '2025-08-02 14:26:00', '2025-08-07 12:55:00', 'event-registration/profile_68889bf706f15.jpg', '', 'active'),
        (112, 'Tarekegn', 'Gizaw', 'Tintagu', '<EMAIL>', '097 5022257', 'Wollo University', 23, '2025-08-03 13:01:00', '2025-08-07 13:02:00', None, '', 'active'),
        (113, 'Sayeh', 'Agegnehu', 'Kassaw', '<EMAIL>', '094 5352020', 'Debre Markos University', 2, '2025-08-03 12:15:00', '2025-08-06 16:25:00', None, '', 'active'),
        (114, 'Abdurahman', 'Ali', 'Kedir', '<EMAIL>', '092 8956909', 'Samara University', 23, '2025-08-03 13:41:00', '2025-08-07 13:41:00', 'event-registration/profile_6888a5d153f96.jpeg', '', 'active'),
        (115, 'Tekeste', 'Lakew', 'Berhanu', '<EMAIL>', '+251 964968316', 'Mekelle University', 23, '2025-08-03 13:39:00', '2025-08-07 13:43:00', 'event-registration/profile_6888a676b16bf.jpeg', '', 'active'),
        (116, 'nigusie', 'lamessa', 'tolesa', '<EMAIL>', '096 6064400', 'ambo university', 25, '2025-08-07 20:07:00', '2025-08-10 19:10:00', None, 'From Ambo University E-learning Dir', 'active'),
        (117, 'Mekonnen', 'Anshebo', 'Abebe', '<EMAIL>', '+251 911927991', 'Addis Ababa Science and Technology University', 4, '2025-08-05 13:50:00', '2025-08-07 13:51:00', None, '', 'active'),
        (118, 'Dr. Bereket', 'Zewude', 'Tessema', '<EMAIL>', '+251 912244726', 'Wolaita Sodo University', 28, '2025-08-05 07:51:00', '2025-08-07 07:55:00', 'event-registration/profile_6888a9e8c2805.jpg', '', 'active'),
        (120, 'TAKELE', 'ATALAY', 'MEKONNEN', '<EMAIL>', '091 1563666', 'ASSOA UNIVERSITY', 27, '2025-08-03 15:55:00', '2025-08-07 10:20:00', 'event-registration/profile_6888adc03572b.jpg', 'የበረራ መድረሻ ሐምሌ 27/2017ዓ.ም ከሠዓት 9:55 ላይ\r\nየመመለሻ በረራ ነሐሴ 1/2017ዓ.ም ከጧቱ 4:20 እንደ ኢትዮጵያ ቀንና ሠዓት አቆጣጠር', 'active'),
        (121, 'Dr.Melkamu', 'Amentie', 'Deressa', '<EMAIL>', '093 0309320', 'Assosa University', 23, '2025-08-03 12:15:00', '2025-08-07 22:20:00', 'event-registration/profile_6888ae88e82cb.jpeg', '', 'active'),
        (122, 'Abebe', 'Demissie', 'Girma', '<EMAIL>', '090 1480119', 'Woldia University', 2, '2025-08-03 14:17:00', '2025-08-06 14:18:00', 'event-registration/profile_6888b18b35eb2.jpg', '', 'active'),
        (123, 'Tsigab', 'Tesfay', 'Aregawi', '<EMAIL>', '091 2692077', 'Adigrat University', 23, '2025-08-03 06:18:00', '2025-08-06 08:33:00', None, '', 'active'),
        (124, 'Dr. Marye', 'Alemayehu', 'Belete', '<EMAIL>', '091 2985785', 'Woldia University', 23, '2025-09-03 08:40:00', '2025-09-07 10:20:00', 'event-registration/profile_6888b81f2c36d.jpg', '', 'active'),
        (125, 'Mitiku', 'Haile', '', '<EMAIL>', '+251 914720339', 'Mekelle University, Ethiopia', 29, '2025-08-03 11:58:00', '2025-08-07 11:02:00', None, '', 'active'),
        (126, 'Dejene', 'Tessema', 'Ayele', '<EMAIL>', '091 4315520', 'Addis Ababa Science and Technology University', 1, '2025-08-03 02:30:00', '2025-08-06 08:30:00', 'event-registration/profile_6888bec385a8c.jpg', 'No comment', 'active'),
        (127, 'ABRAHAM', 'Woldeyohannes', 'Debebe', '<EMAIL>', '093 0097652', 'Addis Ababa Science and Technology University', 23, '2025-08-03 13:35:00', '2025-08-07 08:30:00', None, '', 'active'),
        (128, 'Wosenu', 'Mulatu', 'Bekele', '<EMAIL>', '091 3642218', 'Madda Walabu University', 4, '2025-08-05 15:41:00', '2025-08-07 15:42:00', 'event-registration/profile_6888c404910d4.JPG', '', 'active'),
        (129, 'Mohammed', 'Hajiahmed', 'Abdurehman', '<EMAIL>', '093 5274222', 'Haramaya University', 8, '2025-08-03 16:05:00', '2025-08-06 16:09:00', None, '', 'active'),
        (130, 'Gardachew', 'Fekadu', 'Worku', '<EMAIL>', '093 0352884', 'Injibara University', 2, '2025-08-02 09:00:00', '2025-08-06 16:30:00', 'event-registration/profile_6888c926658b8.jpg', '', 'active'),
        (131, 'Andualem', 'Gete', 'Zerefaw', '<EMAIL>', '091 8714838', 'DTU', 27, '2025-08-03 10:50:00', '2025-08-03 12:15:00', None, '', 'active'),
        (132, 'Tefera', 'Kabtihyimer', 'Tesfaye', '<EMAIL>', '091 1092041', 'Woldia University', 27, '2025-08-03 12:15:00', '2025-08-06 10:05:00', 'event-registration/profile_6888cc8010f4b.jpg', '', 'active'),
        (133, 'Dr. Girma', 'Getnet', 'Tilahun', '<EMAIL>', '091 3891989', 'Mizan Tepi University', 23, '2025-08-03 16:11:00', '2025-08-05 21:12:00', 'event-registration/profile_6888d75c6d59b.png', '', 'active'),
        (134, 'Dr. Wako', 'Obse', 'Geda', '<EMAIL>', '091 1853810', 'Mizan-Tepi University', 2, '2025-08-03 16:19:00', '2025-08-06 09:20:00', 'event-registration/profile_6888d94172caf.jpg', '', 'active'),
        (136, 'Menberu', 'Zeleke', 'Teshome', '<EMAIL>', '093 0295752', 'DTU', 23, '2025-08-03 10:50:00', '2025-08-03 12:15:00', None, '', 'active'),
        (137, 'Terefe', 'Beyene', 'Getachew', '<EMAIL>', '098 3915354', 'Mizan Tepi University', 23, '2025-08-03 13:22:00', '2025-08-07 05:00:00', 'event-registration/profile_6888de5962782.jpg', '', 'active'),
        (138, 'Girma Wossenie Dr.', 'Dagnie', 'Wossenie', '<EMAIL>', '091 1396708', 'CEO, Consortium of Ethiopian Public Universities', 28, '2025-08-03 17:48:00', '2025-08-07 17:49:00', 'event-registration/profile_6888e05ebb450.jpg', '', 'active'),
        (141, 'Yideg', 'Haile', 'Mamo', '<EMAIL>', '+251 963638218', 'Mizan-Tepi University', 23, '2025-08-03 14:00:00', '2025-08-07 14:00:00', None, '', 'active'),
        (142, 'Essayas', 'Lemma', 'Taye', '<EMAIL>', '091 2178590', 'AASTU', 27, '2025-08-03 13:31:00', '2025-08-07 08:23:00', None, '', 'active'),
        (143, 'Solomon', 'Guangul', 'Abegaz', '<EMAIL>', '093 6584410', 'Debre Tabor University', 2, '2025-08-02 12:50:00', '2025-08-06 18:30:00', None, '', 'active'),
        (144, 'Liboro', 'Molloro', 'Hundito', '<EMAIL>', '097 3806130', 'Debre Berhan University', 27, '2025-08-03 16:30:00', '2025-08-07 07:30:00', None, '', 'active'),
        (145, 'Mesele', 'Bogale', 'Alemayehu', '<EMAIL>', '091 3515058', 'Bule hora University', 27, '2025-08-03 02:30:00', '2025-09-03 03:55:00', 'event-registration/profile_6888ebc891cdd.jpg', '', 'active'),
        (146, 'Biruk', 'Kidanemariam', 'Geberetsadik', '<EMAIL>', '091 7827197', 'Mizan tepi Universty', 27, '2025-08-03 15:00:00', '2025-08-06 09:00:00', None, '', 'active'),
        (147, 'Sisay', 'Guangul', 'Mulate', '<EMAIL>', '091 2904860', 'Debre berhan university', 23, '2025-08-10 14:30:00', '2025-08-14 10:14:00', None, '', 'active'),
        (148, 'Enyew', 'Tsegaye', 'Adgo', '<EMAIL>', '091 8765621', 'Bahir Dar University', 23, '2025-08-03 12:15:00', '2025-09-06 09:15:00', None, '', 'active'),
        (149, 'Dr. Melka', 'Degu', 'Hika', '<EMAIL>', '+251 989098650', 'Wollega University', 23, '2025-08-03 12:15:00', '2025-08-07 10:20:00', None, '', 'active'),
        (150, 'Dr.Bezabih', 'Kibret', 'Wondimu', '<EMAIL>', '091 1466319', 'Madda Walabu University', 2, '2025-08-02 21:06:00', '2025-08-05 18:05:00', None, '', 'active'),
        (151, 'belay', 'addisu', 'mengstie', '<EMAIL>', '091 3355341', 'woldia universty', 23, '2025-08-03 22:00:00', '2025-08-07 22:01:00', None, '', 'active'),
        (152, 'Ephrem', 'Yacob', 'Tekle', '<EMAIL>', '091 1436251', 'Kotebe University of Education', 23, '2025-08-03 11:20:00', '2025-08-06 22:21:00', 'event-registration/profile_68892116919a4.JPG', '', 'active'),
        (153, 'Tekalign', 'Tafa', 'Diyana', '<EMAIL>', '092 0453500', 'Oda Bultum University', 27, '2025-08-03 15:25:00', '2025-08-06 13:00:00', None, '', 'active'),
        (154, 'Paulos', 'Shibeshi', 'Taddesse', '<EMAIL>', '093 7458433', 'Arba Minch University', 23, '2025-08-03 15:30:00', '2025-08-07 08:30:00', None, '', 'active'),
        (155, 'Dr Tolossa', 'Wedajo', 'Dadi', '<EMAIL>', '093 0364635', 'Salale University', 23, '2025-08-03 01:35:00', '2025-08-07 10:20:00', None, '', 'active'),
        (156, 'Teferi', 'Jibicho', 'Gishe', '<EMAIL>', '091 6358173', 'Ethiopian civil service university', 27, '2025-08-03 10:55:00', '2025-08-07 10:58:00', None, '', 'active'),
        (157, 'Tadewos', 'Wogasso', 'Mentta', '<EMAIL>', '093 7809795', 'Ethiopian Civil Service University', 23, '2025-08-03 15:00:00', '2025-08-07 14:00:00', None, '', 'active'),
        (158, 'engidaw', 'awoke', '', '<EMAIL>', '091 8030593', 'UoG', 4, '2025-07-31 03:48:00', '2025-08-01 03:48:00', None, '', 'deleted'),
        (159, 'Dr. Abdi', 'Hasan', 'Ahmed', '<EMAIL>', '091 1042563', 'Jigjiga University', 23, '2025-08-03 06:43:00', '2025-08-06 10:00:00', 'event-registration/profile_688ae70354260.jpeg', '', 'active'),
        (160, 'Awoke', 'Melese', 'Azanaw', '<EMAIL>', '091 8305854', 'Debark University', 27, '2025-08-03 08:31:00', '2025-08-03 08:36:00', 'event-registration/profile_688b014d0746c.jpg', '', 'active'),
        (161, 'Eba', 'Negero', 'Mijena', '<EMAIL>', '091 1110148', 'MoE', 8, '2025-08-03 03:01:00', '2025-08-08 09:02:00', None, '', 'active'),
        (162, 'Dr Zakaria', 'Mohamed', 'Abdiwali', '<EMAIL>', '091 3551873', 'University of Kabridahar', 27, '2025-08-03 10:15:00', '2025-08-06 09:15:00', 'event-registration/profile_688b0a3346015.jpg', '', 'active'),
        (163, 'Abebaw', 'Mekonnen', 'Demssie', '<EMAIL>', '092 3656322', 'Wollo university', 27, '2025-08-03 22:30:00', '2025-08-06 07:30:00', None, '', 'active'),
        (164, 'Kelelew', 'Hailemichael', 'Addisu', '<EMAIL>', '093 0318063', 'Bonga University', 23, '2025-08-03 12:15:00', '2025-08-06 10:05:00', None, '', 'active'),
        (165, 'Prof. Bizunesh', 'Borena', 'Mideksa', '<EMAIL>', '+251 944741626', 'Ambo University', 23, '2025-08-03 08:40:00', '2025-08-07 08:30:00', 'event-registration/profile_688b1b86f0095.jpg', '', 'active'),
        (166, 'Temam', 'Bartuga', 'Argaw', '<EMAIL>', '091 5666492', 'Werabe university', 25, '2025-08-03 00:55:00', '2025-08-03 05:30:00', None, '', 'active'),
        (167, 'Yesihak', 'Mummed', 'Yusuf', '<EMAIL>', '093 0760076', 'Haramaya University', 23, '2025-08-03 14:30:00', '2025-08-06 08:00:00', None, '', 'active'),
        (168, 'Eng. Abdifetah', 'Rabi', 'Ahmed', '<EMAIL>', '091 1303733', 'University', 2, '2025-08-03 03:26:00', '2025-08-04 03:50:00', 'event-registration/profile_688b2acc8d99c.jpg', '', 'active'),
        (169, 'Teklu', 'Zara', 'Wegatehu', '<EMAIL>', '+251 911004186', 'Arba Minch University', 23, '2025-08-03 15:30:00', '2025-09-07 02:30:00', 'event-registration/profile_688b2af749fb6.jpg', '', 'active'),
        (170, 'Tamirat', 'Bekele', 'Mulugeta', '<EMAIL>', '092 1335855', 'Ethiopian Police University', 2, '2025-08-03 12:30:00', '2025-09-06 12:32:00', None, 'Acting President', 'active'),
        (171, 'Ahmed', 'Abdinasir', 'Muhumed', '<EMAIL>', '091 5751700', 'University of Kabridahar', 23, '2025-08-03 22:49:00', '2025-08-06 12:50:00', None, '', 'active'),
        (172, 'Bedilu', 'Geleto', 'Teka', '<EMAIL>', '093 7003501', 'Mattu University', 23, '2025-08-03 12:56:00', '2025-08-07 12:57:00', 'event-registration/profiles/profile_3e619b1d-7f31-440e-a56c-7d73936d860a.jpg', 'My arrival and return date is filled in GeG.C not in Ethiopia calendar.', 'active'),
        (173, 'Berhanu', 'Ali', 'Shibeshi', '<EMAIL>', '094 1741764', 'WACHEMO UNIVERSITY', 27, '2025-08-04 14:29:00', '2025-08-07 14:29:00', 'event-registration/profiles/profile_5868e969-a1d6-49e8-a2b5-485eb5ed6fe7.png', '', 'active'),
    ]
    
    added_count = 0
    
    with connection.cursor() as cursor:
        for p_data in participants_data:
            # Check if participant already exists
            if Participant.objects.filter(id=p_data[0]).exists():
                print(f"Participant with ID {p_data[0]} already exists, skipping...")
                continue
            
            # Get participant type
            participant_type = participant_types.get(p_data[7])
            if not participant_type:
                print(f"Warning: Participant type {p_data[7]} not found for {p_data[1]} {p_data[2]}")
                continue
            
            # Parse dates
            arrival_date = None
            departure_date = None
            
            if p_data[8]:  # arrival_date
                try:
                    arrival_date = datetime.strptime(p_data[8], "%Y-%m-%d %H:%M:%S")
                except:
                    arrival_date = None
            
            if p_data[9]:  # departure_date
                try:
                    departure_date = datetime.strptime(p_data[9], "%Y-%m-%d %H:%M:%S")
                except:
                    departure_date = None
            
            # Create participant
            try:
                participant = Participant(
                    first_name=p_data[1],
                    last_name=p_data[2],
                    middle_name=p_data[3] or "",
                    email=p_data[4],
                    phone=p_data[5],
                    institution_name=p_data[6],
                    position="Participant",
                    participant_type=participant_type,
                    arrival_date=arrival_date,
                    departure_date=departure_date,
                    profile_photo=p_data[10],
                    remarks=p_data[11] or "",
                    status=p_data[12],
                    event=event
                )
                participant.save()
                
                # Set the exact ID using raw SQL
                cursor.execute(
                    "UPDATE participants_participant SET id = %s WHERE id = %s",
                    [p_data[0], participant.id]
                )
                
                added_count += 1
                print(f"Added: {p_data[1]} {p_data[2]} with ID {p_data[0]}")
                
            except Exception as e:
                print(f"Error adding {p_data[1]} {p_data[2]}: {str(e)}")
    
    print(f"\nSummary:")
    print(f"Added {added_count} new participants")
    print(f"Total participants now: {Participant.objects.count()}")

if __name__ == "__main__":
    add_participants_101_173()
