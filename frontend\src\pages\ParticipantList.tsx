import React, { useState, useEffect } from 'react';
import { Container, Row, Col, Card, Table, Badge, Button, Form, Spinner, Alert } from 'react-bootstrap';
import { participantService, eventService, participantTypeService, Event, ParticipantType, Participant } from '../services/api';

const ParticipantList: React.FC = () => {
  const [participants, setParticipants] = useState<Participant[]>([]);
  const [events, setEvents] = useState<Event[]>([]);
  const [participantTypes, setParticipantTypes] = useState<ParticipantType[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');

  // Filters
  const [selectedEvent, setSelectedEvent] = useState<string>('');
  const [selectedType, setSelectedType] = useState<string>('');
  const [searchTerm, setSearchTerm] = useState('');

  useEffect(() => {
    fetchData();
  }, []);

  useEffect(() => {
    fetchParticipants();
  }, [selectedEvent, selectedType]);

  const fetchData = async () => {
    try {
      const [eventsResponse, typesResponse] = await Promise.all([
        eventService.getEvents(),
        participantTypeService.getParticipantTypes(),
      ]);

      // Ensure we have arrays
      const eventsData = Array.isArray(eventsResponse.data) ? eventsResponse.data :
                        ((eventsResponse.data as any)?.results ? (eventsResponse.data as any).results : []);
      const typesData = Array.isArray(typesResponse.data) ? typesResponse.data :
                       ((typesResponse.data as any)?.results ? (typesResponse.data as any).results : []);

      setEvents(eventsData);
      setParticipantTypes(typesData);
    } catch (error) {
      console.error('Error fetching data:', error);
      setError('Failed to load data');
    }
  };

  const fetchParticipants = async () => {
    setLoading(true);
    try {
      const eventId = selectedEvent ? parseInt(selectedEvent) : undefined;
      const response = await participantService.getAllParticipants(eventId); // Fetch all participants

      // Ensure we have an array
      let participantsData = Array.isArray(response.data) ? response.data :
                            ((response.data as any)?.results ? (response.data as any).results : []);

      // Filter by participant type
      if (selectedType) {
        participantsData = participantsData.filter(
          (p: any) => p.participant_type.toString() === selectedType
        );
      }

      setParticipants(participantsData);
    } catch (error) {
      console.error('Error fetching participants:', error);
      setError('Failed to load participants');
    } finally {
      setLoading(false);
    }
  };

  const handleConfirmParticipant = async (participantId: number) => {
    try {
      await participantService.confirmParticipant(participantId);
      // Refresh the list
      fetchParticipants();
    } catch (error) {
      console.error('Error confirming participant:', error);
    }
  };

  const handleRegenerateBadge = async (participantId: number) => {
    try {
      await participantService.regenerateBadge(participantId);
      alert('Badge regenerated successfully!');
    } catch (error) {
      console.error('Error regenerating badge:', error);
      alert('Failed to regenerate badge');
    }
  };

  // Filter participants by search term
  const filteredParticipants = Array.isArray(participants) ? participants.filter(participant =>
    participant.full_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    participant.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
    participant.institution_name.toLowerCase().includes(searchTerm.toLowerCase())
  ) : [];

  if (loading && participants.length === 0) {
    return (
      <Container className="text-center mt-5">
        <Spinner animation="border" role="status">
          <span className="visually-hidden">Loading...</span>
        </Spinner>
        <p className="mt-3">Loading participants...</p>
      </Container>
    );
  }

  return (
    <Container className="py-4">
      <Row className="mb-4">
        <Col>
          <h1 className="display-4 fw-bold text-center mb-2">Participants</h1>
          <p className="lead text-center text-muted">
            Manage event participants and their registrations
          </p>
        </Col>
      </Row>

      {error && (
        <Alert variant="danger" className="mb-4">
          <i className="fas fa-exclamation-triangle me-2"></i>
          {error}
        </Alert>
      )}

      {/* Filters */}
      <Card className="mb-4">
        <Card.Body>
          <Row>
            <Col md={3}>
              <Form.Group>
                <Form.Label>Filter by Event</Form.Label>
                <Form.Select
                  value={selectedEvent}
                  onChange={(e) => setSelectedEvent(e.target.value)}
                >
                  <option value="">All Events</option>
                  {Array.isArray(events) && events.map(event => (
                    <option key={event.id} value={event.id}>
                      {event.name}
                    </option>
                  ))}
                </Form.Select>
              </Form.Group>
            </Col>

            <Col md={3}>
              <Form.Group>
                <Form.Label>Filter by Type</Form.Label>
                <Form.Select
                  value={selectedType}
                  onChange={(e) => setSelectedType(e.target.value)}
                >
                  <option value="">All Types</option>
                  {participantTypes.map(type => (
                    <option key={type.id} value={type.id}>
                      {type.name}
                    </option>
                  ))}
                </Form.Select>
              </Form.Group>
            </Col>

            <Col md={6}>
              <Form.Group>
                <Form.Label>Search Participants</Form.Label>
                <Form.Control
                  type="text"
                  placeholder="Search by name, email, or institution..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                />
              </Form.Group>
            </Col>
          </Row>
        </Card.Body>
      </Card>

      {/* Statistics */}
      <Row className="mb-4">
        <Col md={3}>
          <Card className="text-center border-primary">
            <Card.Body>
              <h3 className="text-primary">{filteredParticipants.length}</h3>
              <p className="text-muted mb-0">Total Participants</p>
            </Card.Body>
          </Card>
        </Col>
        <Col md={3}>
          <Card className="text-center border-success">
            <Card.Body>
              <h3 className="text-success">
                {filteredParticipants.filter(p => p.is_confirmed).length}
              </h3>
              <p className="text-muted mb-0">Confirmed</p>
            </Card.Body>
          </Card>
        </Col>
        <Col md={3}>
          <Card className="text-center border-warning">
            <Card.Body>
              <h3 className="text-warning">
                {filteredParticipants.filter(p => !p.is_confirmed).length}
              </h3>
              <p className="text-muted mb-0">Pending</p>
            </Card.Body>
          </Card>
        </Col>
        <Col md={3}>
          <Card className="text-center border-info">
            <Card.Body>
              <h3 className="text-info">
                {filteredParticipants.filter(p => p.badge_generated).length}
              </h3>
              <p className="text-muted mb-0">Badges Generated</p>
            </Card.Body>
          </Card>
        </Col>
      </Row>

      {/* Participants Table */}
      <Card>
        <Card.Header>
          <h5 className="mb-0">
            <i className="fas fa-users me-2"></i>
            Participant List ({filteredParticipants.length})
          </h5>
        </Card.Header>
        <Card.Body className="p-0">
          {filteredParticipants.length === 0 ? (
            <div className="text-center py-5">
              <i className="fas fa-users text-muted" style={{ fontSize: '4rem' }}></i>
              <h4 className="mt-3 text-muted">No Participants Found</h4>
              <p className="text-muted">
                {searchTerm || selectedEvent || selectedType
                  ? 'Try adjusting your filters'
                  : 'No participants have registered yet'}
              </p>
            </div>
          ) : (
            <div className="table-responsive">
              <Table hover className="mb-0">
                <thead className="table-light">
                  <tr>
                    <th>Name</th>
                    <th>Email</th>
                    <th>Institution</th>
                    <th>Type</th>
                    <th>Event</th>
                    <th>Status</th>
                    <th>Badge</th>
                    <th>Actions</th>
                  </tr>
                </thead>
                <tbody>
                  {filteredParticipants.map((participant) => (
                    <tr key={participant.id}>
                      <td>
                        <div>
                          <strong>{participant.full_name}</strong>
                          <br />
                          <small className="text-muted">{participant.position}</small>
                        </div>
                      </td>
                      <td>
                        <a href={`mailto:${participant.email}`}>
                          {participant.email}
                        </a>
                        <br />
                        <small className="text-muted">{participant.phone}</small>
                      </td>
                      <td>{participant.institution_name}</td>
                      <td>
                        <Badge
                          style={{ backgroundColor: participant.participant_type_color }}
                        >
                          {participant.participant_type_name}
                        </Badge>
                      </td>
                      <td>
                        <small>{participant.event_name}</small>
                      </td>
                      <td>
                        <Badge bg={participant.is_confirmed ? 'success' : 'warning'}>
                          {participant.is_confirmed ? 'Confirmed' : 'Pending'}
                        </Badge>
                      </td>
                      <td>
                        <Badge bg={participant.badge_generated ? 'success' : 'secondary'}>
                          {participant.badge_generated ? 'Generated' : 'Not Generated'}
                        </Badge>
                      </td>
                      <td>
                        <div className="btn-group-vertical btn-group-sm">
                          {!participant.is_confirmed && (
                            <Button
                              variant="outline-success"
                              size="sm"
                              onClick={() => handleConfirmParticipant(participant.id)}
                              className="mb-1"
                            >
                              <i className="fas fa-check me-1"></i>
                              Confirm
                            </Button>
                          )}
                          <Button
                            variant="outline-primary"
                            size="sm"
                            onClick={() => handleRegenerateBadge(participant.id)}
                            className="mb-1"
                          >
                            <i className="fas fa-id-badge me-1"></i>
                            Badge
                          </Button>
                          {participant.badge_url && (
                            <Button
                              variant="outline-info"
                              size="sm"
                              href={participant.badge_url}
                              target="_blank"
                            >
                              <i className="fas fa-download me-1"></i>
                              Download
                            </Button>
                          )}
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </Table>
            </div>
          )}
        </Card.Body>
      </Card>
    </Container>
  );
};

export default ParticipantList;
