import React, { useState, useEffect } from 'react';
import { Con<PERSON><PERSON>, <PERSON>, Col, <PERSON>, Button, Alert, Form, Modal, Badge, Table } from 'react-bootstrap';
import { useAuth } from '../contexts/AuthContext';
import api from '../services/api';

interface EmailConfig {
  id: number;
  name: string;
  email_host: string;
  email_host_user: string;
  is_active: boolean;
}

interface EmailTemplate {
  id: number;
  name: string;
  template_type: string;
  subject: string;
  is_active: boolean;
}

interface EmailLog {
  id: number;
  recipient_email: string;
  subject: string;
  template_type: string;
  status: string;
  sent_at: string;
  created_at: string;
}

interface EmailStats {
  total: number;
  sent: number;
  failed: number;
  pending: number;
  recent_week: number;
  success_rate: number;
}

const EmailManagement: React.FC = () => {
  const { isAuthenticated } = useAuth();
  const [activeTab, setActiveTab] = useState('overview');
  const [emailConfigs, setEmailConfigs] = useState<EmailConfig[]>([]);
  const [emailTemplates, setEmailTemplates] = useState<EmailTemplate[]>([]);
  const [emailLogs, setEmailLogs] = useState<EmailLog[]>([]);
  const [emailStats, setEmailStats] = useState<EmailStats | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');

  // Notification modal state
  const [showNotificationModal, setShowNotificationModal] = useState(false);
  const [notificationType, setNotificationType] = useState('');
  const [selectedEvent, setSelectedEvent] = useState('');
  const [events, setEvents] = useState<any[]>([]);

  // SMTP Configuration state
  const [showConfigModal, setShowConfigModal] = useState(false);
  const [editingConfig, setEditingConfig] = useState<EmailConfig | null>(null);
  const [testingConfig, setTestingConfig] = useState<number | null>(null);
  const [testEmail, setTestEmail] = useState('');
  const [configFormData, setConfigFormData] = useState({
    name: '',
    email_host: '',
    email_port: 587,
    email_host_user: '',
    email_host_password: '',
    email_use_tls: true,
    email_use_ssl: false,
    default_from_email: '',
    is_active: false
  });

  useEffect(() => {
    if (isAuthenticated) {
      fetchData();
    }
  }, [isAuthenticated, activeTab]);

  const fetchData = async () => {
    setLoading(true);
    try {
      if (activeTab === 'overview' || activeTab === 'logs') {
        const statsResponse = await api.get('/email-logs/stats/');
        setEmailStats(statsResponse.data);
      }

      if (activeTab === 'configs') {
        const configsResponse = await api.get('/email-configs/');
        setEmailConfigs(configsResponse.data.results || configsResponse.data);
      }

      if (activeTab === 'templates') {
        const templatesResponse = await api.get('/email-templates/');
        setEmailTemplates(templatesResponse.data.results || templatesResponse.data);
      }

      if (activeTab === 'logs') {
        const logsResponse = await api.get('/email-logs/');
        setEmailLogs(logsResponse.data.results || logsResponse.data);
      }
      
      if (activeTab === 'notifications') {
        const eventsResponse = await api.get('/events/');
        setEvents(eventsResponse.data.results || eventsResponse.data);
      }
    } catch (err: any) {
      setError('Failed to fetch data');
      console.error('Error fetching data:', err);
    } finally {
      setLoading(false);
    }
  };

  const sendNotification = async (type: string, eventId: string) => {
    setLoading(true);
    try {
      let endpoint = '';
      const data: any = { event_id: eventId };
      
      switch (type) {
        case 'event_details':
          endpoint = '/email-notifications/send_event_details/';
          break;
        case 'schedule_update':
          endpoint = '/email-notifications/send_schedule_update/';
          break;
        case 'daily_gallery':
          endpoint = '/email-notifications/send_daily_gallery/';
          break;
        case 'event_reminder':
          endpoint = '/email-notifications/send_event_reminder/';
          data.days_before = 1;
          break;
        default:
          throw new Error('Invalid notification type');
      }
      
      const response = await api.post(endpoint, data);
      setSuccess(response.data.message);
      setShowNotificationModal(false);
    } catch (err: any) {
      setError(err.response?.data?.error || 'Failed to send notification');
    } finally {
      setLoading(false);
    }
  };

  const getStatusBadge = (status: string) => {
    const variants: { [key: string]: string } = {
      sent: 'success',
      failed: 'danger',
      pending: 'warning',
      bounced: 'secondary'
    };
    return <Badge bg={variants[status] || 'secondary'}>{status}</Badge>;
  };

  const testEmailConfiguration = async (configId: number) => {
    if (!testEmail) {
      setError('Please enter a test email address');
      return;
    }

    setTestingConfig(configId);
    try {
      const response = await api.post(`/email-configs/${configId}/test_connection/`, {
        test_email: testEmail
      });
      setSuccess(response.data.message || 'Test email sent successfully!');
    } catch (err: any) {
      setError(err.response?.data?.error || 'Failed to send test email');
    } finally {
      setTestingConfig(null);
    }
  };

  const activateConfiguration = async (configId: number) => {
    setLoading(true);
    try {
      // First deactivate all configs
      await Promise.all(
        emailConfigs.map(config =>
          config.is_active ? api.patch(`/email-configs/${config.id}/`, { is_active: false }) : Promise.resolve()
        )
      );

      // Then activate the selected one
      await api.patch(`/email-configs/${configId}/`, { is_active: true });

      setSuccess('Email configuration activated successfully!');
      fetchData(); // Refresh data
    } catch (err: any) {
      setError(err.response?.data?.error || 'Failed to activate configuration');
    } finally {
      setLoading(false);
    }
  };

  const saveConfiguration = async () => {
    setLoading(true);
    try {
      if (editingConfig) {
        await api.patch(`/email-configs/${editingConfig.id}/`, configFormData);
        setSuccess('Configuration updated successfully!');
      } else {
        await api.post('/email-configs/', configFormData);
        setSuccess('Configuration created successfully!');
      }

      setShowConfigModal(false);
      setEditingConfig(null);
      resetConfigForm();
      fetchData();
    } catch (err: any) {
      setError(err.response?.data?.error || 'Failed to save configuration');
    } finally {
      setLoading(false);
    }
  };

  const resetConfigForm = () => {
    setConfigFormData({
      name: '',
      email_host: '',
      email_port: 587,
      email_host_user: '',
      email_host_password: '',
      email_use_tls: true,
      email_use_ssl: false,
      default_from_email: '',
      is_active: false
    });
  };

  const editConfiguration = (config: EmailConfig) => {
    setEditingConfig(config);
    setConfigFormData({
      name: config.name,
      email_host: config.email_host,
      email_port: 587, // Default port
      email_host_user: config.email_host_user,
      email_host_password: '', // Don't populate password for security
      email_use_tls: true,
      email_use_ssl: false,
      default_from_email: '',
      is_active: config.is_active
    });
    setShowConfigModal(true);
  };

  if (!isAuthenticated) {
    return (
      <Container className="py-5">
        <Alert variant="warning">
          Please log in to access email management.
        </Alert>
      </Container>
    );
  }

  return (
    <Container fluid className="py-4">
      <Row>
        <Col>
          <h2 className="mb-4">
            <i className="fas fa-envelope me-2"></i>
            Email Management
          </h2>
          
          {error && (
            <Alert variant="danger" dismissible onClose={() => setError('')}>
              {error}
            </Alert>
          )}
          
          {success && (
            <Alert variant="success" dismissible onClose={() => setSuccess('')}>
              {success}
            </Alert>
          )}

          {/* Navigation Tabs */}
          <div className="mb-4">
            <Button
              variant={activeTab === 'overview' ? 'primary' : 'outline-primary'}
              className="me-2 mb-2"
              onClick={() => setActiveTab('overview')}
            >
              <i className="fas fa-chart-bar me-1"></i>
              Overview
            </Button>
            <Button
              variant={activeTab === 'notifications' ? 'primary' : 'outline-primary'}
              className="me-2 mb-2"
              onClick={() => setActiveTab('notifications')}
            >
              <i className="fas fa-paper-plane me-1"></i>
              Send Notifications
            </Button>
            <Button
              variant={activeTab === 'configs' ? 'primary' : 'outline-primary'}
              className="me-2 mb-2"
              onClick={() => setActiveTab('configs')}
            >
              <i className="fas fa-cog me-1"></i>
              SMTP Config
            </Button>
            <Button
              variant={activeTab === 'templates' ? 'primary' : 'outline-primary'}
              className="me-2 mb-2"
              onClick={() => setActiveTab('templates')}
            >
              <i className="fas fa-file-alt me-1"></i>
              Templates
            </Button>
            <Button
              variant={activeTab === 'logs' ? 'primary' : 'outline-primary'}
              className="me-2 mb-2"
              onClick={() => setActiveTab('logs')}
            >
              <i className="fas fa-history me-1"></i>
              Email Logs
            </Button>
          </div>

          {/* Overview Tab */}
          {activeTab === 'overview' && emailStats && (
            <Row>
              <Col md={3}>
                <Card className="text-center mb-3">
                  <Card.Body>
                    <h3 className="text-primary">{emailStats.total}</h3>
                    <p className="mb-0">Total Emails</p>
                  </Card.Body>
                </Card>
              </Col>
              <Col md={3}>
                <Card className="text-center mb-3">
                  <Card.Body>
                    <h3 className="text-success">{emailStats.sent}</h3>
                    <p className="mb-0">Sent Successfully</p>
                  </Card.Body>
                </Card>
              </Col>
              <Col md={3}>
                <Card className="text-center mb-3">
                  <Card.Body>
                    <h3 className="text-danger">{emailStats.failed}</h3>
                    <p className="mb-0">Failed</p>
                  </Card.Body>
                </Card>
              </Col>
              <Col md={3}>
                <Card className="text-center mb-3">
                  <Card.Body>
                    <h3 className="text-info">{emailStats.success_rate}%</h3>
                    <p className="mb-0">Success Rate</p>
                  </Card.Body>
                </Card>
              </Col>
            </Row>
          )}

          {/* Send Notifications Tab */}
          {activeTab === 'notifications' && (
            <Card>
              <Card.Header>
                <h5 className="mb-0">Send Email Notifications</h5>
              </Card.Header>
              <Card.Body>
                <Row>
                  <Col md={6}>
                    <Button
                      variant="outline-primary"
                      className="w-100 mb-3"
                      onClick={() => {
                        setNotificationType('event_details');
                        setShowNotificationModal(true);
                      }}
                    >
                      <i className="fas fa-info-circle me-2"></i>
                      Send Event Details
                    </Button>
                  </Col>
                  <Col md={6}>
                    <Button
                      variant="outline-success"
                      className="w-100 mb-3"
                      onClick={() => {
                        setNotificationType('schedule_update');
                        setShowNotificationModal(true);
                      }}
                    >
                      <i className="fas fa-calendar me-2"></i>
                      Send Schedule Update
                    </Button>
                  </Col>
                  <Col md={6}>
                    <Button
                      variant="outline-info"
                      className="w-100 mb-3"
                      onClick={() => {
                        setNotificationType('daily_gallery');
                        setShowNotificationModal(true);
                      }}
                    >
                      <i className="fas fa-images me-2"></i>
                      Send Daily Gallery
                    </Button>
                  </Col>
                  <Col md={6}>
                    <Button
                      variant="outline-warning"
                      className="w-100 mb-3"
                      onClick={() => {
                        setNotificationType('event_reminder');
                        setShowNotificationModal(true);
                      }}
                    >
                      <i className="fas fa-bell me-2"></i>
                      Send Event Reminder
                    </Button>
                  </Col>
                </Row>
              </Card.Body>
            </Card>
          )}

          {/* SMTP Configuration Tab */}
          {activeTab === 'configs' && (
            <Row>
              <Col>
                <Card>
                  <Card.Header className="d-flex justify-content-between align-items-center">
                    <h5 className="mb-0">SMTP Configurations</h5>
                    <Button
                      variant="primary"
                      onClick={() => {
                        resetConfigForm();
                        setEditingConfig(null);
                        setShowConfigModal(true);
                      }}
                    >
                      <i className="fas fa-plus me-2"></i>
                      Add Configuration
                    </Button>
                  </Card.Header>
                  <Card.Body>
                    {emailConfigs.length === 0 ? (
                      <div className="text-center py-4">
                        <i className="fas fa-cog fa-3x text-muted mb-3"></i>
                        <h5 className="text-muted">No SMTP configurations found</h5>
                        <p className="text-muted">Create your first SMTP configuration to start sending emails.</p>
                      </div>
                    ) : (
                      <Row>
                        {emailConfigs.map((config) => (
                          <Col md={6} lg={4} key={config.id} className="mb-3">
                            <Card className={`h-100 ${config.is_active ? 'border-success' : ''}`}>
                              <Card.Header className="d-flex justify-content-between align-items-center">
                                <div>
                                  <strong>{config.name}</strong>
                                  {config.is_active && (
                                    <Badge bg="success" className="ms-2">Active</Badge>
                                  )}
                                </div>
                                <div>
                                  <Button
                                    variant="outline-primary"
                                    size="sm"
                                    className="me-1"
                                    onClick={() => editConfiguration(config)}
                                  >
                                    <i className="fas fa-edit"></i>
                                  </Button>
                                  {!config.is_active && (
                                    <Button
                                      variant="outline-success"
                                      size="sm"
                                      onClick={() => activateConfiguration(config.id)}
                                      disabled={loading}
                                    >
                                      <i className="fas fa-check"></i>
                                    </Button>
                                  )}
                                </div>
                              </Card.Header>
                              <Card.Body>
                                <div className="mb-2">
                                  <small className="text-muted">Host:</small><br />
                                  <code>{config.email_host}</code>
                                </div>
                                <div className="mb-3">
                                  <small className="text-muted">User:</small><br />
                                  <code>{config.email_host_user}</code>
                                </div>

                                <div className="border-top pt-3">
                                  <Form.Group className="mb-2">
                                    <Form.Label className="small">Test Email:</Form.Label>
                                    <Form.Control
                                      type="email"
                                      size="sm"
                                      placeholder="<EMAIL>"
                                      value={testEmail}
                                      onChange={(e) => setTestEmail(e.target.value)}
                                    />
                                  </Form.Group>
                                  <Button
                                    variant="outline-info"
                                    size="sm"
                                    className="w-100"
                                    onClick={() => testEmailConfiguration(config.id)}
                                    disabled={testingConfig === config.id || !testEmail}
                                  >
                                    {testingConfig === config.id ? (
                                      <>
                                        <i className="fas fa-spinner fa-spin me-2"></i>
                                        Testing...
                                      </>
                                    ) : (
                                      <>
                                        <i className="fas fa-paper-plane me-2"></i>
                                        Test Connection
                                      </>
                                    )}
                                  </Button>
                                </div>
                              </Card.Body>
                            </Card>
                          </Col>
                        ))}
                      </Row>
                    )}
                  </Card.Body>
                </Card>
              </Col>
            </Row>
          )}

          {/* Email Logs Tab */}
          {activeTab === 'logs' && (
            <Card>
              <Card.Header>
                <h5 className="mb-0">Recent Email Logs</h5>
              </Card.Header>
              <Card.Body>
                <Table responsive striped>
                  <thead>
                    <tr>
                      <th>Recipient</th>
                      <th>Subject</th>
                      <th>Type</th>
                      <th>Status</th>
                      <th>Sent At</th>
                    </tr>
                  </thead>
                  <tbody>
                    {emailLogs.slice(0, 10).map((log) => (
                      <tr key={log.id}>
                        <td>{log.recipient_email}</td>
                        <td>{log.subject}</td>
                        <td>{log.template_type}</td>
                        <td>{getStatusBadge(log.status)}</td>
                        <td>{log.sent_at ? new Date(log.sent_at).toLocaleString() : '-'}</td>
                      </tr>
                    ))}
                  </tbody>
                </Table>
              </Card.Body>
            </Card>
          )}
        </Col>
      </Row>

      {/* Notification Modal */}
      <Modal show={showNotificationModal} onHide={() => setShowNotificationModal(false)}>
        <Modal.Header closeButton>
          <Modal.Title>Send Email Notification</Modal.Title>
        </Modal.Header>
        <Modal.Body>
          <Form>
            <Form.Group className="mb-3">
              <Form.Label>Select Event</Form.Label>
              <Form.Select
                value={selectedEvent}
                onChange={(e) => setSelectedEvent(e.target.value)}
              >
                <option value="">Choose an event...</option>
                {events.map((event) => (
                  <option key={event.id} value={event.id}>
                    {event.name}
                  </option>
                ))}
              </Form.Select>
            </Form.Group>
          </Form>
        </Modal.Body>
        <Modal.Footer>
          <Button variant="secondary" onClick={() => setShowNotificationModal(false)}>
            Cancel
          </Button>
          <Button
            variant="primary"
            onClick={() => sendNotification(notificationType, selectedEvent)}
            disabled={!selectedEvent || loading}
          >
            {loading ? 'Sending...' : 'Send Notification'}
          </Button>
        </Modal.Footer>
      </Modal>

      {/* SMTP Configuration Modal */}
      <Modal show={showConfigModal} onHide={() => setShowConfigModal(false)} size="lg">
        <Modal.Header closeButton>
          <Modal.Title>
            {editingConfig ? 'Edit SMTP Configuration' : 'Add SMTP Configuration'}
          </Modal.Title>
        </Modal.Header>
        <Form onSubmit={(e) => { e.preventDefault(); saveConfiguration(); }}>
          <Modal.Body>
            <Row>
              <Col md={6}>
                <Form.Group className="mb-3">
                  <Form.Label>Configuration Name *</Form.Label>
                  <Form.Control
                    type="text"
                    value={configFormData.name}
                    onChange={(e) => setConfigFormData({...configFormData, name: e.target.value})}
                    placeholder="e.g., Gmail, Office 365"
                    required
                  />
                </Form.Group>
              </Col>
              <Col md={6}>
                <Form.Group className="mb-3">
                  <Form.Label>SMTP Host *</Form.Label>
                  <Form.Control
                    type="text"
                    value={configFormData.email_host}
                    onChange={(e) => setConfigFormData({...configFormData, email_host: e.target.value})}
                    placeholder="smtp.gmail.com"
                    required
                  />
                </Form.Group>
              </Col>
            </Row>

            <Row>
              <Col md={6}>
                <Form.Group className="mb-3">
                  <Form.Label>Port</Form.Label>
                  <Form.Control
                    type="number"
                    value={configFormData.email_port}
                    onChange={(e) => setConfigFormData({...configFormData, email_port: parseInt(e.target.value)})}
                    placeholder="587"
                  />
                </Form.Group>
              </Col>
              <Col md={6}>
                <Form.Group className="mb-3">
                  <Form.Label>Email Address *</Form.Label>
                  <Form.Control
                    type="email"
                    value={configFormData.email_host_user}
                    onChange={(e) => setConfigFormData({...configFormData, email_host_user: e.target.value})}
                    placeholder="<EMAIL>"
                    required
                  />
                </Form.Group>
              </Col>
            </Row>

            <Row>
              <Col md={6}>
                <Form.Group className="mb-3">
                  <Form.Label>Password *</Form.Label>
                  <Form.Control
                    type="password"
                    value={configFormData.email_host_password}
                    onChange={(e) => setConfigFormData({...configFormData, email_host_password: e.target.value})}
                    placeholder="App password or email password"
                    required={!editingConfig}
                  />
                  {editingConfig && (
                    <Form.Text className="text-muted">
                      Leave blank to keep current password
                    </Form.Text>
                  )}
                </Form.Group>
              </Col>
              <Col md={6}>
                <Form.Group className="mb-3">
                  <Form.Label>From Email</Form.Label>
                  <Form.Control
                    type="email"
                    value={configFormData.default_from_email}
                    onChange={(e) => setConfigFormData({...configFormData, default_from_email: e.target.value})}
                    placeholder="<EMAIL>"
                  />
                </Form.Group>
              </Col>
            </Row>

            <Row>
              <Col md={6}>
                <Form.Group className="mb-3">
                  <Form.Check
                    type="checkbox"
                    label="Use TLS (recommended)"
                    checked={configFormData.email_use_tls}
                    onChange={(e) => setConfigFormData({...configFormData, email_use_tls: e.target.checked})}
                  />
                </Form.Group>
              </Col>
              <Col md={6}>
                <Form.Group className="mb-3">
                  <Form.Check
                    type="checkbox"
                    label="Use SSL"
                    checked={configFormData.email_use_ssl}
                    onChange={(e) => setConfigFormData({...configFormData, email_use_ssl: e.target.checked})}
                  />
                </Form.Group>
              </Col>
            </Row>

            <Form.Group className="mb-3">
              <Form.Check
                type="checkbox"
                label="Set as active configuration"
                checked={configFormData.is_active}
                onChange={(e) => setConfigFormData({...configFormData, is_active: e.target.checked})}
              />
            </Form.Group>
          </Modal.Body>
          <Modal.Footer>
            <Button variant="secondary" onClick={() => setShowConfigModal(false)}>
              Cancel
            </Button>
            <Button variant="primary" type="submit" disabled={loading}>
              {loading ? 'Saving...' : (editingConfig ? 'Update' : 'Create')}
            </Button>
          </Modal.Footer>
        </Form>
      </Modal>
    </Container>
  );
};

export default EmailManagement;
