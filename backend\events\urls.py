from django.urls import path, include
from rest_framework.routers import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from .views import EventViewSet, EventScheduleViewSet, EventGalleryViewSet, EventDocumentViewSet, public_statistics, event_schedule_list, event_gallery_list, event_schedule_pdf
from .email_views import EmailConfigurationViewSet, EmailTemplateViewSet, EmailLogViewSet, EmailNotificationViewSet

router = DefaultRouter()
router.register(r'events', EventViewSet)
router.register(r'schedules', EventScheduleViewSet)
router.register(r'gallery', EventGalleryViewSet)
router.register(r'documents', EventDocumentViewSet)
router.register(r'email-configs', EmailConfigurationViewSet)
router.register(r'email-templates', EmailTemplateViewSet)
router.register(r'email-logs', EmailLogViewSet)
router.register(r'email-notifications', EmailNotificationViewSet, basename='email-notifications')

urlpatterns = [
    path('', include(router.urls)),
    path('events/<int:event_id>/schedule/', event_schedule_list, name='event-schedule'),
    path('events/<int:event_id>/schedule/pdf/', event_schedule_pdf, name='event-schedule-pdf'),
    path('events/<int:event_id>/gallery/', event_gallery_list, name='event-gallery'),
    path('public/events/', EventViewSet.as_view({'get': 'list'}), name='public-events'),
    path('public/events/<int:pk>/', EventViewSet.as_view({'get': 'retrieve'}), name='public-event-detail'),
    path('public/statistics/', public_statistics, name='public-statistics'),
]