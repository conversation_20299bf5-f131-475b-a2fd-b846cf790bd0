# Generated by Django 4.2.7 on 2025-07-31 13:46

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('contact_persons', '0001_initial'),
        ('drivers', '0001_initial'),
        ('participants', '0006_remove_participant_badge_generated_and_more'),
    ]

    operations = [
        migrations.CreateModel(
            name='DriverBadge',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('qr_code_data', models.TextField()),
                ('qr_code_image', models.ImageField(blank=True, null=True, upload_to='driver_qr_codes/')),
                ('badge_image', models.ImageField(blank=True, null=True, upload_to='driver_badges/')),
                ('is_generated', models.BooleanField(default=False)),
                ('generated_at', models.DateTimeField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('driver', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='badge', to='drivers.driver')),
            ],
        ),
        migrations.CreateModel(
            name='ContactPersonBadge',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('qr_code_data', models.TextField()),
                ('qr_code_image', models.ImageField(blank=True, null=True, upload_to='contact_qr_codes/')),
                ('badge_image', models.ImageField(blank=True, null=True, upload_to='contact_badges/')),
                ('is_generated', models.BooleanField(default=False)),
                ('generated_at', models.DateTimeField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('contact_person', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='badge', to='contact_persons.contactperson')),
            ],
        ),
        migrations.CreateModel(
            name='BadgeTemplate',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100)),
                ('description', models.TextField(blank=True)),
                ('version', models.CharField(default='v1.0', max_length=20)),
                ('width', models.IntegerField(default=600)),
                ('height', models.IntegerField(default=1200)),
                ('background_color', models.CharField(default='#002D72', max_length=7)),
                ('template_file', models.ImageField(blank=True, null=True, upload_to='badge_templates/')),
                ('variables', models.JSONField(default=dict, help_text='Dynamic fields configuration')),
                ('svg_content', models.TextField(blank=True, help_text='SVG template content')),
                ('is_default', models.BooleanField(default=False)),
                ('is_active', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'unique_together': {('name', 'version')},
            },
        ),
        migrations.CreateModel(
            name='Badge',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('qr_data', models.JSONField(default=dict, help_text='Structured QR code data')),
                ('qr_code_image', models.ImageField(blank=True, null=True, upload_to='qr_codes/')),
                ('badge_image', models.ImageField(blank=True, null=True, upload_to='generated_badges/')),
                ('template_version', models.CharField(default='v1.0', max_length=20)),
                ('cached_svg', models.TextField(blank=True, help_text='Cached SVG content')),
                ('last_rendered', models.DateTimeField(blank=True, null=True)),
                ('content_hash', models.CharField(blank=True, help_text='Hash of badge content for cache invalidation', max_length=64)),
                ('is_generated', models.BooleanField(default=False)),
                ('generated_at', models.DateTimeField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('participant', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='badge', to='participants.participant')),
                ('template', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='badges.badgetemplate')),
            ],
        ),
    ]
