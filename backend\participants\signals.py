from django.db.models.signals import post_save, pre_save
from django.dispatch import receiver
from django.utils import timezone
from .models import Participant
import logging

logger = logging.getLogger(__name__)

@receiver(pre_save, sender=Participant)
def track_status_change(sender, instance, **kwargs):
    """Track status changes to trigger appropriate emails"""
    if instance.pk:
        try:
            # Get the old instance to compare status
            old_instance = Participant.objects.get(pk=instance.pk)
            instance._old_status = old_instance.status
        except Participant.DoesNotExist:
            instance._old_status = None
    else:
        instance._old_status = None

@receiver(post_save, sender=Participant)
def handle_participant_status_change(sender, instance, created, **kwargs):
    """Handle participant status changes and send appropriate emails"""
    
    # Skip if this is a new participant (registration)
    # Registration confirmation is handled in the serializer
    if created:
        logger.info(f"New participant registered: {instance.full_name} ({instance.email})")
        return
    
    # Get the old status from the pre_save signal
    old_status = getattr(instance, '_old_status', None)
    new_status = instance.status
    
    # Only proceed if status actually changed
    if old_status == new_status:
        return
    
    logger.info(f"Participant {instance.full_name} status changed from {old_status} to {new_status}")
    
    try:
        from events.email_service import get_email_service
        email_service = get_email_service()
        
        # Handle different status transitions
        if new_status == 'approved' and old_status == 'pending':
            # Ensure badge is generated before sending approval email
            try:
                from badges.models import Badge
                badge, created = Badge.objects.get_or_create(participant=instance)
                if not badge.is_generated:
                    logger.info(f"Generating badge for {instance.full_name}")
                    badge.generate_badge()
            except Exception as e:
                logger.error(f"Error generating badge for {instance.full_name}: {e}")

            # Send event details email when approved
            logger.info(f"Sending event details email to {instance.email}")
            email_service.send_event_details(instance)
            
        elif new_status == 'assigned' and old_status in ['approved', 'pending']:
            # Send assignment details (hotel/driver) when assigned
            logger.info(f"Sending assignment details email to {instance.email}")
            # You can create a new email template for assignment details
            # email_service.send_assignment_details(instance)
            
        elif new_status == 'badge_generated' and old_status in ['assigned', 'approved']:
            # Send badge notification when badge is generated
            logger.info(f"Sending badge notification email to {instance.email}")
            if instance.badge_file:
                email_service.send_badge_notification(instance, instance.badge_file.path)
            else:
                email_service.send_badge_notification(instance)
                
        elif new_status == 'email_sent':
            # This status is set when detailed emails are sent
            logger.info(f"Detailed emails sent to {instance.email}")
            
        elif new_status == 'rejected' and old_status == 'pending':
            # Send rejection email (you might want to create this template)
            logger.info(f"Participant {instance.email} was rejected")
            # email_service.send_rejection_notification(instance)
            
    except Exception as e:
        logger.error(f"Error sending status change email to {instance.email}: {e}")
        # Don't raise the exception to avoid breaking the save operation
        
    # Clean up the temporary attribute
    if hasattr(instance, '_old_status'):
        delattr(instance, '_old_status')


@receiver(post_save, sender=Participant)
def update_participant_timestamps(sender, instance, created, **kwargs):
    """Update relevant timestamps based on status"""
    
    if not created:  # Only for updates, not new registrations
        old_status = getattr(instance, '_old_status', None)
        new_status = instance.status
        
        # Update approval timestamp when approved
        if new_status == 'approved' and old_status != 'approved':
            if not instance.approved_at:
                # Update without triggering signals again
                Participant.objects.filter(pk=instance.pk).update(
                    approved_at=timezone.now()
                )
                
        # Update confirmation status when approved
        if new_status in ['approved', 'assigned', 'badge_generated', 'email_sent']:
            if not instance.is_confirmed:
                Participant.objects.filter(pk=instance.pk).update(
                    is_confirmed=True
                )


# Email sending helper functions for different scenarios
def send_bulk_event_details(participants):
    """Send event details to multiple approved participants"""
    try:
        from events.email_service import get_email_service
        email_service = get_email_service()
        
        success_count = 0
        for participant in participants:
            if participant.status in ['approved', 'assigned', 'badge_generated']:
                try:
                    email_service.send_event_details(participant)
                    success_count += 1
                    logger.info(f"Event details sent to {participant.email}")
                except Exception as e:
                    logger.error(f"Failed to send event details to {participant.email}: {e}")
                    
        return success_count
    except Exception as e:
        logger.error(f"Error in bulk email sending: {e}")
        return 0


def send_bulk_badge_notifications(participants):
    """Send badge notifications to participants with generated badges"""
    try:
        from events.email_service import get_email_service
        email_service = get_email_service()
        
        success_count = 0
        for participant in participants:
            if participant.status == 'badge_generated' and participant.badge_file:
                try:
                    email_service.send_badge_notification(participant, participant.badge_file.path)
                    success_count += 1
                    logger.info(f"Badge notification sent to {participant.email}")
                except Exception as e:
                    logger.error(f"Failed to send badge notification to {participant.email}: {e}")
                    
        return success_count
    except Exception as e:
        logger.error(f"Error in bulk badge email sending: {e}")
        return 0
