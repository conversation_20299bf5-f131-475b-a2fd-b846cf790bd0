export interface ValidationResult {
  isValid: boolean;
  message?: string;
}

export interface FormErrors {
  [key: string]: string;
}

export const validateEmail = (email: string): ValidationResult => {
  if (!email.trim()) {
    return { isValid: false, message: 'Email is required' };
  }

  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  if (!emailRegex.test(email)) {
    return { isValid: false, message: 'Please enter a valid email address' };
  }

  return { isValid: true };
};

export const validateEmailUniqueness = async (email: string): Promise<ValidationResult> => {
  if (!email.trim()) {
    return { isValid: false, message: 'Email is required' };
  }

  const emailValidation = validateEmail(email);
  if (!emailValidation.isValid) {
    return emailValidation;
  }

  try {
    const response = await fetch(`/api/check-email/?email=${encodeURIComponent(email)}`);
    if (response.ok) {
      const data = await response.json();
      if (data.exists) {
        return { isValid: false, message: 'This email is already registered for this event' };
      }
    }
  } catch (error) {
    // If check fails, allow the email (server will handle duplicate check)
    console.warn('Email uniqueness check failed:', error);
  }

  return { isValid: true };
};

export const validatePhone = (phone: string): ValidationResult => {
  if (!phone.trim()) {
    return { isValid: false, message: 'Phone number is required' };
  }
  
  // Remove all non-digit characters for validation
  const cleanPhone = phone.replace(/\D/g, '');
  
  if (cleanPhone.length < 10) {
    return { isValid: false, message: 'Phone number must be at least 10 digits' };
  }
  
  if (cleanPhone.length > 15) {
    return { isValid: false, message: 'Phone number cannot exceed 15 digits' };
  }
  
  return { isValid: true };
};

export const validateRequired = (value: string, fieldName: string): ValidationResult => {
  if (!value.trim()) {
    return { isValid: false, message: `${fieldName} is required` };
  }
  
  return { isValid: true };
};

export const validateName = (name: string, fieldName: string): ValidationResult => {
  if (!name.trim()) {
    return { isValid: false, message: `${fieldName} is required` };
  }
  
  if (name.trim().length < 2) {
    return { isValid: false, message: `${fieldName} must be at least 2 characters long` };
  }
  
  if (name.trim().length > 50) {
    return { isValid: false, message: `${fieldName} cannot exceed 50 characters` };
  }
  
  // Check for valid characters (letters, spaces, hyphens, apostrophes)
  const nameRegex = /^[a-zA-Z\s\-']+$/;
  if (!nameRegex.test(name.trim())) {
    return { isValid: false, message: `${fieldName} can only contain letters, spaces, hyphens, and apostrophes` };
  }
  
  return { isValid: true };
};

export const validateInstitution = (institution: string): ValidationResult => {
  if (!institution.trim()) {
    return { isValid: false, message: 'Institution name is required' };
  }
  
  if (institution.trim().length < 2) {
    return { isValid: false, message: 'Institution name must be at least 2 characters long' };
  }
  
  if (institution.trim().length > 100) {
    return { isValid: false, message: 'Institution name cannot exceed 100 characters' };
  }
  
  return { isValid: true };
};

export const validatePosition = (position: string): ValidationResult => {
  if (!position.trim()) {
    return { isValid: false, message: 'Position is required' };
  }
  
  if (position.trim().length < 2) {
    return { isValid: false, message: 'Position must be at least 2 characters long' };
  }
  
  if (position.trim().length > 100) {
    return { isValid: false, message: 'Position cannot exceed 100 characters' };
  }
  
  return { isValid: true };
};

export const validateDate = (date: Date, fieldName: string, minDate?: Date, maxDate?: Date): ValidationResult => {
  if (!date) {
    return { isValid: false, message: `${fieldName} is required` };
  }
  
  if (minDate && date < minDate) {
    return { isValid: false, message: `${fieldName} cannot be earlier than ${minDate.toLocaleDateString()}` };
  }
  
  if (maxDate && date > maxDate) {
    return { isValid: false, message: `${fieldName} cannot be later than ${maxDate.toLocaleDateString()}` };
  }
  
  return { isValid: true };
};

export const validateDateRange = (startDate: Date, endDate: Date): ValidationResult => {
  if (!startDate || !endDate) {
    return { isValid: false, message: 'Both arrival and departure dates are required' };
  }

  if (endDate <= startDate) {
    return { isValid: false, message: 'Departure date must be after the arrival date' };
  }

  // Check if departure is on the same day (not allowed)
  const startDateOnly = new Date(startDate.getFullYear(), startDate.getMonth(), startDate.getDate());
  const endDateOnly = new Date(endDate.getFullYear(), endDate.getMonth(), endDate.getDate());

  if (endDateOnly.getTime() === startDateOnly.getTime()) {
    return { isValid: false, message: 'Departure date must be at least one day after arrival date' };
  }

  // Check if the date range is reasonable (not more than 30 days)
  const daysDiff = Math.ceil((endDate.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24));
  if (daysDiff > 30) {
    return { isValid: false, message: 'Event duration cannot exceed 30 days' };
  }

  return { isValid: true };
};

export const validateFileSize = (file: File, maxSizeMB: number = 5): ValidationResult => {
  const maxSizeBytes = maxSizeMB * 1024 * 1024;
  
  if (file.size > maxSizeBytes) {
    return { isValid: false, message: `File size cannot exceed ${maxSizeMB}MB` };
  }
  
  return { isValid: true };
};

export const validateImageFile = (file: File): ValidationResult => {
  const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif'];
  
  if (!allowedTypes.includes(file.type)) {
    return { isValid: false, message: 'Please upload a valid image file (JPEG, PNG, or GIF)' };
  }
  
  return validateFileSize(file, 5);
};

// Comprehensive form validation for participant registration
export const validateParticipantForm = (formData: any): FormErrors => {
  const errors: FormErrors = {};
  
  // Validate first name
  const firstNameResult = validateName(formData.first_name, 'First name');
  if (!firstNameResult.isValid) {
    errors.first_name = firstNameResult.message!;
  }
  
  // Validate last name
  const lastNameResult = validateName(formData.last_name, 'Last name');
  if (!lastNameResult.isValid) {
    errors.last_name = lastNameResult.message!;
  }
  
  // Validate middle name (optional)
  if (formData.middle_name && formData.middle_name.trim()) {
    const middleNameResult = validateName(formData.middle_name, 'Middle name');
    if (!middleNameResult.isValid) {
      errors.middle_name = middleNameResult.message!;
    }
  }
  
  // Validate email
  const emailResult = validateEmail(formData.email);
  if (!emailResult.isValid) {
    errors.email = emailResult.message!;
  }
  
  // Validate phone
  const phoneResult = validatePhone(formData.phone);
  if (!phoneResult.isValid) {
    errors.phone = phoneResult.message!;
  }
  
  // Validate institution
  const institutionResult = validateInstitution(formData.institution_name);
  if (!institutionResult.isValid) {
    errors.institution_name = institutionResult.message!;
  }
  
  // Validate position
  const positionResult = validatePosition(formData.position);
  if (!positionResult.isValid) {
    errors.position = positionResult.message!;
  }
  
  // Validate event selection
  if (!formData.event) {
    errors.event = 'Please select an event';
  }
  
  // Validate participant type
  if (!formData.participant_type) {
    errors.participant_type = 'Please select a participant type';
  }
  
  // Validate arrival date first
  const today = new Date();
  today.setHours(0, 0, 0, 0);

  if (!formData.arrival_date) {
    errors.arrival_date = 'Arrival date is required';
  } else {
    const arrivalResult = validateDate(formData.arrival_date, 'Arrival date', today);
    if (!arrivalResult.isValid) {
      errors.arrival_date = arrivalResult.message!;
    }
  }

  // Validate departure date
  if (!formData.departure_date) {
    errors.departure_date = 'Departure date is required';
  } else if (formData.arrival_date) {
    // Only validate date range if both dates are present and arrival date is valid
    if (!errors.arrival_date) {
      const arrivalDate = new Date(formData.arrival_date);
      const departureDate = new Date(formData.departure_date);
      const dateRangeResult = validateDateRange(arrivalDate, departureDate);
      if (!dateRangeResult.isValid) {
        errors.departure_date = dateRangeResult.message!;
      }
    }
  }
  
  // Validate profile photo (if provided)
  if (formData.profile_photo) {
    const photoResult = validateImageFile(formData.profile_photo);
    if (!photoResult.isValid) {
      errors.profile_photo = photoResult.message!;
    }
  }
  
  return errors;
};
