#!/usr/bin/env python3

import os
import sys
import django

# Add the project root to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'event_management.settings')
django.setup()

from participants.models import Participant

def update_all_participants_approved():
    """Update all participants to approved status"""
    
    print("=== UPDATING ALL PARTICIPANTS TO APPROVED STATUS ===")
    
    # Get current status distribution
    print("Current status distribution:")
    active_count = Participant.objects.filter(status='active').count()
    approved_count = Participant.objects.filter(status='approved').count()
    deleted_count = Participant.objects.filter(status='deleted').count()
    total_count = Participant.objects.count()
    
    print(f"Active: {active_count}")
    print(f"Approved: {approved_count}")
    print(f"Deleted: {deleted_count}")
    print(f"Total: {total_count}")
    
    # Update all participants to approved (except deleted ones)
    updated_count = Participant.objects.exclude(status='deleted').update(status='approved')
    
    print(f"\nUpdated {updated_count} participants to 'approved' status")
    
    # Show new status distribution
    print("\nNew status distribution:")
    active_count = Participant.objects.filter(status='active').count()
    approved_count = Participant.objects.filter(status='approved').count()
    deleted_count = Participant.objects.filter(status='deleted').count()
    
    print(f"Active: {active_count}")
    print(f"Approved: {approved_count}")
    print(f"Deleted: {deleted_count}")
    print(f"Total: {total_count}")
    
    print(f"\n✅ All participants (except deleted ones) are now approved and ready for QR code attendance!")

if __name__ == "__main__":
    update_all_participants_approved()
