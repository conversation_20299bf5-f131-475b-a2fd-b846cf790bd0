#!/usr/bin/env python3

import os
import sys
import django

# Add the project root to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'event_management.settings')
django.setup()

from participants.models import Participant

def check_carly():
    """Check what happened to <PERSON>"""
    
    print("=== CHECKING CARLY GENTRY ===")
    
    carly_participants = Participant.objects.filter(first_name='<PERSON>', last_name='Gentry')
    
    if carly_participants.exists():
        for carly in carly_participants:
            print(f"Found Carly Gentry: ID {carly.id}, Email: {carly.email}, Status: {carly.status}")
    else:
        print("Carly Gentry not found")
        
        # Check if there's a participant with ID 511
        id_511 = Participant.objects.filter(id=511).first()
        if id_511:
            print(f"ID 511 is taken by: {id_511.first_name} {id_511.last_name}, Email: {id_511.email}")
        else:
            print("ID 511 is available")

if __name__ == "__main__":
    check_carly()
