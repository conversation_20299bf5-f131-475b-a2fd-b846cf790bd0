#!/usr/bin/env python
"""
Force regenerate badge for a specific participant to test the new SVG generation
"""
import os
import sys
import django

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'event_management.settings')
django.setup()

from participants.models import Participant
from badges.models import Badge

def force_regenerate_badge():
    """Force regenerate badge to test new SVG generation"""
    print("🔄 Force Regenerating Badge with New SVG Design")
    print("=" * 60)
    
    try:
        # Get the participant from the screenshot (Tewodros Chelol Abeabw)
        participant = Participant.objects.filter(
            first_name__icontains="Tewodros"
        ).first()
        
        if not participant:
            # Get any participant
            participant = Participant.objects.first()
        
        if not participant:
            print("❌ No participants found in database")
            return
        
        print(f"👤 Regenerating badge for: {participant.full_name}")
        print(f"📧 Email: {getattr(participant, 'email', 'N/A')}")
        print(f"🏢 Institution: {getattr(participant, 'institution_name', 'N/A')}")
        print(f"🎯 Type: {participant.participant_type.name if participant.participant_type else 'N/A'}")
        
        # Get existing badge
        try:
            badge = Badge.objects.get(participant=participant)
            print(f"🎫 Found existing badge (ID: {badge.id})")
        except Badge.DoesNotExist:
            badge = Badge.objects.create(participant=participant)
            print(f"🎫 Created new badge (ID: {badge.id})")
        
        # Clear existing badge files to force regeneration
        if badge.badge_image:
            print(f"🗑️  Clearing old badge: {badge.badge_image.name}")
            badge.badge_image.delete(save=False)
        
        if badge.qr_code_image:
            print(f"🗑️  Clearing old QR code: {badge.qr_code_image.name}")
            badge.qr_code_image.delete(save=False)
        
        # Reset generation status
        badge.is_generated = False
        badge.generated_at = None
        badge.save()
        
        print("🎨 Generating NEW SVG-style badge...")
        print("   Using simplified SVG template design")
        print("   Dimensions: 600x1200 pixels")
        print("   Ministry branding and professional layout")
        
        # Force regenerate with new SVG design
        badge_img = badge.generate_badge()
        
        print(f"✅ NEW BADGE GENERATED SUCCESSFULLY!")
        print(f"📏 Dimensions: {badge_img.size}")
        print(f"💾 Badge saved: {badge.badge_image.name}")
        print(f"🔗 QR Code: {badge.qr_code_image.name}")
        print(f"🔄 Generation status: {'Generated' if badge.is_generated else 'Not generated'}")
        print(f"📅 Generated at: {badge.generated_at}")
        
        # Verify the badge uses SVG design
        if badge_img.size == (600, 1200):
            print("✅ CONFIRMED: Badge uses new SVG template dimensions!")
        else:
            print(f"⚠️  WARNING: Badge dimensions {badge_img.size} don't match SVG template")
        
        print("\n🎉 Badge regeneration completed!")
        print("💡 The participant should now see the new SVG-style badge")
        print("🔄 Try refreshing the badge management interface")
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    force_regenerate_badge()
