<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Event Details - {{ event.name }}</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
        }
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
            border-radius: 10px 10px 0 0;
        }
        .content {
            background: #f8f9fa;
            padding: 30px;
            border-radius: 0 0 10px 10px;
        }
        .section {
            background: white;
            margin: 20px 0;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .section h3 {
            color: #667eea;
            margin-top: 0;
            border-bottom: 2px solid #667eea;
            padding-bottom: 10px;
        }
        .info-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
            margin: 15px 0;
        }
        .info-item {
            padding: 10px;
            background: #f8f9fa;
            border-radius: 5px;
        }
        .info-label {
            font-weight: bold;
            color: #495057;
            font-size: 0.9em;
        }
        .info-value {
            color: #212529;
            margin-top: 5px;
        }
        .participant-type {
            display: inline-block;
            padding: 5px 15px;
            border-radius: 20px;
            color: white;
            font-weight: bold;
            background-color: {{ participant_type.color }};
        }
        .important-note {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
        }
        .contact-info {
            background: #e3f2fd;
            border-left: 4px solid #2196f3;
            padding: 15px;
            margin: 15px 0;
        }
        .footer {
            text-align: center;
            margin-top: 30px;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 8px;
            font-size: 0.9em;
            color: #6c757d;
        }
        @media (max-width: 600px) {
            .info-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>{{ event.name }}</h1>
        <p>Event Details & Information</p>
    </div>
    
    <div class="content">
        <div class="section">
            <h3>👤 Participant Information</h3>
            <div class="info-grid">
                <div class="info-item">
                    <div class="info-label">Name</div>
                    <div class="info-value">{{ participant.full_name }}</div>
                </div>
                <div class="info-item">
                    <div class="info-label">Email</div>
                    <div class="info-value">{{ participant.email }}</div>
                </div>
                <div class="info-item">
                    <div class="info-label">Phone</div>
                    <div class="info-value">{{ participant.phone }}</div>
                </div>
                <div class="info-item">
                    <div class="info-label">Institution</div>
                    <div class="info-value">{{ participant.institution_name }}</div>
                </div>
                <div class="info-item">
                    <div class="info-label">Position</div>
                    <div class="info-value">{{ participant.position }}</div>
                </div>
                <div class="info-item">
                    <div class="info-label">Participant Type</div>
                    <div class="info-value">
                        <span class="participant-type">{{ participant_type.name }}</span>
                    </div>
                </div>
            </div>
        </div>

        <div class="section">
            <h3>📅 Event Schedule</h3>
            <div class="info-grid">
                <div class="info-item">
                    <div class="info-label">Event</div>
                    <div class="info-value">{{ event.name }}</div>
                </div>
                <div class="info-item">
                    <div class="info-label">Dates</div>
                    <div class="info-value">{{ event.start_date|date:"F d, Y" }} - {{ event.end_date|date:"F d, Y" }}</div>
                </div>
                <div class="info-item">
                    <div class="info-label">Your Arrival</div>
                    <div class="info-value">{{ participant.arrival_date|date:"F d, Y H:i" }}</div>
                </div>
                <div class="info-item">
                    <div class="info-label">Your Departure</div>
                    <div class="info-value">{{ participant.departure_date|date:"F d, Y H:i" }}</div>
                </div>
            </div>
            {% if event.location %}
            <div class="contact-info">
                <strong>📍 Event Location:</strong><br>
                {{ event.location }}
            </div>
            {% endif %}
        </div>

        {% if hotel %}
        <div class="section">
            <h3>🏨 Hotel Accommodation</h3>
            <div class="info-grid">
                <div class="info-item">
                    <div class="info-label">Hotel Name</div>
                    <div class="info-value">{{ hotel.name }}</div>
                </div>
                {% if room %}
                <div class="info-item">
                    <div class="info-label">Room Number</div>
                    <div class="info-value">{{ room.room_number }} ({{ room.room_type }})</div>
                </div>
                {% endif %}
                <div class="info-item">
                    <div class="info-label">Address</div>
                    <div class="info-value">{{ hotel.address }}</div>
                </div>
                <div class="info-item">
                    <div class="info-label">Contact Person</div>
                    <div class="info-value">{{ hotel.contact_person }}</div>
                </div>
            </div>
            <div class="contact-info">
                <strong>📞 Hotel Contact:</strong><br>
                Phone: {{ hotel.phone }}<br>
                Email: {{ hotel.email }}
            </div>
        </div>
        {% endif %}

        {% if driver %}
        <div class="section">
            <h3>🚗 Transportation Details</h3>
            <div class="info-grid">
                <div class="info-item">
                    <div class="info-label">Driver Name</div>
                    <div class="info-value">{{ driver.name }}</div>
                </div>
                <div class="info-item">
                    <div class="info-label">Phone Number</div>
                    <div class="info-value">{{ driver.phone }}</div>
                </div>
                <div class="info-item">
                    <div class="info-label">Vehicle</div>
                    <div class="info-value">{{ driver.car_model }} {{ driver.car_color }}</div>
                </div>
                <div class="info-item">
                    <div class="info-label">License Plate</div>
                    <div class="info-value">{{ driver.car_plate }}</div>
                </div>
            </div>
            <div class="important-note">
                <strong>⚠️ Important:</strong> Please contact your assigned driver at least 24 hours before your arrival to coordinate pickup details.
            </div>
        </div>
        {% endif %}

        <div class="section">
            <h3>🎫 Event Badge</h3>
            <p>Your personalized event badge is attached to this email. Please:</p>
            <ul>
                <li>Print the badge on quality paper</li>
                <li>Bring it with you to the event</li>
                <li>Wear it during all event activities</li>
                <li>Use it for attendance tracking via QR code scanning</li>
            </ul>
            <div class="important-note">
                <strong>📱 QR Code:</strong> Your badge contains a unique QR code for attendance tracking. Please ensure it's clearly visible and not damaged.
            </div>
        </div>

        <div class="section">
            <h3>📋 Important Instructions</h3>
            <ul>
                <li><strong>Arrival:</strong> Please arrive at your hotel first, then proceed to the event venue</li>
                <li><strong>Check-in:</strong> Event registration will be available at the venue</li>
                <li><strong>Badge:</strong> Wear your badge at all times during the event</li>
                <li><strong>Contact:</strong> Keep your assigned driver's contact information handy</li>
                <li><strong>Emergency:</strong> Contact event organizers for any urgent matters</li>
            </ul>
        </div>

        {% if event.description %}
        <div class="section">
            <h3>ℹ️ About the Event</h3>
            <p>{{ event.description }}</p>
        </div>
        {% endif %}
    </div>

    <div class="footer">
        <p><strong>University of Gujrat - Event Management System</strong></p>
        <p>This is an automated email. Please do not reply to this message.</p>
        <p>For any questions or concerns, please contact the event organizers.</p>
    </div>
</body>
</html>
