#!/usr/bin/env python3

import os
import sys
import django

# Add the project root to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'event_management.settings')
django.setup()

from participants.models import Participant

def check_exact_ids():
    """Check which exact IDs exist in the database"""
    
    print("=== CHECKING EXACT IDS ===")
    print(f"Total participants: {Participant.objects.count()}")
    
    # Test IDs from the user's request
    test_ids = [337, 338, 339, 340, 342, 343, 344, 346, 347, 348, 349, 350, 
                511, 512, 513, 514, 515, 516, 517, 518, 519, 520, 521, 522, 523, 524, 525, 526, 527, 528, 529, 530, 531, 532, 533, 534, 535, 536, 537, 538, 539, 540, 541, 542, 543, 544, 545, 546, 547, 548, 549, 550]
    
    existing_ids = []
    missing_ids = []
    
    for test_id in test_ids:
        if Participant.objects.filter(id=test_id).exists():
            existing_ids.append(test_id)
        else:
            missing_ids.append(test_id)
    
    print(f"\nFound {len(existing_ids)} participants with exact IDs from the list")
    print(f"Missing {len(missing_ids)} participants")
    
    if existing_ids:
        print(f"\nExisting IDs: {existing_ids[:10]}{'...' if len(existing_ids) > 10 else ''}")
    
    if missing_ids:
        print(f"\nMissing IDs: {missing_ids[:10]}{'...' if len(missing_ids) > 10 else ''}")
    
    # Show some existing participants with these IDs
    if existing_ids:
        print(f"\nSample existing participants:")
        for participant_id in existing_ids[:5]:
            p = Participant.objects.get(id=participant_id)
            print(f"ID: {p.id}, Name: {p.first_name} {p.last_name}, Email: {p.email}")
    
    # Check highest ID
    highest = Participant.objects.order_by('-id').first()
    if highest:
        print(f"\nHighest ID in database: {highest.id} ({highest.first_name} {highest.last_name})")
    
    # Check ID ranges
    print(f"\nID Range Analysis:")
    print(f"IDs 1-100: {Participant.objects.filter(id__range=(1, 100)).count()}")
    print(f"IDs 101-200: {Participant.objects.filter(id__range=(101, 200)).count()}")
    print(f"IDs 300-400: {Participant.objects.filter(id__range=(300, 400)).count()}")
    print(f"IDs 500-600: {Participant.objects.filter(id__range=(500, 600)).count()}")

if __name__ == "__main__":
    check_exact_ids()
