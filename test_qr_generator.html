<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>QR Code Generator for Testing</title>
    <script src="https://cdn.jsdelivr.net/npm/qrcode@1.5.3/build/qrcode.min.js"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .qr-container {
            border: 1px solid #ddd;
            margin: 20px 0;
            padding: 20px;
            border-radius: 5px;
            text-align: center;
        }
        canvas {
            margin: 10px;
        }
        .qr-data {
            background-color: #f8f9fa;
            padding: 10px;
            border-radius: 3px;
            font-family: monospace;
            word-break: break-all;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <h1>QR Code Generator for Testing</h1>
    <p>Generate QR codes to test the email-based participant verification.</p>
    
    <div id="qr-codes"></div>

    <script>
        const testCases = [
            {
                name: "✅ Simple Email (Recommended Format)",
                data: "<EMAIL>",
                description: "Simple email - easiest to scan and verify"
            },
            {
                name: "✅ URL with Email Parameter",
                data: "https://event.uog.edu.et/events?email=ademali%40su.edu.et&name=Adem+Kabo",
                description: "URL format with email parameter - works with mobile cameras"
            },
            {
                name: "✅ Direct Email (ARAGAW)",
                data: "<EMAIL>",
                description: "Another simple email format"
            },
            {
                name: "✅ Mixed Text with Email",
                data: "<EMAIL> ADEM KABO",
                description: "Email mixed with name - extracts email automatically"
            },
            {
                name: "✅ JSON with Email",
                data: '{"email": "<EMAIL>", "name": "Adem Kabo"}',
                description: "JSON format containing email"
            },
            {
                name: "❌ No Email (Should Fail)",
                data: "ADEM KABO PARTICIPANT",
                description: "Text without email - should show error message"
            }
        ];

        function generateQRCodes() {
            const container = document.getElementById('qr-codes');
            
            testCases.forEach((testCase, index) => {
                const div = document.createElement('div');
                div.className = 'qr-container';
                
                const title = document.createElement('h3');
                title.textContent = testCase.name;
                div.appendChild(title);
                
                const description = document.createElement('p');
                description.textContent = testCase.description;
                div.appendChild(description);
                
                const dataDiv = document.createElement('div');
                dataDiv.className = 'qr-data';
                dataDiv.textContent = testCase.data;
                div.appendChild(dataDiv);
                
                const canvas = document.createElement('canvas');
                canvas.id = `qr-${index}`;
                div.appendChild(canvas);
                
                container.appendChild(div);
                
                // Generate QR code
                QRCode.toCanvas(canvas, testCase.data, {
                    width: 256,
                    margin: 2,
                    color: {
                        dark: '#000000',
                        light: '#FFFFFF'
                    }
                }, function (error) {
                    if (error) {
                        console.error('QR Code generation error:', error);
                        const errorMsg = document.createElement('p');
                        errorMsg.textContent = 'Error generating QR code';
                        errorMsg.style.color = 'red';
                        div.appendChild(errorMsg);
                    }
                });
            });
        }

        // Generate QR codes when page loads
        window.onload = generateQRCodes;
    </script>
</body>
</html>
