from django.db import models


class Driver(models.Model):
    name = models.Char<PERSON>ield(max_length=200)
    phone = models.Char<PERSON>ield(max_length=20)
    email = models.EmailField()
    photo = models.ImageField(upload_to='driver_photos/', null=True, blank=True)
    car_plate = models.CharField(max_length=20)
    car_code = models.CharField(max_length=50, help_text='Internal car identification code')
    car_model = models.CharField(max_length=100, blank=True)
    car_color = models.CharField(max_length=50, blank=True)
    license_number = models.CharField(max_length=50, blank=True)

    # Availability
    is_available = models.BooleanField(default=True)
    notes = models.TextField(blank=True)

    # Event association
    event = models.ForeignKey('events.Event', on_delete=models.CASCADE, related_name='drivers')

    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ['name']

    def __str__(self):
        return f"{self.name} - {self.car_plate}"


class DriverAssignment(models.Model):
    driver = models.ForeignKey(Driver, on_delete=models.CASCADE, related_name='assignments')
    participant = models.ForeignKey('participants.Participant', on_delete=models.CASCADE, related_name='driver_assignments')
    pickup_location = models.CharField(max_length=300)
    pickup_time = models.DateTimeField()
    dropoff_location = models.CharField(max_length=300)
    dropoff_time = models.DateTimeField(null=True, blank=True)

    # Status
    STATUS_CHOICES = [
        ('scheduled', 'Scheduled'),
        ('in_progress', 'In Progress'),
        ('completed', 'Completed'),
        ('cancelled', 'Cancelled'),
    ]
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='scheduled')

    notes = models.TextField(blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ['pickup_time']

    def __str__(self):
        return f"{self.driver.name} -> {self.participant.full_name} ({self.pickup_time})"
