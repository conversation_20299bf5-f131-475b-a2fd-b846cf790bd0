# Nginx Production Setup for UoG Event Management System

## 🚀 **Production Deployment with Nginx**

This guide explains how to deploy the University of Gondar Event Management System using Nginx as a reverse proxy for production use.

## 📋 **Prerequisites**

- Docker and Docker Compose installed
- Windows PowerShell (for deployment script)
- Network access to IP ************
- At least 4GB RAM and 20GB disk space

## 🏗️ **Architecture Overview**

```
Internet → Nginx (Port 80/443) → Backend (Django) + Frontend (React)
                ↓
            Static Files + Media Files
                ↓
            Database (PostgreSQL) + Cache (Redis)
```

### **Services:**
- **Nginx**: Reverse proxy, static file serving, SSL termination
- **Backend**: Django application (Gunicorn)
- **Frontend**: React application (built and served by <PERSON>inx)
- **Database**: PostgreSQL
- **Cache**: Redis
- **Worker**: Celery for background tasks

## 🔧 **Configuration Files**

### **1. Docker Compose Production (`docker-compose.prod.yml`)**
- Production-ready container configuration
- Nginx reverse proxy setup
- Separate networks for frontend and backend
- Volume management for static/media files

### **2. Nginx Configuration (`nginx/`)**
- **`nginx.conf`**: Main Nginx configuration
- **`conf.d/default.conf`**: HTTP server configuration
- **`conf.d/ssl.conf`**: HTTPS configuration (commented out)
- **`frontend/nginx.conf`**: Frontend container Nginx config

### **3. Environment Configuration (`.env.prod`)**
- Production environment variables
- Database credentials
- Security settings
- Email configuration

## 🚀 **Quick Deployment**

### **Step 1: Prepare Environment**
```powershell
# Copy environment template
Copy-Item ".env.prod" ".env"

# Edit .env file with your production settings
notepad .env
```

### **Step 2: Deploy with Script**
```powershell
# Build and deploy
.\deploy-production.ps1 -Build

# Or just deploy (if images already built)
.\deploy-production.ps1
```

### **Step 3: Verify Deployment**
```powershell
# Check status
.\deploy-production.ps1 -Status

# View logs
.\deploy-production.ps1 -Logs
```

## 🌐 **Access URLs**

After successful deployment:

- **Frontend Application**: `http://************`
- **Backend API**: `http://************/api/`
- **Admin Interface**: `http://************/admin/`
- **Health Check**: `http://************/health/`
- **Static Files**: `http://************/static/`
- **Media Files**: `http://************/media/`

## 🔒 **Security Features**

### **Nginx Security Headers**
- X-Frame-Options: DENY
- X-Content-Type-Options: nosniff
- X-XSS-Protection: 1; mode=block
- Referrer-Policy: strict-origin-when-cross-origin

### **Rate Limiting**
- API endpoints: 10 requests/second
- Login endpoints: 5 requests/minute

### **File Access Protection**
- Blocked access to hidden files (`.git`, `.env`, etc.)
- Blocked access to backup files
- Blocked access to version control directories

## 📊 **Performance Optimizations**

### **Caching**
- Static files: 1 year cache
- Media files: 1 month cache
- HTML files: No cache (for SPA updates)

### **Compression**
- Gzip compression enabled
- Compressed file types: HTML, CSS, JS, JSON, SVG
- Compression level: 6

### **Connection Optimization**
- Keep-alive connections
- HTTP/2 support (when SSL enabled)
- Upstream connection pooling

## 🔧 **Management Commands**

### **Deployment Script Options**
```powershell
# Build and deploy
.\deploy-production.ps1 -Build

# Check status
.\deploy-production.ps1 -Status

# View logs
.\deploy-production.ps1 -Logs

# Restart services
.\deploy-production.ps1 -Restart

# Stop services
.\deploy-production.ps1 -Stop

# Clean up (remove volumes and images)
.\deploy-production.ps1 -Clean
```

### **Manual Docker Commands**
```powershell
# Start services
docker-compose -f docker-compose.prod.yml up -d

# Stop services
docker-compose -f docker-compose.prod.yml down

# View logs
docker-compose -f docker-compose.prod.yml logs -f

# Check status
docker-compose -f docker-compose.prod.yml ps

# Restart specific service
docker-compose -f docker-compose.prod.yml restart nginx
```

## 🔐 **SSL/HTTPS Setup (Optional)**

### **Step 1: Obtain SSL Certificates**
```powershell
# Create SSL directory
mkdir nginx/ssl

# Copy your certificates
# cert.pem (certificate file)
# key.pem (private key file)
```

### **Step 2: Enable SSL Configuration**
```powershell
# Edit nginx/conf.d/ssl.conf
# Uncomment the SSL server blocks
# Update server_name with your domain
```

### **Step 3: Update Environment**
```env
# In .env file
CORS_ALLOWED_ORIGINS=https://************,https://your-domain.com
REACT_APP_API_BASE_URL=https://************/api
REACT_APP_BACKEND_URL=https://************
```

## 📈 **Monitoring and Logs**

### **Log Locations**
- **Nginx Access**: `logs/nginx/access.log`
- **Nginx Error**: `logs/nginx/error.log`
- **Backend**: `logs/backend.log`
- **Celery**: `logs/celery.log`

### **Health Checks**
```powershell
# Backend health
curl http://************/health/

# Frontend health
curl http://************/

# Database health (via backend)
curl http://************/api/health/
```

## 🛠️ **Troubleshooting**

### **Common Issues**

1. **Services not starting**
   ```powershell
   # Check logs
   .\deploy-production.ps1 -Logs
   
   # Check Docker status
   docker ps -a
   ```

2. **502 Bad Gateway**
   ```powershell
   # Check backend service
   docker-compose -f docker-compose.prod.yml logs backend
   
   # Restart backend
   docker-compose -f docker-compose.prod.yml restart backend
   ```

3. **Static files not loading**
   ```powershell
   # Check static file volumes
   docker volume ls
   
   # Rebuild with static files
   .\deploy-production.ps1 -Build
   ```

4. **Database connection issues**
   ```powershell
   # Check database logs
   docker-compose -f docker-compose.prod.yml logs db
   
   # Check environment variables
   docker-compose -f docker-compose.prod.yml config
   ```

## 🔄 **Backup and Maintenance**

### **Database Backup**
```powershell
# Create backup
docker-compose -f docker-compose.prod.yml exec db pg_dump -U postgres uog_event_prod > backup.sql

# Restore backup
docker-compose -f docker-compose.prod.yml exec -T db psql -U postgres uog_event_prod < backup.sql
```

### **Media Files Backup**
```powershell
# Backup media files
docker cp $(docker-compose -f docker-compose.prod.yml ps -q backend):/app/media ./media_backup
```

### **Update Deployment**
```powershell
# Pull latest changes
git pull

# Rebuild and deploy
.\deploy-production.ps1 -Build
```

## 📞 **Support**

For issues or questions:
1. Check the logs: `.\deploy-production.ps1 -Logs`
2. Verify service status: `.\deploy-production.ps1 -Status`
3. Review this documentation
4. Check Docker container health: `docker ps`

---

**Deployment Status**: ✅ **PRODUCTION READY**  
**Access URL**: `http://************`  
**Last Updated**: 2025-07-29
