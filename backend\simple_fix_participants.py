#!/usr/bin/env python3

import os
import sys
import django

# Add the project root to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'event_management.settings')
django.setup()

from participants.models import Participant

def simple_fix_participants():
    """Simple approach: delete placeholders first, then update IDs"""
    
    print("=== STEP 1: DELETING PLACEHOLDER PARTICIPANTS ===")
    
    # Delete all participants with placeholder emails
    placeholder_participants = Participant.objects.filter(email__contains='@example.com')
    placeholder_count = placeholder_participants.count()
    print(f"Found {placeholder_count} participants with placeholder emails")
    
    for p in placeholder_participants:
        print(f"Deleting: {p.first_name} {p.last_name} (ID: {p.id}, Email: {p.email})")
        p.delete()
    
    print(f"Deleted {placeholder_count} placeholder participants")
    print(f"Remaining participants: {Participant.objects.count()}")
    
    print("\n=== STEP 2: UPDATING PARTICIPANT IDS ===")
    
    # Mapping of (first_name, last_name) to desired ID
    name_to_id_mapping = {
        ('Abdella', 'Mohammed'): 337,
        ('Gemechis', 'Duressa'): 338,
        ('Andargachew', 'Deata'): 339,
        ('Pal', 'Dol'): 340,
        ('Yohannes', 'Ejigu'): 342,
        ('Tadiyose', 'Arba'): 343,
        ('Mengesha', 'Mersha'): 344,
        ('Tilahun', 'Retta'): 346,
        ('Tesfahun', 'Yilma'): 347,
        ('Endris', 'Ahmed'): 348,
        ('Zerihun', 'Assefa'): 349,
        ('Engidaw', 'Muche'): 350,
        ('Tesfaye', 'Feyissa'): 351,
        ('Lemma', 'Angessa'): 352,
        ('Dr. Shimelis', 'Admassie'): 353,
        ('Meba Tadesse', 'Delle'): 354,
        ('Dr. Samuel', 'Kidane'): 355,
        ('Kebede', 'Gerbi'): 356,
        ('Carly', 'Gentry'): 511,
        ('Adem', 'Kabo'): 512,
        ('Matebu', 'Jabessa'): 513,
        ('Asmamaw', 'Workneh'): 514,
        ('Dr.Megersa', 'Hussen'): 515,
        ('LAMESGIN', 'TIZAZU'): 516,
        ('Woldeamlak', 'Alemayehu'): 517,
        ('Derbew', 'Yohannes'): 518,
        ('Bizunesh', 'Borena'): 519,
        ('LIDETU', 'GOBENA'): 520,
        ('Alemayehu', 'Mekonnen'): 521,
        ('Asnake', 'Ede'): 523,
        ('Ochan', 'Agwa'): 524,
        ('Dr Abdiselam', 'Mohamed'): 525,
        ('Shelleme', 'Jiru'): 526,
        ('Nega', 'Tessemma'): 527,
        ('Dawit', 'Bezabih'): 528,
        ('Aynishet', 'Gebremariam'): 529,
        ('Lijalem', 'Abate'): 530,
        ('John', 'Firrisa'): 531,
        ('Adane', 'Tesega'): 532,
        ('Biniyam', 'Jimma'): 533,
        ('Serawit', 'Melkato'): 534,
        ('Hana', 'Kumera'): 535,
        ('Dr. Getinet', 'Ashenafi'): 536,
        ('KEBEDE', 'SHIFERAW'): 537,
        ('Zaid', 'Zewde'): 538,
        ('Dunkana', 'Kenie'): 539,
        ('Birhan', 'Miheretu'): 540,
        ('Dr Alemu', 'Ayano'): 541,
        ('Fana Hagos', 'Berhane'): 542,
        ('Ataklti', 'Gebrehiwot'): 543,
        ('Asalf', 'Wondemgezahu'): 544,
        ('Tesfaye', 'Jimma'): 545,
        ('Nebiyu', 'Teklie'): 546,
        ('Henoke', 'demese'): 547,
        ('Zewdu', 'Worku'): 548,
        ('Lediya', 'Negussie'): 549,
        ('Walelign', 'Tilahun'): 550,
    }
    
    updated_count = 0
    
    for (first_name, last_name), desired_id in name_to_id_mapping.items():
        participants = Participant.objects.filter(first_name=first_name, last_name=last_name)
        
        if not participants.exists():
            print(f"Warning: Participant {first_name} {last_name} not found")
            continue
        
        if participants.count() > 1:
            print(f"Warning: Still {participants.count()} participants for {first_name} {last_name}")
            participant = participants.first()
        else:
            participant = participants.first()
        
        old_id = participant.id
        
        # Check if desired ID is already taken
        if Participant.objects.filter(id=desired_id).exists():
            existing = Participant.objects.get(id=desired_id)
            if existing.id != participant.id:
                print(f"Warning: ID {desired_id} already taken by {existing.first_name} {existing.last_name}")
                continue
        
        # Update the ID
        try:
            participant.id = desired_id
            participant.save()
            updated_count += 1
            print(f"Updated: {first_name} {last_name} from ID {old_id} to ID {desired_id}")
        except Exception as e:
            print(f"Error updating {first_name} {last_name}: {str(e)}")
    
    # Handle second Alemayehu Mekonnen for ID 522
    alemayehu_participants = Participant.objects.filter(first_name='Alemayehu', last_name='Mekonnen')
    if alemayehu_participants.count() > 1:
        print(f"\nHandling additional Alemayehu Mekonnen participants...")
        remaining = alemayehu_participants.exclude(id=521)
        if remaining.exists():
            second_alemayehu = remaining.first()
            old_id = second_alemayehu.id
            try:
                second_alemayehu.id = 522
                second_alemayehu.save()
                print(f"Updated second Alemayehu Mekonnen from ID {old_id} to ID 522")
                updated_count += 1
            except Exception as e:
                print(f"Error updating second Alemayehu: {str(e)}")
    
    print(f"\nSummary:")
    print(f"Updated {updated_count} participant IDs")
    print(f"Total participants now: {Participant.objects.count()}")

if __name__ == "__main__":
    simple_fix_participants()
