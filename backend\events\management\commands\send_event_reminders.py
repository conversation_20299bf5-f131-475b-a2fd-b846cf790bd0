from django.core.management.base import BaseCommand
from django.utils import timezone
from datetime import datetime, timedelta
from events.models import Event
from events.email_service import get_email_service
from participants.models import Participant


class Command(BaseCommand):
    help = 'Send event reminder emails to participants'
    
    def add_arguments(self, parser):
        parser.add_argument(
            '--days-before',
            type=int,
            default=1,
            help='Number of days before event to send reminder (default: 1)',
        )
        parser.add_argument(
            '--event-id',
            type=int,
            help='Specific event ID to send reminders for. If not provided, sends for all upcoming events.',
        )
        parser.add_argument(
            '--dry-run',
            action='store_true',
            help='Show what would be sent without actually sending emails.',
        )
    
    def handle(self, *args, **options):
        days_before = options['days_before']
        target_date = timezone.now().date() + timedelta(days=days_before)
        
        self.stdout.write(
            f'Processing event reminders for events on {target_date} ({days_before} days from now)'
        )
        
        # Get events to process
        if options['event_id']:
            try:
                events = [Event.objects.get(id=options['event_id'])]
            except Event.DoesNotExist:
                self.stdout.write(
                    self.style.ERROR(f'Event with ID {options["event_id"]} not found')
                )
                return
        else:
            # Get events starting on the target date
            events = Event.objects.filter(
                start_date__date=target_date,
                is_active=True
            )
        
        if not events:
            self.stdout.write(
                self.style.WARNING(f'No events found starting on {target_date}')
            )
            return
        
        email_service = get_email_service()
        total_sent = 0
        
        for event in events:
            self.stdout.write(f'\nProcessing event: {event.name}')
            self.stdout.write(f'  Event date: {event.start_date.strftime("%Y-%m-%d %H:%M")}')
            self.stdout.write(f'  Location: {event.location}')
            
            # Get participants for this event
            participants = Participant.objects.filter(event=event)
            
            if not participants.exists():
                self.stdout.write(
                    self.style.WARNING('  No participants found for this event')
                )
                continue
            
            self.stdout.write(f'  Found {participants.count()} participants')
            
            if options['dry_run']:
                self.stdout.write(
                    self.style.SUCCESS(
                        f'  [DRY RUN] Would send reminder to {participants.count()} participants'
                    )
                )
                continue
            
            # Send reminder emails
            try:
                sent_count = email_service.send_event_reminder(participants, event, days_before)
                total_sent += sent_count
                
                self.stdout.write(
                    self.style.SUCCESS(
                        f'  Successfully sent reminders to {sent_count}/{participants.count()} participants'
                    )
                )
                
                if sent_count < participants.count():
                    self.stdout.write(
                        self.style.WARNING(
                            f'  {participants.count() - sent_count} emails failed to send'
                        )
                    )
                
            except Exception as e:
                self.stdout.write(
                    self.style.ERROR(f'  Error sending reminder emails: {str(e)}')
                )
        
        if options['dry_run']:
            self.stdout.write(
                self.style.SUCCESS('\n[DRY RUN] No emails were actually sent')
            )
        else:
            self.stdout.write(
                self.style.SUCCESS(f'\nTotal reminder emails sent: {total_sent}')
            )
        
        # Summary of email logs
        if not options['dry_run']:
            from events.models import EmailLog
            recent_logs = EmailLog.objects.filter(
                template_type='event_reminder',
                created_at__date=timezone.now().date()
            )
            
            sent_today = recent_logs.filter(status='sent').count()
            failed_today = recent_logs.filter(status='failed').count()
            
            self.stdout.write(f'\nToday\'s reminder email summary:')
            self.stdout.write(f'  Sent: {sent_today}')
            self.stdout.write(f'  Failed: {failed_today}')
            
            if failed_today > 0:
                self.stdout.write(
                    self.style.WARNING(
                        'Check email logs for details on failed emails'
                    )
                )
