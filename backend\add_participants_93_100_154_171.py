#!/usr/bin/env python3

import os
import sys
import django
from datetime import datetime

# Add the project root to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'event_management.settings')
django.setup()

from participants.models import Participant, ParticipantType
from events.models import Event
from django.db import connection

def add_participants_93_100_154_171():
    """Add participants with IDs 93-100 and 154-171"""
    
    print("=== ADDING PARTICIPANTS 93-100 AND 154-171 ===")
    
    # Get Event ID 1
    try:
        event = Event.objects.get(id=1)
    except Event.DoesNotExist:
        print("Error: Event with ID 1 not found")
        return
    
    # Get participant types
    participant_types = {}
    for pt in ParticipantType.objects.all():
        participant_types[pt.id] = pt
    
    # Participants data
    participants_data = [
        # (id, first_name, last_name, middle_name, email, phone, institution_name, participant_type_id, arrival_date, departure_date, profile_photo, remarks, status)
        (93, 'Yeshihareg', 'Merete', 'Afera', '<EMAIL>', '091 1554080', 'Wolkite University', 23, '2025-08-03 14:46:00', '2025-08-07 09:00:00', None, '', 'active'),
        (94, 'Jemal', 'Hassen', 'Yousuf', '<EMAIL>', '093 0179955', 'Haramaya University', 2, '2025-08-03 15:30:00', '2025-08-06 08:30:00', None, '', 'active'),
        (95, 'Ubah', 'Meder', 'Adem', '<EMAIL>', '092 5010210', 'Dire Dawa University', 2, '2025-08-03 13:20:00', '2025-08-06 16:45:00', None, 'DDU team has made hotel reservation at Haile resort', 'active'),
        (96, 'Diriba', 'Tujuba', 'Eticha', '<EMAIL>', '090 8300079', 'Gambella University', 2, '2025-08-03 06:02:00', '2025-08-06 06:03:00', None, '', 'active'),
        (97, 'Bogale', 'Neka', 'GebreMariam', '<EMAIL>', '091 1053852', 'Arba Minch Water Technology Institute, Arba Minch University', 23, '2025-08-03 15:55:00', '2025-08-06 10:31:00', None, '', 'active'),
        (98, 'Alemayehu', 'Tamiru', 'Bishaw', '<EMAIL>', '+251 909020572', 'Debark University', 29, '2025-08-03 04:35:00', '2025-08-07 04:42:00', None, '', 'active'),
        (99, 'Habte', 'Berry', 'Dulla', '<EMAIL>', '095 4935706', 'Wolkite University', 23, '2025-08-03 09:48:00', '2025-08-07 09:48:00', None, '', 'active'),
        (100, 'Prof. Kitessa', 'Geleta', 'Hundera', '<EMAIL>', '091 7802767', 'Assosa University', 29, '2025-08-03 12:15:00', '2025-08-07 10:20:00', None, '', 'active'),
        (154, 'Paulos', 'Shibeshi', 'Taddesse', '<EMAIL>', '093 7458433', 'Arba Minch University', 23, '2025-08-03 15:30:00', '2025-08-07 08:30:00', None, '', 'active'),
        (155, 'Dr Tolossa', 'Wedajo', 'Dadi', '<EMAIL>', '093 0364635', 'Salale University', 23, '2025-08-03 01:35:00', '2025-08-07 10:20:00', None, '', 'active'),
        (156, 'Teferi', 'Jibicho', 'Gishe', '<EMAIL>', '091 6358173', 'Ethiopian civil service university', 27, '2025-08-03 10:55:00', '2025-08-07 10:58:00', None, '', 'active'),
        (157, 'Tadewos', 'Wogasso', 'Mentta', '<EMAIL>', '093 7809795', 'Ethiopian Civil Service University', 23, '2025-08-03 15:00:00', '2025-08-07 14:00:00', None, '', 'active'),
        (158, 'engidaw', 'awoke', '', '<EMAIL>', '091 8030593', 'UoG', 4, '2025-07-31 03:48:00', '2025-08-01 03:48:00', None, '', 'deleted'),
        (159, 'Dr. Abdi', 'Hasan', 'Ahmed', '<EMAIL>', '091 1042563', 'Jigjiga University', 23, '2025-08-03 06:43:00', '2025-08-06 10:00:00', 'event-registration/profile_688ae70354260.jpeg', '', 'active'),
        (160, 'Awoke', 'Melese', 'Azanaw', '<EMAIL>', '091 8305854', 'Debark University', 27, '2025-08-03 08:31:00', '2025-08-03 08:36:00', 'event-registration/profile_688b014d0746c.jpg', '', 'active'),
        (161, 'Eba', 'Negero', 'Mijena', '<EMAIL>', '091 1110148', 'MoE', 8, '2025-08-03 03:01:00', '2025-08-08 09:02:00', None, '', 'active'),
        (162, 'Dr Zakaria', 'Mohamed', 'Abdiwali', '<EMAIL>', '091 3551873', 'University of Kabridahar', 27, '2025-08-03 10:15:00', '2025-08-06 09:15:00', 'event-registration/profile_688b0a3346015.jpg', '', 'active'),
        (163, 'Abebaw', 'Mekonnen', 'Demssie', '<EMAIL>', '092 3656322', 'Wollo university', 27, '2025-08-03 22:30:00', '2025-08-06 07:30:00', None, '', 'active'),
        (164, 'Kelelew', 'Hailemichael', 'Addisu', '<EMAIL>', '093 0318063', 'Bonga University', 23, '2025-08-03 12:15:00', '2025-08-06 10:05:00', None, '', 'active'),
        (165, 'Prof. Bizunesh', 'Borena', 'Mideksa', '<EMAIL>', '+251 944741626', 'Ambo University', 23, '2025-08-03 08:40:00', '2025-08-07 08:30:00', 'event-registration/profile_688b1b86f0095.jpg', '', 'active'),
        (166, 'Temam', 'Bartuga', 'Argaw', '<EMAIL>', '091 5666492', 'Werabe university', 25, '2025-08-03 00:55:00', '2025-08-03 05:30:00', None, '', 'active'),
        (167, 'Yesihak', 'Mummed', 'Yusuf', '<EMAIL>', '093 0760076', 'Haramaya University', 23, '2025-08-03 14:30:00', '2025-08-06 08:00:00', None, '', 'active'),
        (168, 'Eng. Abdifetah', 'Rabi', 'Ahmed', '<EMAIL>', '091 1303733', 'University', 2, '2025-08-03 03:26:00', '2025-08-04 03:50:00', 'event-registration/profile_688b2acc8d99c.jpg', '', 'active'),
        (169, 'Teklu', 'Zara', 'Wegatehu', '<EMAIL>', '+251 911004186', 'Arba Minch University', 23, '2025-08-03 15:30:00', '2025-09-07 02:30:00', 'event-registration/profile_688b2af749fb6.jpg', '', 'active'),
        (170, 'Tamirat', 'Bekele', 'Mulugeta', '<EMAIL>', '092 1335855', 'Ethiopian Police University', 2, '2025-08-03 12:30:00', '2025-09-06 12:32:00', None, 'Acting President', 'active'),
        (171, 'Ahmed', 'Abdinasir', 'Muhumed', '<EMAIL>', '091 5751700', 'University of Kabridahar', 23, '2025-08-03 22:49:00', '2025-08-06 12:50:00', None, '', 'active'),
    ]
    
    added_count = 0
    updated_count = 0
    
    with connection.cursor() as cursor:
        for p_data in participants_data:
            # Check if participant already exists
            existing_participant = Participant.objects.filter(id=p_data[0]).first()
            
            if existing_participant:
                # Update existing participant if needed
                if existing_participant.email != p_data[4]:
                    print(f"Updating email for ID {p_data[0]}: {existing_participant.email} -> {p_data[4]}")
                    existing_participant.email = p_data[4]
                    existing_participant.save()
                    updated_count += 1
                else:
                    print(f"Participant with ID {p_data[0]} already exists with correct data, skipping...")
                continue
            
            # Get participant type
            participant_type = participant_types.get(p_data[7])
            if not participant_type:
                print(f"Warning: Participant type {p_data[7]} not found for {p_data[1]} {p_data[2]}")
                continue
            
            # Parse dates
            arrival_date = None
            departure_date = None
            
            if p_data[8]:  # arrival_date
                try:
                    arrival_date = datetime.strptime(p_data[8], "%Y-%m-%d %H:%M:%S")
                except:
                    arrival_date = None
            
            if p_data[9]:  # departure_date
                try:
                    departure_date = datetime.strptime(p_data[9], "%Y-%m-%d %H:%M:%S")
                except:
                    departure_date = None
            
            # Create participant
            try:
                participant = Participant(
                    first_name=p_data[1],
                    last_name=p_data[2],
                    middle_name=p_data[3] or "",
                    email=p_data[4],
                    phone=p_data[5],
                    institution_name=p_data[6],
                    position="Participant",
                    participant_type=participant_type,
                    arrival_date=arrival_date,
                    departure_date=departure_date,
                    profile_photo=p_data[10],
                    remarks=p_data[11] or "",
                    status=p_data[12],
                    event=event
                )
                participant.save()
                
                # Set the exact ID using raw SQL
                cursor.execute(
                    "UPDATE participants_participant SET id = %s WHERE id = %s",
                    [p_data[0], participant.id]
                )
                
                added_count += 1
                print(f"Added: {p_data[1]} {p_data[2]} with ID {p_data[0]}")
                
            except Exception as e:
                print(f"Error adding {p_data[1]} {p_data[2]}: {str(e)}")
    
    print(f"\nSummary:")
    print(f"Added {added_count} new participants")
    print(f"Updated {updated_count} existing participants")
    print(f"Total participants now: {Participant.objects.count()}")

if __name__ == "__main__":
    add_participants_93_100_154_171()
