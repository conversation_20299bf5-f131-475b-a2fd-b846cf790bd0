import React, { useState, useEffect } from 'react';
import { Container, Row, Col, Card, Table, Badge, Button, Form, Alert, Spinner, Modal, Tab, Nav, ProgressBar, Dropdown, ButtonGroup } from 'react-bootstrap';
import { feedbackService, EventFeedback, FeedbackStats, eventService, Event, SessionFeedback, FeedbackTemplate } from '../services/api';
import { useToast } from '../contexts/ToastContext';

const FeedbackManagement: React.FC = () => {
  const { showToast } = useToast();

  const [feedback, setFeedback] = useState<EventFeedback[]>([]);
  const [sessionFeedback, setSessionFeedback] = useState<SessionFeedback[]>([]);
  const [feedbackTemplates, setFeedbackTemplates] = useState<FeedbackTemplate[]>([]);
  const [events, setEvents] = useState<Event[]>([]);
  const [selectedEvent, setSelectedEvent] = useState<number | null>(null);
  const [stats, setStats] = useState<FeedbackStats | null>(null);
  const [loading, setLoading] = useState(true);
  const [templatesLoading, setTemplatesLoading] = useState(false);
  const [sessionLoading, setSessionLoading] = useState(false);
  const [error, setError] = useState('');
  const [selectedFeedback, setSelectedFeedback] = useState<EventFeedback | null>(null);
  const [selectedTemplate, setSelectedTemplate] = useState<FeedbackTemplate | null>(null);
  const [showDetailModal, setShowDetailModal] = useState(false);
  const [showTemplateModal, setShowTemplateModal] = useState(false);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [itemToDelete, setItemToDelete] = useState<{ type: 'feedback' | 'template', id: number } | null>(null);
  const [activeTab, setActiveTab] = useState('overview');

  useEffect(() => {
    fetchEvents();
    fetchTemplates();
  }, []);

  useEffect(() => {
    if (selectedEvent) {
      fetchFeedback();
      fetchStats();
      if (activeTab === 'sessions') {
        fetchSessionFeedback();
      }
    }
  }, [selectedEvent, activeTab]);

  const fetchEvents = async () => {
    try {
      const response = await eventService.getEvents();
      setEvents(response.data.results || []);
      if (response.data.results && response.data.results.length > 0) {
        setSelectedEvent(response.data.results[0].id);
      }
    } catch (error: any) {
      console.error('Error fetching events:', error);
      setError('Failed to load events.');
    }
  };

  const fetchFeedback = async () => {
    if (!selectedEvent) return;
    
    try {
      setLoading(true);
      const response = await feedbackService.getEventFeedback(selectedEvent);
      setFeedback(response.data.results || []);
    } catch (error: any) {
      console.error('Error fetching feedback:', error);
      setError('Failed to load feedback.');
    } finally {
      setLoading(false);
    }
  };

  const fetchStats = async () => {
    if (!selectedEvent) return;

    try {
      const response = await feedbackService.getFeedbackStats(selectedEvent);
      setStats(response.data);
    } catch (error: any) {
      console.error('Error fetching stats:', error);
      // Don't show error for stats as it's not critical
    }
  };

  const fetchSessionFeedback = async () => {
    if (!selectedEvent) return;

    try {
      setSessionLoading(true);
      const response = await feedbackService.getSessionFeedback(selectedEvent);
      setSessionFeedback(response.data.results || []);
    } catch (error: any) {
      console.error('Error fetching session feedback:', error);
      showToast({ type: 'error', title: 'Failed to load session feedback' });
    } finally {
      setSessionLoading(false);
    }
  };

  const fetchTemplates = async () => {
    try {
      setTemplatesLoading(true);
      const response = await feedbackService.getFeedbackTemplates();
      setFeedbackTemplates(response.data.results || []);
    } catch (error: any) {
      console.error('Error fetching templates:', error);
      showToast({ type: 'error', title: 'Failed to load feedback templates' });
    } finally {
      setTemplatesLoading(false);
    }
  };

  const handleEventChange = (eventId: string) => {
    setSelectedEvent(parseInt(eventId));
  };

  const handleViewDetails = (feedbackItem: EventFeedback) => {
    setSelectedFeedback(feedbackItem);
    setShowDetailModal(true);
  };

  const handleViewTemplate = (template: FeedbackTemplate) => {
    setSelectedTemplate(template);
    setShowTemplateModal(true);
  };

  const handleDeleteFeedback = async (id: number) => {
    try {
      await feedbackService.deleteFeedback(id);
      showToast({ type: 'success', title: 'Feedback deleted successfully' });
      fetchFeedback();
    } catch (error: any) {
      console.error('Error deleting feedback:', error);
      showToast({ type: 'error', title: 'Failed to delete feedback' });
    }
  };

  const handleDeleteTemplate = async (id: number) => {
    try {
      await feedbackService.deleteTemplate(id);
      showToast({ type: 'success', title: 'Template deleted successfully' });
      fetchTemplates();
    } catch (error: any) {
      console.error('Error deleting template:', error);
      showToast({ type: 'error', title: 'Failed to delete template' });
    }
  };

  const confirmDelete = (type: 'feedback' | 'template', id: number) => {
    setItemToDelete({ type, id });
    setShowDeleteModal(true);
  };

  const executeDelete = async () => {
    if (!itemToDelete) return;

    if (itemToDelete.type === 'feedback') {
      await handleDeleteFeedback(itemToDelete.id);
    } else {
      await handleDeleteTemplate(itemToDelete.id);
    }

    setShowDeleteModal(false);
    setItemToDelete(null);
  };

  const exportFeedbackData = () => {
    if (!feedback.length) {
      showToast({ type: 'warning', title: 'No feedback data to export' });
      return;
    }

    const csvData = feedback.map(item => ({
      'Participant': item.participant_name_display,
      'Email': item.participant_email || '',
      'Institution': item.institution_name || '',
      'Position': item.position_title || '',
      'Overall Satisfaction': item.overall_satisfaction,
      'Session Relevance': item.session_relevance,
      'Speaker Quality': item.speaker_quality,
      'Venue Facilities': item.venue_facilities,
      'Technical Setup': item.technical_setup,
      'Time Management': item.time_management,
      'Transportation': item.transportation_accessibility,
      'Communication': item.pre_event_communication,
      'Average Rating': item.average_rating.toFixed(1),
      'Most Valuable Aspect': item.most_valuable_aspect || '',
      'Improvement Suggestions': item.improvement_suggestions || '',
      'Additional Feedback': item.additional_feedback || '',
      'Networking Sufficient': item.sufficient_networking,
      'Expectations Met': item.met_expectations,
      'Anonymous': item.is_anonymous ? 'Yes' : 'No',
      'Consent Given': item.consent_given ? 'Yes' : 'No',
      'Submitted Date': new Date(item.created_at).toLocaleDateString()
    }));

    const csvContent = [
      Object.keys(csvData[0]).join(','),
      ...csvData.map(row => Object.values(row).map(val => `"${val}"`).join(','))
    ].join('\n');

    const blob = new Blob([csvContent], { type: 'text/csv' });
    const url = window.URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = `feedback-${selectedEventData?.name || 'event'}-${new Date().toISOString().split('T')[0]}.csv`;
    link.click();
    window.URL.revokeObjectURL(url);

    showToast({ type: 'success', title: 'Feedback data exported successfully' });
  };

  const getRatingStars = (rating: number) => {
    return '⭐'.repeat(rating);
  };

  const getRatingColor = (rating: number) => {
    if (rating >= 4.5) return 'success';
    if (rating >= 3.5) return 'warning';
    return 'danger';
  };

  const selectedEventData = events.find(e => e.id === selectedEvent);

  return (
    <Container fluid className="py-4">
      <Row>
        <Col>
          <div className="d-flex justify-content-between align-items-center mb-4">
            <div>
              <h2 className="mb-1">
                <i className="fas fa-comment-alt me-2 text-primary"></i>
                Feedback Management
              </h2>
              <p className="text-muted mb-0">Manage and analyze event feedback</p>
            </div>
          </div>

          {error && (
            <Alert variant="danger" className="mb-4">
              <i className="fas fa-exclamation-triangle me-2"></i>
              {error}
            </Alert>
          )}

          {/* Event Selection */}
          <Card className="mb-4">
            <Card.Body>
              <Row className="align-items-center">
                <Col md={6}>
                  <Form.Group>
                    <Form.Label>Select Event</Form.Label>
                    <Form.Select
                      value={selectedEvent || ''}
                      onChange={(e) => handleEventChange(e.target.value)}
                    >
                      <option value="">Choose an event...</option>
                      {events.map(event => (
                        <option key={event.id} value={event.id}>
                          {event.name} ({new Date(event.start_date).toLocaleDateString()})
                        </option>
                      ))}
                    </Form.Select>
                  </Form.Group>
                </Col>
                <Col md={6}>
                  {selectedEventData && (
                    <div className="text-md-end">
                      <h6 className="mb-1">{selectedEventData.name}</h6>
                      <p className="text-muted mb-0">
                        <i className="fas fa-calendar me-1"></i>
                        {new Date(selectedEventData.start_date).toLocaleDateString()} - {new Date(selectedEventData.end_date).toLocaleDateString()}
                      </p>
                      <p className="text-muted mb-0">
                        <i className="fas fa-map-marker-alt me-1"></i>
                        {selectedEventData.location}, {selectedEventData.city}
                      </p>
                    </div>
                  )}
                </Col>
              </Row>
            </Card.Body>
          </Card>

          {selectedEvent && (
            <Tab.Container activeKey={activeTab} onSelect={(k) => setActiveTab(k || 'overview')}>
              <Nav variant="tabs" className="mb-4">
                <Nav.Item>
                  <Nav.Link eventKey="overview">
                    <i className="fas fa-chart-bar me-2"></i>
                    Overview & Statistics
                  </Nav.Link>
                </Nav.Item>
                <Nav.Item>
                  <Nav.Link eventKey="feedback">
                    <i className="fas fa-comments me-2"></i>
                    Event Feedback ({feedback.length})
                  </Nav.Link>
                </Nav.Item>
                <Nav.Item>
                  <Nav.Link eventKey="sessions">
                    <i className="fas fa-chalkboard-teacher me-2"></i>
                    Session Feedback
                  </Nav.Link>
                </Nav.Item>
                <Nav.Item>
                  <Nav.Link eventKey="templates">
                    <i className="fas fa-file-alt me-2"></i>
                    Templates ({feedbackTemplates.length})
                  </Nav.Link>
                </Nav.Item>
              </Nav>

              <Tab.Content>
                {/* Overview Tab */}
                <Tab.Pane eventKey="overview">
                  {/* Action Buttons */}
                  <Row className="mb-4">
                    <Col>
                      <div className="d-flex justify-content-end gap-2">
                        <Button
                          variant="outline-success"
                          onClick={exportFeedbackData}
                          disabled={!feedback.length}
                        >
                          <i className="fas fa-download me-2"></i>
                          Export Data
                        </Button>
                        <Button
                          variant="outline-primary"
                          onClick={() => window.open(`/events/${selectedEvent}/feedback-form`, '_blank')}
                        >
                          <i className="fas fa-external-link-alt me-2"></i>
                          View Feedback Form
                        </Button>
                      </div>
                    </Col>
                  </Row>

                  {stats && (
                    <>
                      {/* Main Statistics Cards */}
                      <Row className="mb-4">
                        <Col lg={3} md={6} className="mb-4">
                          <Card className="h-100 border-0 shadow-sm">
                            <Card.Body className="text-center">
                              <div className="text-primary mb-2">
                                <i className="fas fa-comments fa-2x"></i>
                              </div>
                              <h3 className="mb-1">{stats.total_feedback}</h3>
                              <p className="text-muted mb-0">Total Feedback</p>
                            </Card.Body>
                          </Card>
                        </Col>
                        <Col lg={3} md={6} className="mb-4">
                          <Card className="h-100 border-0 shadow-sm">
                            <Card.Body className="text-center">
                              <div className="text-success mb-2">
                                <i className="fas fa-star fa-2x"></i>
                              </div>
                              <h3 className="mb-1">{stats.average_overall_satisfaction?.toFixed(1)}/5</h3>
                              <p className="text-muted mb-0">Avg Satisfaction</p>
                            </Card.Body>
                          </Card>
                        </Col>
                        <Col lg={3} md={6} className="mb-4">
                          <Card className="h-100 border-0 shadow-sm">
                            <Card.Body className="text-center">
                              <div className="text-info mb-2">
                                <i className="fas fa-chalkboard-teacher fa-2x"></i>
                              </div>
                              <h3 className="mb-1">{stats.average_session_relevance?.toFixed(1)}/5</h3>
                              <p className="text-muted mb-0">Session Relevance</p>
                            </Card.Body>
                          </Card>
                        </Col>
                        <Col lg={3} md={6} className="mb-4">
                          <Card className="h-100 border-0 shadow-sm">
                            <Card.Body className="text-center">
                              <div className="text-warning mb-2">
                                <i className="fas fa-microphone fa-2x"></i>
                              </div>
                              <h3 className="mb-1">{stats.average_speaker_quality?.toFixed(1)}/5</h3>
                              <p className="text-muted mb-0">Speaker Quality</p>
                            </Card.Body>
                          </Card>
                        </Col>
                      </Row>

                      {/* Satisfaction Distribution */}
                      <Row className="mb-4">
                        <Col lg={6} className="mb-4">
                          <Card>
                            <Card.Header>
                              <h6 className="mb-0">
                                <i className="fas fa-chart-pie me-2"></i>
                                Satisfaction Distribution
                              </h6>
                            </Card.Header>
                            <Card.Body>
                              {Object.entries(stats.satisfaction_distribution || {}).map(([rating, count]) => (
                                <div key={rating} className="mb-2">
                                  <div className="d-flex justify-content-between mb-1">
                                    <span>{getRatingStars(parseInt(rating))} ({rating}/5)</span>
                                    <span>{count} responses</span>
                                  </div>
                                  <ProgressBar
                                    now={(count / stats.total_feedback) * 100}
                                    variant={getRatingColor(parseInt(rating))}
                                  />
                                </div>
                              ))}
                            </Card.Body>
                          </Card>
                        </Col>
                        <Col lg={6} className="mb-4">
                          <Card>
                            <Card.Header>
                              <h6 className="mb-0">
                                <i className="fas fa-chart-bar me-2"></i>
                                Expectations Met
                              </h6>
                            </Card.Header>
                            <Card.Body>
                              {Object.entries(stats.expectations_met_distribution || {}).map(([expectation, count]) => (
                                <div key={expectation} className="mb-2">
                                  <div className="d-flex justify-content-between mb-1">
                                    <span className="text-capitalize">{expectation}</span>
                                    <span>{count} responses</span>
                                  </div>
                                  <ProgressBar
                                    now={(count / stats.total_feedback) * 100}
                                    variant={expectation === 'yes' ? 'success' : expectation === 'somewhat' ? 'warning' : 'danger'}
                                  />
                                </div>
                              ))}
                            </Card.Body>
                          </Card>
                        </Col>
                      </Row>
                    </>
                  )}

                  {stats && stats.top_valuable_aspects.length > 0 && (
                    <Row>
                      <Col lg={6} className="mb-4">
                        <Card>
                          <Card.Header>
                            <h6 className="mb-0">
                              <i className="fas fa-thumbs-up me-2"></i>
                              Most Valuable Aspects
                            </h6>
                          </Card.Header>
                          <Card.Body>
                            {stats.top_valuable_aspects.slice(0, 5).map((aspect, index) => (
                              <div key={index} className="mb-2">
                                <small className="text-muted">#{index + 1}</small>
                                <p className="mb-1">{aspect}</p>
                                {index < stats.top_valuable_aspects.length - 1 && <hr className="my-2" />}
                              </div>
                            ))}
                          </Card.Body>
                        </Card>
                      </Col>
                      <Col lg={6} className="mb-4">
                        <Card>
                          <Card.Header>
                            <h6 className="mb-0">
                              <i className="fas fa-lightbulb me-2"></i>
                              Improvement Suggestions
                            </h6>
                          </Card.Header>
                          <Card.Body>
                            {stats.top_improvement_suggestions.slice(0, 5).map((suggestion, index) => (
                              <div key={index} className="mb-2">
                                <small className="text-muted">#{index + 1}</small>
                                <p className="mb-1">{suggestion}</p>
                                {index < stats.top_improvement_suggestions.length - 1 && <hr className="my-2" />}
                              </div>
                            ))}
                          </Card.Body>
                        </Card>
                      </Col>
                    </Row>
                  )}
                </Tab.Pane>

                {/* Feedback Details Tab */}
                <Tab.Pane eventKey="feedback">
                  <Card>
                    <Card.Header>
                      <div className="d-flex justify-content-between align-items-center">
                        <h6 className="mb-0">
                          <i className="fas fa-list me-2"></i>
                          Event Feedback Submissions ({feedback.length})
                        </h6>
                        <div className="d-flex gap-2">
                          <Button
                            variant="outline-success"
                            size="sm"
                            onClick={exportFeedbackData}
                            disabled={!feedback.length}
                          >
                            <i className="fas fa-download me-1"></i>
                            Export
                          </Button>
                          <Button
                            variant="outline-primary"
                            size="sm"
                            onClick={() => fetchFeedback()}
                          >
                            <i className="fas fa-sync-alt me-1"></i>
                            Refresh
                          </Button>
                        </div>
                      </div>
                    </Card.Header>
                    <Card.Body className="p-0">
                      {loading ? (
                        <div className="text-center py-5">
                          <Spinner animation="border" variant="primary" />
                          <p className="mt-3 text-muted">Loading feedback...</p>
                        </div>
                      ) : feedback.length === 0 ? (
                        <div className="text-center py-5">
                          <i className="fas fa-comment-slash fa-3x text-muted mb-3"></i>
                          <h5 className="text-muted">No Feedback Yet</h5>
                          <p className="text-muted">No feedback has been submitted for this event.</p>
                        </div>
                      ) : (
                        <div className="table-responsive">
                          <Table hover className="mb-0">
                            <thead className="bg-light">
                              <tr>
                                <th>Participant</th>
                                <th>Institution</th>
                                <th>Overall Rating</th>
                                <th>Average Rating</th>
                                <th>Submitted</th>
                                <th>Actions</th>
                              </tr>
                            </thead>
                            <tbody>
                              {feedback.map((item) => (
                                <tr key={item.id}>
                                  <td>
                                    <div>
                                      <strong>{item.participant_name_display}</strong>
                                      {item.is_anonymous && (
                                        <Badge bg="secondary" className="ms-2">Anonymous</Badge>
                                      )}
                                    </div>
                                    {item.participant_email && (
                                      <small className="text-muted">{item.participant_email}</small>
                                    )}
                                  </td>
                                  <td>
                                    {item.institution_name || (
                                      <span className="text-muted">Not provided</span>
                                    )}
                                  </td>
                                  <td>
                                    <div className="d-flex align-items-center">
                                      <span className="me-2">{getRatingStars(item.overall_satisfaction)}</span>
                                      <Badge bg={getRatingColor(item.overall_satisfaction)}>
                                        {item.overall_satisfaction}/5
                                      </Badge>
                                    </div>
                                  </td>
                                  <td>
                                    <Badge bg={getRatingColor(item.average_rating)}>
                                      {item.average_rating.toFixed(1)}/5
                                    </Badge>
                                  </td>
                                  <td>
                                    <small className="text-muted">
                                      {new Date(item.created_at).toLocaleDateString()}
                                    </small>
                                  </td>
                                  <td>
                                    <ButtonGroup size="sm">
                                      <Button
                                        variant="outline-primary"
                                        onClick={() => handleViewDetails(item)}
                                      >
                                        <i className="fas fa-eye me-1"></i>
                                        View
                                      </Button>
                                      <Button
                                        variant="outline-danger"
                                        onClick={() => confirmDelete('feedback', item.id)}
                                      >
                                        <i className="fas fa-trash"></i>
                                      </Button>
                                    </ButtonGroup>
                                  </td>
                                </tr>
                              ))}
                            </tbody>
                          </Table>
                        </div>
                      )}
                    </Card.Body>
                  </Card>
                </Tab.Pane>

                {/* Session Feedback Tab */}
                <Tab.Pane eventKey="sessions">
                  <Card>
                    <Card.Header>
                      <div className="d-flex justify-content-between align-items-center">
                        <h6 className="mb-0">
                          <i className="fas fa-chalkboard-teacher me-2"></i>
                          Session Feedback ({sessionFeedback.length})
                        </h6>
                        <Button
                          variant="outline-primary"
                          size="sm"
                          onClick={() => fetchSessionFeedback()}
                        >
                          <i className="fas fa-sync-alt me-1"></i>
                          Refresh
                        </Button>
                      </div>
                    </Card.Header>
                    <Card.Body className="p-0">
                      {sessionLoading ? (
                        <div className="text-center py-5">
                          <Spinner animation="border" variant="primary" />
                          <p className="mt-3 text-muted">Loading session feedback...</p>
                        </div>
                      ) : sessionFeedback.length === 0 ? (
                        <div className="text-center py-5">
                          <i className="fas fa-chalkboard fa-3x text-muted mb-3"></i>
                          <h5 className="text-muted">No Session Feedback</h5>
                          <p className="text-muted">No session-specific feedback has been submitted yet.</p>
                        </div>
                      ) : (
                        <div className="table-responsive">
                          <Table hover className="mb-0">
                            <thead className="bg-light">
                              <tr>
                                <th>Session</th>
                                <th>Speaker</th>
                                <th>Content Quality</th>
                                <th>Speaker Effectiveness</th>
                                <th>Average Rating</th>
                                <th>Submitted</th>
                              </tr>
                            </thead>
                            <tbody>
                              {sessionFeedback.map((item) => (
                                <tr key={item.id}>
                                  <td>
                                    <strong>{item.session_title}</strong>
                                  </td>
                                  <td>{item.session_speaker}</td>
                                  <td>
                                    <div className="d-flex align-items-center">
                                      <span className="me-2">{getRatingStars(item.content_quality)}</span>
                                      <Badge bg={getRatingColor(item.content_quality)}>
                                        {item.content_quality}/5
                                      </Badge>
                                    </div>
                                  </td>
                                  <td>
                                    <div className="d-flex align-items-center">
                                      <span className="me-2">{getRatingStars(item.speaker_effectiveness)}</span>
                                      <Badge bg={getRatingColor(item.speaker_effectiveness)}>
                                        {item.speaker_effectiveness}/5
                                      </Badge>
                                    </div>
                                  </td>
                                  <td>
                                    <Badge bg={getRatingColor(item.average_rating)}>
                                      {item.average_rating.toFixed(1)}/5
                                    </Badge>
                                  </td>
                                  <td>
                                    <small className="text-muted">
                                      {new Date(item.created_at).toLocaleDateString()}
                                    </small>
                                  </td>
                                </tr>
                              ))}
                            </tbody>
                          </Table>
                        </div>
                      )}
                    </Card.Body>
                  </Card>
                </Tab.Pane>

                {/* Templates Tab */}
                <Tab.Pane eventKey="templates">
                  <Card>
                    <Card.Header>
                      <div className="d-flex justify-content-between align-items-center">
                        <h6 className="mb-0">
                          <i className="fas fa-file-alt me-2"></i>
                          Feedback Templates ({feedbackTemplates.length})
                        </h6>
                        <Button
                          variant="outline-primary"
                          size="sm"
                          onClick={() => fetchTemplates()}
                        >
                          <i className="fas fa-sync-alt me-1"></i>
                          Refresh
                        </Button>
                      </div>
                    </Card.Header>
                    <Card.Body className="p-0">
                      {templatesLoading ? (
                        <div className="text-center py-5">
                          <Spinner animation="border" variant="primary" />
                          <p className="mt-3 text-muted">Loading templates...</p>
                        </div>
                      ) : feedbackTemplates.length === 0 ? (
                        <div className="text-center py-5">
                          <i className="fas fa-file-alt fa-3x text-muted mb-3"></i>
                          <h5 className="text-muted">No Templates</h5>
                          <p className="text-muted">No feedback templates have been created yet.</p>
                        </div>
                      ) : (
                        <div className="table-responsive">
                          <Table hover className="mb-0">
                            <thead className="bg-light">
                              <tr>
                                <th>Name</th>
                                <th>Type</th>
                                <th>Status</th>
                                <th>Default</th>
                                <th>Created</th>
                                <th>Actions</th>
                              </tr>
                            </thead>
                            <tbody>
                              {feedbackTemplates.map((template) => (
                                <tr key={template.id}>
                                  <td>
                                    <strong>{template.name}</strong>
                                    {template.description && (
                                      <div>
                                        <small className="text-muted">{template.description}</small>
                                      </div>
                                    )}
                                  </td>
                                  <td>
                                    <Badge bg="info" className="text-capitalize">
                                      {template.template_type}
                                    </Badge>
                                  </td>
                                  <td>
                                    <Badge bg={template.is_active ? 'success' : 'secondary'}>
                                      {template.is_active ? 'Active' : 'Inactive'}
                                    </Badge>
                                  </td>
                                  <td>
                                    {template.is_default && (
                                      <Badge bg="warning">Default</Badge>
                                    )}
                                  </td>
                                  <td>
                                    <small className="text-muted">
                                      {new Date(template.created_at).toLocaleDateString()}
                                    </small>
                                  </td>
                                  <td>
                                    <ButtonGroup size="sm">
                                      <Button
                                        variant="outline-primary"
                                        onClick={() => handleViewTemplate(template)}
                                      >
                                        <i className="fas fa-eye me-1"></i>
                                        View
                                      </Button>
                                      <Button
                                        variant="outline-danger"
                                        onClick={() => confirmDelete('template', template.id)}
                                        disabled={template.is_default}
                                      >
                                        <i className="fas fa-trash"></i>
                                      </Button>
                                    </ButtonGroup>
                                  </td>
                                </tr>
                              ))}
                            </tbody>
                          </Table>
                        </div>
                      )}
                    </Card.Body>
                  </Card>
                </Tab.Pane>
              </Tab.Content>
            </Tab.Container>
          )}
        </Col>
      </Row>

      {/* Feedback Detail Modal */}
      <Modal show={showDetailModal} onHide={() => setShowDetailModal(false)} size="lg">
        <Modal.Header closeButton>
          <Modal.Title>
            <i className="fas fa-comment-alt me-2"></i>
            Feedback Details
          </Modal.Title>
        </Modal.Header>
        <Modal.Body>
          {selectedFeedback && (
            <div>
              {/* Participant Info */}
              <div className="mb-4 p-3 bg-light rounded">
                <h6 className="text-primary mb-2">Participant Information</h6>
                <Row>
                  <Col md={6}>
                    <p><strong>Name:</strong> {selectedFeedback.participant_name_display}</p>
                    <p><strong>Email:</strong> {selectedFeedback.participant_email || 'Not provided'}</p>
                  </Col>
                  <Col md={6}>
                    <p><strong>Institution:</strong> {selectedFeedback.institution_name || 'Not provided'}</p>
                    <p><strong>Position:</strong> {selectedFeedback.position_title || 'Not provided'}</p>
                  </Col>
                </Row>
              </div>

              {/* Ratings */}
              <div className="mb-4">
                <h6 className="text-primary mb-3">Ratings</h6>
                <Row>
                  <Col md={6}>
                    <p><strong>Overall Satisfaction:</strong> {getRatingStars(selectedFeedback.overall_satisfaction)} ({selectedFeedback.overall_satisfaction}/5)</p>
                    <p><strong>Session Relevance:</strong> {getRatingStars(selectedFeedback.session_relevance)} ({selectedFeedback.session_relevance}/5)</p>
                    <p><strong>Speaker Quality:</strong> {getRatingStars(selectedFeedback.speaker_quality)} ({selectedFeedback.speaker_quality}/5)</p>
                  </Col>
                  <Col md={6}>
                    <p><strong>Venue & Facilities:</strong> {getRatingStars(selectedFeedback.venue_facilities)} ({selectedFeedback.venue_facilities}/5)</p>
                    <p><strong>Time Management:</strong> {getRatingStars(selectedFeedback.time_management)} ({selectedFeedback.time_management}/5)</p>
                    <p><strong>Average Rating:</strong> <Badge bg={getRatingColor(selectedFeedback.average_rating)}>{selectedFeedback.average_rating.toFixed(1)}/5</Badge></p>
                  </Col>
                </Row>
              </div>

              {/* Comments */}
              <div className="mb-4">
                <h6 className="text-primary mb-3">Comments & Feedback</h6>
                
                {selectedFeedback.most_valuable_aspect && (
                  <div className="mb-3">
                    <strong>Most Valuable Aspect:</strong>
                    <p className="mt-1 p-2 bg-light rounded">{selectedFeedback.most_valuable_aspect}</p>
                  </div>
                )}
                
                {selectedFeedback.improvement_suggestions && (
                  <div className="mb-3">
                    <strong>Improvement Suggestions:</strong>
                    <p className="mt-1 p-2 bg-light rounded">{selectedFeedback.improvement_suggestions}</p>
                  </div>
                )}
                
                {selectedFeedback.additional_feedback && (
                  <div className="mb-3">
                    <strong>Additional Feedback:</strong>
                    <p className="mt-1 p-2 bg-light rounded">{selectedFeedback.additional_feedback}</p>
                  </div>
                )}
              </div>

              {/* Metadata */}
              <div className="text-muted">
                <small>
                  <strong>Submitted:</strong> {new Date(selectedFeedback.created_at).toLocaleString()} | 
                  <strong> Consent Given:</strong> {selectedFeedback.consent_given ? 'Yes' : 'No'} | 
                  <strong> Anonymous:</strong> {selectedFeedback.is_anonymous ? 'Yes' : 'No'}
                </small>
              </div>
            </div>
          )}
        </Modal.Body>
        <Modal.Footer>
          <Button variant="secondary" onClick={() => setShowDetailModal(false)}>
            Close
          </Button>
        </Modal.Footer>
      </Modal>

      {/* Template Detail Modal */}
      <Modal show={showTemplateModal} onHide={() => setShowTemplateModal(false)} size="lg">
        <Modal.Header closeButton>
          <Modal.Title>
            <i className="fas fa-file-alt me-2"></i>
            Template Details
          </Modal.Title>
        </Modal.Header>
        <Modal.Body>
          {selectedTemplate && (
            <div>
              {/* Template Info */}
              <div className="mb-4 p-3 bg-light rounded">
                <h6 className="text-primary mb-2">Template Information</h6>
                <Row>
                  <Col md={6}>
                    <p><strong>Name:</strong> {selectedTemplate.name}</p>
                    <p><strong>Type:</strong> <Badge bg="info" className="text-capitalize">{selectedTemplate.template_type}</Badge></p>
                  </Col>
                  <Col md={6}>
                    <p><strong>Status:</strong> <Badge bg={selectedTemplate.is_active ? 'success' : 'secondary'}>{selectedTemplate.is_active ? 'Active' : 'Inactive'}</Badge></p>
                    <p><strong>Default:</strong> {selectedTemplate.is_default ? <Badge bg="warning">Yes</Badge> : 'No'}</p>
                  </Col>
                </Row>
                {selectedTemplate.description && (
                  <p><strong>Description:</strong> {selectedTemplate.description}</p>
                )}
              </div>

              {/* Template Configuration */}
              <div className="mb-4">
                <h6 className="text-primary mb-3">Form Configuration</h6>
                <div className="p-3 bg-light rounded">
                  <pre className="mb-0" style={{ fontSize: '0.875rem', maxHeight: '300px', overflow: 'auto' }}>
                    {JSON.stringify(selectedTemplate.form_config, null, 2)}
                  </pre>
                </div>
              </div>

              {/* Metadata */}
              <div className="text-muted">
                <small>
                  <strong>Created:</strong> {new Date(selectedTemplate.created_at).toLocaleString()} |
                  <strong> Updated:</strong> {new Date(selectedTemplate.updated_at).toLocaleString()}
                </small>
              </div>
            </div>
          )}
        </Modal.Body>
        <Modal.Footer>
          <Button variant="secondary" onClick={() => setShowTemplateModal(false)}>
            Close
          </Button>
        </Modal.Footer>
      </Modal>

      {/* Delete Confirmation Modal */}
      <Modal show={showDeleteModal} onHide={() => setShowDeleteModal(false)}>
        <Modal.Header closeButton>
          <Modal.Title>
            <i className="fas fa-exclamation-triangle me-2 text-warning"></i>
            Confirm Delete
          </Modal.Title>
        </Modal.Header>
        <Modal.Body>
          <p>Are you sure you want to delete this {itemToDelete?.type}?</p>
          <div className="alert alert-warning">
            <i className="fas fa-exclamation-triangle me-2"></i>
            This action cannot be undone.
          </div>
        </Modal.Body>
        <Modal.Footer>
          <Button variant="secondary" onClick={() => setShowDeleteModal(false)}>
            Cancel
          </Button>
          <Button variant="danger" onClick={executeDelete}>
            <i className="fas fa-trash me-2"></i>
            Delete
          </Button>
        </Modal.Footer>
      </Modal>
    </Container>
  );
};

export default FeedbackManagement;
