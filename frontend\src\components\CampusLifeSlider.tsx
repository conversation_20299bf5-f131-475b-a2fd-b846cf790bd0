import React, { useState, useEffect } from 'react';
import { GalleryImage, getMediaUrl } from '../services/api';

interface CampusSlideData extends Partial<GalleryImage> {
  stats?: {
    [key: string]: string;
  };
}

interface CampusLifeSliderProps {
  images: CampusSlideData[];
  autoPlay?: boolean;
  interval?: number;
}

const CampusLifeSlider: React.FC<CampusLifeSliderProps> = ({ 
  images, 
  autoPlay = true, 
  interval = 6000 
}) => {
  const [currentSlide, setCurrentSlide] = useState(0);
  const [isPlaying, setIsPlaying] = useState(autoPlay);
  const [slideDirection, setSlideDirection] = useState<'next' | 'prev'>('next');

  // Auto-play functionality
  useEffect(() => {
    if (!isPlaying || images.length <= 1) return;

    const timer = setInterval(() => {
      setSlideDirection('next');
      setCurrentSlide((prev) => (prev + 1) % images.length);
    }, interval);

    return () => clearInterval(timer);
  }, [isPlaying, interval, images.length]);

  const goToSlide = (index: number) => {
    if (index === currentSlide) return;
    setSlideDirection(index > currentSlide ? 'next' : 'prev');
    setCurrentSlide(index);
  };

  const nextSlide = () => {
    setSlideDirection('next');
    setCurrentSlide((prev) => (prev + 1) % images.length);
  };

  const prevSlide = () => {
    setSlideDirection('prev');
    setCurrentSlide((prev) => (prev - 1 + images.length) % images.length);
  };

  const togglePlayPause = () => {
    setIsPlaying(!isPlaying);
  };

  if (!images || images.length === 0) {
    return (
      <div className="campus-slider-empty">
        <div className="empty-state">
          <i className="fas fa-images fa-3x text-muted mb-3"></i>
          <h4 className="text-muted">No Campus Images Available</h4>
          <p className="text-muted">Campus life images will appear here when available.</p>
        </div>
      </div>
    );
  }

  const currentImage = images[currentSlide];

  return (
    <div className="campus-life-slider-container">
      {/* Main Slider */}
      <div className="campus-slider-main">
        <div className="slider-viewport">
          {images.map((image, index) => (
            <div
              key={image.id}
              className={`slide ${index === currentSlide ? 'active' : ''} ${
                index === currentSlide ? slideDirection : ''
              }`}
            >
              <div className="slide-background">
                <img
                  src={image.image?.startsWith('http') ? image.image : getMediaUrl(image.image || '')}
                  alt={image.title}
                  className="slide-image"
                />
                <div className="slide-overlay"></div>
              </div>
              
              <div className="slide-content">
                <div className="container">
                  <div className="row align-items-center min-vh-100">
                    <div className="col-lg-6">
                      <div className="slide-info">
                        <div className="slide-meta">
                          <span className="slide-category">{image.category_name}</span>
                          {image.location && (
                            <span className="slide-location">
                              <i className="fas fa-map-marker-alt"></i>
                              {image.location}
                            </span>
                          )}
                        </div>
                        
                        <h1 className="slide-title">{image.title}</h1>
                        <p className="slide-description">{image.description}</p>
                        
                        {image.stats && (
                          <div className="slide-stats">
                            {Object.entries(image.stats).map(([key, value]) => (
                              <div key={key} className="stat-item">
                                <span className="stat-value">{value}</span>
                                <span className="stat-label">{key.charAt(0).toUpperCase() + key.slice(1)}</span>
                              </div>
                            ))}
                          </div>
                        )}
                        
                        <div className="slide-actions">
                          <button className="btn btn-primary btn-lg">
                            <i className="fas fa-play me-2"></i>
                            Take Virtual Tour
                          </button>
                          <button className="btn btn-outline-light btn-lg">
                            <i className="fas fa-info-circle me-2"></i>
                            Learn More
                          </button>
                        </div>
                        
                        {(image.photographer || image.date_taken) && (
                          <div className="slide-credits">
                            {image.photographer && (
                              <span className="photographer">
                                <i className="fas fa-camera me-1"></i>
                                {image.photographer}
                              </span>
                            )}
                            {image.date_taken && (
                              <span className="date-taken">
                                <i className="fas fa-calendar me-1"></i>
                                {new Date(image.date_taken).toLocaleDateString()}
                              </span>
                            )}
                          </div>
                        )}
                      </div>
                    </div>
                    
                    <div className="col-lg-6">
                      <div className="slide-visual">
                        <div className="visual-frame">
                          <img
                            src={image.image?.startsWith('http') ? image.image : getMediaUrl(image.image || '')}
                            alt={image.title}
                            className="featured-image"
                          />
                          {image.is_featured && (
                            <div className="featured-badge">
                              <i className="fas fa-star"></i>
                              <span>Featured</span>
                            </div>
                          )}
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
        
        {/* Navigation Controls */}
        <div className="slider-controls">
          <button 
            className="control-btn prev-btn" 
            onClick={prevSlide}
            aria-label="Previous slide"
          >
            <i className="fas fa-chevron-left"></i>
          </button>
          
          <button 
            className="control-btn next-btn" 
            onClick={nextSlide}
            aria-label="Next slide"
          >
            <i className="fas fa-chevron-right"></i>
          </button>
          
          <button 
            className="control-btn play-pause-btn" 
            onClick={togglePlayPause}
            aria-label={isPlaying ? 'Pause slideshow' : 'Play slideshow'}
          >
            <i className={`fas fa-${isPlaying ? 'pause' : 'play'}`}></i>
          </button>
        </div>
        
        {/* Progress Bar */}
        <div className="slider-progress">
          <div 
            className="progress-bar"
            style={{
              width: `${((currentSlide + 1) / images.length) * 100}%`
            }}
          ></div>
        </div>
      </div>
      
      {/* Thumbnail Navigation */}
      <div className="slider-thumbnails">
        <div className="thumbnails-container">
          {images.map((image, index) => (
            <button
              key={image.id}
              className={`thumbnail ${index === currentSlide ? 'active' : ''}`}
              onClick={() => goToSlide(index)}
              aria-label={`Go to slide ${index + 1}: ${image.title}`}
            >
              <img
                src={image.image?.startsWith('http') ? image.image : getMediaUrl(image.image || '')}
                alt={image.title}
                className="thumbnail-image"
              />
              <div className="thumbnail-overlay">
                <span className="thumbnail-title">{image.title}</span>
                <span className="thumbnail-category">{image.category_name}</span>
              </div>
            </button>
          ))}
        </div>
      </div>
      
      {/* Slide Counter */}
      <div className="slide-counter">
        <span className="current">{String(currentSlide + 1).padStart(2, '0')}</span>
        <span className="separator">/</span>
        <span className="total">{String(images.length).padStart(2, '0')}</span>
      </div>
    </div>
  );
};

export default CampusLifeSlider;
