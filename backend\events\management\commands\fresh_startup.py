from django.core.management.base import BaseCommand
from django.utils import timezone
from datetime import timedelta
from authentication.models import CustomUser
from events.models import Event, EventSchedule, EmailConfiguration, EmailTemplate
from participants.models import ParticipantType
from hotels.models import Hotel, HotelRoom
from drivers.models import Driver
from contact_persons.models import ContactPerson
from organizations.models import Organization
from badges.models import BadgeTemplate
import os


class Command(BaseCommand):
    help = 'Fresh startup script - rebuilds everything with University of Gondar data'

    def handle(self, *args, **options):
        self.stdout.write(self.style.WARNING('🚀 FRESH STARTUP - University of Gondar Event Management System'))
        self.stdout.write(self.style.WARNING('=' * 80))
        self.stdout.write(self.style.WARNING('⚠️  This will clear existing data and create fresh setup!'))
        
        # Clear existing data
        self.clear_existing_data()
        
        # Create fresh data
        self.create_organization()
        self.create_participant_types()
        self.create_events()
        self.create_gondar_hotels()
        self.create_drivers()
        self.create_contact_persons()
        self.setup_smtp_config()
        self.create_email_templates()
        self.create_badge_template()
        self.create_admin_user()
        
        self.stdout.write(self.style.SUCCESS('✅ Fresh startup completed successfully!'))
        self.stdout.write(self.style.SUCCESS('🎉 University of Gondar Event Management System is ready!'))

    def clear_existing_data(self):
        """Clear existing data for fresh start"""
        self.stdout.write(self.style.WARNING('🧹 Clearing existing data...'))
        
        # Clear in proper order to avoid foreign key constraints
        models_to_clear = [
            'HotelRoom', 'Hotel', 'Driver', 'ContactPerson', 
            'EventSchedule', 'Event', 'ParticipantType',
            'EmailTemplate', 'EmailConfiguration', 'BadgeTemplate'
        ]
        
        for model_name in models_to_clear:
            try:
                if model_name == 'HotelRoom':
                    from hotels.models import HotelRoom
                    HotelRoom.objects.all().delete()
                elif model_name == 'Hotel':
                    Hotel.objects.all().delete()
                elif model_name == 'Driver':
                    Driver.objects.all().delete()
                elif model_name == 'ContactPerson':
                    ContactPerson.objects.all().delete()
                elif model_name == 'EventSchedule':
                    EventSchedule.objects.all().delete()
                elif model_name == 'Event':
                    Event.objects.all().delete()
                elif model_name == 'ParticipantType':
                    ParticipantType.objects.all().delete()
                elif model_name == 'EmailTemplate':
                    EmailTemplate.objects.all().delete()
                elif model_name == 'EmailConfiguration':
                    EmailConfiguration.objects.all().delete()
                elif model_name == 'BadgeTemplate':
                    BadgeTemplate.objects.all().delete()
                    
                self.stdout.write(f'  ✓ Cleared {model_name}')
            except Exception as e:
                self.stdout.write(f'  ⚠️  Warning clearing {model_name}: {e}')

    def create_organization(self):
        """Create University of Gondar as the primary organization"""
        self.stdout.write(self.style.SUCCESS('🏛️  Creating University of Gondar organization...'))
        
        # Clear existing organizations
        Organization.objects.all().delete()
        
        org = Organization.objects.create(
            name='University of Gondar',
            short_name='UoG',
            motto='Excellence in Education, Research and Community Service',
            description='The University of Gondar is a leading public university in Ethiopia, committed to providing quality education, conducting innovative research, and serving the community.',
            email='<EMAIL>',
            phone='+251-58-114-1240',
            website='https://www.uog.edu.et',
            address_line_1='University of Gondar',
            address_line_2='Main Campus',
            city='Gondar',
            state_province='Amhara Region',
            postal_code='196',
            country='Ethiopia',
            is_primary=True,
            is_active=True
        )
        
        self.stdout.write(f'  ✓ Created organization: {org.name}')

    def create_participant_types(self):
        """Create participant types"""
        self.stdout.write(self.style.SUCCESS('👥 Creating participant types...'))
        
        participant_types = [
            {'name': 'Faculty', 'description': 'University faculty members and professors', 'color': '#dc3545'},
            {'name': 'Student', 'description': 'University students (undergraduate and graduate)', 'color': '#0d6efd'},
            {'name': 'Researcher', 'description': 'Research scholars and scientists', 'color': '#198754'},
            {'name': 'Administrator', 'description': 'University administrative staff', 'color': '#ffc107'},
            {'name': 'Guest Speaker', 'description': 'Invited speakers and keynote presenters', 'color': '#6f42c1'},
            {'name': 'External Participant', 'description': 'Participants from other institutions', 'color': '#fd7e14'},
            {'name': 'VIP', 'description': 'VIP guests and dignitaries', 'color': '#e83e8c'},
            {'name': 'Media', 'description': 'Press and media representatives', 'color': '#20c997'},
        ]

        for pt_data in participant_types:
            pt = ParticipantType.objects.create(**pt_data)
            self.stdout.write(f'  ✓ Created participant type: {pt.name}')

    def create_events(self):
        """Create University of Gondar events"""
        self.stdout.write(self.style.SUCCESS('📅 Creating University of Gondar events...'))
        
        now = timezone.now()
        events_data = [
            {
                'name': 'University of Gondar Annual Academic Conference 2025',
                'description': 'The premier annual academic conference showcasing research excellence, innovation, and scholarly achievements at the University of Gondar. Join faculty, researchers, and students for presentations, workshops, and networking opportunities.',
                'start_date': now + timedelta(days=30),
                'end_date': now + timedelta(days=33),
                'location': 'University of Gondar Main Campus - Conference Hall',
                'city': 'Gondar',
                'country': 'Ethiopia',
                'latitude': 12.6090,
                'longitude': 37.4692,
                'organizer_name': 'Dr. Alemayehu Teshome',
                'organizer_email': '<EMAIL>',
                'organizer_phone': '+251-58-114-1240',
                'is_active': True,
            },
            {
                'name': 'UoG Research Innovation Summit 2025',
                'description': 'A dynamic summit focusing on cutting-edge research, innovation, and technology transfer at the University of Gondar. Featuring research presentations, poster sessions, and industry partnerships.',
                'start_date': now + timedelta(days=60),
                'end_date': now + timedelta(days=62),
                'location': 'University of Gondar Science and Technology Park',
                'city': 'Gondar',
                'country': 'Ethiopia',
                'latitude': 12.6090,
                'longitude': 37.4692,
                'organizer_name': 'Prof. Birtukan Assefa',
                'organizer_email': '<EMAIL>',
                'organizer_phone': '+251-58-114-1241',
                'is_active': True,
            }
        ]

        for event_data in events_data:
            event = Event.objects.create(**event_data)
            self.stdout.write(f'  ✓ Created event: {event.name}')
            
            # Create sample schedule for the event
            self.create_sample_schedule(event)

    def create_sample_schedule(self, event):
        """Create sample schedule for an event"""
        schedule_items = [
            {
                'title': 'Registration & Welcome Coffee',
                'description': 'Participant registration and networking session',
                'start_time': event.start_date.replace(hour=8, minute=0),
                'end_time': event.start_date.replace(hour=9, minute=0),
                'location': 'Main Lobby',
                'session_type': 'registration'
            },
            {
                'title': 'Opening Ceremony',
                'description': 'Official opening ceremony with keynote address',
                'start_time': event.start_date.replace(hour=9, minute=0),
                'end_time': event.start_date.replace(hour=10, minute=30),
                'location': 'Main Auditorium',
                'session_type': 'ceremony'
            },
            {
                'title': 'Research Presentations - Session 1',
                'description': 'Faculty research presentations and discussions',
                'start_time': event.start_date.replace(hour=11, minute=0),
                'end_time': event.start_date.replace(hour=12, minute=30),
                'location': 'Conference Hall A',
                'session_type': 'presentation'
            }
        ]

        for schedule_data in schedule_items:
            EventSchedule.objects.create(event=event, **schedule_data)

    def create_gondar_hotels(self):
        """Create hotels in Gondar"""
        self.stdout.write(self.style.SUCCESS('🏨 Creating Gondar hotels...'))

        # Get the first event for hotel association
        event = Event.objects.first()

        gondar_hotels = [
            {
                'name': 'Goha Hotel',
                'address': 'Piazza Area, Gondar, Amhara Region, Ethiopia',
                'phone': '+251-58-111-1636',
                'email': '<EMAIL>',
                'contact_person': 'Ato Mulugeta Tadesse',
                'latitude': 12.6090,
                'longitude': 37.4692,
                'star_rating': 4,
                'website': 'https://gohahotel.com',
                'description': 'Historic hotel in the heart of Gondar with traditional Ethiopian hospitality and modern amenities.',
                'is_active': True,
                'event': event
            },
            {
                'name': 'Taye Hotel Gondar',
                'address': 'Arada Sub-city, Gondar, Amhara Region, Ethiopia',
                'phone': '+251-58-111-4521',
                'email': '<EMAIL>',
                'contact_person': 'W/ro Almaz Bekele',
                'latitude': 12.6050,
                'longitude': 37.4650,
                'star_rating': 3,
                'website': 'https://tayehotel.com',
                'description': 'Comfortable accommodation with excellent service and proximity to historical sites.',
                'is_active': True,
                'event': event
            },
            {
                'name': 'AG Hotel',
                'address': 'Kebele 01, Gondar, Amhara Region, Ethiopia',
                'phone': '+251-58-111-7890',
                'email': '<EMAIL>',
                'contact_person': 'Ato Getachew Alemu',
                'latitude': 12.6120,
                'longitude': 37.4720,
                'star_rating': 3,
                'website': 'https://aghotel.et',
                'description': 'Modern hotel offering comfortable rooms and conference facilities.',
                'is_active': True,
                'event': event
            },
            {
                'name': 'Fasil Lodge',
                'address': 'Near Fasil Ghebbi, Gondar, Amhara Region, Ethiopia',
                'phone': '+251-58-111-2345',
                'email': '<EMAIL>',
                'contact_person': 'W/ro Tigist Haile',
                'latitude': 12.6080,
                'longitude': 37.4680,
                'star_rating': 4,
                'website': 'https://fasillodge.com',
                'description': 'Boutique lodge near the Royal Enclosure with authentic Ethiopian architecture.',
                'is_active': True,
                'event': event
            },
            {
                'name': 'Gondar Hills Resort',
                'address': 'Gondar Hills, Gondar, Amhara Region, Ethiopia',
                'phone': '+251-58-111-5678',
                'email': '<EMAIL>',
                'contact_person': 'Ato Dawit Mekonnen',
                'latitude': 12.6150,
                'longitude': 37.4750,
                'star_rating': 5,
                'website': 'https://gondarhills.com',
                'description': 'Luxury resort with panoramic views of Gondar and the Simien Mountains.',
                'is_active': True,
                'event': event
            },
            {
                'name': 'Quara Hotel',
                'address': 'Maraki Area, Gondar, Amhara Region, Ethiopia',
                'phone': '+251-58-111-9012',
                'email': '<EMAIL>',
                'contact_person': 'W/ro Selamawit Tesfaye',
                'latitude': 12.6000,
                'longitude': 37.4600,
                'star_rating': 3,
                'website': 'https://quarahotel.com',
                'description': 'Family-friendly hotel with traditional Ethiopian cuisine and cultural shows.',
                'is_active': True,
                'event': event
            },
            {
                'name': 'Landmark Hotel Gondar',
                'address': 'Airport Road, Gondar, Amhara Region, Ethiopia',
                'phone': '+251-58-111-3456',
                'email': '<EMAIL>',
                'contact_person': 'Ato Yohannes Worku',
                'latitude': 12.6200,
                'longitude': 37.4800,
                'star_rating': 4,
                'website': 'https://landmarkgondar.com',
                'description': 'Modern business hotel with conference facilities and airport shuttle service.',
                'is_active': True,
                'event': event
            },
            {
                'name': 'Simien Lodge',
                'address': 'Simien Mountains Road, Gondar, Amhara Region, Ethiopia',
                'phone': '+251-58-111-7654',
                'email': '<EMAIL>',
                'contact_person': 'W/ro Meron Assefa',
                'latitude': 12.6300,
                'longitude': 37.4900,
                'star_rating': 4,
                'website': 'https://simienlodge.com',
                'description': 'Eco-lodge with stunning mountain views and adventure tourism packages.',
                'is_active': True,
                'event': event
            }
        ]

        for hotel_data in gondar_hotels:
            hotel = Hotel.objects.create(**hotel_data)
            self.stdout.write(f'  ✓ Created hotel: {hotel.name}')

            # Create sample rooms for each hotel
            self.create_sample_rooms(hotel)

    def create_sample_rooms(self, hotel):
        """Create sample rooms for a hotel"""
        room_types = [
            {'type': 'Standard Single', 'capacity': 1, 'price': 800.00},  # Ethiopian Birr
            {'type': 'Standard Double', 'capacity': 2, 'price': 1200.00},
            {'type': 'Deluxe Suite', 'capacity': 4, 'price': 2000.00},
            {'type': 'Executive Suite', 'capacity': 6, 'price': 3500.00},
        ]

        for i, room_type in enumerate(room_types):
            for room_num in range(1, 4):  # 3 rooms of each type
                room_number = f"{i+1}{room_num:02d}"
                HotelRoom.objects.create(
                    hotel=hotel,
                    room_number=room_number,
                    room_type=room_type['type'],
                    capacity=room_type['capacity'],
                    price_per_night=room_type['price'],
                    amenities='WiFi, TV, Air Conditioning, Mini Bar, Room Service',
                    is_available=True,
                )

    def create_drivers(self):
        """Create drivers for transportation"""
        self.stdout.write(self.style.SUCCESS('🚗 Creating drivers...'))

        # Get the first event for driver association
        event = Event.objects.first()

        drivers_data = [
            {
                'name': 'Ato Bekele Tadesse',
                'phone': '+251-911-123456',
                'email': '<EMAIL>',
                'car_plate': 'AA-12345',
                'car_code': 'UOG-001',
                'car_model': 'Toyota Hiace',
                'car_color': 'White',
                'license_number': 'ETH-DL-001234',
                'is_available': True,
                'notes': 'Experienced driver with 15+ years. Speaks Amharic and English.',
                'event': event
            },
            {
                'name': 'Ato Mulugeta Alemu',
                'phone': '+251-911-234567',
                'email': '<EMAIL>',
                'car_plate': 'AA-23456',
                'car_code': 'UOG-002',
                'car_model': 'Toyota Coaster',
                'car_color': 'Blue',
                'license_number': 'ETH-DL-002345',
                'is_available': True,
                'notes': 'Reliable driver specializing in group transportation.',
                'event': event
            },
            {
                'name': 'W/ro Almaz Getachew',
                'phone': '+251-911-345678',
                'email': '<EMAIL>',
                'car_plate': 'AA-34567',
                'car_code': 'UOG-003',
                'car_model': 'Toyota Prado',
                'car_color': 'Silver',
                'license_number': 'ETH-DL-003456',
                'is_available': True,
                'notes': 'Professional female driver with excellent safety record.',
                'event': event
            },
            {
                'name': 'Ato Dawit Haile',
                'phone': '+251-911-456789',
                'email': '<EMAIL>',
                'car_plate': 'AA-45678',
                'car_code': 'UOG-004',
                'car_model': 'Isuzu NPR',
                'car_color': 'Red',
                'license_number': 'ETH-DL-004567',
                'is_available': True,
                'notes': 'Heavy-duty vehicle driver for equipment transportation.',
                'event': event
            }
        ]

        for driver_data in drivers_data:
            driver = Driver.objects.create(**driver_data)
            self.stdout.write(f'  ✓ Created driver: {driver.name}')

    def create_contact_persons(self):
        """Create contact persons"""
        self.stdout.write(self.style.SUCCESS('📞 Creating contact persons...'))

        # Get the first event for contact person association
        event = Event.objects.first()

        contact_persons_data = [
            {
                'first_name': 'Alemayehu',
                'last_name': 'Teshome',
                'phone': '+251-58-114-1240',
                'email': '<EMAIL>',
                'position': 'Conference Director',
                'organization': 'Office of the Vice President for Academic Affairs',
                'notes': 'Primary contact for all conference-related matters.',
                'event': event
            },
            {
                'first_name': 'Birtukan',
                'last_name': 'Assefa',
                'phone': '+251-58-114-1241',
                'email': '<EMAIL>',
                'position': 'Research Coordinator',
                'organization': 'Research and Graduate Programs',
                'notes': 'Contact for research presentations and academic sessions.',
                'event': event
            },
            {
                'first_name': 'Getachew',
                'last_name': 'Mekonnen',
                'phone': '+251-58-114-1242',
                'email': '<EMAIL>',
                'position': 'Logistics Manager',
                'organization': 'Event Management Office',
                'notes': 'Contact for accommodation, transportation, and logistics.',
                'event': event
            },
            {
                'first_name': 'Tigist',
                'last_name': 'Haile',
                'phone': '+251-58-114-1243',
                'email': '<EMAIL>',
                'position': 'Registration Coordinator',
                'organization': 'Conference Services',
                'notes': 'Contact for participant registration and badge collection.',
                'event': event
            }
        ]

        for cp_data in contact_persons_data:
            cp = ContactPerson.objects.create(**cp_data)
            self.stdout.write(f'  ✓ Created contact person: {cp.full_name}')

    def setup_smtp_config(self):
        """Setup SMTP configuration for University of Gondar"""
        self.stdout.write(self.style.SUCCESS('📧 Setting up SMTP configuration...'))

        config = EmailConfiguration.objects.create(
            name='UoG Event Email System',
            email_backend='django.core.mail.backends.smtp.EmailBackend',
            email_host='smtp.office365.com',
            email_port=587,
            email_use_tls=True,
            email_use_ssl=False,
            email_host_user='<EMAIL>',
            email_host_password='Eve@456!!',
            default_from_email='University of Gondar Events <<EMAIL>>',
            is_active=True
        )

        self.stdout.write(f'  ✓ Created SMTP configuration: {config.name}')
        self.stdout.write(f'    Host: {config.email_host}:{config.email_port}')
        self.stdout.write(f'    User: {config.email_host_user}')

    def create_email_templates(self):
        """Create email templates"""
        self.stdout.write(self.style.SUCCESS('📨 Creating email templates...'))

        templates = [
            {
                'name': 'UoG Registration Confirmation',
                'template_type': 'registration_confirmation',
                'subject': '✅ Registration Confirmed - {event_name} | University of Gondar',
                'html_content': '''
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Registration Confirmation</title>
    <style>
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; line-height: 1.6; color: #333; margin: 0; padding: 0; background-color: #f4f4f4; }
        .container { max-width: 600px; margin: 0 auto; background: white; border-radius: 10px; overflow: hidden; box-shadow: 0 0 20px rgba(0,0,0,0.1); }
        .header { background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%); color: white; padding: 30px; text-align: center; }
        .logo { max-width: 80px; margin-bottom: 15px; }
        .content { padding: 30px; }
        .highlight { background: #e8f4fd; padding: 20px; border-radius: 8px; margin: 20px 0; border-left: 4px solid #2a5298; }
        .footer { background: #f8f9fa; padding: 20px; text-align: center; color: #666; }
        .btn { display: inline-block; background: #2a5298; color: white; padding: 12px 25px; text-decoration: none; border-radius: 5px; margin: 10px 0; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎉 Registration Confirmed!</h1>
            <p>University of Gondar Event Management</p>
        </div>
        <div class="content">
            <h2>Dear {participant_name},</h2>
            <p>We are delighted to confirm your registration for <strong>{event_name}</strong>.</p>

            <div class="highlight">
                <h3>📅 Event Details</h3>
                <p><strong>Event:</strong> {event_name}</p>
                <p><strong>Date:</strong> {event_date}</p>
                <p><strong>Location:</strong> {event_location}</p>
                <p><strong>Participant Type:</strong> {participant_type}</p>
            </div>

            <p>Your participation contributes to the academic excellence and research innovation at the University of Gondar.</p>

            <h3>📋 Next Steps:</h3>
            <ul>
                <li>Check your email for further updates</li>
                <li>Prepare your presentation materials (if applicable)</li>
                <li>Review the event schedule</li>
                <li>Contact us for any special requirements</li>
            </ul>
        </div>
        <div class="footer">
            <p><strong>University of Gondar</strong><br>
            Excellence in Education, Research and Community Service<br>
            📧 <EMAIL> | 📞 +251-58-114-1240</p>
        </div>
    </div>
</body>
</html>
                ''',
                'text_content': 'Registration confirmed for {event_name} at University of Gondar. Event details: {event_date} at {event_location}.'
            },
            {
                'name': 'UoG Event Details',
                'template_type': 'event_details',
                'subject': '📋 Event Details - {event_name} | University of Gondar',
                'html_content': '''
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Event Details</title>
    <style>
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; line-height: 1.6; color: #333; margin: 0; padding: 0; background-color: #f4f4f4; }
        .container { max-width: 600px; margin: 0 auto; background: white; border-radius: 10px; overflow: hidden; box-shadow: 0 0 20px rgba(0,0,0,0.1); }
        .header { background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%); color: white; padding: 30px; text-align: center; }
        .content { padding: 30px; }
        .info-box { background: #f8f9fa; padding: 20px; border-radius: 8px; margin: 15px 0; }
        .schedule-item { border-left: 4px solid #2a5298; padding-left: 15px; margin: 15px 0; }
        .footer { background: #f8f9fa; padding: 20px; text-align: center; color: #666; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>📋 Event Details</h1>
            <p>University of Gondar</p>
        </div>
        <div class="content">
            <h2>Dear {participant_name},</h2>
            <p>Here are the complete details for <strong>{event_name}</strong>:</p>

            <div class="info-box">
                <h3>🏛️ Venue Information</h3>
                <p><strong>Location:</strong> {event_location}</p>
                <p><strong>Address:</strong> University of Gondar Main Campus, Gondar, Ethiopia</p>
            </div>

            <div class="info-box">
                <h3>🏨 Accommodation</h3>
                {hotel_section}
            </div>

            <div class="info-box">
                <h3>🚗 Transportation</h3>
                {driver_section}
            </div>

            <p>We look forward to your participation in this prestigious academic event.</p>
        </div>
        <div class="footer">
            <p><strong>University of Gondar</strong><br>
            Excellence in Education, Research and Community Service<br>
            📧 <EMAIL> | 📞 +251-58-114-1240</p>
        </div>
    </div>
</body>
</html>
                ''',
                'text_content': 'Event details for {event_name} at University of Gondar. Location: {event_location}.'
            }
        ]

        for template_data in templates:
            template = EmailTemplate.objects.create(**template_data)
            self.stdout.write(f'  ✓ Created email template: {template.name}')

    def create_badge_template(self):
        """Create enhanced badge template"""
        self.stdout.write(self.style.SUCCESS('🏷️  Creating enhanced badge template...'))

        template = BadgeTemplate.objects.create(
            name='UoG Professional Badge Template',
            description='Enhanced professional badge template for University of Gondar events with 600x900 dimensions',
            width=600,
            height=900,
            background_color='#ffffff',
            is_default=True,
        )

        self.stdout.write(f'  ✓ Created badge template: {template.name}')
        self.stdout.write(f'    Dimensions: {template.width}x{template.height}')

    def create_admin_user(self):
        """Create admin user if it doesn't exist"""
        self.stdout.write(self.style.SUCCESS('👤 Creating admin user...'))

        if not CustomUser.objects.filter(username='admin').exists():
            admin_user = CustomUser.objects.create_superuser(
                username='admin',
                email='<EMAIL>',
                password='admin123',
                first_name='System',
                last_name='Administrator'
            )
            self.stdout.write(f'  ✓ Created admin user: {admin_user.username}')
            self.stdout.write(f'    Email: {admin_user.email}')
            self.stdout.write(f'    Password: admin123')
        else:
            self.stdout.write('  ℹ️  Admin user already exists')

        # Display final summary
        self.display_summary()

    def display_summary(self):
        """Display summary of created data"""
        self.stdout.write('\n' + '=' * 80)
        self.stdout.write(self.style.SUCCESS('🎉 FRESH STARTUP COMPLETED SUCCESSFULLY!'))
        self.stdout.write('=' * 80)

        # Count created items
        org_count = Organization.objects.count()
        event_count = Event.objects.count()
        hotel_count = Hotel.objects.count()
        driver_count = Driver.objects.count()
        contact_count = ContactPerson.objects.count()
        pt_count = ParticipantType.objects.count()
        template_count = EmailTemplate.objects.count()

        self.stdout.write(f'📊 Data Summary:')
        self.stdout.write(f'  🏛️  Organizations: {org_count}')
        self.stdout.write(f'  📅 Events: {event_count}')
        self.stdout.write(f'  🏨 Hotels: {hotel_count}')
        self.stdout.write(f'  🚗 Drivers: {driver_count}')
        self.stdout.write(f'  📞 Contact Persons: {contact_count}')
        self.stdout.write(f'  👥 Participant Types: {pt_count}')
        self.stdout.write(f'  📨 Email Templates: {template_count}')

        self.stdout.write(f'\n🔗 Access Information:')
        self.stdout.write(f'  🌐 Application: http://localhost')
        self.stdout.write(f'  🔧 Admin Panel: http://localhost/admin')
        self.stdout.write(f'  👤 Admin Login: admin / admin123')

        self.stdout.write(f'\n📧 Email Configuration:')
        self.stdout.write(f'  📮 SMTP Host: smtp.office365.com:587')
        self.stdout.write(f'  📧 From Email: <EMAIL>')

        self.stdout.write(f'\n🏛️  Organization:')
        self.stdout.write(f'  🎓 University of Gondar (UoG)')
        self.stdout.write(f'  🌍 Gondar, Amhara Region, Ethiopia')
        self.stdout.write(f'  📧 <EMAIL>')

        self.stdout.write('\n' + '=' * 80)
        self.stdout.write(self.style.SUCCESS('🚀 University of Gondar Event Management System is ready!'))
        self.stdout.write(self.style.SUCCESS('✨ All systems initialized with fresh data!'))
        self.stdout.write('=' * 80)
