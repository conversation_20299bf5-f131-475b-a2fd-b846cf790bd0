from django.core.management.base import BaseCommand
from events.models import EmailTemplate


class Command(BaseCommand):
    help = 'Fix registration template with proper CSS escaping'

    def handle(self, *args, **options):
        self.stdout.write("Fixing registration template with proper CSS escaping...")
        
        try:
            reg_template = EmailTemplate.objects.get(template_type='registration_confirmation')
            # Use inline styles only to avoid CSS curly brace issues
            reg_template.html_content = """<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>Registration Confirmed</title>
</head>
<body style="font-family: Arial, sans-serif; margin: 0; padding: 20px; background-color: #f5f5f5;">
    <div style="max-width: 600px; margin: 0 auto; background-color: white; border-radius: 10px; overflow: hidden; box-shadow: 0 2px 10px rgba(0,0,0,0.1);">
        
        <!-- Header -->
        <div style="background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%); color: white; padding: 30px; text-align: center;">
            <h1 style="margin: 0; font-size: 28px;">🎓 University of Gondar</h1>
            <h2 style="margin: 10px 0 0 0; font-size: 24px;">Registration Confirmed!</h2>
            <div style="background: #4CAF50; color: white; padding: 8px 16px; border-radius: 20px; font-size: 14px; font-weight: bold; display: inline-block; margin-top: 15px;">
                ✓ Successfully Registered
            </div>
        </div>
        
        <!-- Content -->
        <div style="padding: 30px;">
            <h2 style="color: #333; margin-top: 0;">Welcome {participant_name}!</h2>
            <p style="color: #666; line-height: 1.6; font-size: 16px;">
                Thank you for registering for <strong style="color: #1e3c72;">{event_name}</strong>. 
                Your registration has been successfully submitted and is currently under review.
            </p>
            
            <!-- Event Info Box -->
            <div style="background: #e8f4fd; border-left: 4px solid #2196F3; padding: 20px; margin: 20px 0; border-radius: 5px;">
                <h3 style="color: #1e3c72; margin-top: 0;">📅 Event Information</h3>
                <table style="width: 100%; border-collapse: collapse;">
                    <tr>
                        <td style="padding: 8px 0; font-weight: bold; color: #1e3c72; width: 30%;">Event Name:</td>
                        <td style="padding: 8px 0; color: #333;">{event_name}</td>
                    </tr>
                    <tr>
                        <td style="padding: 8px 0; font-weight: bold; color: #1e3c72;">Date:</td>
                        <td style="padding: 8px 0; color: #333;">{event_date}</td>
                    </tr>
                    <tr>
                        <td style="padding: 8px 0; font-weight: bold; color: #1e3c72;">Location:</td>
                        <td style="padding: 8px 0; color: #333;">{event_location}</td>
                    </tr>
                    <tr>
                        <td style="padding: 8px 0; font-weight: bold; color: #1e3c72;">Registration Date:</td>
                        <td style="padding: 8px 0; color: #333;">{registration_date}</td>
                    </tr>
                </table>
            </div>
            
            <!-- What's Next Box -->
            <div style="background: #fff3e0; border-left: 4px solid #ff9800; padding: 20px; margin: 20px 0; border-radius: 5px;">
                <h3 style="color: #e65100; margin-top: 0;">📋 What Happens Next?</h3>
                <ol style="color: #666; line-height: 1.6;">
                    <li><strong>Review Process:</strong> Our team will review your registration within 24-48 hours</li>
                    <li><strong>Approval Notification:</strong> You will receive an email once your registration is approved</li>
                    <li><strong>Event Details:</strong> Upon approval, you will receive detailed event information including:
                        <ul style="margin-top: 10px;">
                            <li>Complete event schedule and agenda</li>
                            <li>Hotel and accommodation details (if applicable)</li>
                            <li>Transportation arrangements</li>
                            <li>Contact person information</li>
                            <li>Your personalized event badge</li>
                        </ul>
                    </li>
                </ol>
            </div>
            
            <!-- Contact Info -->
            <div style="background: #e8f5e8; border-left: 4px solid #4CAF50; padding: 20px; margin: 20px 0; border-radius: 5px;">
                <h3 style="color: #2e7d32; margin-top: 0;">📞 Need Help?</h3>
                <p style="color: #666; margin-bottom: 15px;">If you have any questions or need to make changes to your registration, please contact us:</p>
                <ul style="color: #666; line-height: 1.6;">
                    <li><strong>Email:</strong> <EMAIL></li>
                    <li><strong>Phone:</strong> +251-58-114-0481</li>
                    <li><strong>Office Hours:</strong> Monday - Friday, 8:00 AM - 5:00 PM</li>
                </ul>
            </div>
        </div>
        
        <!-- Footer -->
        <div style="background: #f8f9fa; padding: 20px; text-align: center; color: #666; font-size: 14px;">
            <p style="margin: 0;"><strong>University of Gondar Events</strong></p>
            <p style="margin: 5px 0;">This is an automated message. Please do not reply to this email.</p>
            <p style="margin: 5px 0;">&copy; {current_year} University of Gondar. All rights reserved.</p>
        </div>
    </div>
</body>
</html>"""
            reg_template.save()
            self.stdout.write("✓ Fixed registration template with inline styles only")
        except Exception as e:
            self.stdout.write(f"Error: {e}")
        
        self.stdout.write("Registration template fixed!")
