import { Organization, getMediaUrl } from './api';

export interface BrandingData {
  organization: Organization | null;
  isLoading: boolean;
  error: string | null;
}

class BrandingService {
  private static instance: BrandingService;
  private organization: Organization | null = null;
  private isLoading = false;
  private error: string | null = null;
  private listeners: Array<(data: BrandingData) => void> = [];

  private constructor() {}

  public static getInstance(): BrandingService {
    if (!BrandingService.instance) {
      BrandingService.instance = new BrandingService();
    }
    return BrandingService.instance;
  }

  public subscribe(listener: (data: BrandingData) => void): () => void {
    this.listeners.push(listener);
    // Immediately call with current data
    listener(this.getBrandingData());
    
    // Return unsubscribe function
    return () => {
      this.listeners = this.listeners.filter(l => l !== listener);
    };
  }

  public getBrandingData(): BrandingData {
    return {
      organization: this.organization,
      isLoading: this.isLoading,
      error: this.error
    };
  }

  public async loadOrganization(): Promise<void> {
    if (this.organization || this.isLoading) {
      return; // Already loaded or loading
    }

    this.isLoading = true;
    this.error = null;
    this.notifyListeners();

    try {
      const apiBaseUrl = process.env.REACT_APP_API_BASE_URL || '/api';
      const response = await fetch(`${apiBaseUrl}/organizations/primary/`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        // If primary organization endpoint fails, try the fallback
        console.warn('Primary organization endpoint failed, using fallback data');
        this.organization = {
          id: null,
          name: 'University of Gondar',
          short_name: 'UoG',
          motto: 'Excellence in Education, Research and Community Service',
          description: 'The University of Gondar is a premier public university in Ethiopia, committed to providing quality education, conducting innovative research, and serving the community.',
          logo: null,
          website: 'http://www.uog.edu.et',
          email: '<EMAIL>',
          phone: '+251-58-114-1240',
          full_address: 'Gondar, Amhara Region, Ethiopia',
          is_active: true,
          is_primary: true
        };
      } else {
        const data = await response.json();
        this.organization = data;
      }

      this.error = null;
      console.log('Organization loaded:', this.organization);
    } catch (error) {
      this.error = error instanceof Error ? error.message : 'Unknown error';
      console.error('Failed to load organization:', error);

      // Use fallback data even on error
      this.organization = {
        id: null,
        name: 'University of Gondar',
        short_name: 'UoG',
        motto: 'Excellence in Education, Research and Community Service',
        description: 'The University of Gondar is a premier public university in Ethiopia, committed to providing quality education, conducting innovative research, and serving the community.',
        logo: null,
        website: 'http://www.uog.edu.et',
        email: '<EMAIL>',
        phone: '+251-58-114-1240',
        full_address: 'Gondar, Amhara Region, Ethiopia',
        is_active: true,
        is_primary: true
      };
    } finally {
      this.isLoading = false;
      this.notifyListeners();
    }
  }

  private notifyListeners(): void {
    const data = this.getBrandingData();
    this.listeners.forEach(listener => listener(data));
  }

  public getLogoUrl(): string | null {
    if (!this.organization?.logo) return null;
    return getMediaUrl(this.organization.logo);
  }

  public getOrganizationName(): string {
    return this.organization?.name || 'University of Gondar';
  }

  public getShortName(): string {
    return this.organization?.short_name || 'UoG';
  }

  public getMotto(): string {
    return this.organization?.motto || 'Excellence in Education, Research and Community Service';
  }

  public getWebsite(): string {
    return this.organization?.website || 'http://www.uog.edu.et';
  }

  public getEmail(): string {
    return this.organization?.email || '<EMAIL>';
  }

  public getPhone(): string {
    return this.organization?.phone || '+251-58-114-1240';
  }

  public getAddress(): string {
    return this.organization?.full_address || 'Gondar, Amhara Region, Ethiopia';
  }
}

export default BrandingService;
