from django.core.management.base import BaseCommand
from participants.models import Participant, ParticipantType
from events.models import Event
from django.utils import timezone
from datetime import timedelta


class Command(BaseCommand):
    help = 'Test the participant approval email flow'
    
    def add_arguments(self, parser):
        parser.add_argument(
            '--email',
            type=str,
            default='<EMAIL>',
            help='Email address for test participant'
        )
        parser.add_argument(
            '--cleanup',
            action='store_true',
            help='Clean up test participant after testing'
        )
    
    def handle(self, *args, **options):
        test_email = options['email']
        cleanup = options['cleanup']
        
        self.stdout.write(
            self.style.SUCCESS('🧪 Testing Participant Approval Email Flow')
        )
        self.stdout.write('=' * 60)
        
        try:
            # Get test data
            event = Event.objects.first()
            participant_type = ParticipantType.objects.first()
            
            if not event or not participant_type:
                self.stdout.write(
                    self.style.ERROR('❌ No events or participant types found. Please create them first.')
                )
                return
            
            # Clean up existing test participant
            Participant.objects.filter(email=test_email).delete()
            
            # Step 1: Create participant (simulates registration)
            self.stdout.write('\n📝 Step 1: Creating participant (Registration)')
            participant = Participant.objects.create(
                first_name="Test",
                last_name="Approval",
                email=test_email,
                phone="+251-911-123456",
                institution_name="Test University",
                position="Test Position",
                participant_type=participant_type,
                event=event,
                arrival_date=timezone.now() + timedelta(days=1),
                departure_date=timezone.now() + timedelta(days=3),
            )
            
            self.stdout.write(f'✓ Participant created: {participant.full_name}')
            self.stdout.write(f'✓ Status: {participant.status}')
            self.stdout.write(f'✓ Email: {participant.email}')
            self.stdout.write('✓ Registration confirmation email should be sent automatically')
            
            # Step 2: Approve participant
            self.stdout.write('\n✅ Step 2: Approving participant')
            participant.status = 'approved'
            participant.approved_at = timezone.now()
            participant.save()
            
            self.stdout.write(f'✓ Status changed to: {participant.status}')
            self.stdout.write('✓ Event details email should be sent automatically')
            
            # Step 3: Generate badge
            self.stdout.write('\n🎫 Step 3: Generating badge')
            try:
                from badges.models import Badge
                badge, created = Badge.objects.get_or_create(participant=participant)
                badge.generate_badge()
                
                participant.status = 'badge_generated'
                participant.save()
                
                self.stdout.write(f'✓ Badge generated: {badge.badge_file.name if badge.badge_file else "No file"}')
                self.stdout.write('✓ Badge notification email should be sent automatically')
                
            except Exception as e:
                self.stdout.write(f'⚠️ Badge generation failed: {e}')
            
            # Step 4: Check email logs
            self.stdout.write('\n📧 Step 4: Checking email logs')
            from events.models import EmailLog
            
            logs = EmailLog.objects.filter(
                recipient_email=test_email
            ).order_by('-created_at')
            
            if logs.exists():
                self.stdout.write(f'✓ Found {logs.count()} email log(s):')
                for log in logs:
                    status_icon = '✓' if log.status == 'sent' else '✗' if log.status == 'failed' else '⏳'
                    self.stdout.write(
                        f'  {status_icon} {log.template_type} - {log.status} ({log.created_at.strftime("%H:%M:%S")})'
                    )
                    if log.error_message:
                        self.stdout.write(f'    Error: {log.error_message}')
            else:
                self.stdout.write('⚠️ No email logs found')
            
            # Step 5: Test status transitions
            self.stdout.write('\n🔄 Step 5: Testing other status transitions')
            
            # Test assignment
            participant.status = 'assigned'
            participant.save()
            self.stdout.write('✓ Status changed to: assigned')
            
            # Test email sent
            participant.status = 'email_sent'
            participant.save()
            self.stdout.write('✓ Status changed to: email_sent')
            
            # Final status check
            self.stdout.write(f'\n📊 Final participant status: {participant.status}')
            self.stdout.write(f'📊 Is confirmed: {participant.is_confirmed}')
            self.stdout.write(f'📊 Approved at: {participant.approved_at}')
            
            # Cleanup
            if cleanup:
                participant.delete()
                self.stdout.write('\n🧹 Test participant cleaned up')
            else:
                self.stdout.write(f'\n💡 To clean up, run: python manage.py test_approval_flow --email {test_email} --cleanup')
            
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f'❌ Error during test: {e}')
            )
            import traceback
            traceback.print_exc()
        
        self.stdout.write('\n' + '=' * 60)
        self.stdout.write(
            self.style.SUCCESS('✅ Approval flow test completed!')
        )
        
        self.stdout.write('\n📋 Expected Email Flow:')
        self.stdout.write('1. Registration → Registration Confirmation Email')
        self.stdout.write('2. Approval → Event Details Email')
        self.stdout.write('3. Badge Generated → Badge Notification Email')
        self.stdout.write('4. Other status changes → No automatic emails')
        
        self.stdout.write('\n🔗 Check email logs in Django admin: /admin/events/emaillog/')
