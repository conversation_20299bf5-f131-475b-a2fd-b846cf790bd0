from django.contrib import admin
from django.http import HttpResponse
from django.shortcuts import render, redirect
from django.contrib import messages
from django.urls import path, reverse
from django.utils.html import format_html
import csv
import io
from .models import Driver, DriverAssignment


@admin.register(Driver)
class DriverAdmin(admin.ModelAdmin):
    list_display = ['name', 'phone', 'car_plate', 'car_code', 'event', 'is_available', 'assigned_participants_count']
    list_filter = ['event', 'is_available', 'created_at']
    search_fields = ['name', 'phone', 'car_plate', 'car_code', 'email']
    readonly_fields = ['created_at', 'updated_at']

    fieldsets = (
        ('Driver Information', {
            'fields': ('name', 'phone', 'email', 'photo')
        }),
        ('Vehicle Information', {
            'fields': ('car_plate', 'car_code', 'car_model', 'car_color', 'license_number')
        }),
        ('Availability & Event', {
            'fields': ('event', 'is_available', 'notes')
        }),
        ('System Information', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        })
    )

    actions = ['export_to_csv', 'mark_available', 'mark_unavailable', 'generate_badges']

    def assigned_participants_count(self, obj):
        count = obj.assigned_participants.count()
        if count > 0:
            url = reverse('admin:participants_participant_changelist') + f'?assigned_driver__id__exact={obj.id}'
            return format_html('<a href="{}">{} participants</a>', url, count)
        return '0 participants'
    assigned_participants_count.short_description = 'Assigned Participants'

    def mark_available(self, request, queryset):
        updated = queryset.update(is_available=True)
        self.message_user(request, f'{updated} drivers marked as available.')
    mark_available.short_description = "Mark selected drivers as available"

    def mark_unavailable(self, request, queryset):
        updated = queryset.update(is_available=False)
        self.message_user(request, f'{updated} drivers marked as unavailable.')
    mark_unavailable.short_description = "Mark selected drivers as unavailable"

    def generate_badges(self, request, queryset):
        """Generate badges for selected drivers"""
        updated = 0
        for driver in queryset:
            try:
                # Import here to avoid circular imports
                from badges.models import DriverBadge
                badge, created = DriverBadge.objects.get_or_create(driver=driver)
                badge.generate_badge()
                updated += 1
            except Exception as e:
                messages.error(request, f'Error generating badge for {driver.name}: {e}')
        self.message_user(request, f'{updated} driver badges generated successfully.')
    generate_badges.short_description = "Generate badges for selected drivers"

    def export_to_csv(self, request, queryset):
        response = HttpResponse(content_type='text/csv')
        response['Content-Disposition'] = 'attachment; filename="drivers.csv"'

        writer = csv.writer(response)
        writer.writerow([
            'Name', 'Phone', 'Email', 'Car Plate', 'Car Code', 'Car Model',
            'Car Color', 'License Number', 'Event', 'Available', 'Notes'
        ])

        for driver in queryset:
            writer.writerow([
                driver.name,
                driver.phone,
                driver.email,
                driver.car_plate,
                driver.car_code,
                driver.car_model,
                driver.car_color,
                driver.license_number,
                driver.event.name,
                'Yes' if driver.is_available else 'No',
                driver.notes
            ])

        return response
    export_to_csv.short_description = "Export selected drivers to CSV"

    def get_urls(self):
        urls = super().get_urls()
        custom_urls = [
            path('import-csv/', self.admin_site.admin_view(self.import_csv), name='drivers_driver_import_csv'),
        ]
        return custom_urls + urls

    def import_csv(self, request):
        if request.method == 'POST':
            csv_file = request.FILES.get('csv_file')
            if not csv_file:
                messages.error(request, 'Please select a CSV file.')
                return redirect('admin:drivers_driver_import_csv')

            try:
                # Read and process CSV
                csv_content = csv_file.read().decode('utf-8')
                csv_reader = csv.DictReader(io.StringIO(csv_content))

                created_count = 0
                errors = []

                for row_num, row in enumerate(csv_reader, start=2):
                    try:
                        # Get event
                        from events.models import Event
                        event_name = row.get('event', '').strip()
                        if event_name:
                            event = Event.objects.filter(name__icontains=event_name).first()
                            if not event:
                                errors.append(f"Row {row_num}: Event '{event_name}' not found")
                                continue
                        else:
                            errors.append(f"Row {row_num}: Event name is required")
                            continue

                        # Create driver
                        driver = Driver.objects.create(
                            name=row['name'].strip(),
                            phone=row['phone'].strip(),
                            email=row.get('email', '').strip(),
                            car_plate=row['car_plate'].strip(),
                            car_code=row['car_code'].strip(),
                            car_model=row.get('car_model', '').strip(),
                            car_color=row.get('car_color', '').strip(),
                            license_number=row.get('license_number', '').strip(),
                            event=event,
                            is_available=row.get('available', 'yes').lower() in ['yes', 'true', '1'],
                            notes=row.get('notes', '').strip()
                        )
                        created_count += 1

                    except Exception as e:
                        errors.append(f"Row {row_num}: {str(e)}")

                if created_count > 0:
                    messages.success(request, f'Successfully imported {created_count} drivers.')

                if errors:
                    for error in errors[:10]:  # Show first 10 errors
                        messages.error(request, error)
                    if len(errors) > 10:
                        messages.error(request, f'... and {len(errors) - 10} more errors.')

            except Exception as e:
                messages.error(request, f'Error processing CSV file: {str(e)}')

            return redirect('admin:drivers_driver_changelist')

        return render(request, 'admin/drivers/import_csv.html')


@admin.register(DriverAssignment)
class DriverAssignmentAdmin(admin.ModelAdmin):
    list_display = ['driver', 'participant', 'pickup_time', 'status', 'created_at']
    list_filter = ['status', 'pickup_time', 'created_at']
    search_fields = ['driver__name', 'participant__first_name', 'participant__last_name']
    readonly_fields = ['created_at', 'updated_at']
    date_hierarchy = 'pickup_time'
