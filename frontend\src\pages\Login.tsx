import React, { useState, useEffect } from 'react';
import { <PERSON>, Col, Form, <PERSON>ton, Al<PERSON>, Spinner } from 'react-bootstrap';
import { Link, useNavigate, useLocation } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext';
import ModernLayout from '../components/ModernLayout';

const Login: React.FC = () => {
  const [username, setUsername] = useState('');
  const [password, setPassword] = useState('');
  const [error, setError] = useState('');
  const [loading, setLoading] = useState(false);


  const { login, isAuthenticated } = useAuth();
  const navigate = useNavigate();
  const location = useLocation();

  const from = (location.state as any)?.from?.pathname || '/dashboard';

  useEffect(() => {
    if (isAuthenticated) {
      navigate(from, { replace: true });
    }
  }, [isAuthenticated, navigate, from]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError('');
    setLoading(true);

    try {
      await login(username, password);
      navigate(from, { replace: true });
    } catch (error: any) {
      console.error('Login failed:', error);
      setError(
        error.response?.data?.detail || 
        error.response?.data?.non_field_errors?.[0] ||
        'Login failed. Please check your credentials.'
      );
    } finally {
      setLoading(false);
    }
  };



  return (
    <ModernLayout
      showHero={false}
      className="login-page"
    >
      <div className="login-content d-flex align-items-center justify-content-center" style={{ minHeight: '80vh' }}>
        <div className="container-fluid">
        <Row className="min-vh-100 g-0">
          {/* Left Column - Branding */}
          <Col lg={6} className="d-none d-lg-flex align-items-center justify-content-center bg-dark text-white position-relative">
            <div className="text-center p-5">
              <div className="mb-4">
                <i className="fas fa-university fa-5x mb-4" style={{ color: '#ffffff' }}></i>
              </div>
              <h1 className="display-4 fw-bold mb-4">University of Gondar</h1>
              <h3 className="fw-light mb-4">Event Management System</h3>
              <p className="lead mb-4">
                Streamline your event planning and management with our comprehensive platform
              </p>
              <div className="d-flex justify-content-center gap-4 mt-5">
                <div className="text-center">
                  <i className="fas fa-calendar-alt fa-2x mb-2"></i>
                  <p className="small mb-0">Event Planning</p>
                </div>
                <div className="text-center">
                  <i className="fas fa-users fa-2x mb-2"></i>
                  <p className="small mb-0">Participant Management</p>
                </div>
                <div className="text-center">
                  <i className="fas fa-qrcode fa-2x mb-2"></i>
                  <p className="small mb-0">QR Code Integration</p>
                </div>
              </div>
            </div>
          </Col>

          {/* Right Column - Login Form */}
          <Col lg={6} className="d-flex align-items-center justify-content-center bg-white">
            <div className="w-100" style={{ maxWidth: '400px', padding: '2rem' }}>
              {/* Header */}
              <div className="text-center mb-5">
                <div className="d-lg-none mb-3">
                  <i className="fas fa-university text-dark fa-3x"></i>
                </div>
                <h2 className="fw-bold text-dark mb-2">Welcome Back</h2>
                <p className="text-muted">Sign in to your account</p>
              </div>

              {/* Error Alert */}
              {error && (
                <Alert variant="danger" className="mb-4 border-0" style={{ backgroundColor: '#fee', color: '#c33' }}>
                  <i className="fas fa-exclamation-triangle me-2"></i>
                  {error}
                </Alert>
              )}

              {/* Login Form */}
              <Form onSubmit={handleSubmit}>
                <Form.Group className="mb-4">
                  <Form.Label className="fw-semibold text-dark mb-2">
                    <i className="fas fa-user me-2"></i>
                    Username
                  </Form.Label>
                  <Form.Control
                    type="text"
                    placeholder="Enter your username"
                    value={username}
                    onChange={(e) => setUsername(e.target.value)}
                    required
                    size="lg"
                    className="border-2 rounded-3"
                    disabled={loading}
                    style={{
                      borderColor: '#e0e0e0',
                      padding: '12px 16px',
                      fontSize: '16px'
                    }}
                  />
                </Form.Group>

                <Form.Group className="mb-4">
                  <Form.Label className="fw-semibold text-dark mb-2">
                    <i className="fas fa-lock me-2"></i>
                    Password
                  </Form.Label>
                  <Form.Control
                    type="password"
                    placeholder="Enter your password"
                    value={password}
                    onChange={(e) => setPassword(e.target.value)}
                    required
                    size="lg"
                    className="border-2 rounded-3"
                    disabled={loading}
                    style={{
                      borderColor: '#e0e0e0',
                      padding: '12px 16px',
                      fontSize: '16px'
                    }}
                  />
                </Form.Group>

                <Button
                  type="submit"
                  size="lg"
                  className="w-100 mb-4 fw-bold border-0 rounded-3"
                  disabled={loading}
                  style={{
                    backgroundColor: '#000',
                    color: '#fff',
                    padding: '12px',
                    fontSize: '16px',
                    transition: 'all 0.3s ease'
                  }}
                  onMouseEnter={(e) => {
                    e.currentTarget.style.backgroundColor = '#333';
                  }}
                  onMouseLeave={(e) => {
                    e.currentTarget.style.backgroundColor = '#000';
                  }}
                >
                  {loading ? (
                    <>
                      <Spinner animation="border" size="sm" className="me-2" />
                      Signing In...
                    </>
                  ) : (
                    <>
                      <i className="fas fa-sign-in-alt me-2"></i>
                      Sign In
                    </>
                  )}
                </Button>
              </Form>

              {/* Footer Links */}
              <div className="text-center mt-4 pt-3" style={{ borderTop: '1px solid #e0e0e0' }}>
                <Link to="/" className="text-decoration-none me-3" style={{ color: '#666' }}>
                  <i className="fas fa-home me-1"></i>
                  Back to Home
                </Link>
                <Link to="/register" className="text-decoration-none" style={{ color: '#666' }}>
                  <i className="fas fa-user-plus me-1"></i>
                  Register as Participant
                </Link>
              </div>

              {/* Additional Info */}
              <div className="text-center mt-4">
                <small className="text-muted">
                  <i className="fas fa-shield-alt me-1"></i>
                  Secure login powered by University of Gondar
                </small>
              </div>
          </div>
        </Col>
        </Row>
        </div>
      </div>
    </ModernLayout>
  );
};

export default Login;
