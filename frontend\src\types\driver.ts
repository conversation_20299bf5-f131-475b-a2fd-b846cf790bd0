export interface Driver {
  id: number;
  name: string;
  phone: string;
  photo?: string;
  car_code: string;
  car_plate: string;
  car_color?: string;
  status: 'active' | 'inactive';
  created_at: string;
  updated_at: string;
  email?: string;
  alt_phone?: string;
  license_number?: string;
  experience_years?: number;
  car_make?: string;
  car_year?: number;
  car_capacity?: number;
  notes?: string;
}

export interface CreateDriverData {
  name: string;
  phone: string;
  photo?: File;
  car_code: string;
  car_plate: string;
  car_color?: string;
  status: 'active' | 'inactive';
  email?: string;
  alt_phone?: string;
  license_number?: string;
  experience_years?: number;
  car_make?: string;
  car_year?: number;
  car_capacity?: number;
  notes?: string;
}

export interface UpdateDriverData extends Partial<CreateDriverData> {
  id: number;
}

export interface FocalPerson {
  id: number;
  name: string;
  email: string;
  phone: string;
  photo?: string;
  status: 'active' | 'inactive';
  created_at: string;
  updated_at: string;
  title?: string;
  alt_email?: string;
  alt_phone?: string;
  organization?: string;
  specialization?: string;
  languages?: string;
  availability: 'full-time' | 'part-time' | 'on-call';
  notes?: string;
}

export interface Hotel {
  id: number;
  name: string;
  address?: string;
  website?: string;
  email?: string;
  phone?: string;
  contact_person_name?: string;
  contact_person_email?: string;
  contact_person_phone?: string;
  contact_person_photo?: string;
  status: 'active' | 'inactive';
  created_at: string;
  updated_at: string;
}

export interface ParticipantType {
  id: number;
  name: string;
  description?: string;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

export interface PlaceToVisit {
  id: number;
  name: string;
  description?: string;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

export interface Assignment {
  id: number;
  registration_id: number;
  hotel_id?: number;
  driver_id?: number;
  focal_person_id?: number;
  assignment_date: string;
  notes?: string;
  created_at: string;
  updated_at: string;
  is_finalized: boolean;
  finalized_at?: string;
  finalized_by?: number;
}
