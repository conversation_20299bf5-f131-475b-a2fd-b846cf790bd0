import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON><PERSON>, <PERSON>, Col, Card, Nav, Tab, <PERSON><PERSON>, <PERSON><PERSON>, Badge } from 'react-bootstrap';
import Gallery from '../components/Gallery';
import { NewsGallery, AboutGallery, DepartmentGallery, CampusGallery } from '../components/SectionGallery';
import { EventGallery, GalleryImage, GalleryCategory, galleryService, eventService } from '../services/api';

// Interface for converting GalleryImage to EventGallery format for compatibility
interface GalleryDisplayItem {
  id: number;
  title: string;
  description: string;
  image: string;
  uploaded_at: string;
  is_featured: boolean;
  event?: number;
  category_name?: string;
  category_color?: string;
  photographer?: string;
  location?: string;
}

const GalleryDemo: React.FC = () => {
  const [activeTab, setActiveTab] = useState('events');
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Real data state
  const [eventImages, setEventImages] = useState<GalleryDisplayItem[]>([]);
  const [featuredImages, setFeaturedImages] = useState<GalleryDisplayItem[]>([]);
  const [campusImages, setCampusImages] = useState<GalleryDisplayItem[]>([]);
  const [sliderImages, setSliderImages] = useState<GalleryDisplayItem[]>([]);
  const [categories, setCategories] = useState<GalleryCategory[]>([]);
  const [categoryImages, setCategoryImages] = useState<{[key: string]: GalleryDisplayItem[]}>({});

  // Convert GalleryImage to GalleryDisplayItem format
  const convertGalleryImage = (image: GalleryImage): GalleryDisplayItem => ({
    id: image.id,
    title: image.title,
    description: image.description || '',
    image: image.image,
    uploaded_at: image.created_at || new Date().toISOString(),
    is_featured: image.is_featured,
    category_name: image.category_name,
    category_color: image.category_color,
    photographer: image.photographer,
    location: image.location
  });

  // Convert EventGallery to GalleryDisplayItem format
  const convertEventGallery = (image: EventGallery): GalleryDisplayItem => ({
    id: image.id,
    title: image.title,
    description: image.description || '',
    image: image.image,
    uploaded_at: image.uploaded_at,
    is_featured: image.is_featured,
    event: image.event
  });

  // Load all gallery data
  useEffect(() => {
    const loadGalleryData = async () => {
      try {
        setLoading(true);
        setError(null);

        // Load categories first
        const categoriesResponse = await galleryService.getCategories();
        const activeCategories = categoriesResponse.data.results.filter(cat => cat.is_active);
        setCategories(activeCategories);

        // Load featured images
        const featuredResponse = await galleryService.getFeaturedImages({ limit: 12 });
        setFeaturedImages(featuredResponse.data.map(convertGalleryImage));

        // Load campus images
        const campusResponse = await galleryService.getCampusImages({ limit: 8 });
        setCampusImages(campusResponse.data.map(convertGalleryImage));

        // Load slider images
        const sliderResponse = await galleryService.getSliderImages({ limit: 6 });
        setSliderImages(sliderResponse.data.map(convertGalleryImage));

        // Load images for each category
        const categoryImagesData: {[key: string]: GalleryDisplayItem[]} = {};
        for (const category of activeCategories) {
          try {
            const categoryImagesResponse = await galleryService.getCategoryImages(category.id, { limit: 8 });
            categoryImagesData[category.slug] = categoryImagesResponse.data.map(convertGalleryImage);
          } catch (err) {
            console.warn(`Failed to load images for category ${category.name}:`, err);
            categoryImagesData[category.slug] = [];
          }
        }
        setCategoryImages(categoryImagesData);

        // Load recent event gallery images
        try {
          const eventsResponse = await eventService.getPublicEvents();
          if (eventsResponse.data.results.length > 0) {
            const recentEvent = eventsResponse.data.results[0];
            const eventGalleryResponse = await eventService.getEventGallery(recentEvent.id);
            setEventImages(eventGalleryResponse.data.map(convertEventGallery));
          }
        } catch (err) {
          console.warn('Failed to load event gallery:', err);
          setEventImages([]);
        }

      } catch (err) {
        console.error('Failed to load gallery data:', err);
        setError('Failed to load gallery data. Please try again later.');
      } finally {
        setLoading(false);
      }
    };

    loadGalleryData();
  }, []);

  if (loading) {
    return (
      <Container className="py-5">
        <Row className="justify-content-center">
          <Col md={6} className="text-center">
            <Spinner animation="border" variant="primary" className="mb-3" />
            <h4>Loading Gallery...</h4>
            <p className="text-muted">Please wait while we fetch the latest images</p>
          </Col>
        </Row>
      </Container>
    );
  }

  if (error) {
    return (
      <Container className="py-5">
        <Row className="justify-content-center">
          <Col md={8}>
            <Alert variant="danger">
              <Alert.Heading>Error Loading Gallery</Alert.Heading>
              <p>{error}</p>
            </Alert>
          </Col>
        </Row>
      </Container>
    );
  }

  return (
    <Container className="py-5">
      <Row className="mb-5">
        <Col>
          <div className="text-center">
            <h1 className="display-4 fw-bold mb-3">
              <i className="fas fa-images me-3"></i>
              Explore UoG
            </h1>
            <p className="lead text-muted">
              Discover the University of Gondar through our comprehensive visual gallery
            </p>
            <div className="d-flex justify-content-center gap-3 mt-4">
              <Badge bg="primary" className="px-3 py-2">
                <i className="fas fa-camera me-1"></i>
                {featuredImages.length + campusImages.length + eventImages.length + sliderImages.length} Total Images
              </Badge>
              <Badge bg="success" className="px-3 py-2">
                <i className="fas fa-star me-1"></i>
                {featuredImages.length} Featured
              </Badge>
              <Badge bg="info" className="px-3 py-2">
                <i className="fas fa-university me-1"></i>
                {campusImages.length} Campus
              </Badge>
            </div>
          </div>
        </Col>
      </Row>

      <Tab.Container activeKey={activeTab} onSelect={(k) => setActiveTab(k || 'featured')}>
        <Row>
          <Col>
            <Nav variant="pills" className="justify-content-center mb-4 flex-wrap">
              <Nav.Item>
                <Nav.Link eventKey="featured">
                  <i className="fas fa-star me-2"></i>
                  Featured Gallery
                  <Badge bg="warning" className="ms-2">{featuredImages.length}</Badge>
                </Nav.Link>
              </Nav.Item>
              <Nav.Item>
                <Nav.Link eventKey="campus">
                  <i className="fas fa-university me-2"></i>
                  Campus Life
                  <Badge bg="success" className="ms-2">{campusImages.length}</Badge>
                </Nav.Link>
              </Nav.Item>
              <Nav.Item>
                <Nav.Link eventKey="events">
                  <i className="fas fa-calendar-alt me-2"></i>
                  Recent Events
                  <Badge bg="primary" className="ms-2">{eventImages.length}</Badge>
                </Nav.Link>
              </Nav.Item>
              <Nav.Item>
                <Nav.Link eventKey="slider">
                  <i className="fas fa-images me-2"></i>
                  Highlights
                  <Badge bg="info" className="ms-2">{sliderImages.length}</Badge>
                </Nav.Link>
              </Nav.Item>
              {categories.slice(0, 3).map(category => (
                <Nav.Item key={category.id}>
                  <Nav.Link eventKey={`category-${category.slug}`}>
                    <i className={category.icon || 'fas fa-folder'} style={{ color: category.color }}></i>
                    <span className="ms-2">{category.name}</span>
                    <Badge bg="secondary" className="ms-2">{categoryImages[category.slug]?.length || 0}</Badge>
                  </Nav.Link>
                </Nav.Item>
              ))}
            </Nav>

            <Tab.Content>
              {/* Featured Gallery Tab */}
              <Tab.Pane eventKey="featured">
                <Card className="shadow-sm">
                  <Card.Header className="bg-warning text-dark">
                    <h4 className="mb-0">
                      <i className="fas fa-star me-2"></i>
                      Featured Gallery - University Highlights
                    </h4>
                  </Card.Header>
                  <Card.Body>
                    <p className="text-muted mb-4">
                      Our most impressive moments and achievements showcased in a beautiful gallery format.
                    </p>
                    <Gallery
                      images={featuredImages as EventGallery[]}
                      showDateGrouping={false}
                      showDownloadButton={true}
                      showFeaturedBadge={true}
                      gridColumns={{ lg: 3, md: 4, sm: 6 }}
                      downloadPrefix="featured"
                      emptyStateMessage="No featured images available"
                      emptyStateIcon="fas fa-star"
                    />
                  </Card.Body>
                </Card>
              </Tab.Pane>

              {/* Campus Gallery Tab */}
              <Tab.Pane eventKey="campus">
                <Card className="shadow-sm">
                  <Card.Header className="bg-success text-white">
                    <h4 className="mb-0">
                      <i className="fas fa-university me-2"></i>
                      Campus Life - University Facilities
                    </h4>
                  </Card.Header>
                  <Card.Body>
                    <p className="text-muted mb-4">
                      Explore our beautiful campus, modern facilities, and vibrant student life.
                    </p>
                    <Gallery
                      images={campusImages as EventGallery[]}
                      showDateGrouping={false}
                      showDownloadButton={true}
                      showFeaturedBadge={true}
                      gridColumns={{ lg: 3, md: 4, sm: 6 }}
                      imageHeight="250px"
                      downloadPrefix="campus"
                      emptyStateMessage="No campus images available"
                      emptyStateIcon="fas fa-university"
                    />
                  </Card.Body>
                </Card>
              </Tab.Pane>

              {/* Event Gallery Tab */}
              <Tab.Pane eventKey="events">
                <Card className="shadow-sm">
                  <Card.Header className="bg-primary text-white">
                    <h4 className="mb-0">
                      <i className="fas fa-calendar-alt me-2"></i>
                      Recent Events - Date Grouped
                    </h4>
                  </Card.Header>
                  <Card.Body>
                    <p className="text-muted mb-4">
                      Latest event photos with date grouping and download functionality.
                    </p>
                    <Gallery
                      images={eventImages as EventGallery[]}
                      showDateGrouping={true}
                      showDownloadButton={true}
                      showFeaturedBadge={true}
                      gridColumns={{ lg: 3, md: 4, sm: 6 }}
                      downloadPrefix="events"
                      emptyStateMessage="No event photos available"
                      emptyStateIcon="fas fa-calendar-alt"
                    />
                  </Card.Body>
                </Card>
              </Tab.Pane>

              {/* Slider/Highlights Gallery Tab */}
              <Tab.Pane eventKey="slider">
                <Card className="shadow-sm">
                  <Card.Header className="bg-info text-white">
                    <h4 className="mb-0">
                      <i className="fas fa-images me-2"></i>
                      University Highlights - Slider Images
                    </h4>
                  </Card.Header>
                  <Card.Body>
                    <p className="text-muted mb-4">
                      Premium images featured in our homepage slider and promotional materials.
                    </p>
                    <Gallery
                      images={sliderImages as EventGallery[]}
                      showDateGrouping={false}
                      showDownloadButton={true}
                      showFeaturedBadge={true}
                      gridColumns={{ lg: 2, md: 3, sm: 6 }}
                      imageHeight="300px"
                      downloadPrefix="highlights"
                      emptyStateMessage="No highlight images available"
                      emptyStateIcon="fas fa-images"
                    />
                  </Card.Body>
                </Card>
              </Tab.Pane>

              {/* Dynamic Category Tabs */}
              {categories.slice(0, 3).map(category => (
                <Tab.Pane key={category.id} eventKey={`category-${category.slug}`}>
                  <Card className="shadow-sm">
                    <Card.Header style={{ backgroundColor: category.color, color: '#fff' }}>
                      <h4 className="mb-0">
                        <i className={category.icon || 'fas fa-folder'} style={{ marginRight: '8px' }}></i>
                        {category.name} Gallery
                        <Badge bg="light" text="dark" className="ms-2">
                          {categoryImages[category.slug]?.length || 0} Images
                        </Badge>
                      </h4>
                    </Card.Header>
                    <Card.Body>
                      <p className="text-muted mb-4">
                        {category.description || `Explore our ${category.name.toLowerCase()} collection.`}
                      </p>
                      <Gallery
                        images={(categoryImages[category.slug] || []) as EventGallery[]}
                        showDateGrouping={false}
                        showDownloadButton={true}
                        showFeaturedBadge={true}
                        gridColumns={{ lg: 3, md: 4, sm: 6 }}
                        imageHeight="220px"
                        downloadPrefix={category.slug}
                        emptyStateMessage={`No ${category.name.toLowerCase()} images available`}
                        emptyStateIcon={category.icon || 'fas fa-folder'}
                      />
                    </Card.Body>
                  </Card>
                </Tab.Pane>
              ))}
            </Tab.Content>
          </Col>
        </Row>
      </Tab.Container>

      {/* Gallery Statistics */}
      <Row className="mt-5">
        <Col>
          <Card className="bg-gradient-primary text-white">
            <Card.Body>
              <h5 className="mb-3">
                <i className="fas fa-chart-bar me-2"></i>
                Gallery Statistics
              </h5>
              <Row>
                <Col md={3} className="text-center">
                  <h3 className="mb-1">{categories.length}</h3>
                  <small>Categories</small>
                </Col>
                <Col md={3} className="text-center">
                  <h3 className="mb-1">{featuredImages.length}</h3>
                  <small>Featured Images</small>
                </Col>
                <Col md={3} className="text-center">
                  <h3 className="mb-1">{campusImages.length}</h3>
                  <small>Campus Photos</small>
                </Col>
                <Col md={3} className="text-center">
                  <h3 className="mb-1">{eventImages.length}</h3>
                  <small>Event Photos</small>
                </Col>
              </Row>
            </Card.Body>
          </Card>
        </Col>
      </Row>


    </Container>
  );
};
export default GalleryDemo;
