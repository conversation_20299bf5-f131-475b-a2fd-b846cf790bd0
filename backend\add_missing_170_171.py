#!/usr/bin/env python3

import os
import sys
import django
from datetime import datetime

# Add the project root to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'event_management.settings')
django.setup()

from participants.models import Participant, ParticipantType
from events.models import Event
from django.db import connection

def add_missing_170_171():
    """Add missing participants 170 and 171 with unique emails"""
    
    print("=== ADDING MISSING PARTICIPANTS 170 AND 171 ===")
    
    # Get Event ID 1
    try:
        event = Event.objects.get(id=1)
    except Event.DoesNotExist:
        print("Error: Event with ID 1 not found")
        return
    
    # Get participant types
    participant_types = {}
    for pt in ParticipantType.objects.all():
        participant_types[pt.id] = pt
    
    # Missing participants with unique emails
    missing_participants = [
        # (id, first_name, last_name, middle_name, email, phone, institution_name, participant_type_id, arrival_date, departure_date, profile_photo, remarks, status)
        (170, '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON>lug<PERSON>', '<EMAIL>', '092 1335855', 'Ethiopian Police University', 2, '2025-08-03 12:30:00', '2025-09-06 12:32:00', None, 'Acting President', 'active'),
        (171, '<PERSON>', 'Abdinasir', 'Muhumed', '<EMAIL>', '091 5751700', 'University of Kabridahar', 23, '2025-08-03 22:49:00', '2025-08-06 12:50:00', None, '', 'active'),
    ]
    
    added_count = 0
    
    with connection.cursor() as cursor:
        for p_data in missing_participants:
            # Check if participant already exists
            if Participant.objects.filter(id=p_data[0]).exists():
                print(f"Participant with ID {p_data[0]} already exists, skipping...")
                continue
            
            # Get participant type
            participant_type = participant_types.get(p_data[7])
            if not participant_type:
                print(f"Warning: Participant type {p_data[7]} not found for {p_data[1]} {p_data[2]}")
                continue
            
            # Parse dates
            arrival_date = None
            departure_date = None
            
            if p_data[8]:  # arrival_date
                try:
                    arrival_date = datetime.strptime(p_data[8], "%Y-%m-%d %H:%M:%S")
                except:
                    arrival_date = None
            
            if p_data[9]:  # departure_date
                try:
                    departure_date = datetime.strptime(p_data[9], "%Y-%m-%d %H:%M:%S")
                except:
                    departure_date = None
            
            # Create participant
            try:
                participant = Participant(
                    first_name=p_data[1],
                    last_name=p_data[2],
                    middle_name=p_data[3] or "",
                    email=p_data[4],
                    phone=p_data[5],
                    institution_name=p_data[6],
                    position="Participant",
                    participant_type=participant_type,
                    arrival_date=arrival_date,
                    departure_date=departure_date,
                    profile_photo=p_data[10],
                    remarks=p_data[11] or "",
                    status=p_data[12],
                    event=event
                )
                participant.save()
                
                # Set the exact ID using raw SQL
                cursor.execute(
                    "UPDATE participants_participant SET id = %s WHERE id = %s",
                    [p_data[0], participant.id]
                )
                
                added_count += 1
                print(f"Added: {p_data[1]} {p_data[2]} with ID {p_data[0]} and email {p_data[4]}")
                
            except Exception as e:
                print(f"Error adding {p_data[1]} {p_data[2]}: {str(e)}")
    
    print(f"\nSummary:")
    print(f"Added {added_count} missing participants")
    print(f"Total participants now: {Participant.objects.count()}")

if __name__ == "__main__":
    add_missing_170_171()
