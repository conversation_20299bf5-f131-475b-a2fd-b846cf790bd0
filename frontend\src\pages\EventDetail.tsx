import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, Badge, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>b, Tabs, Table, Nav } from 'react-bootstrap';
import { use<PERSON>ara<PERSON>, Link, useLocation } from 'react-router-dom';
import { eventService, participantService, documentService, Event, EventSchedule, EventGallery, EventDocument, Participant, getMediaUrl } from '../services/api';
import { useAuth } from '../contexts/AuthContext';
import PublicEventDetail from './PublicEventDetail';

// Utility function to safely format dates
const formatDate = (dateString: string): string => {
  try {
    const date = new Date(dateString);
    if (isNaN(date.getTime())) {
      return 'Invalid Date';
    }
    return date.toLocaleDateString();
  } catch (error) {
    return 'Invalid Date';
  }
};

// Utility function to safely format times
const formatTime = (timeString: string): string => {
  try {
    // Handle time format like "14:30" or "14:30:00"
    if (timeString && timeString.includes(':')) {
      // If it's just time format (HH:MM or HH:MM:SS), create a date object for today
      const timeParts = timeString.split(':');
      if (timeParts.length >= 2) {
        const hours = parseInt(timeParts[0]);
        const minutes = parseInt(timeParts[1]);

        if (!isNaN(hours) && !isNaN(minutes) && hours >= 0 && hours <= 23 && minutes >= 0 && minutes <= 59) {
          // Create a date object for formatting
          const date = new Date();
          date.setHours(hours, minutes, 0, 0);
          return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
        }
      }
    }

    // Fallback: try to parse as full date string
    const date = new Date(timeString);
    if (isNaN(date.getTime())) {
      return 'Invalid Time';
    }
    return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
  } catch (error) {
    return 'Invalid Time';
  }
};

// Helper function to get file icon based on extension
const getFileIcon = (extension: string): string => {
  switch (extension.toLowerCase()) {
    case 'pdf':
      return 'pdf';
    case 'doc':
    case 'docx':
      return 'word';
    case 'ppt':
    case 'pptx':
      return 'powerpoint';
    case 'xls':
    case 'xlsx':
      return 'excel';
    case 'txt':
      return 'text';
    default:
      return 'alt';
  }
};

const EventDetail: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const { isAuthenticated, user } = useAuth();
  const location = useLocation();

  // Check if this is an admin route
  const isAdminRoute = location.pathname.startsWith('/admin/');

  // Declare all hooks first (React hooks rules)
  const [event, setEvent] = useState<Event | null>(null);
  const [schedules, setSchedules] = useState<EventSchedule[]>([]);
  const [gallery, setGallery] = useState<EventGallery[]>([]);
  const [documents, setDocuments] = useState<EventDocument[]>([]);
  const [participants, setParticipants] = useState<Participant[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');

  useEffect(() => {
    if (id && isAdminRoute) {
      fetchEventData();
    }
  }, [id, isAdminRoute]);

  // If not admin route, render public version
  if (!isAdminRoute) {
    return <PublicEventDetail />;
  }


  const fetchEventData = async () => {
    try {
      const [eventResponse, scheduleResponse, galleryResponse, documentsResponse, participantsResponse] = await Promise.allSettled([
        eventService.getEvent(parseInt(id!)),
        eventService.getEventSchedule(parseInt(id!)),
        eventService.getEventGallery(parseInt(id!)),
        documentService.getDocuments(parseInt(id!)),
        eventService.getEventParticipants(parseInt(id!)),
      ]);

      setEvent(eventResponse.status === 'fulfilled' ? eventResponse.value.data : null);
      setSchedules(scheduleResponse.status === 'fulfilled' ? scheduleResponse.value.data || [] : []);
      setGallery(galleryResponse.status === 'fulfilled' ? galleryResponse.value.data?.slice(0, 6) || [] : []); // Show only first 6 images
      // Fix: API returns paginated response with results array
      setDocuments(documentsResponse.status === 'fulfilled' ? documentsResponse.value.data.results || [] : []);
      setParticipants(participantsResponse.status === 'fulfilled' ? participantsResponse.value.data || [] : []); // Show all participants
    } catch (error) {
      console.error('Error fetching event data:', error);
      setError('Failed to load event details');
    } finally {
      setLoading(false);
    }
  };

  const handleDocumentDownload = async (doc: EventDocument) => {
    try {
      const response = await documentService.downloadDocument(doc.id);

      // Create blob and download
      const blob = new Blob([response.data]);
      const url = window.URL.createObjectURL(blob);
      const link = window.document.createElement('a');
      link.href = url;
      link.download = doc.file.split('/').pop() || doc.title;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);
    } catch (error) {
      console.error('Error downloading document:', error);
      alert('Failed to download document');
    }
  };

  if (loading) {
    return (
      <Container className="text-center mt-5">
        <Spinner animation="border" role="status">
          <span className="visually-hidden">Loading...</span>
        </Spinner>
        <p className="mt-3">Loading event details...</p>
      </Container>
    );
  }

  if (error) {
    return (
      <Container className="py-4">
        <Alert variant="danger">
          <i className="fas fa-exclamation-triangle me-2"></i>
          {error}
        </Alert>
      </Container>
    );
  }

  if (!event) {
    return (
      <Container className="py-4">
        <Alert variant="warning">
          <i className="fas fa-question-circle me-2"></i>
          Event not found
        </Alert>
      </Container>
    );
  }

  const upcomingSchedules = schedules.slice(0, 3);
  const featuredGallery = gallery.filter(img => img.is_featured).slice(0, 3);
  const recentGallery = featuredGallery.length > 0 ? featuredGallery : gallery.slice(0, 3);

  const getEventStatus = (event: Event) => {
    const now = new Date();
    const startDate = new Date(event.start_date);
    const endDate = new Date(event.end_date);

    if (!event.is_active) {
      return <Badge bg="secondary">Inactive</Badge>;
    } else if (startDate > now) {
      return <Badge bg="info">Upcoming</Badge>;
    } else if (startDate <= now && endDate >= now) {
      return <Badge bg="success">Ongoing</Badge>;
    } else {
      return <Badge bg="warning">Completed</Badge>;
    }
  };

  const getParticipantStats = () => {
    const confirmed = participants.filter(p => p.is_confirmed).length;
    const pending = participants.filter(p => !p.is_confirmed).length;
    const withHotel = participants.filter(p => p.assigned_hotel).length;
    const withDriver = participants.filter(p => p.assigned_driver).length;

    return { confirmed, pending, withHotel, withDriver, total: participants.length };
  };

  const stats = getParticipantStats();

  return (
    <Container className="py-4 event-detail-container">
      {/* Management Header */}
      <Row className="mb-4">
        <Col>
          <div className="d-flex justify-content-between align-items-start mb-3">
            <div>
              <Link to="/admin/events" className="btn btn-outline-secondary mb-3">
                <i className="fas fa-arrow-left me-2"></i>
                Back to Events
              </Link>
              <h1 className="display-5 fw-bold text-primary mb-2">{event.name}</h1>
              <div className="d-flex gap-2 mb-3">
                {getEventStatus(event)}
                <Badge bg="primary">
                  <i className="fas fa-users me-1"></i>
                  {stats.total} Registered
                </Badge>
                <Badge bg="info">
                  <i className="fas fa-calendar me-1"></i>
                  {formatDate(event.start_date)}
                </Badge>
              </div>
            </div>
            {/* Only show management buttons to authenticated administrators */}
            {isAuthenticated && user?.role_name === 'administrator' && (
              <div className="d-flex gap-2">
                <Link to={`/events/${event.id}/edit`} className="btn btn-outline-primary">
                  <i className="fas fa-edit me-2"></i>
                  Edit Event
                </Link>
                <Link to={`/participants/manage?event=${event.id}`} className="btn btn-primary">
                  <i className="fas fa-users me-2"></i>
                  Manage Participants
                </Link>
              </div>
            )}
          </div>
        </Col>
      </Row>

      {/* Event Header */}
      <Row className="mb-5">
        <Col>
          {event.banner && (
            <div className="position-relative mb-4">
              <img
                src={getMediaUrl(event.banner)}
                alt={event.name}
                className="img-fluid rounded shadow"
                style={{ width: '100%', height: '300px', objectFit: 'cover' }}
              />
              <div className="position-absolute top-0 start-0 w-100 h-100 bg-dark bg-opacity-25 rounded d-flex align-items-end">
                <div className="p-4 text-white">
                  <h1 className="display-4 fw-bold mb-2">{event.name}</h1>
                  <p className="lead mb-0">{event.description}</p>
                </div>
              </div>
            </div>
          )}

          {!event.banner && (
            <div className="text-center mb-4">
              <h1 className="display-4 fw-bold mb-2">{event.name}</h1>
              <p className="lead text-muted">{event.description}</p>
            </div>
          )}
        </Col>
      </Row>

      {/* Event Info Cards */}
      <Row className="mb-5">
        <Col md={3}>
          <Card className="text-center border-primary h-100">
            <Card.Body>
              <i className="fas fa-calendar-alt text-primary fa-2x mb-3"></i>
              <h6 className="text-muted">Duration</h6>
              <p className="fw-bold">
                {formatDate(event.start_date)} - {' '}
                {formatDate(event.end_date)}
              </p>
            </Card.Body>
          </Card>
        </Col>

        <Col md={3}>
          <Card className="text-center border-success h-100">
            <Card.Body>
              <i className="fas fa-map-marker-alt text-success fa-2x mb-3"></i>
              <h6 className="text-muted">Location</h6>
              <p className="fw-bold">{event.location}</p>
              <small className="text-muted">{event.city}, {event.country}</small>
            </Card.Body>
          </Card>
        </Col>

        <Col md={3}>
          <Card className="text-center border-info h-100">
            <Card.Body>
              <i className="fas fa-users text-info fa-2x mb-3"></i>
              <h6 className="text-muted">Participants</h6>
              <p className="fw-bold">{event.participant_count}</p>
              <small className="text-muted">Registered</small>
            </Card.Body>
          </Card>
        </Col>

        <Col md={3}>
          <Card className="text-center border-warning h-100">
            <Card.Body>
              <i className="fas fa-user text-warning fa-2x mb-3"></i>
              <h6 className="text-muted">Organizer</h6>
              <p className="fw-bold">{event.organizer_name}</p>
              <small className="text-muted">
                <a href={`mailto:${event.organizer_email}`}>
                  {event.organizer_email}
                </a>
              </small>
            </Card.Body>
          </Card>
        </Col>
      </Row>

      {/* Action Buttons */}
      <Row className="mb-5">
        <Col className="text-center">
          <div className="d-flex justify-content-center gap-3 flex-wrap">
            <Link to="/participant-register" className="btn btn-primary btn-lg">
              <i className="fas fa-user-plus me-2"></i>
              Register Now
            </Link>
            <Link to={`/events/${event.id}/schedule`} className="btn btn-outline-primary btn-lg">
              <i className="fas fa-clock me-2"></i>
              View Schedule
            </Link>
            <Link to={`/events/${event.id}/gallery`} className="btn btn-outline-primary btn-lg">
              <i className="fas fa-images me-2"></i>
              Photo Gallery
            </Link>
            <Button variant="outline-secondary" size="lg" disabled>
              <i className="fas fa-map me-2"></i>
              Event Map
            </Button>
          </div>
        </Col>
      </Row>

      {/* Content Tabs */}
      <Tabs defaultActiveKey="overview" className="mb-4">
        <Tab eventKey="overview" title="Overview">
          <Row>
            <Col lg={8}>
              <Card className="mb-4">
                <Card.Header>
                  <h5 className="mb-0">
                    <i className="fas fa-info-circle me-2"></i>
                    About This Event
                  </h5>
                </Card.Header>
                <Card.Body>
                  <p>{event.description}</p>

                  <h6 className="mt-4 mb-3">Event Details</h6>
                  <Row>
                    <Col sm={6}>
                      <strong>Start Date:</strong><br />
                      {formatDate(event.start_date)}
                    </Col>
                    <Col sm={6}>
                      <strong>End Date:</strong><br />
                      {formatDate(event.end_date)}
                    </Col>
                  </Row>

                  <Row className="mt-3">
                    <Col sm={6}>
                      <strong>Venue:</strong><br />
                      {event.location}
                    </Col>
                    <Col sm={6}>
                      <strong>City:</strong><br />
                      {event.city}, {event.country}
                    </Col>
                  </Row>

                  {event.map_embed && (
                    <div className="mt-4">
                      <h6>Location Map</h6>
                      <div
                        className="map-embed rounded shadow-sm"
                        style={{
                          position: 'relative',
                          paddingBottom: '56.25%', // 16:9 aspect ratio
                          height: 0,
                          overflow: 'hidden'
                        }}
                        dangerouslySetInnerHTML={{
                          __html: event.map_embed.replace(
                            'width="600" height="450"',
                            'width="100%" height="100%" style="position: absolute; top: 0; left: 0; border: 0; border-radius: 8px;"'
                          )
                        }}
                      />
                    </div>
                  )}

                  {!event.map_embed && (event.latitude && event.longitude) && (
                    <div className="mt-4">
                      <h6>Location Map</h6>
                      <div className="bg-light rounded p-3 text-center">
                        <i className="fas fa-map-marked-alt fa-3x text-muted mb-2"></i>
                        <p className="text-muted mb-0">
                          Interactive map coming soon<br />
                          <small>Coordinates: {event.latitude}, {event.longitude}</small>
                        </p>
                      </div>
                    </div>
                  )}
                </Card.Body>
              </Card>
            </Col>

            <Col lg={4}>
              <Card className="mb-4">
                <Card.Header>
                  <h6 className="mb-0">
                    <i className="fas fa-phone me-2"></i>
                    Contact Information
                  </h6>
                </Card.Header>
                <Card.Body>
                  <div className="mb-3">
                    <strong>Organizer:</strong><br />
                    {event.organizer_name}
                  </div>
                  <div className="mb-3">
                    <strong>Email:</strong><br />
                    <a href={`mailto:${event.organizer_email}`}>
                      {event.organizer_email}
                    </a>
                  </div>
                  <div>
                    <strong>Phone:</strong><br />
                    <a href={`tel:${event.organizer_phone}`}>
                      {event.organizer_phone}
                    </a>
                  </div>
                </Card.Body>
              </Card>

              <Card>
                <Card.Header>
                  <h6 className="mb-0">
                    <i className="fas fa-share-alt me-2"></i>
                    Share Event
                  </h6>
                </Card.Header>
                <Card.Body>
                  <div className="d-grid gap-2">
                    <Button
                      variant="outline-primary"
                      size="sm"
                      onClick={() => {
                        navigator.clipboard.writeText(window.location.href);
                        alert('Event URL copied to clipboard!');
                      }}
                    >
                      <i className="fas fa-copy me-2"></i>
                      Copy Link
                    </Button>
                    <Button
                      variant="outline-info"
                      size="sm"
                      onClick={() => {
                        const text = `Check out ${event.name} - ${event.description}`;
                        const url = window.location.href;
                        window.open(`https://twitter.com/intent/tweet?text=${encodeURIComponent(text)}&url=${encodeURIComponent(url)}`);
                      }}
                    >
                      <i className="fab fa-twitter me-2"></i>
                      Share on Twitter
                    </Button>
                  </div>
                </Card.Body>
              </Card>
            </Col>
          </Row>
        </Tab>

        <Tab eventKey="documents" title={`Documents (${documents.length})`}>
          <Card>
            <Card.Header>
              <h5 className="mb-0">
                <i className="fas fa-file-alt me-2"></i>
                Event Documents
              </h5>
            </Card.Header>
            <Card.Body>
              {documents.length === 0 ? (
                <div className="text-center py-4">
                  <i className="fas fa-file-alt text-muted fa-3x mb-3"></i>
                  <p className="text-muted">No documents available yet</p>
                </div>
              ) : (
                <div className="row">
                  {documents && documents.length > 0 && documents.map((doc) => (
                    <div key={doc.id} className="col-md-6 col-lg-4 mb-3">
                      <Card className="h-100 border-0 shadow-sm">
                        <Card.Body className="d-flex flex-column">
                          <div className="d-flex align-items-center mb-2">
                            <div className="me-3">
                              <i className={`fas fa-file-${getFileIcon(doc.file_extension)} fa-2x text-primary`}></i>
                            </div>
                            <div className="flex-grow-1">
                              <h6 className="mb-1">{doc.title}</h6>
                              <small className="text-muted">
                                {doc.document_type_display} • {doc.formatted_file_size}
                              </small>
                            </div>
                          </div>
                          {doc.description && (
                            <p className="text-muted small mb-3">{doc.description}</p>
                          )}
                          <div className="mt-auto">
                            <div className="d-flex justify-content-between align-items-center">
                              <small className="text-muted">
                                <i className="fas fa-download me-1"></i>
                                {doc.download_count} downloads
                              </small>
                              <Button
                                variant="outline-primary"
                                size="sm"
                                onClick={() => handleDocumentDownload(doc)}
                              >
                                <i className="fas fa-download me-1"></i>
                                Download
                              </Button>
                            </div>
                          </div>
                        </Card.Body>
                      </Card>
                    </div>
                  )) || null}
                </div>
              )}
            </Card.Body>
          </Card>
        </Tab>

        <Tab eventKey="gallery" title={`Gallery (${gallery.length})`}>
          <Card>
            <Card.Header className="d-flex justify-content-between align-items-center">
              <h5 className="mb-0">
                <i className="fas fa-images me-2"></i>
                Event Photos
              </h5>
              <Link to={`/events/${event.id}/gallery`} className="btn btn-primary btn-sm">
                View All Photos
              </Link>
            </Card.Header>
            <Card.Body>
              {recentGallery.length === 0 ? (
                <div className="text-center py-4">
                  <i className="fas fa-images text-muted fa-3x mb-3"></i>
                  <p className="text-muted">No photos available yet</p>
                </div>
              ) : (
                <Row>
                  {recentGallery.map((image) => (
                    <Col md={4} key={image.id} className="mb-3">
                      <div className="position-relative">
                        <img
                          src={getMediaUrl(image.image)}
                          alt={image.title}
                          className="img-fluid rounded shadow-sm"
                          style={{ width: '100%', height: '200px', objectFit: 'cover' }}
                        />
                        {image.is_featured && (
                          <Badge
                            bg="warning"
                            className="position-absolute top-0 start-0 m-2"
                          >
                            <i className="fas fa-star me-1"></i>
                            Featured
                          </Badge>
                        )}
                        <div className="mt-2">
                          <h6 className="small mb-1">{image.title}</h6>
                          <small className="text-muted">
                            {formatDate(image.uploaded_at)}
                          </small>
                        </div>
                      </div>
                    </Col>
                  ))}
                </Row>
              )}
            </Card.Body>
          </Card>
        </Tab>

        <Tab eventKey="participants" title={`Participants (${participants.length})`}>
          <Card>
            <Card.Header>
              <h5 className="mb-0">
                <i className="fas fa-users me-2"></i>
                Registered Participants
              </h5>
            </Card.Header>
            <Card.Body>
              {participants.length === 0 ? (
                <div className="text-center py-4">
                  <i className="fas fa-user-times text-muted fa-3x mb-3"></i>
                  <p className="text-muted">No participants registered yet</p>
                </div>
              ) : (
                <Row>
                  {participants.map((participant) => (
                    <Col lg={6} key={participant.id} className="mb-3">
                      <div className="d-flex align-items-center p-3 border rounded">
                        <div className="me-3">
                          <div
                            className="rounded-circle d-flex align-items-center justify-content-center text-white fw-bold"
                            style={{
                              width: '50px',
                              height: '50px',
                              backgroundColor: participant.participant_type_color
                            }}
                          >
                            {participant.full_name.split(' ').map(n => n[0]).join('').substring(0, 2)}
                          </div>
                        </div>
                        <div className="flex-grow-1">
                          <h6 className="mb-1">{participant.full_name}</h6>
                          <p className="text-muted small mb-1">{participant.position}</p>
                          <p className="text-muted small mb-0">{participant.institution_name}</p>
                        </div>
                        <Badge
                          style={{ backgroundColor: participant.participant_type_color }}
                          className="ms-2"
                        >
                          {participant.participant_type_name}
                        </Badge>
                      </div>
                    </Col>
                  ))}
                </Row>
              )}

              {participants.length > 0 && (
                <div className="text-center mt-4">
                  <Link to="/participants" className="btn btn-outline-primary">
                    <i className="fas fa-users me-2"></i>
                    View All Participants
                  </Link>
                </div>
              )}
            </Card.Body>
          </Card>
        </Tab>
      </Tabs>
    </Container>
  );
};

export default EventDetail;
