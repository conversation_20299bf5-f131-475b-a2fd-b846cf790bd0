/* Modern Landing Page Styles */

/* Professional color scheme - University of Gondar */
:root {
  --primary-color: #007bff;
  --secondary-color: #6c757d;
  --success-color: #28a745;
  --info-color: #17a2b8;
  --warning-color: #ffc107;
  --danger-color: #dc3545;
  --light-color: #f8f9fa;
  --dark-color: #343a40;
  --white: #ffffff;
  --uog-blue: #003366;
  --uog-gold: #FFD700;
  --uog-light-blue: #4A90E2;
  --gradient-primary: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
  --gradient-uog: linear-gradient(135deg, var(--uog-blue) 0%, var(--uog-light-blue) 100%);
  --gradient-secondary: linear-gradient(135deg, #6c757d 0%, #495057 100%);
  --shadow-sm: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
  --shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
  --shadow-lg: 0 1rem 3rem rgba(0, 0, 0, 0.175);
}

/* Global reset and modern typography */
html, body {
  margin: 0;
  padding: 0;
  overflow-x: hidden;
  width: 100%;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  line-height: 1.6;
}

/* Modern landing page container */
.landing-page {
  width: 100%;
  margin: 0;
  padding: 0;
  overflow-x: hidden;
}

/* Landing Page Navigation */
.landing-nav {
  backdrop-filter: blur(15px);
  background: rgba(255, 255, 255, 0.98) !important;
  transition: all 0.3s ease;
  border-bottom: none;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
}

.landing-nav .modern-container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 2rem;
}

.landing-nav a {
  transition: all 0.3s ease;
  font-weight: 500;
  padding: 8px 16px;
  border-radius: 8px;
  border: 1px solid transparent;
}

.landing-nav a:hover {
  color: #667eea !important;
  transform: translateY(-1px);
  background: rgba(102, 126, 234, 0.1);
  border-color: rgba(102, 126, 234, 0.2);
  box-shadow: 0 2px 8px rgba(102, 126, 234, 0.15);
}

/* Hero Section */
.hero-section {
  min-height: 100vh;
  position: relative;
  overflow: hidden;
  width: 100%;
}

.hero-background {
  background-size: cover;
  background-position: center;
  background-attachment: fixed;
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
}

.particles {
  pointer-events: none;
}

.particle {
  border-radius: 50%;
  animation: float 3s ease-in-out infinite alternate;
}

.hero-content .modern-container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 2rem;
}

.hero-title, .hero-subtitle, .hero-description {
  animation: fadeInUp 1s ease-out;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.hero-subtitle {
  animation-delay: 0.2s;
  opacity: 0;
  animation-fill-mode: forwards;
}

.hero-description {
  animation-delay: 0.4s;
  opacity: 0;
  animation-fill-mode: forwards;
}

.hero-actions {
  animation: fadeInUp 1s ease-out 0.6s both;
}

.event-details {
  animation: fadeInUp 1s ease-out 0.8s both;
}

.slide-indicators {
  display: flex;
  justify-content: center;
  gap: 10px;
  margin-top: 2rem;
}

.indicator {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  border: 2px solid rgba(255, 255, 255, 0.5);
  background: transparent;
  cursor: pointer;
  transition: all 0.3s ease;
  outline: none;
}

.indicator:hover {
  border-color: rgba(255, 255, 255, 0.8);
  transform: scale(1.1);
}

.indicator.active {
  background: white;
  border-color: white;
  box-shadow: 0 0 10px rgba(255, 255, 255, 0.5);
}

/* Section Containers */
.section-container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 2rem;
}

/* Modern section spacing */
section {
  position: relative;
}

section:not(:last-child) {
  margin-bottom: 0;
}

/* Add subtle section separators */
.university-section::after,
.attractions-section::after,
.campus-section::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 100px;
  height: 2px;
  background: linear-gradient(90deg, transparent, rgba(0,0,0,0.1), transparent);
}

/* University Section */
.university-section {
  position: relative;
  overflow: hidden;
}

.stat-card {
  transition: all 0.3s ease;
  border: 1px solid rgba(0, 0, 0, 0.05);
  backdrop-filter: blur(10px);
  border-radius: 16px !important;
  overflow: hidden;
}

.stat-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1) !important;
  border-color: rgba(102, 126, 234, 0.2);
}

.counter {
  font-size: 2.5rem;
  font-weight: 700;
}

.highlight-item {
  transition: transform 0.3s ease;
}

.highlight-item:hover {
  transform: translateX(10px);
}

/* Attractions Section */
.attraction-card {
  transition: all 0.3s ease;
  border: 1px solid rgba(0, 0, 0, 0.05);
  backdrop-filter: blur(10px);
  border-radius: 20px !important;
  overflow: hidden;
}

.attraction-card:hover {
  transform: translateY(-10px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15) !important;
  border-color: rgba(102, 126, 234, 0.2);
}

.attraction-icon {
  transition: all 0.3s ease;
}

.attraction-card:hover .attraction-icon {
  transform: scale(1.1) rotate(5deg);
}

/* Campus Visit Section */
.visit-option {
  transition: all 0.3s ease;
  border: 1px solid rgba(0, 0, 0, 0.05);
  backdrop-filter: blur(10px);
  border-radius: 20px !important;
  overflow: hidden;
}

.visit-option:hover {
  transform: translateY(-5px);
  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1) !important;
  border-color: rgba(255, 255, 255, 0.3);
}

/* Features Section */
.features-section {
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
}

.feature-card {
  transition: all 0.3s ease;
  border: 1px solid rgba(0, 0, 0, 0.05);
  backdrop-filter: blur(10px);
  border-radius: 20px !important;
  overflow: hidden;
}

.feature-card:hover {
  transform: translateY(-10px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15) !important;
  border-color: rgba(102, 126, 234, 0.2);
}

.feature-icon {
  transition: all 0.3s ease;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.feature-card:hover .feature-icon {
  transform: scale(1.1) rotate(5deg);
  box-shadow: 0 15px 40px rgba(0, 0, 0, 0.2);
}

/* CTA Section */
.cta-section {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  position: relative;
  overflow: hidden;
}

/* Button Styles */
.btn {
  transition: all 0.3s ease;
  border: none;
  font-weight: 600;
  letter-spacing: 0.5px;
}

.btn-lg {
  padding: 12px 30px;
  font-size: 1.1rem;
}

.btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
}

.btn-light:hover {
  background: #ffffff;
  color: #667eea;
}

.btn-outline-light:hover {
  background: rgba(255, 255, 255, 0.1);
  border-color: white;
  color: white;
}

.rounded-pill {
  border-radius: 50px !important;
}

.shadow-lg {
  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1) !important;
}

/* Flag Animation */
.flag-icon {
  animation: bounce 2s ease-in-out infinite;
}

/* Footer */
footer {
  background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%) !important;
}

/* Animations */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes float {
  from {
    transform: translateY(0px);
  }
  to {
    transform: translateY(-20px);
  }
}

@keyframes bounce {
  0%, 20%, 50%, 80%, 100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-10px);
  }
  60% {
    transform: translateY(-5px);
  }
}

/* Responsive Design */
@media (max-width: 1200px) {
  .section-container,
  .hero-content .modern-container,
  .landing-nav .modern-container {
    max-width: 1140px;
    padding: 0 1.5rem;
  }
}

@media (max-width: 992px) {
  .section-container,
  .hero-content .modern-container,
  .landing-nav .modern-container {
    max-width: 960px;
    padding: 0 1rem;
  }
}

@media (max-width: 768px) {
  .section-container,
  .hero-content .modern-container,
  .landing-nav .modern-container {
    padding: 0 1rem;
  }

  .hero-title {
    font-size: 2.5rem !important;
  }

  .hero-subtitle {
    font-size: 1.8rem !important;
  }

  .hero-description {
    font-size: 1.1rem !important;
  }

  .btn-lg {
    padding: 10px 25px;
    font-size: 1rem;
  }

  .particles {
    display: none;
  }

  .campus-life-text {
    font-size: 3rem !important;
    letter-spacing: 0.1em;
  }

  .campus-card {
    margin-bottom: 2rem;
  }
}

@media (max-width: 576px) {
  .section-container,
  .hero-content .modern-container,
  .landing-nav .modern-container {
    padding: 0 0.75rem;
  }

  .hero-title {
    font-size: 2rem !important;
  }

  .hero-subtitle {
    font-size: 1.5rem !important;
  }

  .campus-life-text {
    font-size: 2.5rem !important;
    letter-spacing: 0.05em;
  }

  .countdown-timer h5 {
    font-size: 1rem;
  }

  .countdown-number {
    font-size: 1.8rem !important;
  }

  .stat-number {
    font-size: 1.5rem !important;
  }

  .hero-glass-panel {
    padding: 1.5rem;
  }

  .hero-glass-card {
    padding: 0.75rem !important;
  }
}

/* Campus Life Section Styles */
.campus-life-section {
  min-height: 100vh;
}

.campus-background {
  z-index: 1;
}

.campus-life-text {
  font-size: 6rem;
  letter-spacing: 0.2em;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
  font-weight: 900;
}

.campus-card {
  transition: all 0.3s ease;
  border: none;
  backdrop-filter: blur(10px);
}

.campus-card:hover {
  transform: translateY(-10px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2) !important;
}

.campus-card-image img {
  transition: transform 0.3s ease;
}

.campus-card:hover .campus-card-image img {
  transform: scale(1.05);
}

.campus-nav-btn {
  width: 50px;
  height: 50px;
  border: none;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.9);
  color: #333;
  font-size: 1.2rem;
  transition: all 0.3s ease;
  z-index: 10;
}

.campus-nav-btn:hover {
  background: white;
  transform: scale(1.1);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

/* Legacy hero section styles - updated for glassmorphism */
.event-stats .stat-item {
  transition: all 0.3s ease;
}

.countdown-timer {
  position: relative;
}

.countdown-item {
  transition: all 0.3s ease;
  position: relative;
}

/* Remove space between sections */
section {
  margin-bottom: 0 !important;
}

/* Scroll offset adjustment */
section {
  scroll-margin-top: 80px;
}

/* Developer hover cards */
.developer-item {
  cursor: pointer;
  transition: transform 0.3s ease;
}

.developer-item:hover {
  transform: scale(1.1);
}

.developer-card {
  display: none;
  bottom: 100%;
  left: 50%;
  transform: translateX(-50%);
  width: 280px;
  z-index: 1000;
  border: 1px solid rgba(0, 0, 0, 0.1);
}

.developer-item:hover .developer-card {
  display: block;
  animation: fadeInUp 0.3s ease-out;
}

.developer-photo,
.developer-avatar {
  transition: all 0.3s ease;
  border: 2px solid transparent;
}

.developer-item:hover .developer-photo,
.developer-item:hover .developer-avatar {
  border-color: #667eea;
  box-shadow: 0 0 15px rgba(102, 126, 234, 0.3);
}

/* Event category cards */
.event-category-card {
  transition: all 0.3s ease;
  border: 1px solid rgba(0, 0, 0, 0.05);
  border-radius: 20px !important;
  overflow: hidden;
}

.event-category-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1) !important;
  border-color: rgba(102, 126, 234, 0.2);
}

.category-icon {
  transition: all 0.3s ease;
}

.event-category-card:hover .category-icon {
  transform: scale(1.1);
}

/* System cards */
.system-card {
  transition: all 0.3s ease;
  border: 1px solid rgba(0, 0, 0, 0.05);
  border-radius: 20px !important;
  overflow: hidden;
}

.system-card:hover {
  transform: translateY(-10px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15) !important;
  border-color: rgba(102, 126, 234, 0.2);
}

.system-icon {
  transition: all 0.3s ease;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.system-card:hover .system-icon {
  transform: scale(1.1) rotate(5deg);
  box-shadow: 0 15px 40px rgba(0, 0, 0, 0.2);
}

/* Hero glassmorphism effects */
.hero-glass-panel {
  backdrop-filter: blur(20px);
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 20px;
  padding: 2rem;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.hero-glass-card {
  backdrop-filter: blur(15px);
  background: rgba(255, 255, 255, 0.08);
  border: 1px solid rgba(255, 255, 255, 0.15);
  border-radius: 16px;
  transition: all 0.3s ease;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
}

.hero-glass-card:hover {
  background: rgba(255, 255, 255, 0.12);
  border-color: rgba(255, 255, 255, 0.25);
  transform: translateY(-2px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
}

/* Enhanced statistics styling */
.stat-number {
  font-size: 2rem;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
  font-weight: 800;
}

.stat-label {
  font-weight: 600;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
  opacity: 0.9;
}

/* Enhanced countdown styling */
.countdown-title {
  font-weight: 600;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.countdown-number {
  font-size: 2.5rem;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
  font-weight: 800;
  background: linear-gradient(45deg, #fff, #f0f0f0);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.countdown-label {
  font-weight: 600;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
  opacity: 0.9;
}

/* Animated glow effects */
.hero-glass-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  border-radius: 16px;
  padding: 1px;
  background: linear-gradient(45deg, rgba(255,255,255,0.2), rgba(255,255,255,0.05));
  mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
  mask-composite: exclude;
  pointer-events: none;
}

/* Hero actions panel specific styling */
.hero-actions-panel {
  position: relative;
}

.hero-actions-panel::before {
  content: '';
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  border-radius: 22px;
  background: linear-gradient(45deg, rgba(255,255,255,0.1), rgba(255,255,255,0.05));
  z-index: -1;
}

/* Smooth scrolling */
html {
  scroll-behavior: smooth;
}

/* Scroll offset for fixed navigation */
section {
  scroll-margin-top: 100px;
}

/* Footer styles */
.footer-link {
  color: rgba(255, 255, 255, 0.95) !important;
  text-decoration: none;
  transition: all 0.3s ease;
  font-size: 0.9rem;
  font-weight: 500;
}

.footer-link:hover {
  color: white !important;
  padding-left: 0.5rem;
  text-shadow: 0 0 8px rgba(255, 255, 255, 0.5);
  transform: translateX(5px);
}

/* Footer text justification */
.footer-description {
  text-align: justify !important;
  line-height: 1.6 !important;
}

/* Improve footer text visibility */
.footer-section .text-light {
  color: rgba(255, 255, 255, 0.95) !important;
}

.footer-section .text-white {
  color: white !important;
  font-weight: 600;
}

.footer-copyright,
.footer-credits {
  font-size: 0.9rem;
  opacity: 0.9;
}

/* Professional Branding Styles - University of Gondar */
.navbar-brand img,
.footer-logo {
  transition: all 0.3s ease;
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.1));
}

.navbar-brand:hover img {
  transform: scale(1.05);
  filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.15));
}

.footer-logo {
  max-height: 80px;
  width: auto;
  object-fit: contain;
}

.organization-logo {
  text-align: center;
}

.organization-logo img {
  transition: all 0.3s ease;
}

.organization-logo:hover img {
  transform: scale(1.05);
}

/* Professional color accents */
.text-uog-blue {
  color: var(--uog-blue) !important;
}

.text-uog-gold {
  color: var(--uog-gold) !important;
}

.bg-uog-blue {
  background-color: var(--uog-blue) !important;
}

.bg-uog-gold {
  background-color: var(--uog-gold) !important;
}

.btn-uog {
  background: var(--gradient-uog);
  border: none;
  color: white;
}

.btn-uog:hover {
  background: var(--uog-blue);
  color: white;
  transform: translateY(-2px);
  box-shadow: 0 8px 20px rgba(0, 51, 102, 0.3);
}

/* Professional social links */
.social-link {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.1);
  color: rgba(255, 255, 255, 0.8);
  text-decoration: none;
  transition: all 0.3s ease;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.social-link:hover {
  background: rgba(255, 255, 255, 0.2);
  color: white;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(255, 255, 255, 0.2);
  border-color: rgba(255, 255, 255, 0.4);
}

/* Professional typography */
.professional-heading {
  font-family: 'Georgia', serif;
  font-weight: 600;
  letter-spacing: 0.5px;
}

.professional-text {
  font-family: 'Inter', sans-serif;
  line-height: 1.7;
}

/* Enhanced footer branding */
.footer-section {
  background: linear-gradient(135deg, var(--uog-blue) 0%, #1a365d 100%) !important;
  position: relative;
  overflow: hidden;
}

.footer-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="50" cy="50" r="1" fill="rgba(255,255,255,0.02)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
  opacity: 0.5;
  pointer-events: none;
}

.footer-content {
  position: relative;
  z-index: 1;
}

.footer-bottom {
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  background: rgba(0, 0, 0, 0.2);
}
