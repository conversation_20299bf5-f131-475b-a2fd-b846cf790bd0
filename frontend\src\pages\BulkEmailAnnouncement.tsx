import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON><PERSON>, <PERSON>, Col, <PERSON>, <PERSON>, <PERSON><PERSON>, Al<PERSON>, <PERSON><PERSON>, <PERSON>ge, ProgressBar } from 'react-bootstrap';
import { eventService, participantTypeService, organizationService } from '../services/api';
import { Event, Participant, ParticipantType } from '../services/api';

interface BulkEmailData {
  event_id: string;
  subject: string;
  content: string;
  recipient_type: 'all' | 'participant_type';
  participant_type_ids: number[];
}

interface EmailStats {
  success_count: number;
  failed_count: number;
  total_count: number;
}

const BulkEmailAnnouncement: React.FC = () => {
  const [events, setEvents] = useState<Event[]>([]);
  const [participantTypes, setParticipantTypes] = useState<ParticipantType[]>([]);
  const [loading, setLoading] = useState(true);
  const [sending, setSending] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');
  const [emailStats, setEmailStats] = useState<EmailStats | null>(null);
  
  const [formData, setFormData] = useState<BulkEmailData>({
    event_id: '',
    subject: '',
    content: '',
    recipient_type: 'all',
    participant_type_ids: []
  });

  const [previewData, setPreviewData] = useState({
    subject: 'Your subject will appear here...',
    content: 'Your content will appear here...'
  });

  useEffect(() => {
    loadInitialData();
  }, []);

  useEffect(() => {
    updatePreview();
  }, [formData.subject, formData.content]);

  const loadInitialData = async () => {
    try {
      setLoading(true);
      const [eventsResponse, participantTypesResponse] = await Promise.all([
        eventService.getEvents(),
        participantTypeService.getParticipantTypes()
      ]);
      
      setEvents(eventsResponse.data.results || eventsResponse.data);
      setParticipantTypes(participantTypesResponse.data);
    } catch (err) {
      setError('Failed to load data. Please refresh the page.');
      console.error('Error loading data:', err);
    } finally {
      setLoading(false);
    }
  };

  const updatePreview = () => {
    const sampleData = {
      participant_name: 'John Doe',
      event_name: 'Sample Event',
      event_date: 'January 15, 2024',
      event_location: 'University Campus'
    };

    let previewSubject = formData.subject || 'Your subject will appear here...';
    let previewContent = formData.content || 'Your content will appear here...';

    // Replace placeholders with sample data
    Object.entries(sampleData).forEach(([key, value]) => {
      const placeholder = `{${key}}`;
      previewSubject = previewSubject.replace(new RegExp(placeholder, 'g'), value);
      previewContent = previewContent.replace(new RegExp(placeholder, 'g'), value);
    });

    setPreviewData({
      subject: previewSubject,
      content: previewContent
    });
  };

  const handleInputChange = (field: keyof BulkEmailData, value: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
    setError('');
    setSuccess('');
  };

  const handleParticipantTypeChange = (typeId: number, checked: boolean) => {
    setFormData(prev => ({
      ...prev,
      participant_type_ids: checked
        ? [...prev.participant_type_ids, typeId]
        : prev.participant_type_ids.filter(id => id !== typeId)
    }));
  };

  const validateForm = (): boolean => {
    if (!formData.event_id) {
      setError('Please select an event.');
      return false;
    }
    if (!formData.subject.trim()) {
      setError('Please enter an email subject.');
      return false;
    }
    if (!formData.content.trim()) {
      setError('Please enter email content.');
      return false;
    }
    if (formData.recipient_type === 'participant_type' && formData.participant_type_ids.length === 0) {
      setError('Please select at least one participant type.');
      return false;
    }
    return true;
  };

  const sendBulkEmail = async () => {
    if (!validateForm()) return;

    try {
      setSending(true);
      setError('');
      setSuccess('');
      setEmailStats(null);

      const response = await organizationService.sendBulkEmail(formData);
      const data = response.data;

      if (data.success) {
        setSuccess(data.message);
        setEmailStats({
          success_count: data.success_count,
          failed_count: data.failed_count,
          total_count: data.total_count
        });
        
        // Reset form
        setFormData({
          event_id: '',
          subject: '',
          content: '',
          recipient_type: 'all',
          participant_type_ids: []
        });
      } else {
        setError(data.error || 'Failed to send emails. Please try again.');
      }
    } catch (err) {
      setError('Network error. Please check your connection and try again.');
      console.error('Error sending bulk email:', err);
    } finally {
      setSending(false);
    }
  };

  const selectedEvent = events.find(e => e.id === parseInt(formData.event_id));

  if (loading) {
    return (
      <Container className="py-5">
        <div className="text-center">
          <Spinner animation="border" variant="primary" style={{ width: '3rem', height: '3rem' }} />
          <p className="mt-3 text-muted">Loading...</p>
        </div>
      </Container>
    );
  }

  return (
    <Container className="py-4">
      {/* Header */}
      <Row className="mb-4">
        <Col>
          <div className="text-center">
            <h1 className="display-5 fw-bold text-primary mb-3">
              <i className="fas fa-bullhorn me-3"></i>
              📢 Bulk Email Announcements
            </h1>
            <p className="lead text-muted">
              Send professional announcements to event participants with ease
            </p>
          </div>
        </Col>
      </Row>

      {/* Main Content */}
      <Row className="justify-content-center">
        <Col lg={10} xl={8}>
          <Card className="bulk-email-card">
            <Card.Header className="bulk-email-header">
              <h3 className="mb-0">
                <i className="fas fa-envelope me-2"></i>
                Compose Announcement
              </h3>
            </Card.Header>
            <Card.Body className="p-4 bulk-email-form">
              {/* Alerts */}
              {error && (
                <Alert variant="danger" dismissible onClose={() => setError('')}>
                  <i className="fas fa-exclamation-triangle me-2"></i>
                  {error}
                </Alert>
              )}
              
              {success && (
                <Alert variant="success" dismissible onClose={() => setSuccess('')}>
                  <i className="fas fa-check-circle me-2"></i>
                  {success}
                  {emailStats && (
                    <div className="mt-2">
                      <small>
                        <strong>Details:</strong> {emailStats.success_count} sent successfully, 
                        {emailStats.failed_count} failed out of {emailStats.total_count} total recipients.
                      </small>
                    </div>
                  )}
                </Alert>
              )}

              <Form>
                {/* Event Selection */}
                <Row className="mb-4">
                  <Col>
                    <Form.Group>
                      <Form.Label className="fw-bold">
                        <i className="fas fa-calendar me-2 text-primary"></i>
                        Select Event *
                      </Form.Label>
                      <Form.Select
                        value={formData.event_id}
                        onChange={(e) => handleInputChange('event_id', e.target.value)}
                        size="lg"
                      >
                        <option value="">Choose an event...</option>
                        {events.map(event => (
                          <option key={event.id} value={event.id}>
                            {event.name} ({new Date(event.start_date).toLocaleDateString()})
                          </option>
                        ))}
                      </Form.Select>
                      <Form.Text className="text-muted">
                        Select the event whose participants will receive the announcement
                      </Form.Text>
                    </Form.Group>
                  </Col>
                </Row>

                {/* Recipient Selection */}
                <Row className="mb-4">
                  <Col>
                    <Form.Label className="fw-bold">
                      <i className="fas fa-users me-2 text-primary"></i>
                      Recipients *
                    </Form.Label>
                    <div className="recipient-selection">
                      <div
                        className={`recipient-option ${formData.recipient_type === 'all' ? 'selected' : ''}`}
                        onClick={() => handleInputChange('recipient_type', 'all')}
                      >
                        <Form.Check
                          type="radio"
                          id="all-participants"
                          name="recipient_type"
                          label="All Participants"
                          checked={formData.recipient_type === 'all'}
                          onChange={() => handleInputChange('recipient_type', 'all')}
                          className="mb-0"
                        />
                        <small className="text-muted d-block mt-1">Send to all confirmed participants</small>
                      </div>
                      <div
                        className={`recipient-option ${formData.recipient_type === 'participant_type' ? 'selected' : ''}`}
                        onClick={() => handleInputChange('recipient_type', 'participant_type')}
                      >
                        <Form.Check
                          type="radio"
                          id="by-type"
                          name="recipient_type"
                          label="By Participant Type"
                          checked={formData.recipient_type === 'participant_type'}
                          onChange={() => handleInputChange('recipient_type', 'participant_type')}
                          className="mb-0"
                        />
                        <small className="text-muted d-block mt-1">Send to specific participant types</small>
                      </div>
                    </div>

                    {formData.recipient_type === 'participant_type' && (
                      <div className="participant-types-section">
                        <Form.Label className="fw-bold mb-3">
                          <i className="fas fa-filter me-2 icon-primary"></i>
                          Select Participant Types:
                        </Form.Label>
                        <Row>
                          {participantTypes.map(type => (
                            <Col md={6} key={type.id} className="mb-2">
                              <Form.Check
                                type="checkbox"
                                id={`type-${type.id}`}
                                label={type.name}
                                checked={formData.participant_type_ids.includes(type.id)}
                                onChange={(e) => handleParticipantTypeChange(type.id, e.target.checked)}
                              />
                            </Col>
                          ))}
                        </Row>
                      </div>
                    )}
                  </Col>
                </Row>

                {/* Subject */}
                <Row className="mb-4">
                  <Col>
                    <Form.Group>
                      <Form.Label className="fw-bold">
                        <i className="fas fa-tag me-2 text-primary"></i>
                        Email Subject *
                      </Form.Label>
                      <Form.Control
                        type="text"
                        size="lg"
                        value={formData.subject}
                        onChange={(e) => handleInputChange('subject', e.target.value)}
                        placeholder="Enter email subject..."
                      />
                      <Form.Text className="form-help-text">
                        💡 You can use placeholders: {'{participant_name}'}, {'{event_name}'}, {'{event_date}'}, {'{event_location}'}
                      </Form.Text>
                    </Form.Group>
                  </Col>
                </Row>

                {/* Content */}
                <Row className="mb-4">
                  <Col>
                    <Form.Group>
                      <Form.Label className="fw-bold">
                        <i className="fas fa-edit me-2 text-primary"></i>
                        Email Content *
                      </Form.Label>
                      <Form.Control
                        as="textarea"
                        rows={8}
                        value={formData.content}
                        onChange={(e) => handleInputChange('content', e.target.value)}
                        placeholder="Enter your announcement message..."
                        style={{ fontSize: '16px' }}
                      />
                      <Form.Text className="form-help-text">
                        ✍️ Write your announcement message. You can use the same placeholders as in the subject. Line breaks will be preserved.
                      </Form.Text>
                    </Form.Group>
                  </Col>
                </Row>

                {/* Preview */}
                <Row className="mb-4">
                  <Col>
                    <div className="preview-section">
                      <h5 className="mb-3">
                        <i className="fas fa-eye me-2 icon-primary"></i>
                        📋 Email Preview
                      </h5>
                      <div className="mb-3">
                        <strong>Subject:</strong>
                        <div className="preview-content mt-1">
                          {previewData.subject}
                        </div>
                      </div>
                      <div>
                        <strong>Content Preview:</strong>
                        <div className="preview-content mt-1" style={{ minHeight: '120px' }}>
                          {previewData.content}
                        </div>
                      </div>
                      {selectedEvent && (
                        <div className="mt-3 p-3 bg-info bg-opacity-10 border border-info rounded">
                          <small>
                            <strong>📅 Event Info:</strong> {selectedEvent.name} • {new Date(selectedEvent.start_date).toLocaleDateString()} • {selectedEvent.location || 'TBD'}
                          </small>
                        </div>
                      )}
                    </div>
                  </Col>
                </Row>

                {/* Send Button */}
                <Row>
                  <Col className="text-center">
                    <Button
                      onClick={sendBulkEmail}
                      disabled={sending}
                      className="send-button"
                    >
                      {sending ? (
                        <>
                          <Spinner animation="border" size="sm" className="me-2" />
                          Sending Emails...
                        </>
                      ) : (
                        <>
                          <i className="fas fa-paper-plane me-2"></i>
                          📧 Send Bulk Email
                        </>
                      )}
                    </Button>
                  </Col>
                </Row>
              </Form>
            </Card.Body>
          </Card>
        </Col>
      </Row>
    </Container>
  );
};

export default BulkEmailAnnouncement;
