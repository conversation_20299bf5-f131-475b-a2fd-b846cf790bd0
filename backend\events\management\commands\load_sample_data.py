from django.core.management.base import BaseCommand
from django.utils import timezone
from datetime import timedelta
from events.models import Event, EventSchedule
from participants.models import ParticipantType
from hotels.models import Hotel, HotelRoom
from drivers.models import Driver
from badges.models import BadgeTemplate


class Command(BaseCommand):
    help = 'Load sample data for the event management system'

    def handle(self, *args, **options):
        self.stdout.write(self.style.WARNING('WARNING: This command creates test data and should not be used in production!'))
        self.stdout.write(self.style.SUCCESS('Loading sample data...'))

        # Create participant types
        participant_types = [
            {'name': 'Speaker', 'description': 'Event speakers and presenters', 'color': '#dc3545'},
            {'name': 'Attendee', 'description': 'General event attendees', 'color': '#0d6efd'},
            {'name': 'VIP', 'description': 'VIP guests and sponsors', 'color': '#ffc107'},
            {'name': 'Staff', 'description': 'Event staff and organizers', 'color': '#198754'},
            {'name': 'Media', 'description': 'Press and media representatives', 'color': '#6f42c1'},
        ]

        for ptype_data in participant_types:
            ptype, created = ParticipantType.objects.get_or_create(
                name=ptype_data['name'],
                defaults=ptype_data
            )
            if created:
                self.stdout.write(f'Created participant type: {ptype.name}')

        # Create sample events
        now = timezone.now()
        events_data = [
            {
                'name': 'International Technology Conference 2025',
                'description': 'A premier technology conference bringing together industry leaders, innovators, and tech enthusiasts from around the world.',
                'start_date': now + timedelta(days=30),
                'end_date': now + timedelta(days=33),
                'location': 'Grand Convention Center',
                'city': 'New York',
                'country': 'USA',
                'latitude': 40.7128,
                'longitude': -74.0060,
                'organizer_name': 'Tech Events International',
                'organizer_email': '<EMAIL>',
                'organizer_phone': '******-0123',
                'is_active': True,
            },
            {
                'name': 'Global Healthcare Summit 2025',
                'description': 'Leading healthcare professionals discuss the future of medicine and patient care.',
                'start_date': now + timedelta(days=60),
                'end_date': now + timedelta(days=62),
                'location': 'Medical Center Auditorium',
                'city': 'Boston',
                'country': 'USA',
                'latitude': 42.3601,
                'longitude': -71.0589,
                'organizer_name': 'Healthcare Global',
                'organizer_email': '<EMAIL>',
                'organizer_phone': '******-0456',
                'is_active': True,
            }
        ]

        for event_data in events_data:
            event, created = Event.objects.get_or_create(
                name=event_data['name'],
                defaults=event_data
            )
            if created:
                self.stdout.write(f'Created event: {event.name}')
                
                # Create sample schedule for the event
                self.create_sample_schedule(event)

        # Create sample hotels
        hotels_data = [
            {
                'name': 'Grand Plaza Hotel',
                'address': '123 Main Street, New York, NY 10001',
                'phone': '******-1000',
                'email': '<EMAIL>',
                'contact_person': 'John Smith',
                'latitude': 40.7589,
                'longitude': -73.9851,
                'star_rating': 5,
                'website': 'https://grandplaza.com',
                'description': 'Luxury hotel in the heart of Manhattan',
                'is_active': True,
            },
            {
                'name': 'Business Inn',
                'address': '456 Business Ave, Boston, MA 02101',
                'phone': '******-2000',
                'email': '<EMAIL>',
                'contact_person': 'Jane Doe',
                'latitude': 42.3584,
                'longitude': -71.0598,
                'star_rating': 4,
                'website': 'https://businessinn.com',
                'description': 'Modern business hotel near medical district',
                'is_active': True,
            }
        ]

        for hotel_data in hotels_data:
            # Get the event for this hotel (simplified assignment)
            event = Event.objects.first()
            hotel_data['event'] = event
            
            hotel, created = Hotel.objects.get_or_create(
                name=hotel_data['name'],
                defaults=hotel_data
            )
            if created:
                self.stdout.write(f'Created hotel: {hotel.name}')
                
                # Create sample rooms
                self.create_sample_rooms(hotel)

        # Create sample drivers
        drivers_data = [
            {
                'name': 'Mike Johnson',
                'phone': '******-3001',
                'email': '<EMAIL>',
                'car_plate': 'NYC-001',
                'car_code': 'SEDAN-01',
                'car_model': 'Toyota Camry',
                'car_color': 'Black',
                'license_number': 'DL123456789',
                'is_available': True,
            },
            {
                'name': 'Sarah Wilson',
                'phone': '******-3002',
                'email': '<EMAIL>',
                'car_plate': 'NYC-002',
                'car_code': 'SUV-01',
                'car_model': 'Honda CR-V',
                'car_color': 'White',
                'license_number': 'DL987654321',
                'is_available': True,
            }
        ]

        for driver_data in drivers_data:
            event = Event.objects.first()
            driver_data['event'] = event
            
            driver, created = Driver.objects.get_or_create(
                name=driver_data['name'],
                defaults=driver_data
            )
            if created:
                self.stdout.write(f'Created driver: {driver.name}')

        # Create default badge template
        template, created = BadgeTemplate.objects.get_or_create(
            name='Default Template',
            defaults={
                'description': 'Default professional badge template',
                'width': 400,
                'height': 600,
                'background_color': '#ffffff',
                'is_default': True,
            }
        )
        if created:
            self.stdout.write(f'Created badge template: {template.name}')

        self.stdout.write(self.style.SUCCESS('Sample data loaded successfully!'))

    def create_sample_schedule(self, event):
        """Create sample schedule for an event"""
        base_date = event.start_date.date()
        
        schedules = [
            {
                'title': 'Registration & Welcome Coffee',
                'description': 'Check-in and networking with welcome refreshments',
                'start_time': timezone.datetime.combine(base_date, timezone.datetime.min.time().replace(hour=8, minute=0)),
                'end_time': timezone.datetime.combine(base_date, timezone.datetime.min.time().replace(hour=9, minute=0)),
                'location': 'Main Lobby',
                'speaker': '',
                'is_break': True,
            },
            {
                'title': 'Opening Keynote: The Future of Technology',
                'description': 'An inspiring keynote about emerging technologies and their impact on society',
                'start_time': timezone.datetime.combine(base_date, timezone.datetime.min.time().replace(hour=9, minute=0)),
                'end_time': timezone.datetime.combine(base_date, timezone.datetime.min.time().replace(hour=10, minute=0)),
                'location': 'Main Auditorium',
                'speaker': 'Dr. Alex Thompson',
                'is_break': False,
            },
            {
                'title': 'Coffee Break',
                'description': 'Networking break with refreshments',
                'start_time': timezone.datetime.combine(base_date, timezone.datetime.min.time().replace(hour=10, minute=0)),
                'end_time': timezone.datetime.combine(base_date, timezone.datetime.min.time().replace(hour=10, minute=30)),
                'location': 'Exhibition Hall',
                'speaker': '',
                'is_break': True,
            },
            {
                'title': 'Panel Discussion: AI and Machine Learning',
                'description': 'Expert panel discussing the latest developments in AI and ML',
                'start_time': timezone.datetime.combine(base_date, timezone.datetime.min.time().replace(hour=10, minute=30)),
                'end_time': timezone.datetime.combine(base_date, timezone.datetime.min.time().replace(hour=12, minute=0)),
                'location': 'Conference Room A',
                'speaker': 'Panel of Experts',
                'is_break': False,
            },
            {
                'title': 'Lunch Break',
                'description': 'Networking lunch with all participants',
                'start_time': timezone.datetime.combine(base_date, timezone.datetime.min.time().replace(hour=12, minute=0)),
                'end_time': timezone.datetime.combine(base_date, timezone.datetime.min.time().replace(hour=13, minute=30)),
                'location': 'Grand Ballroom',
                'speaker': '',
                'is_break': True,
            },
        ]

        for schedule_data in schedules:
            schedule_data['event'] = event
            schedule_data['start_time'] = timezone.make_aware(schedule_data['start_time'])
            schedule_data['end_time'] = timezone.make_aware(schedule_data['end_time'])
            
            EventSchedule.objects.get_or_create(
                event=event,
                title=schedule_data['title'],
                defaults=schedule_data
            )

    def create_sample_rooms(self, hotel):
        """Create sample rooms for a hotel"""
        room_types = [
            {'type': 'Standard Single', 'capacity': 1, 'price': 150.00},
            {'type': 'Standard Double', 'capacity': 2, 'price': 200.00},
            {'type': 'Deluxe Suite', 'capacity': 4, 'price': 350.00},
            {'type': 'Executive Suite', 'capacity': 6, 'price': 500.00},
        ]

        for i, room_type in enumerate(room_types):
            for room_num in range(1, 6):  # 5 rooms of each type
                room_number = f"{i+1}{room_num:02d}"
                HotelRoom.objects.get_or_create(
                    hotel=hotel,
                    room_number=room_number,
                    defaults={
                        'room_type': room_type['type'],
                        'capacity': room_type['capacity'],
                        'price_per_night': room_type['price'],
                        'amenities': 'WiFi, TV, Air Conditioning, Mini Bar',
                        'is_available': True,
                    }
                )
