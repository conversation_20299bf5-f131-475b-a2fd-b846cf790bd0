import React, { useState, useEffect } from 'react';
import { useLocation } from 'react-router-dom';
import { <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, Spin<PERSON>, <PERSON>ge } from 'react-bootstrap';
import { participantService } from '../services/api';

interface ParticipantData {
  id: number;
  full_name: string;
  email: string;
  phone: string;
  institution_name: string;
  position: string;
  participant_type_name: string;
  participant_type_color: string;
  event_name: string;
  status: string;
  can_check_in: boolean;
  verification_time: string;
  verification_message?: string;
  verification_status?: string;
  participant_status?: string;
  is_confirmed?: boolean;
  verification_checks?: {
    is_confirmed: boolean;
    status_approved: boolean;
    status_not_rejected: boolean;
    has_email: boolean;
    participant_exists: boolean;
  };
}

const MobileQRLanding: React.FC = () => {
  const location = useLocation();
  const [loading, setLoading] = useState(true);
  const [participant, setParticipant] = useState<ParticipantData | null>(null);
  const [error, setError] = useState('');

  useEffect(() => {
    const processQRFromURL = async () => {
      try {
        const urlParams = new URLSearchParams(location.search);
        const email = urlParams.get('email');
        const name = urlParams.get('name');

        console.log('URL Params:', { email, name });

        if (!email) {
          setError('No email parameter found in QR code');
          setLoading(false);
          return;
        }

        // Decode URL-encoded email
        const decodedEmail = decodeURIComponent(email);
        console.log('Decoded email:', decodedEmail);

        // Verify participant
        console.log('Calling API with email:', decodedEmail);
        const response = await participantService.verifyParticipant(decodedEmail);
        console.log('API Response:', response.data);
        setParticipant(response.data);

      } catch (error: any) {
        console.error('Verification error:', error);
        console.error('Error details:', error.response);
        setError(error.response?.data?.error || error.message || 'Participant not found');
      } finally {
        setLoading(false);
      }
    };

    processQRFromURL();
  }, [location]);

  if (loading) {
    return (
      <Container className="mt-5 text-center">
        <Spinner animation="border" role="status" />
        <p className="mt-3">Verifying participant...</p>
      </Container>
    );
  }

  if (error) {
    return (
      <Container className="mt-5">
        <Alert variant="danger">
          <Alert.Heading>Verification Failed</Alert.Heading>
          <p>{error}</p>
          <hr />
          <p className="mb-0">
            Please contact the event organizers if you believe this is an error.
          </p>
        </Alert>
      </Container>
    );
  }

  if (!participant) {
    return (
      <Container className="mt-5">
        <Alert variant="warning">
          <Alert.Heading>No Participant Data</Alert.Heading>
          <p>Unable to load participant information.</p>
        </Alert>
      </Container>
    );
  }

  return (
    <Container className="mt-4">
      <Card className="shadow">
        <Card.Header className="bg-primary text-white text-center">
          <h4 className="mb-0">✅ Participant Verified</h4>
        </Card.Header>
        <Card.Body>
          <div className="text-center mb-4">
            <h3 className="text-primary">{participant.full_name}</h3>
            <Badge 
              bg="primary" 
              style={{ backgroundColor: participant.participant_type_color }}
              className="fs-6 mb-2"
            >
              {participant.participant_type_name}
            </Badge>
          </div>

          <div className="row">
            <div className="col-12 mb-3">
              <strong>📧 Email:</strong><br />
              <span className="text-muted">{participant.email}</span>
            </div>
            
            <div className="col-12 mb-3">
              <strong>📞 Phone:</strong><br />
              <span className="text-muted">{participant.phone}</span>
            </div>
            
            <div className="col-12 mb-3">
              <strong>🏢 Institution:</strong><br />
              <span className="text-muted">{participant.institution_name}</span>
            </div>
            
            <div className="col-12 mb-3">
              <strong>💼 Position:</strong><br />
              <span className="text-muted">{participant.position}</span>
            </div>
            
            <div className="col-12 mb-3">
              <strong>🎯 Event:</strong><br />
              <span className="text-muted">{participant.event_name}</span>
            </div>
          </div>

          <div className="text-center mt-4">
            {participant.can_check_in ? (
              <Alert variant="success" className="mb-0">
                <strong>✅ Ready for Check-in</strong><br />
                {participant.verification_message || 'This participant is approved and can be checked in to event sessions.'}
              </Alert>
            ) : (
              <Alert variant={participant.verification_status === 'rejected' ? 'danger' : 'warning'} className="mb-0">
                <strong>
                  {participant.verification_status === 'rejected' ? '❌ Registration Rejected' :
                   participant.verification_status === 'pending' ? '⏳ Pending Approval' :
                   '⚠️ Cannot Check In'}
                </strong><br />
                {participant.verification_message || 'This participant is not yet approved for check-in.'}
              </Alert>
            )}

            {/* Show detailed verification info for debugging */}
            {participant.verification_checks && (
              <div className="mt-3">
                <small className="text-muted">
                  <strong>Status Details:</strong><br />
                  Status: {participant.participant_status} |
                  Confirmed: {participant.is_confirmed ? 'Yes' : 'No'} |
                  Verification: {participant.verification_status}
                </small>
              </div>
            )}
          </div>

          <div className="text-center mt-3">
            <small className="text-muted">
              Verified at: {new Date(participant.verification_time).toLocaleString()}
            </small>
          </div>
        </Card.Body>
      </Card>

      <div className="text-center mt-4 mb-5">
        <p className="text-muted">
          <strong>University of Gondar</strong><br />
          Event Management System
        </p>
      </div>
    </Container>
  );
};

export default MobileQRLanding;
