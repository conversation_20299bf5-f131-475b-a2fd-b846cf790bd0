#!/usr/bin/env python
"""
Script to recreate production data with exact IDs for QR code compatibility.
This will clear existing data and recreate it with the provided structure.
"""

import os
import sys
import django
from datetime import datetime
from django.utils import timezone

# Setup Django
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'event_management.settings')
django.setup()

from participants.models import ParticipantType, Participant, VisitingInterest
from hotels.models import Hotel
from drivers.models import Driver
from contact_persons.models import Contact<PERSON>erson
from events.models import Event

def clear_existing_data():
    """Clear existing data"""
    print("Clearing existing data...")
    Participant.objects.all().delete()
    VisitingInterest.objects.all().delete()
    ParticipantType.objects.all().delete()
    Hotel.objects.all().delete()
    Driver.objects.all().delete()
    ContactPerson.objects.all().delete()
    print("Existing data cleared.")

def create_participant_types():
    """Create participant types with exact IDs"""
    print("Creating participant types...")

    participant_types = [
        (1, "VIP", ""),
        (2, "President", ""),
        (3, "Driver", "Default participant type"),
        (4, "Faculty", "Default participant type"),
        (6, "Student", "Default participant type"),
        (8, "Guest", "Default participant type"),
        (9, "Security", "Default participant type"),
        (23, "Vice President", ""),
        (24, "Media", ""),
        (25, "Coordinator", ""),
        (26, "Housekeeping", ""),
        (27, "Plan Director", ""),
        (28, "Other", ""),
        (29, "Vice Chairperson of the Board", ""),
        (32, "Staff", "Default participant type"),
        (33, "External Participant", "Default participant type"),
        (34, "Guest Speaker", "Default participant type"),
    ]

    for pt_id, name, description in participant_types:
        pt = ParticipantType(
            id=pt_id,
            name=name,
            description=description
        )
        pt.save()

    print(f"Created {len(participant_types)} participant types.")

def create_visiting_interests():
    """Create visiting interests with exact IDs"""
    print("Creating visiting interests...")

    # Get the first event to associate visiting interests with
    event = Event.objects.first()
    if not event:
        print("No events found. Please create an event first.")
        return

    visiting_interests = [
        (1, "University Campuses", ""),
        (16, "Castle of Fasil", ""),
        (17, "Corridor Development", ""),
        (18, "St. Trinity church", ""),
        (19, "Fasil Bath", ""),
    ]

    for vi_id, name, description in visiting_interests:
        vi = VisitingInterest(
            id=vi_id,
            name=name,
            description=description,
            event=event,
            is_active=True
        )
        vi.save()

    print(f"Created {len(visiting_interests)} visiting interests.")

def create_hotels():
    """Create hotels with exact IDs"""
    print("Creating hotels...")

    # Get the first event to associate hotels with
    event = Event.objects.first()
    if not event:
        print("No events found. Please create an event first.")
        return

    hotels = [
        (1, "Jantekel Hotel", "", "<EMAIL>", "0965363738", "Efriem Ejigu"),
        (2, "Haile resourt", "", "<EMAIL>", "0901010101", "Aderajew Amha"),
        (3, "Goha Hotel", "Buluko", "<EMAIL>", "0581110694", "Yordanos Yigzaw"),
        (4, "Zobil Resourt", "Bulko", "<EMAIL>", "0582113737", "Robel Taye"),
        (5, "Florida Hotel", "Maraki", "<EMAIL>", "0918350860", "Muluye Fissha"),
        (6, "Olympic Hotel", "Maraki", "", "0915300505", "Worku Mulaw"),
        (7, "Rozeo Hotel", "Embassy", "<EMAIL>", "0582111222", "Alazar"),
        (8, "PLAZA Hotel", "MarakiI", "<EMAIL>", "0582112121", "Girma Worku"),
        (9, "AG Hotel", "Piaza", "<EMAIL>", "0975154646", "Atinafu Yirsaw"),
        (10, "Gondar Hills", "", "", "0939378626", "Miracle"),
    ]

    for hotel_data in hotels:
        hotel = Hotel(
            id=hotel_data[0],
            name=hotel_data[1],
            address=hotel_data[2],
            email=hotel_data[3],
            phone=hotel_data[4],
            contact_person=hotel_data[5],
            event=event,
            is_active=True
        )
        hotel.save()

    print(f"Created {len(hotels)} hotels.")

def create_drivers():
    """Create drivers with exact IDs"""
    print("Creating drivers...")

    # Get the first event to associate drivers with
    event = Event.objects.first()
    if not event:
        print("No events found. Please create an event first.")
        return

    drivers = [
        (1, "Getnet Mehari", "0918036666", "event_photo_1754059938_ጌትነት-መሀሪ.jpg", "04-08391"),
        (2, "Dawit Semu", "0918791360", "event_photo_1754060087_ዳዊት-ሠሙ.jpg", "04-08389"),
        (3, "Addisu Shumye", "0918704213", "event_photo_1754060071_አዲሱ-ሹምየ-.jpg", "04-08388"),
        (4, "Mandefro Asefa", "0918774583", "event_photo_1754060050_Mandefro-Asefa-.jpg", "04-08390"),
        (5, "Endinew Marie", "0918076590", "", "25997"),
        (6, "Henok Tadesse", "0925176158", "", "19993"),
        (7, "Daniel Bayable", "0918033549", "", "22881"),
        (8, "Thomas Asnakew", "0918194134", "", "22890"),
        (9, "Yibeltal Mesfin", "0918705505", "", "21305"),
        (10, "Kasahun Endayeh", "0967150817", "", "20262"),
        (11, "Addisu Yohannes", "0986804684", "", "32017"),
        (12, "Endalamaw Abuhay", "0918298380", "", "52459"),
        (13, "Shikur Aman", "0918787332", "", "29561"),
        (14, "Kefyalew Mihret", "0918044197", "", "29561"),
        (15, "Bewket Shiferaw", "0922554052", "", "1991"),
        (16, "Wasyehun Sisay", "0918046922", "", "10864"),
        (17, "Worku Genet", "0927747378", "", "21302"),
        (18, "Eshetu Kemal", "0918805252", "", "29565"),
        (19, "Fentahun Yitayew", "0918046904", "", "29566"),
        (20, "Takele Alebachew", "0918042015", "", "29562"),
        (21, "Maru Shiferaw", "0918810968", "", "23742"),
        (22, "Getahun Fentie", "0918033725", "", "20495"),
        (24, "Wanna Sajin", "0900000000", "", "20428"),
        (25, "Alewond Kenaw", "0905972702", "", "18532"),
        (26, "Desalegn Worku", "0918149804", "", "09535"),
    ]

    for driver_data in drivers:
        driver = Driver(
            id=driver_data[0],
            name=driver_data[1],
            phone=driver_data[2],
            email="<EMAIL>",  # Default email since it's required
            photo=driver_data[3],
            car_plate=driver_data[4],
            car_code=driver_data[4],  # Using car_plate as car_code
            event=event,
            is_available=True
        )
        driver.save()

    print(f"Created {len(drivers)} drivers.")

def create_contact_persons():
    """Create contact persons with exact IDs"""
    print("Creating contact persons...")

    # Get the first event to associate contact persons with
    event = Event.objects.first()
    if not event:
        print("No events found. Please create an event first.")
        return

    contact_persons = [
        (1, "Belay", "", "", "<EMAIL>", "0918805970", ""),
        (2, "Demis", "Mulatu", "", "<EMAIL>", "0924110714", ""),
        (3, "Mebratu", "Tilahun", "", "<EMAIL>", "0933577374", ""),
        (4, "Dr.", "Alene", "", "<EMAIL>", "0921252233", ""),
        (5, "Dr. Worku", "Abebe", "", "<EMAIL>", "0910108943", ""),
        (6, "Yonas", "Addisu", "", "<EMAIL>", "0918441624", ""),
        (7, "Nega", "Nigussie", "", "<EMAIL>", "0972227034", ""),
        (9, "Tadesse", "Woldegebriel", "", "<EMAIL>", "0918778670", "event_photo_1754143265_Tadesse1.jpg"),
        (10, "Tades", "", "", "<EMAIL>", "0918778670", "event_photo_1754143535_Tadesse1.jpg"),
        (11, "Yenesew", "Alene", "", "<EMAIL>", "0910040073", ""),
    ]

    for cp_data in contact_persons:
        cp = ContactPerson(
            id=cp_data[0],
            first_name=cp_data[1],
            middle_name=cp_data[2],
            last_name=cp_data[3],
            email=cp_data[4],
            phone=cp_data[5],
            photo=cp_data[6],
            event=event,
            is_available=True
        )
        cp.save()

    print(f"Created {len(contact_persons)} contact persons.")

def main():
    """Main function to recreate all data"""
    print("Starting data recreation with exact IDs...")
    print("WARNING: This will delete all existing data!")

    # Clear existing data
    clear_existing_data()

    # Create new data with exact IDs
    create_participant_types()
    create_visiting_interests()
    create_hotels()
    create_drivers()
    create_contact_persons()

    print("\nData recreation completed successfully!")
    print("All data has been recreated with exact IDs for QR code compatibility.")
    print("\nTo create participants data, run: python create_participants_data.py")

if __name__ == "__main__":
    main()
