from rest_framework import serializers
from .models import Hotel, HotelRoom, HotelReservation


class HotelRoomSerializer(serializers.ModelSerializer):
    hotel_name = serializers.CharField(source='hotel.name', read_only=True)

    class Meta:
        model = HotelRoom
        fields = '__all__'


class HotelSerializer(serializers.ModelSerializer):
    event_name = serializers.CharField(source='event.name', read_only=True)
    rooms = HotelRoomSerializer(many=True, read_only=True)
    available_rooms_count = serializers.SerializerMethodField()

    class Meta:
        model = Hotel
        fields = '__all__'

    def get_available_rooms_count(self, obj):
        return obj.rooms.filter(is_available=True).count()


class HotelListSerializer(serializers.ModelSerializer):
    """Simplified serializer for hotel list views"""
    event_name = serializers.CharField(source='event.name', read_only=True)
    available_rooms_count = serializers.SerializerMethodField()

    class Meta:
        model = Hotel
        fields = ['id', 'name', 'address', 'phone', 'email', 'contact_person',
                 'star_rating', 'website', 'event_name', 'is_active', 'available_rooms_count']

    def get_available_rooms_count(self, obj):
        return obj.rooms.filter(is_available=True).count()


class HotelReservationSerializer(serializers.ModelSerializer):
    participant_name = serializers.CharField(source='participant.full_name', read_only=True)
    participant_email = serializers.CharField(source='participant.email', read_only=True)
    participant_phone = serializers.CharField(source='participant.phone', read_only=True)
    hotel_name = serializers.CharField(source='hotel.name', read_only=True)
    room_number = serializers.CharField(source='room.room_number', read_only=True)
    room_type = serializers.CharField(source='room.room_type', read_only=True)

    class Meta:
        model = HotelReservation
        fields = '__all__'
