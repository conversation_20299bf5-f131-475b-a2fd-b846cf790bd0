from rest_framework import serializers
from .models import GalleryCategory, GalleryImage, EventSlider


class GalleryCategorySerializer(serializers.ModelSerializer):
    image_count = serializers.ReadOnlyField()
    
    class Meta:
        model = GalleryCategory
        fields = [
            'id', 'name', 'description', 'slug', 'icon', 'color', 
            'is_active', 'order', 'image_count', 'created_at', 'updated_at'
        ]


class GalleryImageSerializer(serializers.ModelSerializer):
    category_name = serializers.CharField(source='category.name', read_only=True)
    category_slug = serializers.CharField(source='category.slug', read_only=True)
    category_color = serializers.CharField(source='category.color', read_only=True)
    tag_list = serializers.ReadOnlyField()
    
    class Meta:
        model = GalleryImage
        fields = [
            'id', 'title', 'description', 'image', 'thumbnail',
            'category', 'category_name', 'category_slug', 'category_color',
            'tags', 'tag_list', 'is_featured', 'is_slider', 'is_campus',
            'is_active', 'photographer', 'date_taken', 'location',
            'alt_text', 'order', 'created_at', 'updated_at'
        ]


class GalleryImageListSerializer(serializers.ModelSerializer):
    """Simplified serializer for list views"""
    category_name = serializers.CharField(source='category.name', read_only=True)
    category_color = serializers.CharField(source='category.color', read_only=True)
    
    class Meta:
        model = GalleryImage
        fields = [
            'id', 'title', 'image', 'thumbnail', 'category_name', 
            'category_color', 'is_featured', 'is_slider', 'is_campus'
        ]


class EventSliderSerializer(serializers.ModelSerializer):
    event_name = serializers.CharField(source='event.name', read_only=True)
    event_start_date = serializers.DateTimeField(source='event.start_date', read_only=True)
    event_end_date = serializers.DateTimeField(source='event.end_date', read_only=True)
    event_location = serializers.CharField(source='event.location', read_only=True)
    event_city = serializers.CharField(source='event.city', read_only=True)
    event_country = serializers.CharField(source='event.country', read_only=True)
    event_description = serializers.CharField(source='event.description', read_only=True)
    event_participant_count = serializers.IntegerField(source='event.participant_count', read_only=True)
    
    display_title = serializers.ReadOnlyField()
    display_subtitle = serializers.ReadOnlyField()
    display_description = serializers.ReadOnlyField()
    background_gradient = serializers.ReadOnlyField()
    
    class Meta:
        model = EventSlider
        fields = [
            'id', 'event', 'event_name', 'event_start_date', 'event_end_date',
            'event_location', 'event_city', 'event_country', 'event_description',
            'event_participant_count', 'slider_title', 'slider_subtitle',
            'slider_description', 'display_title', 'display_subtitle',
            'display_description', 'background_image', 'background_color',
            'background_color_end', 'background_gradient', 'is_active',
            'order', 'cta_text', 'cta_url', 'created_at', 'updated_at'
        ]


class FeaturedGallerySerializer(serializers.ModelSerializer):
    """Serializer for featured gallery items on homepage"""
    category_name = serializers.CharField(source='category.name', read_only=True)
    
    class Meta:
        model = GalleryImage
        fields = ['id', 'title', 'description', 'image', 'category_name', 'is_featured']
