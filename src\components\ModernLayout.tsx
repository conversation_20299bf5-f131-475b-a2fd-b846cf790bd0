import React from 'react';
import ModernNavbar from './ModernNavbar';
import ModernFooter from './ModernFooter';

interface ModernLayoutProps {
  children: React.ReactNode;
  title?: string;
  subtitle?: string;
  showHero?: boolean;
  heroContent?: React.ReactNode;
  className?: string;
  contentInHero?: boolean;
  heroBackground?: string;
  heroOverlay?: boolean;
}

const ModernLayout: React.FC<ModernLayoutProps> = ({
  children,
  title,
  subtitle,
  showHero = true,
  heroContent,
  className = '',
  contentInHero = false,
  heroBackground = 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
  heroOverlay = true
}) => {
  return (
    <>
      <style>{`
        .modern-layout {
          min-height: 100vh;
          display: flex;
          flex-direction: column;
          background: #ffffff;
        }

        .modern-hero {
          position: relative;
          min-height: ${contentInHero ? 'auto' : '60vh'};
          margin-top: 0;
          padding-top: 80px;
          display: flex;
          align-items: center;
          justify-content: center;
          background: ${heroBackground};
          background-size: cover;
          background-position: center;
          background-attachment: fixed;
          overflow: hidden;
          z-index: 2;
        }

        /* Add professional geometric patterns like modern home page */
        .modern-hero::before {
          content: '';
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          background-image:
            linear-gradient(45deg, rgba(255, 255, 255, 0.03) 25%, transparent 25%),
            linear-gradient(-45deg, rgba(255, 255, 255, 0.03) 25%, transparent 25%),
            linear-gradient(45deg, transparent 75%, rgba(255, 255, 255, 0.03) 75%),
            linear-gradient(-45deg, transparent 75%, rgba(255, 255, 255, 0.03) 75%);
          background-size: 60px 60px;
          background-position: 0 0, 0 30px, 30px -30px, -30px 0px;
          animation: patternMove 30s linear infinite;
          z-index: 1;
        }

        @keyframes patternMove {
          0% { transform: translateX(0px) translateY(0px); }
          100% { transform: translateX(60px) translateY(60px); }
        }

        .modern-hero::before {
          content: '';
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          background: ${heroOverlay ? 'rgba(0, 0, 0, 0.4)' : 'transparent'};
          z-index: 1;
        }

        .modern-hero::after {
          content: '';
          position: absolute;
          bottom: 0;
          left: 0;
          width: 100%;
          height: 100px;
          background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1200 120" preserveAspectRatio="none"><path d="M0,0V46.29c47.79,22.2,103.59,32.17,158,28,70.36-5.37,136.33-33.31,206.8-37.5C438.64,32.43,512.34,53.67,583,72.05c69.27,18,138.3,24.88,209.4,13.08,36.15-6,69.85-17.84,104.45-29.34C989.49,25,1113-14.29,1200,52.47V0Z" fill="%23ffffff"/></svg>');
          background-size: cover;
          background-repeat: no-repeat;
          z-index: 2;
        }

        .hero-content {
          position: relative;
          z-index: 3;
          text-align: center;
          color: white;
          max-width: 800px;
          padding: 2rem;
        }

        .hero-title {
          font-size: 3.5rem;
          font-weight: 700;
          margin-bottom: 1rem;
          text-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
          line-height: 1.2;
        }

        .hero-subtitle {
          font-size: 1.3rem;
          margin-bottom: 2rem;
          opacity: 0.9;
          text-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
          line-height: 1.4;
        }

        .hero-content-wrapper {
          position: relative;
          z-index: 3;
          padding: 2rem 0;
        }

        .hero-glass-panel {
          background: rgba(255, 255, 255, 0.1);
          backdrop-filter: blur(20px);
          border-radius: 20px;
          border: 1px solid rgba(255, 255, 255, 0.2);
          box-shadow: 0 25px 50px rgba(0, 0, 0, 0.2);
          padding: 2rem;
          margin: 2rem auto;
          max-width: 1200px;
          transition: all 0.3s ease;
        }

        .hero-glass-panel:hover {
          transform: translateY(-5px);
          box-shadow: 0 35px 70px rgba(0, 0, 0, 0.3);
        }

        .main-content-modern {
          flex: 1;
          position: relative;
          background: #ffffff;
          padding: 4rem 0;
          min-height: auto;
        }

        .main-content-modern.no-hero {
          padding-top: 120px;
        }

        .content-wrapper-modern {
          position: relative;
          z-index: 1;
        }

        .floating-elements {
          position: absolute;
          top: 0;
          left: 0;
          width: 100%;
          height: 100%;
          overflow: hidden;
          pointer-events: none;
          z-index: 0;
        }

        .floating-shape {
          position: absolute;
          background: linear-gradient(135deg, rgba(0, 123, 255, 0.1), rgba(0, 86, 179, 0.05));
          border-radius: 50%;
          animation: float-shape 8s ease-in-out infinite;
        }

        @keyframes float-shape {
          0%, 100% { transform: translateY(0px) rotate(0deg); }
          50% { transform: translateY(-30px) rotate(180deg); }
        }

        .section-divider {
          height: 2px;
          background: linear-gradient(90deg, transparent, #007bff, transparent);
          margin: 3rem 0;
          border-radius: 1px;
        }

        @media (max-width: 768px) {
          .hero-title {
            font-size: 2.5rem;
          }

          .hero-subtitle {
            font-size: 1.1rem;
          }

          .modern-hero {
            min-height: ${contentInHero ? 'auto' : '50vh'};
            background-attachment: scroll;
          }

          .hero-glass-panel {
            margin: 1rem;
            padding: 1.5rem;
          }

          .main-content-modern {
            padding: 2rem 0;
          }

          .main-content-modern.no-hero {
            padding-top: 100px;
          }
        }

        @media (max-width: 576px) {
          .hero-title {
            font-size: 2rem;
          }

          .hero-subtitle {
            font-size: 1rem;
          }

          .hero-content {
            padding: 1rem;
          }

          .hero-glass-panel {
            padding: 1rem;
          }
        }

        /* Smooth scroll behavior */
        html {
          scroll-behavior: smooth;
        }

        /* Custom scrollbar */
        ::-webkit-scrollbar {
          width: 8px;
        }

        ::-webkit-scrollbar-track {
          background: #f1f1f1;
        }

        ::-webkit-scrollbar-thumb {
          background: linear-gradient(135deg, #007bff, #0056b3);
          border-radius: 4px;
        }

        ::-webkit-scrollbar-thumb:hover {
          background: linear-gradient(135deg, #0056b3, #004085);
        }

        /* Loading animation for images */
        img {
          transition: opacity 0.3s ease;
        }

        img[loading="lazy"] {
          opacity: 0;
        }

        img[loading="lazy"].loaded {
          opacity: 1;
        }
      `}</style>

      <div className="modern-layout">
        {/* Navigation */}
        <ModernNavbar />

        {/* Hero Section */}
        {showHero && (
          <section className="modern-hero">
            <div className="floating-elements">
              {[...Array(5)].map((_, i) => (
                <div
                  key={i}
                  className="floating-shape"
                  style={{
                    left: `${Math.random() * 100}%`,
                    top: `${Math.random() * 100}%`,
                    width: `${Math.random() * 100 + 50}px`,
                    height: `${Math.random() * 100 + 50}px`,
                    animationDelay: `${Math.random() * 8}s`,
                    animationDuration: `${Math.random() * 4 + 6}s`
                  }}
                />
              ))}
            </div>

            {contentInHero ? (
              <div className="hero-content-wrapper">
                <div className="container">
                  <div className="hero-glass-panel">
                    {children}
                  </div>
                </div>
              </div>
            ) : (
              <div className="hero-content">
                {heroContent || (
                  <>
                    {title && <h1 className="hero-title">{title}</h1>}
                    {subtitle && <p className="hero-subtitle">{subtitle}</p>}
                  </>
                )}
              </div>
            )}
          </section>
        )}

        {/* Main Content */}
        {!contentInHero && (
          <main className={`main-content-modern ${!showHero ? 'no-hero' : ''} ${className}`}>
            <div className="floating-elements">
              {[...Array(3)].map((_, i) => (
                <div
                  key={i}
                  className="floating-shape"
                  style={{
                    left: `${Math.random() * 100}%`,
                    top: `${Math.random() * 100}%`,
                    width: `${Math.random() * 60 + 30}px`,
                    height: `${Math.random() * 60 + 30}px`,
                    animationDelay: `${Math.random() * 8}s`,
                    animationDuration: `${Math.random() * 6 + 8}s`
                  }}
                />
              ))}
            </div>
            <div className="content-wrapper-modern">
              <div className="container">
                {children}
              </div>
            </div>
          </main>
        )}

        {/* Section Divider */}
        <div className="section-divider"></div>

        {/* Footer */}
        <ModernFooter />
      </div>
    </>
  );
};

export default ModernLayout;
