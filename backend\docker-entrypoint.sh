#!/bin/bash
set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}🚀 Starting University of Gondar Event Management System Backend...${NC}"

# Wait for database to be ready
echo -e "${YELLOW}⏳ Waiting for database to be ready...${NC}"
until nc -z db 5432; do
  echo -e "${YELLOW}⏳ Database is unavailable - sleeping${NC}"
  sleep 1
done
echo -e "${GREEN}✅ Database is ready!${NC}"

# Run database migrations
echo -e "${BLUE}🔄 Running database migrations...${NC}"
python manage.py migrate --noinput

# Create default user roles
echo -e "${BLUE}👥 Creating default user roles...${NC}"
python manage.py create_default_roles

# Create superuser if it doesn't exist
echo -e "${BLUE}👑 Creating superuser if needed...${NC}"
python manage.py shell << EOF
from authentication.models import CustomUser, User<PERSON>ole
import os

username = os.environ.get('DJANGO_SUPERUSER_USERNAME', 'admin')
email = os.environ.get('DJANGO_SUPERUSER_EMAIL', '<EMAIL>')
password = os.environ.get('DJANGO_SUPERUSER_PASSWORD', 'admin123')

if not CustomUser.objects.filter(username=username).exists():
    admin_role = UserRole.objects.get(name='admin')
    CustomUser.objects.create_superuser(
        username=username,
        email=email,
        password=password,
        first_name='System',
        last_name='Administrator',
        role=admin_role
    )
    print(f"Superuser '{username}' created successfully!")
else:
    print(f"Superuser '{username}' already exists.")
EOF

# Collect static files
echo -e "${BLUE}📦 Collecting static files...${NC}"
python manage.py collectstatic --noinput

# Load sample data if in development
if [ "$DJANGO_DEBUG" = "True" ]; then
    echo -e "${BLUE}📊 Loading sample data for development...${NC}"
    python manage.py shell << EOF
from events.models import Event, ParticipantType
from django.utils import timezone
from datetime import timedelta

# Create sample participant types if they don't exist
participant_types = [
    {'name': 'Speaker', 'color': '#dc3545', 'description': 'Event speakers and presenters'},
    {'name': 'Attendee', 'color': '#6c757d', 'description': 'General event attendees'},
    {'name': 'VIP', 'color': '#ffc107', 'description': 'VIP guests and dignitaries'},
    {'name': 'Staff', 'color': '#28a745', 'description': 'Event staff and volunteers'},
    {'name': 'Media', 'color': '#17a2b8', 'description': 'Media representatives'},
]

for pt_data in participant_types:
    ParticipantType.objects.get_or_create(
        name=pt_data['name'],
        defaults=pt_data
    )

# Create sample event if it doesn't exist
if not Event.objects.exists():
    Event.objects.create(
        name='University of Gondar Academic Conference 2025',
        description='Annual academic conference showcasing research and innovation at the University of Gondar',
        start_date=timezone.now().date(),
        end_date=timezone.now().date() + timedelta(days=3),
        location='University of Gondar Main Campus',
        city='Gondar',
        country='Ethiopia',
        organizer_name='Dr. Academic Director',
        organizer_email='<EMAIL>',
        organizer_phone='+251-911-123456',
        participant_count=0
    )
    print("Sample event created!")

print("Sample data loaded successfully!")
EOF
fi

echo -e "${GREEN}✅ Backend initialization complete!${NC}"
echo -e "${BLUE}🌐 Starting Django server...${NC}"

# Execute the main command
exec "$@"
