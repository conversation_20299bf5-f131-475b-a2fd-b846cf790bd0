#!/usr/bin/env python3

import os
import sys
import django

# Add the project root to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'event_management.settings')
django.setup()

from participants.models import Participant

def verify_final_participants():
    """Verify all participants from 551-602 are added"""
    
    print("=== FINAL PARTICIPANT VERIFICATION ===")
    
    # Check for participants 551-602
    expected_ids = list(range(551, 603))  # 551 to 602 inclusive
    
    found_count = 0
    missing_ids = []
    
    for participant_id in expected_ids:
        participant = Participant.objects.filter(id=participant_id).first()
        if participant:
            found_count += 1
            if participant_id <= 555:  # Show first few as examples
                print(f"✓ ID {participant_id}: {participant.first_name} {participant.last_name} ({participant.email})")
        else:
            missing_ids.append(participant_id)
    
    print(f"\nResults for IDs 551-602:")
    print(f"Found: {found_count}/52 participants")
    
    if missing_ids:
        print(f"Missing IDs: {missing_ids}")
    else:
        print("✓ All participants 551-602 successfully added!")
    
    # Show highest IDs
    print(f"\nHighest 5 participants by ID:")
    highest_participants = Participant.objects.order_by('-id')[:5]
    for p in highest_participants:
        print(f"ID: {p.id}, Name: {p.first_name} {p.last_name}, Email: {p.email}, Status: {p.status}")
    
    print(f"\nTotal participants in database: {Participant.objects.count()}")
    
    # Show distribution by status
    active_count = Participant.objects.filter(status='active').count()
    deleted_count = Participant.objects.filter(status='deleted').count()
    approved_count = Participant.objects.filter(status='approved').count()
    
    print(f"\nStatus distribution:")
    print(f"Active: {active_count}")
    print(f"Approved: {approved_count}")
    print(f"Deleted: {deleted_count}")

if __name__ == "__main__":
    verify_final_participants()
