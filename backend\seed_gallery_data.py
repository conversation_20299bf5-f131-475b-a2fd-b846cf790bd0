#!/usr/bin/env python
"""
Script to seed sample gallery data to fix 404 errors
"""
import os
import sys
import django

# Setup Django
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'event_management.settings')
django.setup()

from gallery.models import GalleryCategory, GalleryImage

def create_sample_gallery_data():
    """Create sample gallery categories and images"""
    
    print("🖼️ Creating sample gallery data...")
    
    # Create gallery categories
    categories_data = [
        {
            'name': 'University Campus',
            'description': 'Beautiful views of University of Gondar campus',
            'slug': 'university-campus',
            'icon': 'fas fa-university',
            'color': '#007bff',
            'is_active': True,
            'order': 1,
        },
        {
            'name': 'Events & Conferences',
            'description': 'Photos from various university events and conferences',
            'slug': 'events-conferences',
            'icon': 'fas fa-calendar-alt',
            'color': '#28a745',
            'is_active': True,
            'order': 2,
        },
        {
            'name': 'Academic Life',
            'description': 'Student life and academic activities',
            'slug': 'academic-life',
            'icon': 'fas fa-graduation-cap',
            'color': '#ffc107',
            'is_active': True,
            'order': 3,
        },
        {
            'name': 'Research & Innovation',
            'description': 'Research facilities and innovation centers',
            'slug': 'research-innovation',
            'icon': 'fas fa-microscope',
            'color': '#17a2b8',
            'is_active': True,
            'order': 4,
        },
    ]
    
    created_categories = []
    for cat_data in categories_data:
        category, created = GalleryCategory.objects.get_or_create(
            slug=cat_data['slug'],
            defaults=cat_data
        )
        if created:
            print(f"   ✓ Created category: {category.name}")
        else:
            print(f"   • Category already exists: {category.name}")
        created_categories.append(category)
    
    print(f"✅ Created/verified {len(created_categories)} gallery categories")
    
    # Note: We're not creating actual images since we don't have image files
    # The categories alone should be enough to fix the 404 errors
    # The frontend should handle empty image lists gracefully
    
    return created_categories

def verify_api_endpoints():
    """Verify that the API endpoints are working"""
    
    print("\n🔍 Verifying API endpoints...")
    
    categories = GalleryCategory.objects.all()
    images = GalleryImage.objects.all()
    
    print(f"   • Gallery categories: {categories.count()}")
    print(f"   • Gallery images: {images.count()}")
    
    if categories.exists():
        print("   ✅ Gallery categories API should now work")
    else:
        print("   ❌ No gallery categories found")
    
    print("   ✅ Gallery images API should return empty list (no 404)")

if __name__ == "__main__":
    categories = create_sample_gallery_data()
    verify_api_endpoints()
    print("\n🎉 Gallery data seeded successfully!")
    print("   The 404 errors for gallery endpoints should now be resolved.")
