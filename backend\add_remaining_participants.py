#!/usr/bin/env python3

import os
import sys
import django
from datetime import datetime

# Add the project root to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'event_management.settings')
django.setup()

from participants.models import Participant, ParticipantType
from events.models import Event
from django.db import connection

def add_remaining_participants():
    """Add remaining participants (337-356 and 511)"""
    
    print("=== ADDING REMAINING PARTICIPANTS ===")
    
    # Get Event ID 1
    try:
        event = Event.objects.get(id=1)
    except Event.DoesNotExist:
        print("Error: Event with ID 1 not found")
        return
    
    # Get participant types
    participant_types = {}
    for pt in ParticipantType.objects.all():
        participant_types[pt.id] = pt
    
    # Remaining participants data
    remaining_participants = [
        # (id, first_name, last_name, middle_name, email, phone, institution_name, participant_type_id, arrival_date, departure_date, profile_photo, remarks, status)
        (337, '<PERSON><PERSON>', '<PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON>b<PERSON>@gmail.com', '093 3585793', 'Arbaminch University', 2, '2025-08-03 15:39:00', '2025-08-07 08:30:00', None, '', 'active'),
        (338, 'Gemechis', 'Duressa', '', '<EMAIL>', '094 2565096', 'Jimma University', 23, '2025-08-03 16:48:00', '2025-08-07 20:48:00', None, 'NA', 'active'),
        (339, 'Andargachew', 'Deata', 'Baylie', '<EMAIL>', '092 8558827', 'Debre Markos University', 23, '2025-08-03 23:50:00', '2025-09-03 22:50:00', None, '', 'active'),
        (340, 'Pal', 'Dol', 'Both', '<EMAIL>', '091 7303194', 'Gambella University', 23, '2025-09-03 17:54:00', '2025-09-06 05:55:00', None, '', 'active'),
        (342, 'Yohannes', 'Ejigu', 'Yitbarek', '<EMAIL>', '+251 920437380', 'Jinka University', 2, '2025-08-03 15:40:00', '2025-08-07 08:35:00', None, '', 'active'),
        (343, 'Tadiyose', 'Arba', 'Arba', '<EMAIL>', '091 0153087', 'Prof Kindy\'s Security', 9, '2025-07-31 17:47:00', '2025-08-06 17:47:00', None, '', 'active'),
        (344, 'Mengesha', 'Mersha', 'Admasu', '<EMAIL>', '091 8350278', 'Raya University', 29, '2025-08-03 15:55:00', '2025-08-07 08:30:00', None, '', 'active'),
        (346, 'Tilahun', 'Retta', 'Teshome', '<EMAIL>', '091 1684491', 'AAU', 29, '2025-08-03 17:34:00', '2025-08-07 17:36:00', None, '', 'deleted'),
        (347, 'Tesfahun', 'Yilma', 'Melese', '<EMAIL>', '091 8779820', 'University of Gondar', 25, '2025-07-31 17:30:00', '2025-07-31 22:30:00', None, '', 'deleted'),
        (348, 'Endris', 'Ahmed', 'Seid', '<EMAIL>', '091 1034154', 'Samara University', 27, '2025-08-02 14:30:00', '2025-08-07 18:45:00', None, '', 'active'),
        (349, 'Zerihun', 'Assefa', '', '<EMAIL>', '098 3001916', 'Jimma University', 23, '2025-08-03 12:10:00', '2025-08-06 12:00:00', None, 'The arrival and departure times may change.', 'active'),
        (350, 'Engidaw', 'Muche', 'Awoke', '<EMAIL>', '+251 918030593', 'UoG', 4, '2025-07-30 17:11:00', '2025-07-31 17:11:00', None, '', 'deleted'),
        (351, 'Tesfaye', 'Feyissa', 'Tolu', '<EMAIL>', '+251 911677704', 'Ethiopian Defence University', 23, '2025-08-03 02:45:00', '2025-08-07 08:45:00', None, '', 'active'),
        (352, 'Lemma', 'Angessa', 'Gudissa', '<EMAIL>', '091 1968772', 'Ethiopian Civil Service University', 23, '2025-08-03 11:56:00', '2025-08-07 11:56:00', None, '', 'active'),
        (353, 'Dr. Shimelis', 'Admassie', 'Zewdie', '<EMAIL>', '091 1718823', 'Kotebe University of Education', 23, '2025-08-03 10:50:00', '2025-08-06 04:25:00', None, '', 'active'),
        (354, 'Meba Tadesse', 'Delle', '', '<EMAIL>', '091 1114759', 'Addis Ababa University', 27, '2025-08-03 11:38:00', '2025-08-07 11:38:00', None, '', 'active'),
        (355, 'Dr. Samuel', 'Kidane', 'Kifle', '<EMAIL>', '+251 911244079', 'Addis Ababa University', 2, '2025-08-03 11:27:00', '2025-08-06 11:26:00', None, '', 'active'),
        (356, 'Kebede', 'Gerbi', 'Regassa', '<EMAIL>', '+251 932180457', 'Ethiopian Defence University', 2, '2025-08-03 02:45:00', '2025-08-07 08:45:00', None, '', 'active'),
        (511, 'Carly', 'Gentry', 'Alice', '<EMAIL>', '091 0000000', 'Example University', 4, '2025-08-03 08:00:00', '2025-08-07 18:00:00', None, '', 'active'),
    ]
    
    added_count = 0
    updated_count = 0
    
    with connection.cursor() as cursor:
        for p_data in remaining_participants:
            # Check if participant already exists
            existing_participant = Participant.objects.filter(id=p_data[0]).first()
            
            if existing_participant:
                # Update existing participant if needed
                if existing_participant.email != p_data[4]:
                    print(f"Updating email for ID {p_data[0]}: {existing_participant.email} -> {p_data[4]}")
                    existing_participant.email = p_data[4]
                    existing_participant.save()
                    updated_count += 1
                else:
                    print(f"Participant with ID {p_data[0]} already exists with correct data, skipping...")
                continue
            
            # Get participant type
            participant_type = participant_types.get(p_data[7])
            if not participant_type:
                print(f"Warning: Participant type {p_data[7]} not found for {p_data[1]} {p_data[2]}")
                continue
            
            # Parse dates
            arrival_date = None
            departure_date = None
            
            if p_data[8]:  # arrival_date
                try:
                    arrival_date = datetime.strptime(p_data[8], "%Y-%m-%d %H:%M:%S")
                except:
                    arrival_date = None
            
            if p_data[9]:  # departure_date
                try:
                    departure_date = datetime.strptime(p_data[9], "%Y-%m-%d %H:%M:%S")
                except:
                    departure_date = None
            
            # Create participant
            try:
                participant = Participant(
                    first_name=p_data[1],
                    last_name=p_data[2],
                    middle_name=p_data[3] or "",
                    email=p_data[4],
                    phone=p_data[5],
                    institution_name=p_data[6],
                    position="Participant",
                    participant_type=participant_type,
                    arrival_date=arrival_date,
                    departure_date=departure_date,
                    profile_photo=p_data[10],
                    remarks=p_data[11] or "",
                    status=p_data[12],
                    event=event
                )
                participant.save()
                
                # Set the exact ID using raw SQL
                cursor.execute(
                    "UPDATE participants_participant SET id = %s WHERE id = %s",
                    [p_data[0], participant.id]
                )
                
                added_count += 1
                print(f"Added: {p_data[1]} {p_data[2]} with ID {p_data[0]}")
                
            except Exception as e:
                print(f"Error adding {p_data[1]} {p_data[2]}: {str(e)}")
    
    print(f"\nSummary:")
    print(f"Added {added_count} new participants")
    print(f"Updated {updated_count} existing participants")
    print(f"Total participants now: {Participant.objects.count()}")

if __name__ == "__main__":
    add_remaining_participants()
