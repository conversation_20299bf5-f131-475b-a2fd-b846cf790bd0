from rest_framework import viewsets, status
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework.permissions import AllowAny, IsAuthenticated
from django.http import HttpResponse, JsonResponse
from django.core.files.storage import default_storage
import csv
import io
from .models import Driver, DriverAssignment
from .serializers import DriverSerializer, DriverAssignmentSerializer


class DriverViewSet(viewsets.ModelViewSet):
    queryset = Driver.objects.all()
    serializer_class = DriverSerializer

    def get_permissions(self):
        """Allow public access for list, retrieve, create, update, and export actions"""
        if self.action in ['list', 'retrieve', 'create', 'update', 'partial_update', 'export_csv', 'download_sample', 'import_csv']:
            permission_classes = [AllowAny]
        else:
            permission_classes = [IsAuthenticated]
        return [permission() for permission in permission_classes]

    def get_queryset(self):
        queryset = super().get_queryset()
        event_id = self.request.query_params.get('event', None)
        is_available = self.request.query_params.get('available', None)

        if event_id is not None:
            queryset = queryset.filter(event=event_id)
        if is_available is not None:
            queryset = queryset.filter(is_available=is_available.lower() == 'true')

        return queryset

    @action(detail=True, methods=['post'])
    def toggle_availability(self, request, pk=None):
        """Toggle driver availability"""
        driver = self.get_object()
        driver.is_available = not driver.is_available
        driver.save()

        serializer = self.get_serializer(driver)
        return Response(serializer.data)

    @action(detail=False, methods=['get'])
    def export_csv(self, request):
        """Export drivers to CSV"""
        response = HttpResponse(content_type='text/csv')
        response['Content-Disposition'] = 'attachment; filename="drivers_export.csv"'

        writer = csv.writer(response)
        writer.writerow([
            'Name', 'Phone', 'Email', 'Car Plate', 'Car Code', 'Car Model',
            'Car Color', 'License Number', 'Event ID', 'Is Available', 'Notes'
        ])

        for driver in self.get_queryset():
            writer.writerow([
                driver.name, driver.phone, driver.email, driver.car_plate,
                driver.car_code, driver.car_model, driver.car_color,
                driver.license_number, driver.event.id if driver.event else '',
                driver.is_available, driver.notes
            ])

        return response

    @action(detail=False, methods=['get'])
    def download_sample(self, request):
        """Download sample CSV template"""
        response = HttpResponse(content_type='text/csv')
        response['Content-Disposition'] = 'attachment; filename="drivers_sample.csv"'

        writer = csv.writer(response)
        writer.writerow([
            'Name', 'Phone', 'Email', 'Car Plate', 'Car Code', 'Car Model',
            'Car Color', 'License Number', 'Event ID', 'Is Available', 'Notes'
        ])
        # Add sample rows (replace with actual data)
        writer.writerow([
            'Driver Name', '+251-911-123456', '<EMAIL>', 'ETH-001',
            'CAR001', 'Toyota Corolla', 'White', 'DL123456789', '1', 'True',
            'Professional driver with experience'
        ])

        return response

    @action(detail=False, methods=['post'])
    def import_csv(self, request):
        """Import drivers from CSV"""
        if 'file' not in request.FILES:
            return Response({'error': 'No file provided'}, status=status.HTTP_400_BAD_REQUEST)

        csv_file = request.FILES['file']
        if not csv_file.name.endswith('.csv'):
            return Response({'error': 'File must be a CSV'}, status=status.HTTP_400_BAD_REQUEST)

        try:
            decoded_file = csv_file.read().decode('utf-8')
            io_string = io.StringIO(decoded_file)
            reader = csv.DictReader(io_string)

            created_count = 0
            errors = []

            for row_num, row in enumerate(reader, start=2):
                try:
                    # Get or create event
                    event = None
                    if row.get('Event ID'):
                        from events.models import Event
                        try:
                            event = Event.objects.get(id=int(row['Event ID']))
                        except Event.DoesNotExist:
                            errors.append(f"Row {row_num}: Event with ID {row['Event ID']} not found")
                            continue

                    # Create driver
                    driver_data = {
                        'name': row['Name'],
                        'phone': row['Phone'],
                        'email': row['Email'],
                        'car_plate': row['Car Plate'],
                        'car_code': row['Car Code'],
                        'car_model': row['Car Model'],
                        'car_color': row['Car Color'],
                        'license_number': row['License Number'],
                        'event': event,
                        'is_available': row.get('Is Available', 'True').lower() == 'true',
                        'notes': row.get('Notes', '')
                    }

                    driver = Driver.objects.create(**driver_data)
                    created_count += 1

                except Exception as e:
                    errors.append(f"Row {row_num}: {str(e)}")

            return Response({
                'message': f'Successfully imported {created_count} drivers',
                'created_count': created_count,
                'errors': errors
            })

        except Exception as e:
            return Response({'error': f'Error processing file: {str(e)}'},
                          status=status.HTTP_400_BAD_REQUEST)


class DriverAssignmentViewSet(viewsets.ModelViewSet):
    queryset = DriverAssignment.objects.all()
    serializer_class = DriverAssignmentSerializer

    def get_queryset(self):
        queryset = super().get_queryset()
        driver_id = self.request.query_params.get('driver', None)
        participant_id = self.request.query_params.get('participant', None)
        status_filter = self.request.query_params.get('status', None)

        if driver_id is not None:
            queryset = queryset.filter(driver=driver_id)
        if participant_id is not None:
            queryset = queryset.filter(participant=participant_id)
        if status_filter is not None:
            queryset = queryset.filter(status=status_filter)

        return queryset

    @action(detail=True, methods=['post'])
    def update_status(self, request, pk=None):
        """Update assignment status"""
        assignment = self.get_object()
        new_status = request.data.get('status')

        if new_status in dict(DriverAssignment.STATUS_CHOICES):
            assignment.status = new_status
            assignment.save()

            serializer = self.get_serializer(assignment)
            return Response(serializer.data)
        else:
            return Response({'error': 'Invalid status'}, status=status.HTTP_400_BAD_REQUEST)
