from django.db import models


class Hotel(models.Model):
    name = models.CharField(max_length=200)
    address = models.TextField()
    phone = models.CharField(max_length=20)
    email = models.EmailField()
    contact_person = models.CharField(max_length=200)

    # Location
    latitude = models.DecimalField(max_digits=9, decimal_places=6, null=True, blank=True)
    longitude = models.DecimalField(max_digits=9, decimal_places=6, null=True, blank=True)

    # Hotel Information
    star_rating = models.IntegerField(choices=[(i, f"{i} Star") for i in range(1, 6)], null=True, blank=True)
    website = models.URLField(blank=True)
    description = models.TextField(blank=True)

    # Event association
    event = models.ForeignKey('events.Event', on_delete=models.CASCADE, related_name='hotels')

    # Availability
    is_active = models.BooleanField(default=True)

    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ['name']

    def __str__(self):
        return self.name


class HotelRoom(models.Model):
    hotel = models.ForeignKey(Hotel, on_delete=models.CASCADE, related_name='rooms')
    room_number = models.CharField(max_length=20)
    room_type = models.CharField(max_length=100)  # Single, Double, Suite, etc.
    capacity = models.IntegerField(default=1)
    price_per_night = models.DecimalField(max_digits=10, decimal_places=2, null=True, blank=True)
    amenities = models.TextField(blank=True, help_text='List of room amenities')
    is_available = models.BooleanField(default=True)

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        unique_together = ['hotel', 'room_number']
        ordering = ['hotel', 'room_number']

    def __str__(self):
        return f"{self.hotel.name} - Room {self.room_number}"


class HotelReservation(models.Model):
    participant = models.ForeignKey('participants.Participant', on_delete=models.CASCADE, related_name='hotel_reservations')
    hotel = models.ForeignKey(Hotel, on_delete=models.CASCADE, related_name='reservations')
    room = models.ForeignKey(HotelRoom, on_delete=models.CASCADE, related_name='reservations', null=True, blank=True)

    # Reservation Details
    check_in_date = models.DateField()
    check_out_date = models.DateField()
    number_of_guests = models.IntegerField(default=1)

    # Status
    STATUS_CHOICES = [
        ('pending', 'Pending'),
        ('confirmed', 'Confirmed'),
        ('checked_in', 'Checked In'),
        ('checked_out', 'Checked Out'),
        ('cancelled', 'Cancelled'),
    ]
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='pending')

    # Additional Information
    special_requests = models.TextField(blank=True)
    total_cost = models.DecimalField(max_digits=10, decimal_places=2, null=True, blank=True)

    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ['check_in_date']

    def __str__(self):
        return f"{self.participant.full_name} - {self.hotel.name} ({self.check_in_date} to {self.check_out_date})"
