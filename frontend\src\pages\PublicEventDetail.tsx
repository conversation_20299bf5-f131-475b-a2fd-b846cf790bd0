import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, Badge, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>se, Modal, Form } from 'react-bootstrap';
import { useParams, Link, useSearchParams, useLocation } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext';
import { eventService, documentService, Event, EventSchedule, EventGallery, EventDocument, Participant, getMediaUrl } from '../services/api';
import ModernLayout from '../components/ModernLayout';
import DailySchedule from '../components/DailySchedule';
import Gallery from '../components/Gallery';
import { useScrollToTop } from '../hooks/useScrollToTop';

// Utility function to safely format dates
const formatDate = (dateString: string): string => {
  try {
    const date = new Date(dateString);
    if (isNaN(date.getTime())) {
      return 'Invalid Date';
    }
    return date.toLocaleDateString();
  } catch (error) {
    return 'Invalid Date';
  }
};

// Utility function to safely format times
const formatTime = (timeString: string): string => {
  try {
    // Handle time format like "14:30" or "14:30:00"
    if (timeString && timeString.includes(':')) {
      // If it's just time format (HH:MM or HH:MM:SS), create a date object for today
      const timeParts = timeString.split(':');
      if (timeParts.length >= 2) {
        const hours = parseInt(timeParts[0]);
        const minutes = parseInt(timeParts[1]);

        if (!isNaN(hours) && !isNaN(minutes) && hours >= 0 && hours <= 23 && minutes >= 0 && minutes <= 59) {
          // Create a date object for formatting
          const date = new Date();
          date.setHours(hours, minutes, 0, 0);
          return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
        }
      }
    }

    // Fallback: try to parse as full date string
    const date = new Date(timeString);
    if (isNaN(date.getTime())) {
      return 'Invalid Time';
    }
    return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
  } catch (error) {
    return 'Invalid Time';
  }
};

// Simple Schedule Display Component with Collapsible Days
interface SimpleScheduleDisplayProps {
  schedules: EventSchedule[];
}

const SimpleScheduleDisplay: React.FC<SimpleScheduleDisplayProps> = ({ schedules }) => {
  const [expandedDays, setExpandedDays] = useState<{ [key: string]: boolean }>({});

  // Group schedules by date
  const groupSchedulesByDate = (schedules: EventSchedule[]) => {
    const grouped: { [key: string]: EventSchedule[] } = {};

    schedules.forEach(schedule => {
      if (!schedule.start_time) return;

      try {
        // Handle different date formats more robustly
        let dateKey: string;

        // If start_time is already in YYYY-MM-DD format
        if (schedule.start_time.match(/^\d{4}-\d{2}-\d{2}$/)) {
          dateKey = schedule.start_time;
        } else {
          // Try to parse as a full datetime
          const date = new Date(schedule.start_time);
          if (isNaN(date.getTime())) {
            // Skip invalid dates silently
            return;
          }

          // Extract date part safely
          const year = date.getFullYear();
          const month = String(date.getMonth() + 1).padStart(2, '0');
          const day = String(date.getDate()).padStart(2, '0');
          dateKey = `${year}-${month}-${day}`;
        }

        if (!grouped[dateKey]) {
          grouped[dateKey] = [];
        }
        grouped[dateKey].push(schedule);
      } catch (error) {
        console.error('Error parsing date:', schedule.start_time, error);
      }
    });

    return grouped;
  };

  const groupedSchedules = groupSchedulesByDate(schedules);

  // Initialize all days as expanded
  useEffect(() => {
    const initialState: { [key: string]: boolean } = {};
    Object.keys(groupedSchedules).forEach(dateKey => {
      initialState[dateKey] = true;
    });
    setExpandedDays(initialState);
  }, [schedules]);

  const toggleDay = (dateKey: string) => {
    setExpandedDays(prev => ({
      ...prev,
      [dateKey]: !prev[dateKey]
    }));
  };

  if (Object.keys(groupedSchedules).length === 0) {
    return (
      <div className="text-center py-5">
        <i className="fas fa-calendar-times fa-3x text-muted mb-3"></i>
        <h5 className="text-muted">No Schedule Available</h5>
        <p className="text-muted">The event schedule has not been published yet.</p>
      </div>
    );
  }

  return (
    <div>
      {Object.entries(groupedSchedules)
        .sort(([a], [b]) => a.localeCompare(b))
        .map(([dateKey, daySchedules]) => {
          // Parse date more safely
          let date: Date;
          let isValidDate = false;

          try {
            // Try parsing the dateKey as YYYY-MM-DD
            if (dateKey.match(/^\d{4}-\d{2}-\d{2}$/)) {
              date = new Date(dateKey + 'T00:00:00');
              isValidDate = !isNaN(date.getTime());
            } else {
              date = new Date(dateKey);
              isValidDate = !isNaN(date.getTime());
            }
          } catch (error) {
            console.error('Error parsing date for display:', dateKey, error);
            date = new Date();
            isValidDate = false;
          }

          return (
            <div key={dateKey} className="mb-4">
              <Card className="border-0 shadow-sm">
                <Card.Header
                  className="bg-primary text-white cursor-pointer"
                  onClick={() => toggleDay(dateKey)}
                  style={{ cursor: 'pointer' }}
                >
                  <div className="d-flex justify-content-between align-items-center">
                    <h5 className="mb-0 text-white">
                      <i className="fas fa-calendar-day me-2"></i>
                      {isValidDate ? date.toLocaleDateString('en-US', {
                        weekday: 'long',
                        year: 'numeric',
                        month: 'long',
                        day: 'numeric'
                      }) : `Day ${dateKey}`}
                    </h5>
                    <div className="d-flex align-items-center gap-3">
                      <Badge bg="light" text="dark">
                        {daySchedules.length} sessions
                      </Badge>
                      <i className={`fas fa-chevron-${expandedDays[dateKey] ? 'up' : 'down'}`}></i>
                    </div>
                  </div>
                </Card.Header>

                <Collapse in={expandedDays[dateKey]}>
                  <div>
                    <Card.Body className="p-0">
                      {daySchedules
                        .sort((a, b) => {
                          try {
                            const dateA = new Date(a.start_time);
                            const dateB = new Date(b.start_time);
                            if (isNaN(dateA.getTime()) || isNaN(dateB.getTime())) {
                              return 0; // Keep original order if dates are invalid
                            }
                            return dateA.getTime() - dateB.getTime();
                          } catch (error) {
                            return 0;
                          }
                        })
                        .map((schedule, index) => (
                          <div key={schedule.id} className={`p-4 ${index < daySchedules.length - 1 ? 'border-bottom' : ''}`}>
                            <Row className="align-items-center">
                              <Col md={3} className="text-center">
                                <div className="time-block p-3 rounded bg-light border">
                                  <div className="h6 text-primary mb-1 fw-bold">
                                    {formatTime(schedule.start_time)}
                                  </div>
                                  <small className="text-muted d-block">
                                    {formatTime(schedule.end_time)}
                                  </small>
                                </div>
                              </Col>
                              <Col md={9}>
                                <div className="d-flex align-items-start justify-content-between">
                                  <div>
                                    <h6 className="mb-2 fw-bold">{schedule.title}</h6>
                                    {schedule.description && (
                                      <p className="text-muted mb-2">{schedule.description}</p>
                                    )}
                                    <div className="d-flex flex-wrap gap-3 small text-muted">
                                      {schedule.location && (
                                        <span>
                                          <i className="fas fa-map-marker-alt me-1"></i>
                                          {schedule.location}
                                        </span>
                                      )}
                                      {schedule.speaker && (
                                        <span>
                                          <i className="fas fa-user me-1"></i>
                                          {schedule.speaker}
                                        </span>
                                      )}
                                      {schedule.is_break && (
                                        <Badge bg="warning">
                                          <i className="fas fa-coffee me-1"></i>
                                          Break
                                        </Badge>
                                      )}
                                    </div>
                                  </div>
                                </div>
                              </Col>
                            </Row>
                          </div>
                        ))}
                    </Card.Body>
                  </div>
                </Collapse>
              </Card>
            </div>
          );
        })}
    </div>
  );
};

// Helper function to get file icon based on extension
const getFileIcon = (extension: string): string => {
  switch (extension.toLowerCase()) {
    case 'pdf':
      return 'pdf';
    case 'doc':
    case 'docx':
      return 'word';
    case 'ppt':
    case 'pptx':
      return 'powerpoint';
    case 'xls':
    case 'xlsx':
      return 'excel';
    case 'txt':
      return 'text';
    default:
      return 'alt';
  }
};

const PublicEventDetail: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const [searchParams] = useSearchParams();
  const location = useLocation();
  const { isAuthenticated, user } = useAuth();
  const [event, setEvent] = useState<Event | null>(null);
  const [schedules, setSchedules] = useState<EventSchedule[]>([]);
  const [gallery, setGallery] = useState<EventGallery[]>([]);
  const [documents, setDocuments] = useState<EventDocument[]>([]);
  const [participants, setParticipants] = useState<Participant[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [activeTab, setActiveTab] = useState<string>('overview');


  // Scroll to top when component mounts or route changes
  useScrollToTop();

  useEffect(() => {
    if (id) {
      fetchEventData();
    }
  }, [id]);

  useEffect(() => {
    const tabParam = searchParams.get('tab');
    if (tabParam && ['overview', 'documents', 'gallery', 'participants', 'feedback'].includes(tabParam)) {
      setActiveTab(tabParam);
    }
  }, [searchParams]);

  const handleDownloadPDF = async () => {
    try {
      const response = await fetch(`/api/events/${id}/schedule/pdf/`);
      if (!response.ok) {
        throw new Error('Failed to download PDF');
      }

      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `${event?.name.replace(/\s+/g, '_')}_Schedule.pdf` || 'Event_Schedule.pdf';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);
    } catch (error) {
      console.error('Error downloading PDF:', error);
      alert('Failed to download schedule PDF. Please try again.');
    }
  };

  const fetchEventData = async () => {
    try {
      const [eventResponse, scheduleResponse, galleryResponse, documentsResponse, participantsResponse] = await Promise.allSettled([
        eventService.getEvent(parseInt(id!)),
        eventService.getEventSchedule(parseInt(id!)),
        eventService.getEventGallery(parseInt(id!)),
        documentService.getDocuments(parseInt(id!)),
        eventService.getEventParticipants(parseInt(id!)),
      ]);

      setEvent(eventResponse.status === 'fulfilled' ? eventResponse.value.data : null);
      setSchedules(scheduleResponse.status === 'fulfilled' ? scheduleResponse.value.data || [] : []); // Show all schedules
      setGallery(galleryResponse.status === 'fulfilled' ? galleryResponse.value.data || [] : []); // Show all gallery images

      // Set documents from API response
      setDocuments(documentsResponse.status === 'fulfilled' ? documentsResponse.value.data.results || [] : []);

      setParticipants(participantsResponse.status === 'fulfilled' ? participantsResponse.value.data || [] : []); // Show all participants
    } catch (error) {
      console.error('Error fetching event data:', error);
      setError('Failed to load event details');
    } finally {
      setLoading(false);
    }
  };

  const handleDocumentDownload = async (doc: EventDocument) => {
    try {
      const response = await documentService.downloadDocument(doc.id);

      // Create blob and download
      const blob = new Blob([response.data]);
      const url = window.URL.createObjectURL(blob);
      const link = window.document.createElement('a');
      link.href = url;
      link.download = doc.file.split('/').pop() || doc.title;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);
    } catch (error) {
      console.error('Error downloading document:', error);
      alert('Failed to download document');
    }
  };

  const getEventStatus = (event: Event) => {
    const now = new Date();
    const startDate = new Date(event.start_date);
    const endDate = new Date(event.end_date);

    if (now < startDate) {
      return <span className="badge bg-warning text-dark">Upcoming</span>;
    } else if (now >= startDate && now <= endDate) {
      return <span className="badge bg-success">Ongoing</span>;
    } else {
      return <span className="badge bg-secondary">Past</span>;
    }
  };



  if (loading) {
    return (
      <ModernLayout showHero={false}>
        <div className="container py-5">
          <div className="text-center py-5">
            <div className="spinner-border text-primary" role="status" style={{ width: '3rem', height: '3rem' }}>
              <span className="visually-hidden">Loading...</span>
            </div>
            <h4 className="mt-4 text-muted">Loading Event Details...</h4>
            <p className="text-muted">Please wait while we fetch the latest information</p>
          </div>
        </div>
      </ModernLayout>
    );
  }

  if (error || !event) {
    return (
      <ModernLayout showHero={false}>
        <div className="container py-5">
          <div className="text-center py-5">
            <i className="fas fa-exclamation-triangle fa-4x text-warning mb-4"></i>
            <h2 className="text-dark mb-3">Event Not Found</h2>
            <p className="text-muted mb-4 lead">{error || 'The requested event could not be found or may have been removed.'}</p>
            <Link to="/#events" className="btn btn-primary btn-lg">
              <i className="fas fa-arrow-left me-2"></i>
              Back to Events
            </Link>
          </div>
        </div>
      </ModernLayout>
    );
  }

  return (
    <ModernLayout className="event-detail-page" showHero={false}>
      <style>{`
        .gallery-item .hover-overlay {
          transition: all 0.3s ease;
        }
        .gallery-item:hover .hover-overlay {
          background-color: rgba(0, 0, 0, 0.5) !important;
        }
        .gallery-item:hover .hover-overlay .opacity-0 {
          opacity: 1 !important;
        }
        .nav-tabs .nav-link {
          border: none;
          border-radius: 0;
          padding: 1rem 1.5rem;
          font-weight: 600;
          color: #6c757d;
          transition: all 0.3s ease;
        }
        .nav-tabs .nav-link:hover {
          border-color: transparent;
          color: #007bff;
          background: rgba(0, 123, 255, 0.1);
        }
        .nav-tabs .nav-link.active {
          color: #007bff;
          background: white;
          border-color: #007bff #007bff #fff;
          border-width: 2px 2px 0;
        }
        .timeline-item:not(:last-child)::after {
          content: '';
          position: absolute;
          left: 50%;
          bottom: -2rem;
          width: 2px;
          height: 2rem;
          background: linear-gradient(to bottom, #007bff, transparent);
          transform: translateX(-50%);
        }
        @media (max-width: 768px) {
          .timeline-item::after {
            display: none;
          }
        }
      `}</style>
      <Container className="py-5">
        {/* Feedback Success Message */}
        {location.state?.feedbackSubmitted && (
          <Row className="justify-content-center mb-4">
            <Col lg={8}>
              <Alert variant="success" className="text-center shadow-sm border-0">
                <div className="d-flex align-items-center justify-content-center mb-2">
                  <i className="fas fa-check-circle fa-2x text-success me-3"></i>
                  <div>
                    <h4 className="alert-heading mb-1">Feedback Submitted Successfully!</h4>
                    <p className="mb-0">Thank you for sharing your valuable feedback. Your input helps us improve future events.</p>
                  </div>
                </div>
              </Alert>
            </Col>
          </Row>
        )}

        <Row className="justify-content-center">
          <Col xs={12} lg={11} xl={10}>
            {/* Page Header */}
            <div className="text-center mb-5">
              <h1 className="display-4 fw-bold text-primary mb-3">{event.name}</h1>
              <p className="lead text-muted mb-4">{event.description}</p>
            </div>

            {/* Event Status and Info */}
            <div className="d-flex flex-wrap gap-2 justify-content-center align-items-center mb-4">
              {getEventStatus(event)}
              <Badge bg="primary" className="px-3 py-2">
                <i className="fas fa-users me-1"></i>
                {participants.length} Participants
              </Badge>
              <Badge bg="info" className="px-3 py-2">
                <i className="fas fa-calendar me-1"></i>
                {formatDate(event.start_date)} - {formatDate(event.end_date)}
              </Badge>
              <Badge bg="success" className="px-3 py-2">
                <i className="fas fa-map-marker-alt me-1"></i>
                {event.location}
              </Badge>
            </div>

            {/* CTA Buttons */}
            <div className="d-flex flex-wrap gap-3 justify-content-center mb-5">
              <Link to="/participant-register" className="btn btn-primary btn-lg">
                <i className="fas fa-user-plus me-2"></i>
                Register Now
              </Link>

            </div>

            {/* Event Info Cards */}
            <Row className="mb-5 g-4">
              <Col xs={6} lg={3}>
                <Card className="text-center border-primary h-100 shadow-sm">
                  <Card.Body>
                    <i className="fas fa-calendar-alt text-primary fa-2x mb-3"></i>
                    <h6 className="text-muted">Duration</h6>
                    <p className="fw-bold small">
                      {formatDate(event.start_date)} - {' '}
                      {formatDate(event.end_date)}
                    </p>
                  </Card.Body>
                </Card>
              </Col>

              <Col xs={6} lg={3}>
                <Card className="text-center border-success h-100 shadow-sm">
                  <Card.Body>
                    <i className="fas fa-map-marker-alt text-success fa-2x mb-3"></i>
                    <h6 className="text-muted">Location</h6>
                    <p className="fw-bold">{event.location}</p>
                    <small className="text-muted">{event.city}, {event.country}</small>
                  </Card.Body>
                </Card>
              </Col>

              <Col xs={6} lg={3}>
                <Card className="text-center border-warning h-100 shadow-sm">
                  <Card.Body>
                    <i className="fas fa-users text-warning fa-2x mb-3"></i>
                    <h6 className="text-muted">Participants</h6>
                    <p className="fw-bold">{participants.length}</p>
                    <small className="text-muted">Registered</small>
                  </Card.Body>
                </Card>
              </Col>

              <Col xs={6} lg={3}>
                <Card className="text-center border-info h-100 shadow-sm">
                  <Card.Body>
                    <i className="fas fa-clock text-info fa-2x mb-3"></i>
                    <h6 className="text-muted">Schedule</h6>
                    <p className="fw-bold">{schedules.length}</p>
                    <small className="text-muted">Sessions</small>
                  </Card.Body>
                </Card>
              </Col>
            </Row>

            {/* Professional Tabbed Interface */}
            <Card className="border-0 shadow-lg">
              <Card.Body className="p-0">
                <Tabs
                  activeKey={activeTab}
                  onSelect={(k) => setActiveTab(k || 'overview')}
                  id="event-details-tabs"
                  className="nav-fill"
                  style={{
                    borderBottom: '2px solid #e9ecef',
                    background: 'linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%)'
                  }}
                >
                  {/* Overview Tab */}
                  <Tab
                    eventKey="overview"
                    title={
                      <span>
                        <i className="fas fa-info-circle me-2"></i>
                        Overview
                      </span>
                    }
                  >
                    <div className="p-4">
                      <h4 className="text-primary mb-4">
                        <i className="fas fa-info-circle me-2"></i>
                        About This Event
                      </h4>
                      <div className="row">
                        <div className="col-lg-8">
                          <p style={{ lineHeight: '1.8', fontSize: '1.1rem' }}>
                            {event.description}
                          </p>

                          {/* Event Banner in Overview */}
                          {event.banner && (
                            <div className="mt-4">
                              <h5 className="text-primary mb-3">Event Banner</h5>
                              <div className="event-banner rounded-4 overflow-hidden shadow-lg">
                                <img
                                  src={getMediaUrl(event.banner)}
                                  alt={event.name}
                                  className="img-fluid w-100"
                                  style={{ maxHeight: '300px', objectFit: 'cover' }}
                                />
                              </div>
                            </div>
                          )}
                        </div>
                        <div className="col-lg-4">
                          <Card className="border-primary">
                            <Card.Header className="bg-primary text-white">
                              <h6 className="mb-0">
                                <i className="fas fa-calendar me-2"></i>
                                Event Details
                              </h6>
                            </Card.Header>
                            <Card.Body>
                              <div className="mb-3">
                                <strong className="text-muted">Organizer:</strong>
                                <p className="mb-1">{event.organizer_name}</p>
                                <small className="text-muted">{event.organizer_email}</small>
                              </div>
                              <div className="mb-3">
                                <strong className="text-muted">Contact:</strong>
                                <p className="mb-0">{event.organizer_phone}</p>
                              </div>
                              <div className="mb-3">
                                <strong className="text-muted">Location:</strong>
                                <p className="mb-0">{event.city}, {event.country}</p>
                              </div>
                            </Card.Body>
                          </Card>
                        </div>
                      </div>
                    </div>
                  </Tab>

                  {/* Schedule Tab */}
                  <Tab
                    eventKey="documents"
                    title={
                      <span>
                        <i className="fas fa-file-alt me-2"></i>
                        Documents ({documents.length})
                      </span>
                    }
                  >
                    <div className="p-4">
                      <h4 className="text-primary mb-4">
                        <i className="fas fa-file-alt me-2"></i>
                        Event Documents
                      </h4>

                      {documents.length === 0 ? (
                        <div className="text-center py-5">
                          <i className="fas fa-file-alt text-muted fa-3x mb-3"></i>
                          <h5 className="text-muted">No Documents Available</h5>
                          <p className="text-muted">Event documents will be published here when available.</p>
                        </div>
                      ) : (
                        <div className="row">
                          {documents && documents.length > 0 && documents.map((doc) => (
                            <div key={doc.id} className="col-md-6 col-lg-4 mb-4">
                              <Card className="h-100 border-0 shadow-sm hover-shadow">
                                <Card.Body className="d-flex flex-column">
                                  <div className="d-flex align-items-center mb-3">
                                    <div className="me-3">
                                      <i className={`fas fa-file-${getFileIcon(doc.file_extension)} fa-2x text-primary`}></i>
                                    </div>
                                    <div className="flex-grow-1">
                                      <h6 className="mb-1 fw-bold">{doc.title}</h6>
                                      <small className="text-muted">
                                        <Badge bg="secondary" className="me-2">{doc.document_type_display}</Badge>
                                        {doc.formatted_file_size}
                                      </small>
                                    </div>
                                  </div>
                                  {doc.description && (
                                    <p className="text-muted small mb-3 flex-grow-1">{doc.description}</p>
                                  )}
                                  <div className="mt-auto">
                                    <div className="d-flex justify-content-between align-items-center">
                                      <small className="text-muted">
                                        <i className="fas fa-download me-1"></i>
                                        {doc.download_count} downloads
                                      </small>
                                      <Button
                                        variant="primary"
                                        size="sm"
                                        onClick={() => handleDocumentDownload(doc)}
                                        className="px-3"
                                      >
                                        <i className="fas fa-download me-1"></i>
                                        Download
                                      </Button>
                                    </div>
                                  </div>
                                </Card.Body>
                              </Card>
                            </div>
                          )) || null}
                        </div>
                      )}
                    </div>
                  </Tab>

                  {/* Gallery Tab */}
                  <Tab
                    eventKey="gallery"
                    title={
                      <span>
                        <i className="fas fa-images me-2"></i>
                        Gallery ({gallery.length})
                      </span>
                    }
                  >
                    <div className="p-4">
                      <h4 className="text-primary mb-4">
                        <i className="fas fa-images me-2"></i>
                        Event Gallery
                      </h4>

                      {/* Full Gallery Component */}
                      <Gallery
                        images={gallery}
                        showDateGrouping={true}
                        showDownloadButton={true}
                        showFeaturedBadge={true}
                        gridColumns={{ lg: 3, md: 4, sm: 6 }}
                        downloadPrefix={event?.name || 'Event'}
                        emptyStateMessage="No Event Photos Available"
                        emptyStateIcon="fas fa-camera"
                        imageHeight="250px"
                      />
                    </div>
                  </Tab>

                  {/* Participants Tab */}
                  <Tab
                    eventKey="participants"
                    title={
                      <span>
                        <i className="fas fa-users me-2"></i>
                        Participants ({participants.length})
                      </span>
                    }
                  >
                    <div className="p-4">
                      <h4 className="text-primary mb-4">
                        <i className="fas fa-users me-2"></i>
                        Registered Participants
                      </h4>
                      {participants.length > 0 ? (
                        <>
                          <Row className="g-3">
                            {participants.map((participant) => (
                              <Col key={participant.id} md={6} lg={4}>
                                <Card className="border-0 shadow-sm h-100">
                                  <Card.Body className="text-center">
                                    <div className="mb-3">
                                      <div className="bg-primary text-white rounded-circle d-inline-flex align-items-center justify-content-center" style={{ width: '60px', height: '60px' }}>
                                        <i className="fas fa-user fa-2x"></i>
                                      </div>
                                    </div>
                                    <h6 className="mb-1">{participant.first_name} {participant.last_name}</h6>
                                    <small className="text-muted">{participant.institution_name}</small>
                                    <div className="mt-2">
                                      <Badge bg="outline-primary" className="small">
                                        {participant.participant_type_name}
                                      </Badge>
                                    </div>
                                  </Card.Body>
                                </Card>
                              </Col>
                            ))}
                          </Row>
                          {participants.length > 0 && (
                            <div className="text-center mt-4">
                              <p className="text-muted">
                                Showing all {participants.length} participants
                              </p>
                            </div>
                          )}
                        </>
                      ) : (
                        <div className="text-center py-5">
                          <i className="fas fa-user-plus fa-3x text-muted mb-3"></i>
                          <h5 className="text-muted">No Participants Yet</h5>
                          <p className="text-muted">Be the first to register for this event!</p>
                          <Link to="/participant-register" className="btn btn-primary">
                            <i className="fas fa-user-plus me-2"></i>
                            Register Now
                          </Link>
                        </div>
                      )}
                    </div>
                  </Tab>

                  {/* Feedback Tab */}
                  <Tab
                    eventKey="feedback"
                    title={
                      <span>
                        <i className="fas fa-comment-alt me-2"></i>
                        Feedback
                      </span>
                    }
                  >
                    <div className="p-4">
                      <div className="text-center py-5">
                        <i className="fas fa-comment-alt fa-3x text-primary mb-3"></i>
                        <h4 className="text-primary mb-3">Share Your Feedback</h4>
                        <p className="text-muted mb-4">
                          Help us improve future events by sharing your experience and suggestions.
                        </p>
                        <Button
                          variant="primary"
                          size="lg"
                          onClick={() => window.open(`/events/${id}/feedback`, '_blank')}
                          className="px-4"
                        >
                          <i className="fas fa-external-link-alt me-2"></i>
                          Open Feedback Form
                        </Button>
                      </div>
                    </div>
                  </Tab>
                </Tabs>
              </Card.Body>
            </Card>
          </Col>
        </Row>
      </Container>


    </ModernLayout>
  );
};

export default PublicEventDetail;
