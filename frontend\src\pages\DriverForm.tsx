import React, { useState, useEffect } from 'react';
import { Container, Row, Col, Card, Form, Button, Alert, Spinner } from 'react-bootstrap';
import { useNavigate, useParams } from 'react-router-dom';
import { driverService, eventService, Event } from '../services/api';

const DriverForm: React.FC = () => {
  const navigate = useNavigate();
  const { id } = useParams<{ id: string }>();
  const isEdit = Boolean(id);

  const [loading, setLoading] = useState(false);
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [events, setEvents] = useState<Event[]>([]);
  const [photoPreview, setPhotoPreview] = useState<string | null>(null);

  const [formData, setFormData] = useState({
    name: '',
    phone: '',
    email: '',
    car_plate: '',
    car_code: '',
    car_model: '',
    car_color: '',
    license_number: '',
    event: '',
    is_available: true,
    notes: '',
    photo: null as File | null
  });

  useEffect(() => {
    fetchEvents();
    if (isEdit && id) {
      fetchDriver(parseInt(id));
    }
  }, [isEdit, id]);

  const fetchEvents = async () => {
    try {
      const response = await eventService.getEvents();
      const eventsData = Array.isArray(response.data) ? response.data : 
                        ((response.data as any)?.results ? (response.data as any).results : []);
      setEvents(eventsData);
    } catch (err) {
      console.error('Error fetching events:', err);
      setError('Failed to load events. Please try again.');
    }
  };

  const fetchDriver = async (driverId: number) => {
    try {
      setLoading(true);
      const response = await driverService.getDriver(driverId);
      const driver = response.data;
      
      setFormData({
        name: driver.name,
        phone: driver.phone,
        email: driver.email,
        car_plate: driver.car_plate,
        car_code: driver.car_code,
        car_model: driver.car_model,
        car_color: driver.car_color,
        license_number: driver.license_number,
        event: driver.event.toString(),
        is_available: driver.is_available,
        notes: driver.notes,
        photo: null
      });

      if (driver.photo) {
        setPhotoPreview(driver.photo);
      }
    } catch (err) {
      console.error('Error fetching driver:', err);
      setError('Failed to load driver data. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value, type } = e.target;
    
    if (type === 'checkbox') {
      const checked = (e.target as HTMLInputElement).checked;
      setFormData(prev => ({ ...prev, [name]: checked }));
    } else {
      setFormData(prev => ({ ...prev, [name]: value }));
    }
  };

  const handlePhotoChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      setFormData(prev => ({ ...prev, photo: file }));
      
      // Create preview
      const reader = new FileReader();
      reader.onload = (e) => {
        setPhotoPreview(e.target?.result as string);
      };
      reader.readAsDataURL(file);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!formData.name || !formData.phone || !formData.email || !formData.car_plate || !formData.car_code || !formData.event) {
      setError('Please fill in all required fields.');
      return;
    }

    try {
      setSaving(true);
      setError(null);

      const submitData = new FormData();
      submitData.append('name', formData.name);
      submitData.append('phone', formData.phone);
      submitData.append('email', formData.email);
      submitData.append('car_plate', formData.car_plate);
      submitData.append('car_code', formData.car_code);
      submitData.append('car_model', formData.car_model);
      submitData.append('car_color', formData.car_color);
      submitData.append('license_number', formData.license_number);
      submitData.append('event', formData.event);
      submitData.append('is_available', formData.is_available.toString());
      submitData.append('notes', formData.notes);
      
      if (formData.photo) {
        submitData.append('photo', formData.photo);
      }

      if (isEdit && id) {
        await driverService.updateDriver(parseInt(id), submitData);
      } else {
        await driverService.createDriver(submitData);
      }

      navigate('/drivers');
    } catch (err: any) {
      console.error('Error saving driver:', err);
      if (err.response?.data) {
        const errorData = err.response.data;
        if (typeof errorData === 'object') {
          const errorMessages = Object.entries(errorData)
            .map(([field, messages]) => `${field}: ${Array.isArray(messages) ? messages.join(', ') : messages}`)
            .join('\n');
          setError(errorMessages);
        } else {
          setError('Failed to save driver. Please check your input and try again.');
        }
      } else {
        setError('Failed to save driver. Please try again.');
      }
    } finally {
      setSaving(false);
    }
  };

  if (loading) {
    return (
      <Container className="text-center mt-5">
        <Spinner animation="border" role="status">
          <span className="visually-hidden">Loading...</span>
        </Spinner>
        <p className="mt-3">Loading driver data...</p>
      </Container>
    );
  }

  return (
    <Container className="py-4">
      <Row className="justify-content-center">
        <Col lg={8}>
          <Card className="shadow">
            <Card.Header className="bg-primary text-white">
              <h4 className="mb-0">
                <i className="fas fa-car me-2"></i>
                {isEdit ? 'Edit Driver' : 'Add New Driver'}
              </h4>
            </Card.Header>
            <Card.Body>
              {error && (
                <Alert variant="danger" className="mb-4">
                  <i className="fas fa-exclamation-triangle me-2"></i>
                  <pre style={{ whiteSpace: 'pre-wrap', margin: 0 }}>{error}</pre>
                </Alert>
              )}

              <Form onSubmit={handleSubmit}>
                <Row>
                  <Col md={6}>
                    <h5 className="text-primary mb-3">
                      <i className="fas fa-user me-2"></i>
                      Personal Information
                    </h5>
                    
                    <Form.Group className="mb-3">
                      <Form.Label>Full Name *</Form.Label>
                      <Form.Control
                        type="text"
                        name="name"
                        value={formData.name}
                        onChange={handleInputChange}
                        required
                        placeholder="Enter driver's full name"
                      />
                    </Form.Group>

                    <Form.Group className="mb-3">
                      <Form.Label>Phone Number *</Form.Label>
                      <Form.Control
                        type="tel"
                        name="phone"
                        value={formData.phone}
                        onChange={handleInputChange}
                        required
                        placeholder="+92-300-1234567"
                      />
                    </Form.Group>

                    <Form.Group className="mb-3">
                      <Form.Label>Email Address *</Form.Label>
                      <Form.Control
                        type="email"
                        name="email"
                        value={formData.email}
                        onChange={handleInputChange}
                        required
                        placeholder="<EMAIL>"
                      />
                    </Form.Group>

                    <Form.Group className="mb-3">
                      <Form.Label>Driver's License Number</Form.Label>
                      <Form.Control
                        type="text"
                        name="license_number"
                        value={formData.license_number}
                        onChange={handleInputChange}
                        placeholder="DL123456789"
                      />
                    </Form.Group>

                    <Form.Group className="mb-3">
                      <Form.Label>Driver Photo</Form.Label>
                      <Form.Control
                        type="file"
                        accept="image/*"
                        onChange={handlePhotoChange}
                      />
                      {photoPreview && (
                        <div className="mt-2">
                          <img 
                            src={photoPreview.startsWith('data:') ? photoPreview : photoPreview}
                            alt="Driver preview" 
                            className="img-thumbnail"
                            style={{ maxWidth: '150px', maxHeight: '150px' }}
                          />
                        </div>
                      )}
                    </Form.Group>
                  </Col>

                  <Col md={6}>
                    <h5 className="text-primary mb-3">
                      <i className="fas fa-car me-2"></i>
                      Vehicle Information
                    </h5>

                    <Form.Group className="mb-3">
                      <Form.Label>Car Plate Number *</Form.Label>
                      <Form.Control
                        type="text"
                        name="car_plate"
                        value={formData.car_plate}
                        onChange={handleInputChange}
                        required
                        placeholder="ABC-123"
                        style={{ textTransform: 'uppercase' }}
                      />
                    </Form.Group>

                    <Form.Group className="mb-3">
                      <Form.Label>Car Code *</Form.Label>
                      <Form.Control
                        type="text"
                        name="car_code"
                        value={formData.car_code}
                        onChange={handleInputChange}
                        required
                        placeholder="CAR001"
                        style={{ textTransform: 'uppercase' }}
                      />
                    </Form.Group>

                    <Form.Group className="mb-3">
                      <Form.Label>Car Model</Form.Label>
                      <Form.Control
                        type="text"
                        name="car_model"
                        value={formData.car_model}
                        onChange={handleInputChange}
                        placeholder="Toyota Corolla"
                      />
                    </Form.Group>

                    <Form.Group className="mb-3">
                      <Form.Label>Car Color</Form.Label>
                      <Form.Control
                        type="text"
                        name="car_color"
                        value={formData.car_color}
                        onChange={handleInputChange}
                        placeholder="White"
                      />
                    </Form.Group>

                    <Form.Group className="mb-3">
                      <Form.Label>Event *</Form.Label>
                      <Form.Select
                        name="event"
                        value={formData.event}
                        onChange={handleInputChange}
                        required
                      >
                        <option value="">Select an event</option>
                        {events.map(event => (
                          <option key={event.id} value={event.id}>
                            {event.name}
                          </option>
                        ))}
                      </Form.Select>
                    </Form.Group>

                    <Form.Group className="mb-3">
                      <Form.Check
                        type="checkbox"
                        name="is_available"
                        checked={formData.is_available}
                        onChange={handleInputChange}
                        label="Available for assignments"
                      />
                    </Form.Group>
                  </Col>
                </Row>

                <Form.Group className="mb-4">
                  <Form.Label>Additional Notes</Form.Label>
                  <Form.Control
                    as="textarea"
                    rows={3}
                    name="notes"
                    value={formData.notes}
                    onChange={handleInputChange}
                    placeholder="Any additional information about the driver..."
                  />
                </Form.Group>

                <div className="d-flex justify-content-between">
                  <Button 
                    variant="secondary" 
                    onClick={() => navigate('/drivers')}
                    disabled={saving}
                  >
                    <i className="fas fa-arrow-left me-2"></i>
                    Cancel
                  </Button>
                  <Button 
                    type="submit" 
                    variant="primary"
                    disabled={saving}
                  >
                    {saving ? (
                      <>
                        <Spinner animation="border" size="sm" className="me-2" />
                        {isEdit ? 'Updating...' : 'Creating...'}
                      </>
                    ) : (
                      <>
                        <i className="fas fa-save me-2"></i>
                        {isEdit ? 'Update Driver' : 'Create Driver'}
                      </>
                    )}
                  </Button>
                </div>
              </Form>
            </Card.Body>
          </Card>
        </Col>
      </Row>
    </Container>
  );
};

export default DriverForm;
