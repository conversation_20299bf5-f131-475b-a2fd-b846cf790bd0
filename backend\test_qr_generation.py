#!/usr/bin/env python3

import os
import sys
import django
import qrcode
import json
from datetime import datetime

# Add the project root to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'event_management.settings')
django.setup()

from participants.models import Participant

def generate_test_qr_codes():
    """Generate test QR codes for different participants"""
    
    print("=== GENERATING TEST QR CODES ===")
    
    # Get some sample participants
    participants = Participant.objects.filter(status='approved')[:5]
    
    if not participants:
        print("No approved participants found!")
        return
    
    for participant in participants:
        print(f"\nGenerating QR codes for: {participant.full_name} (ID: {participant.id})")
        
        # Format 1: JSON structured data (like your badge system)
        qr_data_json = {
            'participant_id': participant.id,
            'full_name': participant.full_name,
            'email': participant.email,
            'event_id': participant.event.id if participant.event else None,
            'event_name': participant.event.name if participant.event else None,
            'generated_at': datetime.now().isoformat(),
            'type': 'participant_badge',
            'version': '2.0'
        }
        
        # Format 2: Simple URL format
        url_format = f"https://event.uog.edu.et/verify/{participant.uuid}"
        
        # Format 3: Simple ID format
        id_format = str(participant.id)
        
        # Generate QR codes
        formats = [
            ('JSON', json.dumps(qr_data_json, separators=(',', ':'))),
            ('URL', url_format),
            ('ID', id_format)
        ]
        
        for format_name, data in formats:
            qr = qrcode.QRCode(
                version=1,
                error_correction=qrcode.constants.ERROR_CORRECT_L,
                box_size=10,
                border=4,
            )
            qr.add_data(data)
            qr.make(fit=True)
            
            # Create QR code image
            qr_img = qr.make_image(fill_color="black", back_color="white")
            
            # Save QR code
            filename = f"test_qr_{participant.id}_{format_name.lower()}.png"
            qr_img.save(filename)
            
            print(f"  - {format_name} QR Code saved as: {filename}")
            print(f"    Data: {data[:100]}{'...' if len(data) > 100 else ''}")
    
    print(f"\n✅ Generated QR codes for {len(participants)} participants")
    print("You can now test these QR codes with your scanner!")

def test_qr_data_parsing():
    """Test QR data parsing logic"""
    
    print("\n=== TESTING QR DATA PARSING ===")
    
    # Get a sample participant
    participant = Participant.objects.filter(status='approved').first()
    if not participant:
        print("No approved participants found!")
        return
    
    # Test different QR code formats
    test_formats = [
        {
            'name': 'JSON Format',
            'data': json.dumps({
                'participant_id': participant.id,
                'full_name': participant.full_name,
                'email': participant.email,
                'type': 'participant_badge'
            })
        },
        {
            'name': 'URL Format',
            'data': f"https://event.uog.edu.et/verify/{participant.uuid}"
        },
        {
            'name': 'Simple ID',
            'data': str(participant.id)
        },
        {
            'name': 'UUID Format',
            'data': str(participant.uuid)
        }
    ]
    
    for test_format in test_formats:
        print(f"\nTesting {test_format['name']}:")
        print(f"Data: {test_format['data']}")
        
        # Simulate parsing logic
        data = test_format['data']
        participant_identifier = None
        
        try:
            # Try JSON parsing
            parsed_json = json.loads(data)
            participant_identifier = parsed_json.get('participant_id') or parsed_json.get('uuid')
            print(f"  ✅ Parsed as JSON, identifier: {participant_identifier}")
        except (json.JSONDecodeError, AttributeError):
            # Try URL parsing
            if '/verify/' in data:
                participant_identifier = data.split('/verify/')[-1]
                print(f"  ✅ Parsed as URL, identifier: {participant_identifier}")
            else:
                # Assume direct identifier
                participant_identifier = data.strip()
                print(f"  ✅ Parsed as direct identifier: {participant_identifier}")
        
        # Test lookup
        try:
            if participant_identifier == str(participant.id):
                found_participant = Participant.objects.get(id=participant_identifier)
                print(f"  ✅ Found participant by ID: {found_participant.full_name}")
            elif participant_identifier == str(participant.uuid):
                found_participant = Participant.objects.get(uuid=participant_identifier)
                print(f"  ✅ Found participant by UUID: {found_participant.full_name}")
            else:
                print(f"  ❌ Could not find participant with identifier: {participant_identifier}")
        except Exception as e:
            print(f"  ❌ Error finding participant: {e}")

if __name__ == "__main__":
    generate_test_qr_codes()
    test_qr_data_parsing()
