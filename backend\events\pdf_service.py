from reportlab.lib.pagesizes import letter, A4
from reportlab.lib import colors
from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
from reportlab.lib.units import inch
from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer, Table, TableStyle, PageBreak
from reportlab.platypus.frames import Frame
from reportlab.platypus.doctemplate import PageTemplate, BaseDocTemplate
from reportlab.lib.enums import TA_CENTER, TA_LEFT, TA_RIGHT
from django.http import HttpResponse
from django.utils import timezone
from io import BytesIO
import os
from .models import Event, EventSchedule


class SchedulePDFGenerator:
    def __init__(self, event):
        self.event = event
        self.schedules = EventSchedule.objects.filter(event=event).order_by('start_time')
        
    def generate_pdf(self):
        """Generate PDF for event schedule"""
        buffer = BytesIO()
        doc = SimpleDocTemplate(
            buffer,
            pagesize=A4,
            rightMargin=72,
            leftMargin=72,
            topMargin=72,
            bottomMargin=18
        )
        
        # Container for the 'Flowable' objects
        elements = []
        
        # Get styles
        styles = getSampleStyleSheet()
        
        # Custom styles
        title_style = ParagraphStyle(
            'CustomTitle',
            parent=styles['Heading1'],
            fontSize=24,
            spaceAfter=30,
            alignment=TA_CENTER,
            textColor=colors.HexColor('#0d6efd')
        )
        
        subtitle_style = ParagraphStyle(
            'CustomSubtitle',
            parent=styles['Heading2'],
            fontSize=16,
            spaceAfter=20,
            alignment=TA_CENTER,
            textColor=colors.HexColor('#6c757d')
        )
        
        header_style = ParagraphStyle(
            'CustomHeader',
            parent=styles['Heading3'],
            fontSize=14,
            spaceAfter=12,
            textColor=colors.HexColor('#0d6efd')
        )
        
        # Title
        title = Paragraph(f"{self.event.name}", title_style)
        elements.append(title)
        
        # Subtitle
        subtitle = Paragraph("Event Schedule", subtitle_style)
        elements.append(subtitle)
        
        # Event details
        event_details = f"""
        <b>Date:</b> {self.event.start_date.strftime('%B %d, %Y')} - {self.event.end_date.strftime('%B %d, %Y')}<br/>
        <b>Location:</b> {self.event.location}, {self.event.city}, {self.event.country}<br/>
        <b>Organizer:</b> {self.event.organizer_name}<br/>
        <b>Generated:</b> {timezone.now().strftime('%B %d, %Y at %I:%M %p')}
        """
        
        details_para = Paragraph(event_details, styles['Normal'])
        elements.append(details_para)
        elements.append(Spacer(1, 20))
        
        if not self.schedules.exists():
            no_schedule = Paragraph("No schedule items available for this event.", styles['Normal'])
            elements.append(no_schedule)
        else:
            # Group schedules by date
            grouped_schedules = self._group_schedules_by_date()
            
            for date_str, day_schedules in grouped_schedules.items():
                # Date header
                date_header = Paragraph(f"<b>{date_str}</b>", header_style)
                elements.append(date_header)
                
                # Create table data
                table_data = [['Time', 'Session', 'Location', 'Speaker']]
                
                for schedule in day_schedules:
                    start_time = schedule.start_time.strftime('%I:%M %p')
                    end_time = schedule.end_time.strftime('%I:%M %p')
                    time_str = f"{start_time} - {end_time}"
                    
                    # Format session title with type
                    session_title = schedule.title
                    if schedule.session_type != 'presentation':
                        session_title += f" ({schedule.session_type.title()})"
                    
                    table_data.append([
                        time_str,
                        session_title,
                        schedule.location or 'TBA',
                        schedule.speaker or 'TBA'
                    ])
                
                # Create table
                table = Table(table_data, colWidths=[1.5*inch, 3*inch, 1.5*inch, 1.5*inch])
                table.setStyle(TableStyle([
                    # Header row
                    ('BACKGROUND', (0, 0), (-1, 0), colors.HexColor('#0d6efd')),
                    ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
                    ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
                    ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
                    ('FONTSIZE', (0, 0), (-1, 0), 10),
                    ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
                    
                    # Data rows
                    ('FONTNAME', (0, 1), (-1, -1), 'Helvetica'),
                    ('FONTSIZE', (0, 1), (-1, -1), 9),
                    ('ROWBACKGROUNDS', (0, 1), (-1, -1), [colors.white, colors.HexColor('#f8f9fa')]),
                    ('GRID', (0, 0), (-1, -1), 1, colors.HexColor('#dee2e6')),
                    ('VALIGN', (0, 0), (-1, -1), 'TOP'),
                    ('LEFTPADDING', (0, 0), (-1, -1), 6),
                    ('RIGHTPADDING', (0, 0), (-1, -1), 6),
                    ('TOPPADDING', (0, 0), (-1, -1), 6),
                    ('BOTTOMPADDING', (0, 0), (-1, -1), 6),
                ]))
                
                elements.append(table)
                elements.append(Spacer(1, 20))
        
        # Footer
        footer_text = f"""
        <br/><br/>
        <i>This schedule is subject to change. Please check the event website for the most up-to-date information.</i><br/>
        <b>Contact:</b> {self.event.organizer_email} | {self.event.organizer_phone}<br/>
        <b>Website:</b> https://event.uog.edu.et
        """
        footer = Paragraph(footer_text, styles['Normal'])
        elements.append(footer)
        
        # Build PDF
        doc.build(elements)
        
        # Get the value of the BytesIO buffer and return it
        pdf = buffer.getvalue()
        buffer.close()
        return pdf
    
    def _group_schedules_by_date(self):
        """Group schedules by date"""
        grouped = {}
        for schedule in self.schedules:
            date_str = schedule.start_time.strftime('%A, %B %d, %Y')
            if date_str not in grouped:
                grouped[date_str] = []
            grouped[date_str].append(schedule)
        return grouped
    
    def get_http_response(self):
        """Generate PDF and return as HTTP response"""
        pdf_content = self.generate_pdf()
        
        response = HttpResponse(pdf_content, content_type='application/pdf')
        filename = f"{self.event.name.replace(' ', '_')}_Schedule.pdf"
        response['Content-Disposition'] = f'attachment; filename="{filename}"'
        
        return response
