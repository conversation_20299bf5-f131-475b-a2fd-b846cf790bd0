# Generated by Django 4.2.7 on 2025-07-26 04:21

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ("events", "0001_initial"),
        ("participants", "0001_initial"),
    ]

    operations = [
        migrations.CreateModel(
            name="Driver",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("name", models.CharField(max_length=200)),
                ("phone", models.CharField(max_length=20)),
                ("email", models.EmailField(max_length=254)),
                (
                    "photo",
                    models.ImageField(
                        blank=True, null=True, upload_to="driver_photos/"
                    ),
                ),
                ("car_plate", models.CharField(max_length=20)),
                (
                    "car_code",
                    models.CharField(
                        help_text="Internal car identification code", max_length=50
                    ),
                ),
                ("car_model", models.Char<PERSON><PERSON>(blank=True, max_length=100)),
                ("car_color", models.<PERSON>r<PERSON>ield(blank=True, max_length=50)),
                ("license_number", models.<PERSON>r<PERSON>ield(blank=True, max_length=50)),
                ("is_available", models.BooleanField(default=True)),
                ("notes", models.TextField(blank=True)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "event",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="drivers",
                        to="events.event",
                    ),
                ),
            ],
            options={
                "ordering": ["name"],
            },
        ),
        migrations.CreateModel(
            name="DriverAssignment",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("pickup_location", models.CharField(max_length=300)),
                ("pickup_time", models.DateTimeField()),
                ("dropoff_location", models.CharField(max_length=300)),
                ("dropoff_time", models.DateTimeField(blank=True, null=True)),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("scheduled", "Scheduled"),
                            ("in_progress", "In Progress"),
                            ("completed", "Completed"),
                            ("cancelled", "Cancelled"),
                        ],
                        default="scheduled",
                        max_length=20,
                    ),
                ),
                ("notes", models.TextField(blank=True)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "driver",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="assignments",
                        to="drivers.driver",
                    ),
                ),
                (
                    "participant",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="driver_assignments",
                        to="participants.participant",
                    ),
                ),
            ],
            options={
                "ordering": ["pickup_time"],
            },
        ),
    ]
