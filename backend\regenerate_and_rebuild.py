#!/usr/bin/env python
"""
Regenerate badges with new SVG design and rebuild frontend
"""
import os
import sys
import django
import subprocess

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'event_management.settings')
django.setup()

from participants.models import Participant
from badges.models import Badge

def regenerate_badges():
    """Regenerate all badges with new SVG design"""
    print("🔄 Regenerating Badges with New SVG Design")
    print("=" * 60)
    
    try:
        # Get all participants
        participants = Participant.objects.all()[:10]  # Limit to first 10 for testing
        print(f"📊 Found {len(participants)} participants to process")
        
        if not participants:
            print("❌ No participants found")
            return False
        
        regenerated_count = 0
        
        for participant in participants:
            try:
                print(f"\n👤 Processing: {participant.full_name}")
                
                # Get or create badge
                badge, created = Badge.objects.get_or_create(participant=participant)
                
                # Clear existing badge files to force regeneration
                if badge.badge_image:
                    print(f"   🗑️  Clearing old badge: {badge.badge_image.name}")
                    badge.badge_image.delete(save=False)
                
                if badge.qr_code_image:
                    print(f"   🗑️  Clearing old QR code: {badge.qr_code_image.name}")
                    badge.qr_code_image.delete(save=False)
                
                # Reset generation status
                badge.is_generated = False
                badge.generated_at = None
                badge.save()
                
                # Generate new SVG badge
                print("   🎨 Generating NEW SVG-style badge...")
                badge_img = badge.generate_badge()
                
                regenerated_count += 1
                print(f"   ✅ SUCCESS - Dimensions: {badge_img.size}")
                
                # Verify SVG dimensions
                if badge_img.size == (600, 1200):
                    print("   🎯 CONFIRMED: Using new SVG template!")
                else:
                    print(f"   ⚠️  WARNING: Unexpected dimensions {badge_img.size}")
                    
            except Exception as e:
                print(f"   ❌ Error: {str(e)}")
        
        print(f"\n✅ Regenerated {regenerated_count} badges with new SVG design!")
        return regenerated_count > 0
        
    except Exception as e:
        print(f"❌ Error: {str(e)}")
        return False

def rebuild_frontend():
    """Rebuild the frontend to reflect changes"""
    print("\n🔨 Rebuilding Frontend")
    print("=" * 60)
    
    try:
        # Change to frontend directory
        frontend_dir = os.path.join(os.path.dirname(__file__), '..', 'frontend')
        
        if not os.path.exists(frontend_dir):
            print("❌ Frontend directory not found")
            return False
        
        print(f"📁 Frontend directory: {frontend_dir}")
        
        # Build the frontend
        print("🔨 Building React frontend...")
        result = subprocess.run(
            ['npm', 'run', 'build'],
            cwd=frontend_dir,
            capture_output=True,
            text=True,
            timeout=300  # 5 minutes timeout
        )
        
        if result.returncode == 0:
            print("✅ Frontend build successful!")
            print("📦 New build created in frontend/build/")
            return True
        else:
            print("❌ Frontend build failed!")
            print("STDOUT:", result.stdout)
            print("STDERR:", result.stderr)
            return False
            
    except subprocess.TimeoutExpired:
        print("❌ Frontend build timed out (5 minutes)")
        return False
    except Exception as e:
        print(f"❌ Error building frontend: {str(e)}")
        return False

def main():
    """Main function"""
    print("🚀 REGENERATE BADGES AND REBUILD FRONTEND")
    print("=" * 60)
    
    # Step 1: Regenerate badges
    badges_success = regenerate_badges()
    
    if not badges_success:
        print("\n❌ Badge regeneration failed. Skipping frontend rebuild.")
        return
    
    # Step 2: Rebuild frontend
    frontend_success = rebuild_frontend()
    
    # Summary
    print("\n" + "=" * 60)
    print("📊 SUMMARY")
    print(f"🎨 Badge Regeneration: {'✅ SUCCESS' if badges_success else '❌ FAILED'}")
    print(f"🔨 Frontend Rebuild: {'✅ SUCCESS' if frontend_success else '❌ FAILED'}")
    
    if badges_success and frontend_success:
        print("\n🎉 ALL DONE!")
        print("💡 The badge preview page should now show the new SVG design")
        print("🔄 You may need to clear browser cache or hard refresh")
        print("🌐 Visit: https://event.uog.edu.et/badge-preview")
    else:
        print("\n⚠️  Some steps failed. Check the output above for details.")

if __name__ == "__main__":
    main()
