#!/usr/bin/env python3

import os
import sys
import django
from datetime import datetime

# Add the project root to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'event_management.settings')
django.setup()

from participants.models import Participant, ParticipantType
from events.models import Event

def create_additional_participants():
    """Create additional participants with exact IDs"""
    
    # Get the event (assuming Event ID 1)
    try:
        event = Event.objects.get(id=1)
    except Event.DoesNotExist:
        print("Error: Event with ID 1 not found")
        return
    
    # Get all participant types
    participant_types = {pt.id: pt for pt in ParticipantType.objects.all()}
    
    # Additional participants data
    additional_participants = [
        # (id, first_name, last_name, middle_name, email, phone, institution_name, participant_type_id, arrival_date, departure_date, profile_photo, remarks, status)
        (154, "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<EMAIL>", "093 7458433", "Arba Minch University", 23, "2025-08-03 15:30:00", "2025-08-07 08:30:00", None, "", "approved"),
        (155, "Dr Tolossa", "Wedajo", "<PERSON>i", "<EMAIL>", "093 0364635", "<PERSON>ale University", 23, "2025-08-03 01:35:00", "2025-08-07 10:20:00", None, "", "approved"),
        (156, "<PERSON>feri", "Jibicho", "Gishe", "<EMAIL>", "091 6358173", "Ethiopian civil service university", 27, "2025-08-03 10:55:00", "2025-08-07 10:58:00", None, "", "approved"),
        (157, "Tadewos", "Wogasso", "Mentta", "<EMAIL>", "093 7809795", "Ethiopian Civil Service University", 23, "2025-08-03 15:00:00", "2025-08-07 14:00:00", None, "", "approved"),
        (158, "engidaw", "awoke", "", "<EMAIL>", "091 8030593", "UoG", 4, "2025-07-31 03:48:00", "2025-08-01 03:48:00", None, "", "pending"),
        (159, "Dr. Abdi", "Hasan", "Ahmed", "<EMAIL>", "091 1042563", "Jigjiga University", 23, "2025-08-03 06:43:00", "2025-08-06 10:00:00", "event-registration/profile_688ae70354260.jpeg", "", "approved"),
        (160, "Awoke", "Melese", "Azanaw", "<EMAIL>", "091 8305854", "Debark University", 27, "2025-08-03 08:31:00", "2025-08-03 08:36:00", "event-registration/profile_688b014d0746c.jpg", "", "approved"),
        (161, "Eba", "Negero", "Mijena", "<EMAIL>", "091 1110148", "MoE", 8, "2025-08-03 03:01:00", "2025-08-08 09:02:00", None, "", "approved"),
        (162, "Dr Zakaria", "Mohamed", "Abdiwali", "<EMAIL>", "091 3551873", "University of Kabridahar", 27, "2025-08-03 10:15:00", "2025-08-06 09:15:00", "event-registration/profile_688b0a3346015.jpg", "", "approved"),
        (163, "Abebaw", "Mekonnen", "Demssie", "<EMAIL>", "092 3656322", "Wollo university", 27, "2025-08-03 22:30:00", "2025-08-06 07:30:00", None, "", "approved"),
        (164, "Kelelew", "Hailemichael", "Addisu", "<EMAIL>", "093 0318063", "Bonga University", 23, "2025-08-03 12:15:00", "2025-08-06 10:05:00", None, "", "approved"),
        (165, "Prof. Bizunesh", "Borena", "Mideksa", "<EMAIL>", "+251 944741626", "Ambo University", 23, "2025-08-03 08:40:00", "2025-08-07 08:30:00", "event-registration/profile_688b1b86f0095.jpg", "", "approved"),
        (166, "Temam", "Bartuga", "Argaw", "<EMAIL>", "091 5666492", "Werabe university", 25, "2025-08-03 00:55:00", "2025-08-03 05:30:00", None, "", "approved"),
        (167, "Yesihak", "Mummed", "Yusuf", "<EMAIL>", "093 0760076", "Haramaya University", 23, "2025-08-03 14:30:00", "2025-08-06 08:00:00", None, "", "approved"),
        (168, "Eng. Abdifetah", "Rabi", "Ahmed", "<EMAIL>", "091 1303733", "University", 2, "2025-08-03 03:26:00", "2025-08-04 03:50:00", "event-registration/profile_688b2acc8d99c.jpg", "", "approved"),
        (169, "Teklu", "Zara", "Wegatehu", "<EMAIL>", "+251 911004186", "Arba Minch University", 23, "2025-08-03 15:30:00", "2025-09-07 02:30:00", "event-registration/profile_688b2af749fb6.jpg", "", "approved"),
        (170, "Tamirat", "Bekele", "Mulugeta", "<EMAIL>", "092 1335855", "Ethiopian Police University", 2, "2025-08-03 12:30:00", "2025-09-06 12:32:00", None, "Acting President", "approved"),
        (171, "Ahmed", "Abdinasir", "Muhumed", "<EMAIL>", "091 5751700", "University of Kabridahar", 23, "2025-08-03 22:49:00", "2025-08-06 12:50:00", None, "", "approved"),
        (172, "Bedilu", "Geleto", "Teka", "<EMAIL>", "093 7003501", "Mattu University", 23, "2025-08-03 12:56:00", "2025-08-07 12:57:00", "event-registration/profiles/profile_3e619b1d-7f31-", "My arrival and return date is filled in GeG.C not", "approved"),
        (173, "Berhanu", "Ali", "Shibeshi", "<EMAIL>", "094 1741764", "WACHEMO UNIVERSITY", 27, "2025-08-04 14:29:00", "2025-08-07 14:29:00", "event-registration/profiles/profile_5868e969-a1d6-", "", "approved"),
        # More participants
        (328, "Prof Ajebu", "Nurfeta", "", "<EMAIL>", "091 6032359", "Hawassa University", 29, "2025-08-03 16:41:00", "2025-08-06 16:42:00", None, "None", "approved"),
        (329, "Mulugeta", "Gebremedhin", "Berihu", "<EMAIL>", "+251 932314972", "Aksun University", 23, "2025-08-03 21:00:00", "2025-08-06 21:00:00", None, "", "approved"),
        (330, "Dr Dawit", "Borsamo", "Hayeso", "<EMAIL>", "093 8984001", "Wachemo university", 2, "2025-08-03 03:29:00", "2025-08-07 09:30:00", None, "", "approved"),
        (331, "Dr. Tadesse", "Tessema", "Habtamu", "<EMAIL>", "091 1962721", "Jimma University", 23, "2025-08-03 16:22:00", "2025-08-07 10:24:00", None, "Nothing special", "approved"),
        (332, "Nigus", "Bshe", "Tadesse", "<EMAIL>", "094 2523422", "Ethiopian Civi Service University", 2, "2025-08-04 13:44:00", "2025-08-04 13:45:00", None, "", "approved"),
        (333, "Jema", "Mohammed", "Haji", "<EMAIL>", "091 1780518", "Haramaya University", 29, "2025-08-03 15:55:00", "2025-08-07 10:20:00", None, "", "approved"),
        (334, "Argaw", "Bayih", "Ambelu", "<EMAIL>", "091 1826218", "Addis Ababa University", 29, "2025-08-03 13:25:00", "2025-08-06 13:25:00", None, "", "approved"),
        (335, "Sara", "Muzein", "Shikur", "<EMAIL>", "091 1891235", "Wachemo University", 23, "2025-08-03 15:30:00", "2025-08-07 03:30:00", None, "", "approved"),
        (336, "Ebrahim", "Mohammed", "Jemal", "<EMAIL>", "+251 910990926", "Jinka University", 23, "2025-08-03 11:01:00", "2025-08-07 11:10:00", None, "", "approved"),
        (337, "Abdella", "Mohammed", "Kemal", "<EMAIL>", "093 3585793", "Arbaminch University", 2, "2025-08-03 15:39:00", "2025-08-07 08:30:00", None, "", "approved"),
        (338, "Gemechis", "Duressa", "", "<EMAIL>", "094 2565096", "Jimma University", 23, "2025-08-03 16:48:00", "2025-08-07 20:48:00", None, "NA", "approved"),
        (339, "Andargachew", "Deata", "Baylie", "<EMAIL>", "092 8558827", "Debre Markos University", 23, "2025-08-03 23:50:00", "2025-09-03 22:50:00", None, "", "approved"),
        (340, "Pal", "Dol", "Both", "<EMAIL>", "091 7303194", "Gambella University", 23, "2025-09-03 17:54:00", "2025-09-06 05:55:00", None, "", "approved"),
        (342, "Yohannes", "Ejigu", "Yitbarek", "<EMAIL>", "+251 920437380", "Jinka University", 2, "2025-08-03 15:40:00", "2025-08-07 08:35:00", None, "", "approved"),
        (343, "Tadiyose", "Arba", "Arba", "<EMAIL>", "091 0153087", "Prof Kindy's Security", 9, "2025-07-31 17:47:00", "2025-08-06 17:47:00", None, "", "approved"),
        (344, "Mengesha", "Mersha", "Admasu", "<EMAIL>", "091 8350278", "Raya University", 29, "2025-08-03 15:55:00", "2025-08-07 08:30:00", None, "", "approved"),
        (346, "Tilahun", "Retta", "Teshome", "<EMAIL>", "091 1684491", "AAU", 29, "2025-08-03 17:34:00", "2025-08-07 17:36:00", None, "", "pending"),
        (347, "Tesfahun", "Yilma", "Melese", "<EMAIL>", "091 8779820", "University of Gondar", 25, "2025-07-31 17:30:00", "2025-07-31 22:30:00", None, "", "pending"),
        (348, "Endris", "Ahmed", "Seid", "<EMAIL>", "091 1034154", "Samara University", 27, "2025-08-02 14:30:00", "2025-08-07 18:45:00", None, "", "approved"),
        (349, "Zerihun", "Assefa", "", "<EMAIL>", "098 3001916", "Jimma University", 23, "2025-08-03 12:10:00", "2025-08-06 12:00:00", None, "The arrival and departure times may change.", "approved"),
        (350, "Engidaw", "Muche", "Awoke", "<EMAIL>", "+251 918030593", "UoG", 4, "2025-07-30 17:11:00", "2025-07-31 17:11:00", None, "", "pending"),
        (351, "Tesfaye", "Feyissa", "Tolu", "<EMAIL>", "+251 911677704", "Ethiopian Defence University", 23, "2025-08-03 02:45:00", "2025-08-07 08:45:00", None, "", "approved"),
        (352, "Lemma", "Angessa", "Gudissa", "<EMAIL>", "091 1968772", "Ethiopian Civil Service University", 23, "2025-08-03 11:56:00", "2025-08-07 11:56:00", None, "", "approved"),
        (353, "Dr. Shimelis", "Admassie", "Zewdie", "<EMAIL>", "091 1718823", "Kotebe University of Education", 23, "2025-08-03 10:50:00", "2025-08-06 04:25:00", None, "", "approved"),
        (354, "Meba Tadesse", "Delle", "", "<EMAIL>", "091 1114759", "Addis Ababa University", 27, "2025-08-03 11:38:00", "2025-08-07 11:38:00", None, "", "approved"),
        (355, "Dr. Samuel", "Kidane", "Kifle", "<EMAIL>", "+251 911244079", "Addis Ababa University", 2, "2025-08-03 11:27:00", "2025-08-06 11:26:00", None, "", "approved"),
        (356, "Kebede", "Gerbi", "Regassa", "<EMAIL>", "+251 932180457", "Ethiopian Defence University", 2, "2025-08-03 02:45:00", "2025-08-07 08:45:00", None, "", "approved"),
        (511, "Carly", "Gentry", "Alice Ortiz", "<EMAIL>", "099 0542178", "Anderson Maddox Associates", 6, "2025-08-01 16:28:00", "2025-08-08 23:16:00", "event-registration/profiles/profile_9c38531b-9305-", "Ipsam reiciendis ali", "pending"),
        (512, "Adem", "Kabo", "Ali", "<EMAIL>", "091 2127133", "Samara university", 23, "2025-08-03 15:39:00", "2025-08-06 15:40:00", "event-registration/profiles/profile_d1974986-bdd2-", "Ok", "approved"),
        (513, "Matebu", "Jabessa", "Gezahegn", "<EMAIL>", "094 2632924", "Jimma University", 27, "2025-08-03 16:31:00", "2025-08-07 16:31:00", None, "", "approved"),
        (514, "Asmamaw", "Workneh", "Zegeye", "<EMAIL>", "096 0100825", "Debark university", 2, "2025-08-03 17:16:00", "2025-08-07 17:16:00", "event-registration/profiles/profile_5d540ecb-6723-", "", "approved"),
        (515, "Dr.Megersa", "Hussen", "Kasim", "<EMAIL>", "099 3440333", "Dire Dawa University", 23, "2025-08-03 00:30:00", "2025-09-06 16:25:00", "event-registration/profiles/profile_797e48f7-ae85-", "", "approved"),
        (516, "LAMESGIN", "TIZAZU", "AYELE", "<EMAIL>", "091 2768313", "Dire dawa University", 27, "2025-08-03 14:03:00", "2025-08-06 04:25:00", None, "", "approved"),
        (517, "Woldeamlak", "Alemayehu", "Bewket", "<EMAIL>", "091 1608122", "Ambo University", 29, "2025-08-03 13:35:00", "2025-08-07 10:20:00", None, "", "approved"),
        (518, "Derbew", "Yohannes", "Belew", "<EMAIL>", "+251 946511627", "Wollega University", 29, "2025-08-03 13:25:00", "2025-08-07 10:20:00", "event-registration/profiles/profile_a5240abe-4745-", "", "approved"),
        (519, "Bizunesh", "Borena", "Mideksa", "<EMAIL>", "+251 944741626", "Ambo University", 23, "2025-08-03 08:40:00", "2025-09-07 08:30:00", "event-registration/profiles/profile_58750392-4301-", "", "approved"),
        (520, "LIDETU", "GOBENA", "GIZAW", "<EMAIL>", "+251 913307451", "BONGA UNIVERSITY", 27, "2025-08-03 12:15:00", "2025-08-06 10:05:00", None, "Thanks!", "approved"),
        (521, "Alemayehu", "Mekonnen", "Debebe", "<EMAIL>", "+251 935998680", "Ethiopian Civil Service University", 23, "2025-08-03 13:00:00", "2025-08-07 10:00:00", None, "None", "approved"),
        (522, "Alemayehu", "Mekonnen", "Debebe", "<EMAIL>", "+251 935998680", "Ethiopian Civil Service University", 23, "2025-08-03 13:00:00", "2025-08-07 10:00:00", None, "None", "approved"),
        (523, "Asnake", "Ede", "Gudisa", "<EMAIL>", "096 5568511", "Kotebe University of Education", 27, "2025-08-03 12:15:00", "2025-08-06 05:45:00", "event-registration/profiles/profile_c441fb03-985b-", "", "approved"),
    ]
    
    created_count = 0
    
    for p_data in additional_participants:
        # Check if participant type exists
        if p_data[7] not in participant_types:
            print(f"Warning: Participant type {p_data[7]} not found, skipping participant {p_data[1]} {p_data[2]}")
            continue
            
        # Check if participant already exists
        if Participant.objects.filter(id=p_data[0]).exists():
            print(f"Participant with ID {p_data[0]} already exists, skipping {p_data[1]} {p_data[2]}")
            continue
            
        # Check if email already exists
        if Participant.objects.filter(email=p_data[4]).exists():
            print(f"Email {p_data[4]} already exists, skipping participant {p_data[1]} {p_data[2]}")
            continue
            
        try:
            participant = Participant(
                first_name=p_data[1],
                last_name=p_data[2],
                middle_name=p_data[3],
                email=p_data[4],
                phone=p_data[5],
                institution_name=p_data[6],
                position="Participant",  # Default position
                participant_type=participant_types[p_data[7]],
                arrival_date=p_data[8],
                departure_date=p_data[9],
                profile_photo=p_data[10],
                remarks=p_data[11] or "",
                status=p_data[12],
                event=event
            )
            participant.save()
            
            # Set the ID after saving to preserve exact IDs
            participant.id = p_data[0]
            participant.save()
            
            created_count += 1
            print(f"Created participant: {p_data[1]} {p_data[2]} (ID: {p_data[0]})")
            
        except Exception as e:
            print(f"Error creating participant {p_data[1]} {p_data[2]}: {str(e)}")
    
    print(f"\nCreated {created_count} additional participants.")

if __name__ == "__main__":
    create_additional_participants()
