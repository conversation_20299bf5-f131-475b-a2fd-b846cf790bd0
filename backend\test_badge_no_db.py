#!/usr/bin/env python
"""
Test badge generation without database dependencies
This tests the new SVG-style badge generation methods
"""
import sys
import os

# Add the project root to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Test the badge generation methods directly
from PIL import Image, ImageDraw, ImageFont
import qrcode
from io import BytesIO

class TestBadgeGenerator:
    """Test class that mimics the Badge model methods"""
    
    def __init__(self):
        self.participant = self.MockParticipant()
    
    class MockParticipant:
        def __init__(self):
            self.id = 1
            self.first_name = "<PERSON>"
            self.last_name = "<PERSON><PERSON>"
            self.position = "Software Engineer"
            self.institution_name = "University of Gondar"
            self.participant_type = self.MockParticipantType()
    
        class MockParticipantType:
            def __init__(self):
                self.name = "Keynote Speaker"

    def generate_qr_code(self):
        """Generate QR code for testing"""
        qr = qrcode.QRCode(version=1, box_size=10, border=5)
        qr.add_data(f"https://event.uog.edu.et/verify/{self.participant.id}")
        qr.make(fit=True)
        return qr.make_image(fill_color="black", back_color="white")

    def _add_svg_gradient_background(self, draw, width, height):
        """Add gradient background like SVG template"""
        for y in range(height):
            progress = y / height
            r = int(0 + (2 - 0) * progress)
            g = int(45 + (27 - 45) * progress)
            b = int(114 + (58 - 114) * progress)
            draw.line([(0, y), (width, y)], fill=(r, g, b))

    def _add_svg_decorative_elements(self, draw, width, height):
        """Add decorative dots like SVG template"""
        dots = [(50, 150), (70, 170), (90, 190), (110, 210), (130, 230)]
        for x, y in dots:
            draw.ellipse([x-3, y-3, x+3, y+3], fill=(255, 255, 255, 38))
        
        dots = [(550, 180), (530, 200), (510, 220), (490, 240)]
        for x, y in dots:
            draw.ellipse([x-3, y-3, x+3, y+3], fill=(255, 255, 255, 38))

    def _add_svg_top_ribbon(self, draw, width, height):
        """Add top decorative ribbon like SVG template"""
        points = [(0, 0), (width, 0), (width, 120), (0, 300)]
        draw.polygon(points, fill=(0, 75, 145, 102))
        
        points = [(0, 300), (width, 120), (width, 140), (0, 320)]
        draw.polygon(points, fill=(0, 75, 145, 77))

    def _add_svg_ministry_logo(self, draw, width, height):
        """Add ministry logo area like SVG template"""
        center_x, center_y = 300, 90
        radius = 50
        draw.ellipse([center_x-radius, center_y-radius, center_x+radius, center_y+radius], 
                    fill='white')
        
        try:
            logo_font = ImageFont.truetype("arial.ttf", 40)
        except:
            logo_font = ImageFont.load_default()
        
        draw.text((center_x, center_y), "🎓", fill='#002D72', font=logo_font, anchor='mm')

    def _add_svg_ministry_title(self, draw, width, height):
        """Add ministry title like SVG template"""
        try:
            title_font = ImageFont.truetype("arial.ttf", 24)
            subtitle_font = ImageFont.truetype("arial.ttf", 20)
        except:
            title_font = ImageFont.load_default()
            subtitle_font = ImageFont.load_default()
        
        title = "MINISTRY OF EDUCATION OF FDRE"
        draw.text((width//2, 165), title, fill='white', font=title_font, anchor='mm')
        
        subtitle = "HIGHER EDUCATION DEVELOPMENT SECTOR"
        draw.text((width//2, 195), subtitle, fill='white', font=subtitle_font, anchor='mm')

    def _add_svg_theme(self, draw, width, height):
        """Add theme section like SVG template"""
        draw.rounded_rectangle([150, 210, 450, 240], radius=15, fill=(255, 255, 255, 38))
        
        try:
            theme_font = ImageFont.truetype("arial.ttf", 18)
        except:
            theme_font = ImageFont.load_default()
        
        theme_text = "HIGHER EDUCATION FOR HIGHER IMPACT"
        draw.text((width//2, 230), theme_text, fill='white', font=theme_font, anchor='mm')

    def _add_svg_photo_area(self, draw, width, height):
        """Add photo area like SVG template"""
        draw.rounded_rectangle([150, 260, 450, 540], radius=20, 
                             fill=(255, 255, 255, 20), outline=(255, 255, 255, 140), width=2)
        
        draw.rounded_rectangle([175, 285, 425, 515], radius=15, 
                             fill=(255, 255, 255, 38), outline='white', width=2)
        
        try:
            photo_font = ImageFont.truetype("arial.ttf", 16)
        except:
            photo_font = ImageFont.load_default()
        
        draw.text((300, 420), "Participant Photo", fill=(255, 255, 255, 128), 
                 font=photo_font, anchor='mm')

    def _add_svg_participant_info(self, draw, width, height):
        """Add participant information like SVG template"""
        try:
            name_font = ImageFont.truetype("arial.ttf", 28)
            position_font = ImageFont.truetype("arial.ttf", 22)
            institution_font = ImageFont.truetype("arial.ttf", 20)
        except:
            name_font = ImageFont.load_default()
            position_font = ImageFont.load_default()
            institution_font = ImageFont.load_default()
        
        full_name = f"{self.participant.first_name} {self.participant.last_name}"
        draw.text((width//2, 560), full_name, fill='white', font=name_font, anchor='mm')
        
        position = getattr(self.participant, 'position', 'Position Title')
        draw.text((width//2, 590), position, fill=(255, 255, 255, 221), font=position_font, anchor='mm')
        
        institution = getattr(self.participant, 'institution_name', 'Institution Name')
        draw.text((width//2, 620), institution, fill=(255, 255, 255, 170), font=institution_font, anchor='mm')

    def _add_svg_participant_type(self, draw, width, height):
        """Add participant type badge like SVG template"""
        draw.rounded_rectangle([150, 650, 450, 700], radius=25, 
                             fill='white', outline='#002D72', width=2)
        
        try:
            type_font = ImageFont.truetype("arial.ttf", 20)
        except:
            type_font = ImageFont.load_default()
        
        ptype = self.participant.participant_type.name.upper() if self.participant.participant_type else "PARTICIPANT TYPE"
        draw.text((300, 675), ptype, fill='#002D72', font=type_font, anchor='mm')

    def _add_svg_qr_code(self, draw, width, height, qr_img):
        """Add QR code like SVG template"""
        draw.rounded_rectangle([175, 720, 425, 970], radius=15, 
                             fill=(255, 255, 255, 26), outline=(255, 255, 255, 77), width=1)
        
        draw.rectangle([200, 745, 400, 945], fill='white')
        
        try:
            qr_font = ImageFont.truetype("arial.ttf", 16)
        except:
            qr_font = ImageFont.load_default()
        
        draw.text((300, 980), "Scan QR Code for Verification", 
                 fill=(255, 255, 255, 170), font=qr_font, anchor='mm')

    def _add_svg_sponsors(self, draw, width, height):
        """Add sponsors section like SVG template"""
        draw.line([(100, 1000), (500, 1000)], fill=(255, 255, 255, 77), width=1)
        
        try:
            sponsor_font = ImageFont.truetype("arial.ttf", 18)
            sponsor_label_font = ImageFont.truetype("arial.ttf", 14)
        except:
            sponsor_font = ImageFont.load_default()
            sponsor_label_font = ImageFont.load_default()
        
        draw.text((300, 1030), "SPONSORED BY", fill=(255, 255, 255, 204), 
                 font=sponsor_font, anchor='mm')
        
        sponsor_boxes = [
            (100, 1050, 220, 1130, "Sponsor 1"),
            (240, 1050, 360, 1130, "Sponsor 2"),
            (380, 1050, 500, 1130, "Sponsor 3")
        ]
        
        for x1, y1, x2, y2, label in sponsor_boxes:
            draw.rounded_rectangle([x1, y1, x2, y2], radius=10, 
                                 fill=(255, 255, 255, 26), outline=(255, 255, 255, 77), width=1)
            center_x = (x1 + x2) // 2
            center_y = (y1 + y2) // 2
            draw.text((center_x, center_y), label, fill='white', 
                     font=sponsor_label_font, anchor='mm')

    def _add_svg_footer(self, draw, width, height):
        """Add footer like SVG template"""
        draw.line([(100, 1150), (500, 1150)], fill=(255, 255, 255, 77), width=1)
        
        try:
            footer_font = ImageFont.truetype("arial.ttf", 16)
            social_font = ImageFont.truetype("arial.ttf", 14)
        except:
            footer_font = ImageFont.load_default()
            social_font = ImageFont.load_default()
        
        draw.text((300, 1180), "www.moe.gov.et | <EMAIL>", 
                 fill='white', font=footer_font, anchor='mm')
        
        draw.text((300, 1210), "Follow us: @MoEEthiopia", 
                 fill=(255, 255, 255, 170), font=social_font, anchor='mm')

    def generate_badge(self):
        """Generate badge using exact SVG template design"""
        qr_img = self.generate_qr_code()
        
        width = 600
        height = 1200
        
        badge_img = Image.new('RGB', (width, height), color='#002D72')
        draw = ImageDraw.Draw(badge_img)
        
        self._add_svg_gradient_background(draw, width, height)
        self._add_svg_decorative_elements(draw, width, height)
        self._add_svg_top_ribbon(draw, width, height)
        self._add_svg_ministry_logo(draw, width, height)
        self._add_svg_ministry_title(draw, width, height)
        self._add_svg_theme(draw, width, height)
        self._add_svg_photo_area(draw, width, height)
        self._add_svg_participant_info(draw, width, height)
        self._add_svg_participant_type(draw, width, height)
        self._add_svg_qr_code(draw, width, height, qr_img)
        
        qr_resized = qr_img.resize((200, 200), Image.Resampling.LANCZOS)
        badge_img.paste(qr_resized, (200, 745))
        
        self._add_svg_sponsors(draw, width, height)
        self._add_svg_footer(draw, width, height)
        
        return badge_img

def test_badge_generation():
    """Test the badge generation without database"""
    print("🧪 Testing SVG Badge Generation (No Database)")
    print("=" * 50)
    
    try:
        # Test badge generation
        print("🎨 Creating badge generator...")
        generator = TestBadgeGenerator()
        
        print("🖼️  Generating badge...")
        badge_img = generator.generate_badge()
        
        print("💾 Saving badge...")
        output_path = "production_test_badge.png"
        badge_img.save(output_path)
        
        print(f"✅ Badge generated successfully!")
        print(f"📁 Saved to: {output_path}")
        print(f"📏 Dimensions: {badge_img.size}")
        print(f"👤 Participant: {generator.participant.first_name} {generator.participant.last_name}")
        print(f"🏢 Institution: {generator.participant.institution_name}")
        print(f"🎯 Type: {generator.participant.participant_type.name}")
        
        print("\n✨ Badge generation methods tested:")
        print("   ✅ Gradient background")
        print("   ✅ Decorative elements")
        print("   ✅ Ministry logo")
        print("   ✅ Participant information")
        print("   ✅ QR code generation")
        print("   ✅ Sponsor sections")
        print("   ✅ Footer information")
        
        print(f"\n🎉 All SVG badge generation methods working correctly!")
        
    except Exception as e:
        print(f"❌ Error: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_badge_generation()
