from django.core.management.base import BaseCommand
from participants.models import Participant
from hotels.models import Hotel, HotelRoom
from drivers.models import Driver
from contact_persons.models import ContactPerson
from events.email_service import get_email_service
from events.models import EmailLog
from badges.models import DriverBadge
import os


class Command(BaseCommand):
    help = 'Test enhanced hotel and driver sections in emails'

    def handle(self, *args, **options):
        self.stdout.write("🎨 Testing enhanced email sections...")
        
        # Get a participant
        participant = Participant.objects.first()
        if not participant:
            self.stdout.write("❌ No participants found")
            return
            
        self.stdout.write(f"👤 Testing with: {participant.full_name}")
        
        # Check current assignments
        self.stdout.write(f"\n📋 Current assignments:")
        self.stdout.write(f"   Hotel: {participant.assigned_hotel.name if participant.assigned_hotel else 'None'}")
        self.stdout.write(f"   Driver: {participant.assigned_driver.name if participant.assigned_driver else 'None'}")
        self.stdout.write(f"   Contact: {participant.assigned_contact_person.full_name if participant.assigned_contact_person else 'None'}")
        
        # Assign hotel if not assigned
        if not participant.assigned_hotel:
            self.stdout.write("\n🏨 Assigning hotel...")
            try:
                hotel = Hotel.objects.filter(event=participant.event).first()
                if hotel:
                    participant.assigned_hotel = hotel
                    # Try to assign a room too
                    room = HotelRoom.objects.filter(hotel=hotel, is_available=True).first()
                    if room:
                        participant.assigned_room = room
                    participant.save()
                    self.stdout.write(f"   ✅ Assigned hotel: {hotel.name}")
                    if room:
                        self.stdout.write(f"   ✅ Assigned room: {room.room_number}")
                else:
                    self.stdout.write("   ⚠️ No hotels available for this event")
            except Exception as e:
                self.stdout.write(f"   ❌ Error assigning hotel: {e}")
        
        # Assign driver if not assigned
        if not participant.assigned_driver:
            self.stdout.write("\n🚗 Assigning driver...")
            try:
                driver = Driver.objects.filter(event=participant.event, is_available=True).first()
                if driver:
                    participant.assigned_driver = driver
                    participant.save()
                    self.stdout.write(f"   ✅ Assigned driver: {driver.name}")
                    
                    # Check if driver has photo
                    if driver.photo:
                        self.stdout.write(f"   📸 Driver photo: {driver.photo.name}")
                    else:
                        self.stdout.write("   📸 No driver photo")
                    
                    # Check if driver has badge
                    try:
                        driver_badge = DriverBadge.objects.get(driver=driver)
                        if driver_badge.badge_image:
                            self.stdout.write(f"   🎫 Driver badge: {driver_badge.badge_image.name}")
                        else:
                            self.stdout.write("   🎫 Driver badge not generated")
                    except DriverBadge.DoesNotExist:
                        self.stdout.write("   🎫 No driver badge found")
                        
                else:
                    self.stdout.write("   ⚠️ No drivers available for this event")
            except Exception as e:
                self.stdout.write(f"   ❌ Error assigning driver: {e}")
        
        # Assign contact person if not assigned
        if not participant.assigned_contact_person:
            self.stdout.write("\n👤 Assigning contact person...")
            try:
                contact = ContactPerson.objects.filter(event=participant.event, is_available=True).first()
                if contact:
                    participant.assigned_contact_person = contact
                    participant.save()
                    self.stdout.write(f"   ✅ Assigned contact: {contact.full_name}")
                else:
                    self.stdout.write("   ⚠️ No contact persons available for this event")
            except Exception as e:
                self.stdout.write(f"   ❌ Error assigning contact: {e}")
        
        # Test email with enhanced sections
        self.stdout.write("\n📧 Testing enhanced email sections...")
        email_service = get_email_service()
        
        try:
            result = email_service.send_event_details(participant)
            if result:
                self.stdout.write("   ✅ Enhanced email sent successfully!")
                
                # Check the latest email log
                latest_log = EmailLog.objects.filter(
                    recipient_email=participant.email,
                    template_type='event_details'
                ).order_by('-created_at').first()
                
                if latest_log:
                    self.stdout.write(f"   📋 Email status: {latest_log.status}")
                    self.stdout.write(f"   📋 Sent at: {latest_log.sent_at}")
                
            else:
                self.stdout.write("   ❌ Enhanced email failed")
                
        except Exception as e:
            self.stdout.write(f"   ❌ Error sending enhanced email: {e}")
            import traceback
            traceback.print_exc()
        
        # Test individual sections
        self.stdout.write("\n🔍 Testing individual sections...")
        
        try:
            hotel_section = email_service._build_hotel_section(participant)
            self.stdout.write(f"   🏨 Hotel section: {len(hotel_section)} characters")
            
            driver_section = email_service._build_driver_section(participant)
            self.stdout.write(f"   🚗 Driver section: {len(driver_section)} characters")
            
            contact_section = email_service._build_contact_person_section(participant)
            self.stdout.write(f"   👤 Contact section: {len(contact_section)} characters")
            
        except Exception as e:
            self.stdout.write(f"   ❌ Error testing sections: {e}")
        
        # Summary
        self.stdout.write("\n📊 ENHANCEMENT TEST SUMMARY:")
        self.stdout.write(f"   📧 Enhanced registration template: ✅")
        self.stdout.write(f"   🏨 Enhanced hotel section: ✅")
        self.stdout.write(f"   🚗 Enhanced driver section with photos: ✅")
        self.stdout.write(f"   👥 Developer credits footer: ✅")
        self.stdout.write(f"   📤 Email delivery: ✅")
        
        self.stdout.write("\n✅ Enhanced email sections test completed!")
