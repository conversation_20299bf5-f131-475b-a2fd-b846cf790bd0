import React, { useState, useEffect } from 'react';
import { useLocation } from 'react-router-dom';
import { Container, Row, Col, Card, Button, Alert, Form, Table } from 'react-bootstrap';
import { Html5QrcodeScanner } from 'html5-qrcode';
import { participantService } from '../services/api';

const QRCodeInfo: React.FC = () => {
  const location = useLocation();
  const [scanning, setScanning] = useState(false);
  const [qrData, setQrData] = useState<any>(null);
  const [participantData, setParticipantData] = useState<any>(null);
  const [rawQrText, setRawQrText] = useState('');
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');

  // Handle URL parameters when component loads (for mobile camera QR codes)
  useEffect(() => {
    const urlParams = new URLSearchParams(location.search);
    const email = urlParams.get('email');
    const name = urlParams.get('name');

    if (email) {
      // Decode URL-encoded email
      const decodedEmail = decodeURIComponent(email);
      const simulatedQRData = `${location.pathname}${location.search}`;

      setRawQrText(simulatedQRData);
      setSuccess(`Automatically processing QR code from mobile camera...`);

      // Process the QR data automatically
      processQRData(simulatedQRData);
    }
  }, [location]);

  const startScanning = () => {
    setScanning(true);
    setError('');
    setSuccess('');

    const scanner = new Html5QrcodeScanner(
      'qr-info-reader',
      {
        fps: 10,
        qrbox: { width: 250, height: 250 },
        aspectRatio: 1.0,
        disableFlip: false,
        // Enhanced camera configuration for mobile back camera
        videoConstraints: {
          facingMode: { exact: "environment" }, // Force back camera
          width: { ideal: 1280 },
          height: { ideal: 720 }
        },
        // Additional configuration for better mobile support
        experimentalFeatures: {
          useBarCodeDetectorIfSupported: true
        },
        rememberLastUsedCamera: true,
        showTorchButtonIfSupported: true
      },
      false
    );

    scanner.render(
      (decodedText, decodedResult) => {
        handleScanSuccess(decodedText);
        scanner.clear();
        setScanning(false);
      },
      (error) => {
        console.warn('QR scan error:', error);
      }
    );
  };

  const stopScanning = () => {
    setScanning(false);
    const element = document.getElementById('qr-info-reader');
    if (element) {
      element.innerHTML = '';
    }
  };

  const processQRData = async (decodedText: string) => {
    setRawQrText(decodedText);
    setError('');
    
    try {
      // Try to parse as JSON
      let parsedData = null;
      try {
        parsedData = JSON.parse(decodedText);
        setQrData(parsedData);
        setSuccess('QR Code contains structured JSON data');
      } catch (jsonError) {
        setQrData({ raw_text: decodedText, format: 'text/url' });
        setSuccess('QR Code contains text/URL data');
      }

      // Simplified verification - extract email only
      let participantEmail = null;

      // First, try to extract email from any format
      const emailPattern = /([a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,})/;
      const emailMatch = decodedText.match(emailPattern);

      if (emailMatch) {
        // Found email in the text
        participantEmail = emailMatch[1];
      } else if (parsedData && parsedData.email) {
        // Found email in JSON data
        participantEmail = parsedData.email;
      } else if (decodedText.includes('?')) {
        // Try to extract email from URL parameters
        try {
          const url = new URL(decodedText);
          participantEmail = url.searchParams.get('email');

          // Decode URL-encoded email if needed
          if (participantEmail && participantEmail.includes('%40')) {
            participantEmail = decodeURIComponent(participantEmail);
          }
        } catch (urlError) {
          // If URL parsing fails, try manual parameter extraction
          const urlParams = new URLSearchParams(decodedText.split('?')[1]);
          participantEmail = urlParams.get('email');

          // Decode URL-encoded email if needed
          if (participantEmail && participantEmail.includes('%40')) {
            participantEmail = decodeURIComponent(participantEmail);
          }
        }
      } else if (decodedText.includes('@')) {
        // Assume the whole text is an email
        participantEmail = decodedText.trim();
      }

      // Try to verify participant using the raw QR data
      try {
        const response = await participantService.verifyParticipant(decodedText);
        setParticipantData(response.data);
      } catch (participantError) {
        console.warn('Could not fetch participant data:', participantError);
        setParticipantData(null);
      }

    } catch (error: any) {
      setError(`Error processing QR code: ${error.message}`);
    }
  };

  const handleScanSuccess = async (decodedText: string) => {
    await processQRData(decodedText);
  };

  const clearData = () => {
    setQrData(null);
    setParticipantData(null);
    setRawQrText('');
    setError('');
    setSuccess('');
  };

  return (
    <Container className="py-4">
      <Row className="justify-content-center">
        <Col lg={10}>
          <Card className="shadow">
            <Card.Header className="bg-info text-white">
              <h3 className="mb-0">
                <i className="fas fa-info-circle me-2"></i>
                QR Code Information Viewer
              </h3>
            </Card.Header>
            <Card.Body className="p-4">
              {success && (
                <Alert variant="success" className="mb-4">
                  <i className="fas fa-check-circle me-2"></i>
                  {success}
                </Alert>
              )}

              {error && (
                <Alert variant="danger" className="mb-4">
                  <i className="fas fa-exclamation-triangle me-2"></i>
                  {error}
                </Alert>
              )}

              {/* Scanner Controls */}
              <div className="text-center mb-4">
                {!scanning ? (
                  <>
                    <Button
                      variant="info"
                      size="lg"
                      onClick={startScanning}
                      className="px-4 py-3 me-3"
                    >
                      <i className="fas fa-camera me-2"></i>
                      Scan QR Code
                    </Button>
                    {(qrData || participantData) && (
                      <Button
                        variant="secondary"
                        size="lg"
                        onClick={clearData}
                        className="px-4 py-3"
                      >
                        <i className="fas fa-trash me-2"></i>
                        Clear Data
                      </Button>
                    )}
                  </>
                ) : (
                  <Button
                    variant="danger"
                    size="lg"
                    onClick={stopScanning}
                    className="px-4 py-3"
                  >
                    <i className="fas fa-stop me-2"></i>
                    Stop Scanner
                  </Button>
                )}
              </div>

              {/* QR Scanner */}
              <div className="qr-scanner-container">
                <div id="qr-info-reader" className="mb-4"></div>
              </div>

              {/* Raw QR Data */}
              {rawQrText && (
                <Card className="mb-4">
                  <Card.Header className="bg-secondary text-white">
                    <h5 className="mb-0">
                      <i className="fas fa-code me-2"></i>
                      Raw QR Code Data & Email Extraction Debug
                    </h5>
                  </Card.Header>
                  <Card.Body>
                    <div className="mb-3">
                      <strong>Raw QR Data:</strong>
                      <pre className="bg-light p-3 rounded mt-2" style={{ fontSize: '12px', overflow: 'auto' }}>
                        {rawQrText}
                      </pre>
                    </div>

                    <div className="mb-3">
                      <strong>🔍 Email Extraction Debug:</strong>
                      <div className="bg-info bg-opacity-10 p-3 rounded mt-2">
                        {(() => {
                          // Debug email extraction
                          const emailPattern = /([a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,})/;
                          const emailMatch = rawQrText.match(emailPattern);

                          if (emailMatch) {
                            return (
                              <div>
                                <span className="badge bg-success me-2">✅ Email Found</span>
                                <code>{emailMatch[1]}</code>
                              </div>
                            );
                          } else {
                            return (
                              <div>
                                <span className="badge bg-danger me-2">❌ No Email Found</span>
                                <small className="text-muted">
                                  QR code must contain a valid email address for verification
                                </small>
                              </div>
                            );
                          }
                        })()}
                      </div>
                    </div>

                    <div>
                      <strong>📋 QR Data Analysis:</strong>
                      <ul className="mt-2 mb-0">
                        <li>Contains '@' symbol: {rawQrText.includes('@') ? '✅ Yes' : '❌ No'}</li>
                        <li>Contains 'email' parameter: {rawQrText.toLowerCase().includes('email') ? '✅ Yes' : '❌ No'}</li>
                        <li>Looks like JSON: {rawQrText.trim().startsWith('{') ? '✅ Yes' : '❌ No'}</li>
                        <li>Looks like URL: {rawQrText.includes('http') ? '✅ Yes' : '❌ No'}</li>
                        <li>Data length: {rawQrText.length} characters</li>
                      </ul>
                    </div>
                  </Card.Body>
                </Card>
              )}

              {/* Parsed QR Data */}
              {qrData && (
                <Card className="mb-4">
                  <Card.Header className="bg-primary text-white">
                    <h5 className="mb-0">
                      <i className="fas fa-database me-2"></i>
                      Parsed QR Code Information
                    </h5>
                  </Card.Header>
                  <Card.Body>
                    <Table striped bordered hover responsive>
                      <tbody>
                        {Object.entries(qrData).map(([key, value]) => (
                          <tr key={key}>
                            <td><strong>{key.replace(/_/g, ' ').toUpperCase()}</strong></td>
                            <td>{typeof value === 'object' ? JSON.stringify(value, null, 2) : String(value)}</td>
                          </tr>
                        ))}
                      </tbody>
                    </Table>
                  </Card.Body>
                </Card>
              )}

              {/* Participant Information */}
              {participantData && (
                <Card className="mb-4">
                  <Card.Header className="bg-success text-white">
                    <h5 className="mb-0">
                      <i className="fas fa-user me-2"></i>
                      Participant Information
                    </h5>
                  </Card.Header>
                  <Card.Body>
                    <Row>
                      <Col md={6}>
                        <Table striped bordered hover responsive>
                          <tbody>
                            <tr>
                              <td><strong>ID</strong></td>
                              <td>{participantData.id}</td>
                            </tr>
                            <tr>
                              <td><strong>UUID</strong></td>
                              <td style={{ fontSize: '12px' }}>{participantData.uuid}</td>
                            </tr>
                            <tr>
                              <td><strong>Full Name</strong></td>
                              <td>{participantData.full_name}</td>
                            </tr>
                            <tr>
                              <td><strong>Email</strong></td>
                              <td>{participantData.email}</td>
                            </tr>
                            <tr>
                              <td><strong>Phone</strong></td>
                              <td>{participantData.phone || 'N/A'}</td>
                            </tr>
                            <tr>
                              <td><strong>Institution</strong></td>
                              <td>{participantData.institution_name}</td>
                            </tr>
                          </tbody>
                        </Table>
                      </Col>
                      <Col md={6}>
                        <Table striped bordered hover responsive>
                          <tbody>
                            <tr>
                              <td><strong>Position</strong></td>
                              <td>{participantData.position || 'N/A'}</td>
                            </tr>
                            <tr>
                              <td><strong>Type</strong></td>
                              <td>
                                <span
                                  className="badge"
                                  style={{ backgroundColor: participantData.participant_type_color }}
                                >
                                  {participantData.participant_type_name}
                                </span>
                              </td>
                            </tr>
                            <tr>
                              <td><strong>Status</strong></td>
                              <td>
                                <span className={`badge ${participantData.status === 'approved' ? 'bg-success' : 'bg-warning'}`}>
                                  {participantData.status?.toUpperCase()}
                                </span>
                              </td>
                            </tr>
                            <tr>
                              <td><strong>Can Check-in</strong></td>
                              <td>
                                <span className={`badge ${participantData.can_check_in ? 'bg-success' : 'bg-danger'}`}>
                                  {participantData.can_check_in ? 'YES' : 'NO'}
                                </span>
                                {participantData.verification_message && (
                                  <div className="mt-1">
                                    <small className="text-muted">{participantData.verification_message}</small>
                                  </div>
                                )}
                              </td>
                            </tr>
                            {participantData.verification_status && (
                              <tr>
                                <td><strong>Verification Status</strong></td>
                                <td>
                                  <span className={`badge ${
                                    participantData.verification_status === 'approved' ? 'bg-success' :
                                    participantData.verification_status === 'rejected' ? 'bg-danger' :
                                    participantData.verification_status === 'pending' ? 'bg-warning' :
                                    'bg-secondary'
                                  }`}>
                                    {participantData.verification_status.toUpperCase()}
                                  </span>
                                </td>
                              </tr>
                            )}
                            {participantData.participant_status && (
                              <tr>
                                <td><strong>Registration Status</strong></td>
                                <td>
                                  <span className={`badge ${
                                    participantData.participant_status === 'approved' ? 'bg-success' :
                                    participantData.participant_status === 'rejected' ? 'bg-danger' :
                                    participantData.participant_status === 'pending' ? 'bg-warning' :
                                    'bg-info'
                                  }`}>
                                    {participantData.participant_status.toUpperCase()}
                                  </span>
                                </td>
                              </tr>
                            )}
                            <tr>
                              <td><strong>Arrival Date</strong></td>
                              <td>{participantData.arrival_date ? new Date(participantData.arrival_date).toLocaleString() : 'N/A'}</td>
                            </tr>
                            <tr>
                              <td><strong>Departure Date</strong></td>
                              <td>{participantData.departure_date ? new Date(participantData.departure_date).toLocaleString() : 'N/A'}</td>
                            </tr>
                          </tbody>
                        </Table>
                      </Col>
                    </Row>
                  </Card.Body>
                </Card>
              )}

              {/* Instructions */}
              <Alert variant="info">
                <h6><i className="fas fa-info-circle me-2"></i>How to Use:</h6>
                <ul className="mb-0">
                  <li>Click "Scan QR Code" to start the camera scanner</li>
                  <li>Point your device's back camera at any QR code</li>
                  <li>The system will display the raw data and parsed information</li>
                  <li>If it's a participant QR code, full participant details will be shown</li>
                  <li>Use this tool to debug QR code issues and verify data format</li>
                </ul>
              </Alert>
            </Card.Body>
          </Card>
        </Col>
      </Row>
    </Container>
  );
};

export default QRCodeInfo;
