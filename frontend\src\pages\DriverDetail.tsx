import React, { useState, useEffect } from 'react';
import { Con<PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>ton, Badge, <PERSON><PERSON>, Spin<PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, Tabs } from 'react-bootstrap';
import { useParams, useNavigate, Link } from 'react-router-dom';
import { driverService, Driver, getMediaUrl } from '../services/api';

const DriverDetail: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  
  const [driver, setDriver] = useState<Driver | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [deleting, setDeleting] = useState(false);

  useEffect(() => {
    if (id) {
      fetchDriver(parseInt(id));
    }
  }, [id]);

  const fetchDriver = async (driverId: number) => {
    try {
      setLoading(true);
      const response = await driverService.getDriver(driverId);
      setDriver(response.data);
      setError(null);
    } catch (err) {
      console.error('Error fetching driver:', err);
      setError('Failed to load driver details. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const handleToggleAvailability = async () => {
    if (!driver) return;
    
    try {
      await driverService.toggleAvailability(driver.id);
      setDriver(prev => prev ? { ...prev, is_available: !prev.is_available } : null);
    } catch (err) {
      console.error('Error toggling availability:', err);
      setError('Failed to update driver availability. Please try again.');
    }
  };

  const handleDelete = async () => {
    if (!driver) return;
    
    try {
      setDeleting(true);
      await driverService.deleteDriver(driver.id);
      navigate('/drivers');
    } catch (err) {
      console.error('Error deleting driver:', err);
      setError('Failed to delete driver. Please try again.');
      setDeleting(false);
    }
  };

  if (loading) {
    return (
      <Container className="text-center mt-5">
        <Spinner animation="border" role="status">
          <span className="visually-hidden">Loading...</span>
        </Spinner>
        <p className="mt-3">Loading driver details...</p>
      </Container>
    );
  }

  if (error && !driver) {
    return (
      <Container className="py-4">
        <Alert variant="danger">
          <i className="fas fa-exclamation-triangle me-2"></i>
          {error}
        </Alert>
        <Button variant="primary" onClick={() => navigate('/drivers')}>
          <i className="fas fa-arrow-left me-2"></i>
          Back to Drivers
        </Button>
      </Container>
    );
  }

  if (!driver) {
    return (
      <Container className="py-4">
        <Alert variant="warning">
          <i className="fas fa-exclamation-triangle me-2"></i>
          Driver not found.
        </Alert>
        <Button variant="primary" onClick={() => navigate('/drivers')}>
          <i className="fas fa-arrow-left me-2"></i>
          Back to Drivers
        </Button>
      </Container>
    );
  }

  return (
    <Container className="py-4">
      {error && (
        <Alert variant="danger" className="mb-4">
          <i className="fas fa-exclamation-triangle me-2"></i>
          {error}
        </Alert>
      )}

      {/* Header */}
      <Row className="mb-4">
        <Col>
          <div className="d-flex justify-content-between align-items-center">
            <div>
              <Button 
                variant="outline-secondary" 
                onClick={() => navigate('/drivers')}
                className="mb-2"
              >
                <i className="fas fa-arrow-left me-2"></i>
                Back to Drivers
              </Button>
              <h1 className="display-6 fw-bold text-primary mb-0">
                <i className="fas fa-car me-3"></i>
                {driver.name}
              </h1>
              <p className="lead text-muted">Driver Details & Information</p>
            </div>
            <div>
              <Badge 
                bg={driver.is_available ? 'success' : 'warning'} 
                className="fs-6 me-2"
              >
                {driver.is_available ? 'Available' : 'Busy'}
              </Badge>
            </div>
          </div>
        </Col>
      </Row>

      <Tabs defaultActiveKey="details" className="mb-4">
        <Tab eventKey="details" title={<><i className="fas fa-info-circle me-2"></i>Details</>}>
          <Row>
            <Col lg={4}>
              {/* Driver Photo and Basic Info */}
              <Card className="mb-4">
                <Card.Body className="text-center">
                  {driver.photo ? (
                    <img 
                      src={getMediaUrl(driver.photo)} 
                      alt={driver.name}
                      className="rounded-circle mb-3"
                      style={{ width: '150px', height: '150px', objectFit: 'cover' }}
                    />
                  ) : (
                    <div 
                      className="rounded-circle bg-secondary d-flex align-items-center justify-content-center mx-auto mb-3"
                      style={{ width: '150px', height: '150px' }}
                    >
                      <i className="fas fa-user fa-4x text-white"></i>
                    </div>
                  )}
                  <h4 className="mb-2">{driver.name}</h4>
                  <Badge bg={driver.is_available ? 'success' : 'warning'} className="mb-3">
                    {driver.is_available ? 'Available' : 'Busy'}
                  </Badge>
                  
                  <div className="d-grid gap-2">
                    <Button 
                      variant={driver.is_available ? 'warning' : 'success'}
                      onClick={handleToggleAvailability}
                    >
                      <i className={`fas ${driver.is_available ? 'fa-pause' : 'fa-play'} me-2`}></i>
                      {driver.is_available ? 'Set as Busy' : 'Set as Available'}
                    </Button>
                    
                    <Link 
                      to={`/drivers/${driver.id}/edit`} 
                      className="btn btn-primary"
                    >
                      <i className="fas fa-edit me-2"></i>
                      Edit Driver
                    </Link>
                    
                    <Button 
                      variant="danger" 
                      onClick={() => setShowDeleteModal(true)}
                    >
                      <i className="fas fa-trash me-2"></i>
                      Delete Driver
                    </Button>
                  </div>
                </Card.Body>
              </Card>
            </Col>

            <Col lg={8}>
              {/* Contact Information */}
              <Card className="mb-4">
                <Card.Header>
                  <h5 className="mb-0">
                    <i className="fas fa-address-card me-2"></i>
                    Contact Information
                  </h5>
                </Card.Header>
                <Card.Body>
                  <Row>
                    <Col md={6}>
                      <div className="mb-3">
                        <label className="form-label fw-bold">Phone Number</label>
                        <p className="mb-0">
                          <i className="fas fa-phone me-2 text-primary"></i>
                          <a href={`tel:${driver.phone}`} className="text-decoration-none">
                            {driver.phone}
                          </a>
                        </p>
                      </div>
                    </Col>
                    <Col md={6}>
                      <div className="mb-3">
                        <label className="form-label fw-bold">Email Address</label>
                        <p className="mb-0">
                          <i className="fas fa-envelope me-2 text-primary"></i>
                          <a href={`mailto:${driver.email}`} className="text-decoration-none">
                            {driver.email}
                          </a>
                        </p>
                      </div>
                    </Col>
                    {driver.license_number && (
                      <Col md={6}>
                        <div className="mb-3">
                          <label className="form-label fw-bold">License Number</label>
                          <p className="mb-0">
                            <i className="fas fa-id-card me-2 text-primary"></i>
                            {driver.license_number}
                          </p>
                        </div>
                      </Col>
                    )}
                  </Row>
                </Card.Body>
              </Card>

              {/* Vehicle Information */}
              <Card className="mb-4">
                <Card.Header>
                  <h5 className="mb-0">
                    <i className="fas fa-car me-2"></i>
                    Vehicle Information
                  </h5>
                </Card.Header>
                <Card.Body>
                  <Row>
                    <Col md={6}>
                      <div className="mb-3">
                        <label className="form-label fw-bold">License Plate</label>
                        <p className="mb-0 fs-5 fw-bold text-primary">{driver.car_plate}</p>
                      </div>
                    </Col>
                    <Col md={6}>
                      <div className="mb-3">
                        <label className="form-label fw-bold">Car Code</label>
                        <p className="mb-0 fs-5 fw-bold text-secondary">{driver.car_code}</p>
                      </div>
                    </Col>
                    {driver.car_model && (
                      <Col md={6}>
                        <div className="mb-3">
                          <label className="form-label fw-bold">Model</label>
                          <p className="mb-0">{driver.car_model}</p>
                        </div>
                      </Col>
                    )}
                    {driver.car_color && (
                      <Col md={6}>
                        <div className="mb-3">
                          <label className="form-label fw-bold">Color</label>
                          <p className="mb-0">{driver.car_color}</p>
                        </div>
                      </Col>
                    )}
                  </Row>
                </Card.Body>
              </Card>

              {/* Event Information */}
              <Card className="mb-4">
                <Card.Header>
                  <h5 className="mb-0">
                    <i className="fas fa-calendar me-2"></i>
                    Event Information
                  </h5>
                </Card.Header>
                <Card.Body>
                  <div className="mb-3">
                    <label className="form-label fw-bold">Assigned Event</label>
                    <p className="mb-0">
                      <Link to={`/events/${driver.event}`} className="text-decoration-none">
                        <i className="fas fa-external-link-alt me-2"></i>
                        View Event Details
                      </Link>
                    </p>
                  </div>
                </Card.Body>
              </Card>

              {/* Notes */}
              {driver.notes && (
                <Card className="mb-4">
                  <Card.Header>
                    <h5 className="mb-0">
                      <i className="fas fa-sticky-note me-2"></i>
                      Additional Notes
                    </h5>
                  </Card.Header>
                  <Card.Body>
                    <p className="mb-0" style={{ whiteSpace: 'pre-wrap' }}>
                      {driver.notes}
                    </p>
                  </Card.Body>
                </Card>
              )}

              {/* System Information */}
              <Card>
                <Card.Header>
                  <h5 className="mb-0">
                    <i className="fas fa-info me-2"></i>
                    System Information
                  </h5>
                </Card.Header>
                <Card.Body>
                  <Row>
                    <Col md={6}>
                      <div className="mb-3">
                        <label className="form-label fw-bold">Created</label>
                        <p className="mb-0">
                          <i className="fas fa-calendar-plus me-2 text-success"></i>
                          {new Date(driver.created_at).toLocaleString()}
                        </p>
                      </div>
                    </Col>
                    <Col md={6}>
                      <div className="mb-3">
                        <label className="form-label fw-bold">Last Updated</label>
                        <p className="mb-0">
                          <i className="fas fa-calendar-edit me-2 text-info"></i>
                          {new Date(driver.updated_at).toLocaleString()}
                        </p>
                      </div>
                    </Col>
                  </Row>
                </Card.Body>
              </Card>
            </Col>
          </Row>
        </Tab>

        <Tab eventKey="assignments" title={<><i className="fas fa-route me-2"></i>Assignments</>}>
          <Card>
            <Card.Body className="text-center py-5">
              <i className="fas fa-route fa-4x text-muted mb-3"></i>
              <h4 className="text-muted">Assignment Management</h4>
              <p className="text-muted">
                Driver assignment functionality will be implemented here.
              </p>
              <Button variant="primary" disabled>
                <i className="fas fa-plus me-2"></i>
                Manage Assignments
              </Button>
            </Card.Body>
          </Card>
        </Tab>
      </Tabs>

      {/* Delete Confirmation Modal */}
      <Modal show={showDeleteModal} onHide={() => setShowDeleteModal(false)}>
        <Modal.Header closeButton>
          <Modal.Title>
            <i className="fas fa-exclamation-triangle text-danger me-2"></i>
            Confirm Delete
          </Modal.Title>
        </Modal.Header>
        <Modal.Body>
          <p>
            Are you sure you want to delete <strong>{driver.name}</strong>?
          </p>
          <div className="alert alert-warning">
            <i className="fas fa-exclamation-triangle me-2"></i>
            This action cannot be undone. All assignments and related data will be lost.
          </div>
        </Modal.Body>
        <Modal.Footer>
          <Button variant="secondary" onClick={() => setShowDeleteModal(false)}>
            Cancel
          </Button>
          <Button 
            variant="danger" 
            onClick={handleDelete}
            disabled={deleting}
          >
            {deleting ? (
              <>
                <Spinner animation="border" size="sm" className="me-2" />
                Deleting...
              </>
            ) : (
              <>
                <i className="fas fa-trash me-2"></i>
                Delete Driver
              </>
            )}
          </Button>
        </Modal.Footer>
      </Modal>
    </Container>
  );
};

export default DriverDetail;
