import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { Container, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, Spin<PERSON>, <PERSON><PERSON> } from 'react-bootstrap';
import { eventService, Event } from '../services/api';
import FeedbackForm from '../components/FeedbackForm';
import CommonLayout from '../components/CommonLayout';
import { useToast } from '../contexts/ToastContext';

const EventFeedback: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const { showToast } = useToast();
  
  const [event, setEvent] = useState<Event | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [feedbackSubmitted, setFeedbackSubmitted] = useState(false);

  useEffect(() => {
    if (id) {
      fetchEvent();
    }
  }, [id]);

  const fetchEvent = async () => {
    try {
      setLoading(true);
      const response = await eventService.getPublicEvent(parseInt(id!));
      setEvent(response.data);
    } catch (error: any) {
      console.error('Error fetching event:', error);
      setError('Event not found or not accessible.');
    } finally {
      setLoading(false);
    }
  };

  const handleFeedbackSuccess = () => {
    setFeedbackSubmitted(true);
    showToast({
      type: 'success',
      title: 'Thank You!',
      message: 'Your feedback has been submitted successfully.'
    });
  };

  const handleBackToEvent = () => {
    navigate(`/public/events/${id}`);
  };

  if (loading) {
    return (
      <CommonLayout>
        <Container className="py-5">
          <Row className="justify-content-center">
            <Col lg={8} className="text-center">
              <Spinner animation="border" variant="primary" />
              <p className="mt-3 text-muted">Loading event information...</p>
            </Col>
          </Row>
        </Container>
      </CommonLayout>
    );
  }

  if (error || !event) {
    return (
      <CommonLayout>
        <Container className="py-5">
          <Row className="justify-content-center">
            <Col lg={8}>
              <Alert variant="danger" className="text-center">
                <i className="fas fa-exclamation-triangle fa-2x mb-3"></i>
                <h4>Event Not Found</h4>
                <p>{error || 'The requested event could not be found.'}</p>
                <Button variant="primary" onClick={() => navigate('/')}>
                  <i className="fas fa-home me-2"></i>
                  Return to Home
                </Button>
              </Alert>
            </Col>
          </Row>
        </Container>
      </CommonLayout>
    );
  }

  if (feedbackSubmitted) {
    return (
      <CommonLayout>
        <Container className="py-5">
          <Row className="justify-content-center">
            <Col lg={8}>
              <Card className="shadow-sm text-center">
                <Card.Body className="p-5">
                  <div className="mb-4">
                    <i className="fas fa-check-circle text-success" style={{ fontSize: '4rem' }}></i>
                  </div>
                  <h2 className="text-success mb-3">Feedback Submitted Successfully!</h2>
                  <p className="text-muted mb-4">
                    Thank you for taking the time to provide feedback for <strong>{event.name}</strong>. 
                    Your input is valuable and will help us improve future events.
                  </p>
                  <div className="d-flex justify-content-center gap-3">
                    <Button variant="primary" onClick={handleBackToEvent}>
                      <i className="fas fa-arrow-left me-2"></i>
                      Back to Event
                    </Button>
                    <Button variant="outline-primary" onClick={() => navigate('/')}>
                      <i className="fas fa-home me-2"></i>
                      Home
                    </Button>
                  </div>
                </Card.Body>
              </Card>
            </Col>
          </Row>
        </Container>
      </CommonLayout>
    );
  }

  return (
    <CommonLayout
      title="Event Feedback"
      subtitle={`Share your experience with ${event.name}`}
      showHero={true}
      heroContent={
        <div className="text-center">
          <div className="mb-4">
            <i className="fas fa-comment-alt text-white" style={{ fontSize: '3rem' }}></i>
          </div>
          <h1 className="display-4 fw-bold text-white mb-3">Event Feedback</h1>
          <p className="lead text-white-50 mb-4">
            Help us improve future events by sharing your valuable feedback
          </p>
          <div className="bg-white bg-opacity-10 rounded p-3 d-inline-block">
            <h5 className="text-white mb-1">{event.name}</h5>
            <p className="text-white-50 mb-0">
              <i className="fas fa-calendar me-2"></i>
              {new Date(event.start_date).toLocaleDateString()} - {new Date(event.end_date).toLocaleDateString()}
            </p>
            <p className="text-white-50 mb-0">
              <i className="fas fa-map-marker-alt me-2"></i>
              {event.location}, {event.city}
            </p>
          </div>
        </div>
      }
    >
      <div className="py-5">
        <FeedbackForm
          event={event}
          onSubmitSuccess={handleFeedbackSuccess}
          onCancel={handleBackToEvent}
        />
      </div>
    </CommonLayout>
  );
};

export default EventFeedback;
