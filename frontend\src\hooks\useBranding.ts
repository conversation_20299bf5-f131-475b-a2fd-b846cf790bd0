import { useState, useEffect } from 'react';
import BrandingService, { BrandingData } from '../services/branding';

export const useBranding = () => {
  const [brandingData, setBrandingData] = useState<BrandingData>(() => 
    BrandingService.getInstance().getBrandingData()
  );

  useEffect(() => {
    const brandingService = BrandingService.getInstance();
    
    // Subscribe to branding updates
    const unsubscribe = brandingService.subscribe(setBrandingData);
    
    // Load organization data if not already loaded
    brandingService.loadOrganization();
    
    return unsubscribe;
  }, []);

  return brandingData;
};
