from django.db import models
from django.conf import settings
from django.utils import timezone
import uuid


class ParticipantType(models.Model):
    name = models.CharField(max_length=100, unique=True)
    description = models.TextField(blank=True)
    color = models.CharField(max_length=7, default='#007bff', help_text='Hex color code for badge')
    created_at = models.DateTimeField(auto_now_add=True)

    def __str__(self):
        return self.name


class VisitingInterest(models.Model):
    """Places or activities that participants can visit during events"""
    name = models.CharField(max_length=200)
    description = models.TextField()
    location = models.CharField(max_length=300, blank=True)
    event = models.ForeignKey('events.Event', on_delete=models.CASCADE, related_name='visiting_interests')
    is_active = models.BooleanField(default=True)
    max_participants = models.PositiveIntegerField(null=True, blank=True, help_text='Maximum number of participants allowed')
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ['name']
        unique_together = ['name', 'event']

    def __str__(self):
        return f"{self.name} - {self.event.name}"

    @property
    def current_participants_count(self):
        """Count of participants who selected this interest"""
        return self.participant_interests.count()

    @property
    def is_full(self):
        """Check if the interest has reached maximum capacity"""
        if self.max_participants:
            return self.current_participants_count >= self.max_participants
        return False


class ParticipantVisitingInterest(models.Model):
    """Many-to-many relationship between participants and visiting interests"""
    participant = models.ForeignKey('Participant', on_delete=models.CASCADE, related_name='visiting_interests')
    visiting_interest = models.ForeignKey(VisitingInterest, on_delete=models.CASCADE, related_name='participant_interests')
    priority = models.PositiveIntegerField(default=1, help_text='1 = First choice, 2 = Second choice, etc.')
    selected_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        unique_together = ['participant', 'visiting_interest']
        ordering = ['priority', 'selected_at']

    def __str__(self):
        return f"{self.participant.full_name} - {self.visiting_interest.name} (Priority: {self.priority})"


class Participant(models.Model):
    # Unique identifier for QR code
    uuid = models.UUIDField(default=uuid.uuid4, editable=False, unique=True)

    # Personal Information
    first_name = models.CharField(max_length=100)
    last_name = models.CharField(max_length=100)
    middle_name = models.CharField(max_length=100, blank=True)
    email = models.EmailField(unique=True)
    phone = models.CharField(max_length=20)

    # Professional Information
    institution_name = models.CharField(max_length=200)
    position = models.CharField(max_length=200)

    # Event Information
    event = models.ForeignKey('events.Event', on_delete=models.CASCADE, related_name='participants')
    participant_type = models.ForeignKey(ParticipantType, on_delete=models.CASCADE)

    # Travel Information
    arrival_date = models.DateTimeField()
    departure_date = models.DateTimeField()

    # Profile Information
    profile_photo = models.ImageField(upload_to='participant_photos/', blank=True, null=True)

    # Additional Information
    remarks = models.TextField(blank=True)

    # Registration Information
    registration_date = models.DateTimeField(auto_now_add=True)
    is_confirmed = models.BooleanField(default=False)

    # Admin Assignment Information
    STATUS_CHOICES = [
        ('pending', 'Pending Review'),
        ('approved', 'Approved'),
        ('rejected', 'Rejected'),
        ('assigned', 'Hotel & Driver Assigned'),
        ('email_sent', 'Details Email Sent'),
    ]
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='pending')

    # Admin assignments
    assigned_hotel = models.ForeignKey('hotels.Hotel', on_delete=models.SET_NULL, null=True, blank=True, related_name='assigned_participants')
    assigned_room = models.ForeignKey('hotels.HotelRoom', on_delete=models.SET_NULL, null=True, blank=True, related_name='assigned_participants')
    assigned_driver = models.ForeignKey('drivers.Driver', on_delete=models.SET_NULL, null=True, blank=True, related_name='assigned_participants')
    assigned_contact_person = models.ForeignKey('contact_persons.ContactPerson', on_delete=models.SET_NULL, null=True, blank=True, related_name='assigned_participants')

    # Admin notes and approval
    admin_notes = models.TextField(blank=True, help_text='Internal admin notes for evaluation')
    approved_by = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.SET_NULL, null=True, blank=True, related_name='approved_participants')
    approved_at = models.DateTimeField(null=True, blank=True)
    rejection_reason = models.TextField(blank=True)

    # Email tracking
    details_email_sent = models.BooleanField(default=False)
    details_email_sent_at = models.DateTimeField(null=True, blank=True)

    # Badge file (for admin display)
    badge_file = models.ImageField(upload_to='participant_badges/', blank=True, null=True)

    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ['last_name', 'first_name']

    def __str__(self):
        return f"{self.first_name} {self.last_name}"

    @property
    def full_name(self):
        if self.middle_name:
            return f"{self.first_name} {self.middle_name} {self.last_name}"
        return f"{self.first_name} {self.last_name}"

    @property
    def badge_generated(self):
        """Check if participant has a generated badge"""
        try:
            return hasattr(self, 'badge') and self.badge.is_generated
        except:
            return False


class Attendance(models.Model):
    participant = models.ForeignKey(Participant, on_delete=models.CASCADE, related_name='attendances')
    event_schedule = models.ForeignKey('events.EventSchedule', on_delete=models.CASCADE, related_name='attendances')
    checked_in_at = models.DateTimeField(auto_now_add=True)
    checked_in_by = models.CharField(max_length=100, blank=True)  # Staff member who scanned
    notes = models.TextField(blank=True)

    class Meta:
        unique_together = ['participant', 'event_schedule']
        ordering = ['-checked_in_at']

    def __str__(self):
        return f"{self.participant.full_name} - {self.event_schedule.title}"
