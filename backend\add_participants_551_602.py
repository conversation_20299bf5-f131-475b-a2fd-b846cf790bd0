#!/usr/bin/env python3

import os
import sys
import django
from datetime import datetime

# Add the project root to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'event_management.settings')
django.setup()

from participants.models import Participant, ParticipantType
from events.models import Event
from django.db import connection

def add_participants_551_602():
    """Add participants with IDs 551-602"""
    
    print("=== ADDING PARTICIPANTS 551-602 ===")
    
    # Get Event ID 1
    try:
        event = Event.objects.get(id=1)
    except Event.DoesNotExist:
        print("Error: Event with ID 1 not found")
        return
    
    # Get participant types
    participant_types = {}
    for pt in ParticipantType.objects.all():
        participant_types[pt.id] = pt
    
    # Latest participants data (IDs 551-602)
    latest_participants = [
        # (id, first_name, last_name, middle_name, email, phone, institution_name, participant_type_id, arrival_date, departure_date, profile_photo, remarks, status)
        (551, "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<EMAIL>", "091 9818743", "Diplomat podcast", 24, "2025-08-03 14:00:00", "2025-08-07 14:00:00", "event-registration/profiles/profile_9e9be7d3-883f-", "", "active"),
        (552, "Habetamu", "G. Mikaele", "Miteku", "<EMAIL>", "093 1548289", "Debark Universkty", 3, "2025-08-03 14:08:00", "2025-08-06 14:09:00", None, "", "active"),
        (553, "Abulie", "Takele", "Melku", "<EMAIL>", "094 8862338", "Ministry of Education", 28, "2025-08-04 00:31:00", "2025-08-07 14:16:00", None, "I am from Ministry of Education, Research Ethics D...", "active"),
        (554, "Ferew", "Adera", "Abebe", "<EMAIL>", "911617935", "Diretube ,Media", 24, "2025-08-03 14:18:00", "2025-08-03 18:19:00", "event-registration/profiles/profile_1cd1e060-5100-", "-", "active"),
        (555, "Siraw", "Tesfa", "Yenesew", "<EMAIL>", "091 3314342", "Mekelle University", 27, "2025-08-03 14:33:00", "2025-08-06 14:33:00", "event-registration/profiles/profile_f51f8589-0fdb-", "", "active"),
        (556, "Dr. Tafere", "Yalew", "melaku", "<EMAIL>", "098 7990457", "Forum for Higher Education Institutions in Amhara", 2, "2025-08-05 08:45:00", "2025-08-07 08:10:00", None, "", "active"),
        (557, "Fekade", "Fetene", "Desalegn", "<EMAIL>", "091 1176828", "MoE", 8, "2025-08-03 08:32:00", "2025-08-07 14:36:00", None, "", "active"),
        (558, "Gebeyehu", "Tiruneh", "Ashagrie", "<EMAIL>", "091 1287533", "Bule Hora University", 23, "2025-08-03 13:35:00", "2025-08-05 12:10:00", None, "", "active"),
        (559, "Dr. Jejaw", "Mebirat", "Demamu", "<EMAIL>", "098 7259301", "Forum for Higher Education Institutions in Amhara", 2, "2025-08-05 08:43:00", "2025-08-07 08:43:00", None, "", "active"),
        (560, "Tamirat", "Wetite", "Beyene", "<EMAIL>", "090 3124988", "Dilla University", 23, "2025-08-03 05:28:00", "2025-08-06 08:28:00", "event-registration/profiles/profile_9b933aeb-e40e-", "", "active"),
        (561, "Mulugeta", "Bedaso", "Burka", "<EMAIL>", "093 0278966", "29234948", 27, "2025-08-03 13:35:00", "2025-08-07 02:08:00", "event-registration/profiles/profile_0de5e87e-a3b1-", "", "active"),
        (562, "Ephrem", "Megersa", "Alemayehu", "<EMAIL>", "+251 918120244", "Assosa University", 28, "2025-08-03 15:30:00", "2025-08-07 15:30:00", "event-registration/profiles/profile_1eedba92-398c-", "When you add university presdants protocol offers...", "active"),
        (563, "ALEMNESH", "KUMSA", "REGA", "<EMAIL>", "+251 911645458", "DYNAMIC INTERAIMENET", 24, "2025-08-02 08:56:00", "2025-08-02 14:59:00", None, "", "active"),
        (564, "Tegegn", "Ejigu", "Dagne", "<EMAIL>", "091 1419278", "MoE", 28, "2025-08-04 15:21:00", "2025-08-04 15:28:00", "event-registration/profiles/profile_6a025fcd-17ea-", "No", "active"),
        (565, "Habtye", "Adane", "Nigussie", "<EMAIL>", "091 8770944", "University of Gondar", 32, "2025-07-31 23:58:00", "2025-08-01 23:58:00", None, "", "active"),
        (566, "Tilahun", "Retta", "Teshome", "<EMAIL>", "091 1684491", "Jimma University", 29, "2025-08-03 03:55:00", "2025-08-07 08:30:00", None, "", "active"),
        (567, "Meseret", "Getahun", "Deribu", "<EMAIL>", "091 1154712", "Ministry of Education", 33, "2025-08-03 07:00:00", "2025-08-07 14:00:00", "event-registration/profiles/profile_650536fd-dde7-", "Participant from Ministry of Education. (Assigned...", "active"),
        (568, "teshome", "Ayele", "Nigussie", "<EMAIL>", "091 8807913", "University of Gondar", 32, "2025-08-01 00:17:00", "2025-08-02 00:18:00", None, "", "active"),
        (569, "Abun", "Yisma", "Arega", "<EMAIL>", "+251 911308189", "MoE", 8, "2025-08-03 07:05:00", "2025-08-06 07:06:00", None, "", "active"),
        (570, "Mesafint", "Demlie", "Fentie", "<EMAIL>", "091 8592149", "University of Gondar", 32, "2025-08-01 00:21:00", "2025-08-02 00:21:00", None, "", "active"),
        (571, "Damena", "Moreda", "Bikila", "<EMAIL>", "091 2206819", "MoE", 25, "2025-08-03 15:56:00", "2025-08-06 16:56:00", None, "", "active"),
        (572, "Lielina", "Asfaw", "Emiru", "<EMAIL>", "091 8736942", "University of Gondar", 32, "2025-08-01 00:28:00", "2025-08-02 00:28:00", None, "", "active"),
        (573, "Misganaw", "Tegegne", "Tilahun", "<EMAIL>", "093 2822355", "University of Gondar", 25, "2025-08-04 16:12:00", "2027-08-08 13:10:00", "event-registration/profiles/profile_b760f97e-9a95-", "", "active"),
        (574, "Shimelis", "Abo", "Lemma", "<EMAIL>", "096 4396877", "Ministry of education", 8, "2025-08-03 16:14:00", "2025-08-06 16:17:00", None, "", "active"),
        (575, "Prof Teketel", "Ashebo", "Yohannes", "<EMAIL>", "+251 911408839", "University of Gondar", 29, "2025-08-03 17:55:00", "2025-08-07 16:25:00", None, "", "active"),
        (576, "Tsegaye", "Mulate", "Endale", "<EMAIL>", "091 1098756", "Ministry of Education", 33, "2025-08-03 06:23:00", "2025-08-07 08:24:00", None, "", "active"),
        (577, "Begna", "Mulugeta", "Dejene", "<EMAIL>", "090 1951680", "Oromia State University", 8, "2025-08-05 15:00:00", "2025-08-07 09:00:00", "event-registration/profiles/profile_d0b49219-5ee5-", "I'm ICT Director of Oromia State University", "active"),
        (578, "Seid", "Yemer", "Mohammed", "<EMAIL>", "091 1048883", "Ministry of Education", 33, "2025-08-03 00:20:00", "2025-08-07 04:20:00", None, "Happy to visit UoG", "active"),
        (579, "Tsega", "Yinesu", "Mekonnen", "<EMAIL>", "091 2059936", "Ministry of Education", 8, "2025-08-03 16:30:00", "2025-08-05 16:30:00", None, "", "active"),
        (580, "Chara", "Disasa", "Bekele", "<EMAIL>", "921174462", "Dambidollo university", 27, "2025-08-03 17:21:00", "2025-08-07 17:21:00", None, "", "active"),
        (581, "Wubshet", "Tadele", "Alula", "<EMAIL>", "094 4363227", "FDRE Education and Training Authority", 1, "2025-08-03 15:45:00", "2025-08-07 14:35:00", None, "", "active"),
        (582, "Meseret Alemneh", "Banti", "", "<EMAIL>", "091 8402648", "University of Gondar", 24, "2025-08-02 01:00:00", "2025-08-05 11:00:00", None, "", "active"),
        (583, "Yidagnu Mandefro", "Zeleke", "", "<EMAIL>", "091 2759569", "University of Gondar", 25, "2025-08-01 17:42:00", "2025-08-01 22:40:00", None, "", "active"),
        (584, "Molla", "Hagos", "Tsegaye", "<EMAIL>", "+251 911241260", "Admas University", 2, "2025-08-03 18:00:00", "2025-08-07 18:02:00", None, "", "active"),
        (585, "Temesgen", "Abiyu", "Assefa", "<EMAIL>", "091 1832932", "Ministry of Education", 8, "2025-08-03 08:30:00", "2025-08-07 02:00:00", None, "", "active"),
        (586, "Dereje", "Woldemariam", "Awgichew", "<EMAIL>", "091 1164076", "Ministry of Education", 28, "2025-08-03 07:20:00", "2025-08-07 16:30:00", "event-registration/profiles/profile_7ceee310-4c13-", "", "active"),
        (587, "Akiber", "Chufo", "", "<EMAIL>", "+251 911813633", "Wolaita Sodo University", 23, "2025-08-04 07:34:00", "2025-08-06 08:30:00", "event-registration/profiles/profile_f4245829-7739-", "", "active"),
        (588, "Mulat", "Legesse", "Abegaz", "<EMAIL>", "093 0177610", "AASTU Board", 29, "2025-08-03 08:20:00", "2025-08-05 04:20:00", "event-registration/profiles/profile_8d94d300-58b6-", "", "active"),
        (589, "Engdaw", "Hailu", "Mewesha", "<EMAIL>", "097 0691450", "UOG", 24, "2025-08-04 20:24:00", "2025-08-07 20:24:00", None, "Pr", "active"),
        (590, "Belay", "Ayenew", "Mesfin", "<EMAIL>", "091 8805970", "university of gondar", 24, "2025-08-01 20:31:00", "2025-08-04 20:31:00", "event-registration/profiles/profile_2d6cdf95-d13b-", "", "active"),
        (591, "Lemlem", "Hailemariyam", "Demissie", "<EMAIL>", "0911928439", "MoE", 25, "2025-08-03 07:30:00", "2025-08-07 16:10:00", "event-registration/profiles/profile_5317ef2c-7bcf-", "", "active"),
        (592, "Hermela", "Fekade", "Belayhun", "<EMAIL>", "091 2179860", "NBC Ethiopia", 24, "2025-08-03 01:40:00", "2025-08-07 10:40:00", None, "", "active"),
        (593, "Tesfahun", "Yilma", "Melese", "<EMAIL>", "091 8779820", "University of Gondar", 25, "2025-08-01 22:32:00", "2025-08-02 22:32:00", "event-registration/profiles/profile_b8a273e9-e268-", "", "active"),
        (594, "Sileshi", "Assefa", "Abbi", "<EMAIL>", "+251 930355379", "Mekdela Amba university", 23, "2025-08-04 22:45:00", "2025-08-07 22:47:00", None, "", "active"),
        (595, "Tewodros", "Chekol", "Abebaw", "<EMAIL>", "091 0153087", "Universtiy of Gondar", 32, None, None, "event-registration/profiles/profile_2217eeef-85f5-", "", "deleted"),
        (596, "Reagan", "Jennings", "Nehru Underwood", "<EMAIL>", "094 3616975", "Delaney Alvarez Associates", 4, None, None, None, "Anim tempore neque", "deleted"),
        (597, "Solomon", "Gebretsadik", "Fantaw", "<EMAIL>", "093 7402246", "University of Gondar", 25, None, None, "event-registration/profiles/profile_bc54bbed-dc8f-", "", "active"),
        (598, "Dr. Yitayal", "Mengistu", "Alemu", "<EMAIL>", "091 1601812", "University of Gondar", 25, None, None, None, "", "active"),
        (599, "Chanie", "Faris", "Adefris", "<EMAIL>", "+251 911167250", "Ministry of education", 25, None, None, None, "", "active"),
        (600, "Demiss", "Geberu", "Mulatu", "<EMAIL>", "092 4110714", "University of Gondar", 25, None, None, "event-registration/profiles/profile_328dcfd2-59e7-", "", "active"),
        (601, "Tadesse", "Baymot", "Weldegebreal", "<EMAIL>", "091 8778670", "University of Gondar", 25, None, None, None, "", "active"),
        (602, "Getasew", "Ayalew", "Abebaw", "<EMAIL>", "913336448", "UoG", 25, None, None, "event-registration/profiles/profile_835e9bae-6366-", "", "active"),
    ]
    
    added_count = 0
    
    with connection.cursor() as cursor:
        for p_data in latest_participants:
            # Check if participant already exists
            if Participant.objects.filter(id=p_data[0]).exists():
                print(f"Participant with ID {p_data[0]} already exists, skipping...")
                continue
            
            # Get participant type
            participant_type = participant_types.get(p_data[7])
            if not participant_type:
                print(f"Warning: Participant type {p_data[7]} not found for {p_data[1]} {p_data[2]}")
                continue
            
            # Parse dates
            arrival_date = None
            departure_date = None
            
            if p_data[8]:  # arrival_date
                try:
                    arrival_date = datetime.strptime(p_data[8], "%Y-%m-%d %H:%M:%S")
                except:
                    arrival_date = None
            
            if p_data[9]:  # departure_date
                try:
                    departure_date = datetime.strptime(p_data[9], "%Y-%m-%d %H:%M:%S")
                except:
                    departure_date = None
            
            # Create participant
            try:
                participant = Participant(
                    first_name=p_data[1],
                    last_name=p_data[2],
                    middle_name=p_data[3] or "",
                    email=p_data[4],
                    phone=p_data[5],
                    institution_name=p_data[6],
                    position="Participant",
                    participant_type=participant_type,
                    arrival_date=arrival_date,
                    departure_date=departure_date,
                    profile_photo=p_data[10],
                    remarks=p_data[11] or "",
                    status=p_data[12],
                    event=event
                )
                participant.save()
                
                # Set the exact ID using raw SQL
                cursor.execute(
                    "UPDATE participants_participant SET id = %s WHERE id = %s",
                    [p_data[0], participant.id]
                )
                
                added_count += 1
                print(f"Added: {p_data[1]} {p_data[2]} with ID {p_data[0]}")
                
            except Exception as e:
                print(f"Error adding {p_data[1]} {p_data[2]}: {str(e)}")
    
    print(f"\nSummary:")
    print(f"Added {added_count} new participants")
    print(f"Total participants now: {Participant.objects.count()}")

if __name__ == "__main__":
    add_participants_551_602()
