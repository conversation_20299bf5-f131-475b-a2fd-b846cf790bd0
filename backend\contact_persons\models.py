from django.db import models


class <PERSON><PERSON><PERSON>(models.Model):
    first_name = models.Char<PERSON><PERSON>(max_length=100)
    middle_name = models.Char<PERSON><PERSON>(max_length=100, blank=True)
    last_name = models.Char<PERSON>ield(max_length=100)
    phone = models.CharField(max_length=20)
    email = models.EmailField()
    photo = models.ImageField(upload_to='contact_person_photos/', null=True, blank=True)

    # Additional contact information
    position = models.CharField(max_length=200, blank=True, help_text='Job title or position')
    organization = models.CharField(max_length=200, blank=True, help_text='Organization or company')

    # Availability
    is_available = models.BooleanField(default=True)
    notes = models.TextField(blank=True)

    # Event association
    event = models.ForeignKey('events.Event', on_delete=models.CASCADE, related_name='contact_persons')

    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ['first_name', 'last_name']
        verbose_name = 'Contact Person'
        verbose_name_plural = 'Contact Persons'

    def __str__(self):
        return self.full_name

    @property
    def full_name(self):
        """Return the full name of the contact person"""
        if self.middle_name:
            return f"{self.first_name} {self.middle_name} {self.last_name}"
        return f"{self.first_name} {self.last_name}"
