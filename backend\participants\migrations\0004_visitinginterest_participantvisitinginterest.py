# Generated by Django 4.2.7 on 2025-07-27 09:03

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ("events", "0004_auto_20250727_0048"),
        ("participants", "0003_alter_participant_profile_photo"),
    ]

    operations = [
        migrations.CreateModel(
            name="VisitingInterest",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("name", models.<PERSON>r<PERSON><PERSON>(max_length=200)),
                ("description", models.TextField()),
                ("location", models.CharField(blank=True, max_length=300)),
                ("is_active", models.BooleanField(default=True)),
                (
                    "max_participants",
                    models.PositiveIntegerField(
                        blank=True,
                        help_text="Maximum number of participants allowed",
                        null=True,
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "event",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="visiting_interests",
                        to="events.event",
                    ),
                ),
            ],
            options={
                "ordering": ["name"],
                "unique_together": {("name", "event")},
            },
        ),
        migrations.CreateModel(
            name="ParticipantVisitingInterest",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "priority",
                    models.PositiveIntegerField(
                        default=1, help_text="1 = First choice, 2 = Second choice, etc."
                    ),
                ),
                ("selected_at", models.DateTimeField(auto_now_add=True)),
                (
                    "participant",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="visiting_interests",
                        to="participants.participant",
                    ),
                ),
                (
                    "visiting_interest",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="participant_interests",
                        to="participants.visitinginterest",
                    ),
                ),
            ],
            options={
                "ordering": ["priority", "selected_at"],
                "unique_together": {("participant", "visiting_interest")},
            },
        ),
    ]
