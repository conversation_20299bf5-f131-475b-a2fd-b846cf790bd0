import React from 'react';
import { Toast as BootstrapToast, ToastContainer as BootstrapToastContainer } from 'react-bootstrap';
import { useToast } from '../contexts/ToastContext';

const ToastContainer: React.FC = () => {
  const { toasts, removeToast } = useToast();

  const getToastVariant = (type: string) => {
    switch (type) {
      case 'success':
        return 'success';
      case 'error':
        return 'danger';
      case 'warning':
        return 'warning';
      case 'info':
        return 'info';
      default:
        return 'light';
    }
  };

  const getToastIcon = (type: string) => {
    switch (type) {
      case 'success':
        return 'fas fa-check-circle';
      case 'error':
        return 'fas fa-exclamation-circle';
      case 'warning':
        return 'fas fa-exclamation-triangle';
      case 'info':
        return 'fas fa-info-circle';
      default:
        return 'fas fa-bell';
    }
  };

  if (toasts.length === 0) {
    return null;
  }

  return (
    <BootstrapToastContainer
      position="top-end"
      className="p-3"
      style={{
        position: 'fixed',
        top: '20px',
        right: '20px',
        zIndex: 9999,
      }}
    >
      {toasts.map((toast) => (
        <BootstrapToast
          key={toast.id}
          onClose={() => removeToast(toast.id)}
          show={true}
          delay={toast.duration}
          autohide={toast.duration ? toast.duration > 0 : false}
          bg={getToastVariant(toast.type)}
          className="mb-2"
          style={{
            minWidth: '300px',
            maxWidth: '400px',
          }}
        >
          <BootstrapToast.Header>
            <i className={`${getToastIcon(toast.type)} me-2`}></i>
            <strong className="me-auto">{toast.title}</strong>
            <small className="text-muted">now</small>
          </BootstrapToast.Header>
          {toast.message && (
            <BootstrapToast.Body className={toast.type === 'error' || toast.type === 'warning' ? 'text-white' : ''}>
              {toast.message}
              {toast.action && (
                <div className="mt-2">
                  <button
                    className={`btn btn-sm ${
                      toast.type === 'error' || toast.type === 'warning' 
                        ? 'btn-outline-light' 
                        : 'btn-outline-primary'
                    }`}
                    onClick={() => {
                      toast.action?.onClick();
                      removeToast(toast.id);
                    }}
                  >
                    {toast.action.label}
                  </button>
                </div>
              )}
            </BootstrapToast.Body>
          )}
        </BootstrapToast>
      ))}
    </BootstrapToastContainer>
  );
};

export default ToastContainer;
