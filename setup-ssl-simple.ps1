# Simple Let's Encrypt SSL Setup for Windows Server
# University of Gondar Event Management System

Write-Host "Let's Encrypt SSL Setup for UoG Event Management System" -ForegroundColor Blue
Write-Host "=======================================================" -ForegroundColor Blue

# Configuration
$DOMAIN = "event.uog.edu.et"
$EMAIL = "<EMAIL>"
$COMPOSE_FILE = "docker-compose.prod.yml"

# Check if running as Administrator
if (-NOT ([Security.Principal.WindowsPrincipal] [Security.Principal.WindowsIdentity]::GetCurrent()).IsInRole([Security.Principal.WindowsBuiltInRole] "Administrator")) {
    Write-Host "ERROR: This script must be run as Administrator" -ForegroundColor Red
    Write-Host "Right-click PowerShell and select 'Run as Administrator'" -ForegroundColor Red
    exit 1
}

Write-Host "Checking prerequisites..." -ForegroundColor Yellow

# Check if Docker is running
try {
    docker version | Out-Null
    Write-Host "SUCCESS: Docker is running" -ForegroundColor Green
} catch {
    Write-Host "ERROR: Docker is not running or not installed" -ForegroundColor Red
    exit 1
}

# Check if application is accessible
try {
    $response = Invoke-WebRequest -Uri "http://localhost" -Method Head -TimeoutSec 10
    Write-Host "SUCCESS: Application is accessible on HTTP" -ForegroundColor Green
} catch {
    Write-Host "ERROR: Application is not accessible on HTTP" -ForegroundColor Red
    Write-Host "Please ensure the application is running first" -ForegroundColor Red
    exit 1
}

# Create certificate directories
Write-Host "Creating certificate directories..." -ForegroundColor Yellow
New-Item -ItemType Directory -Force -Path ".\certbot\conf" | Out-Null
New-Item -ItemType Directory -Force -Path ".\certbot\www" | Out-Null
New-Item -ItemType Directory -Force -Path ".\logs\certbot" | Out-Null

Write-Host "Generating Let's Encrypt SSL certificate..." -ForegroundColor Yellow
Write-Host "Using Let's Encrypt PRODUCTION environment" -ForegroundColor Green

# Generate SSL certificate
try {
    Write-Host "Running certbot to generate certificate..." -ForegroundColor Yellow
    docker-compose -f $COMPOSE_FILE run --rm certbot certonly --webroot --webroot-path=/var/www/certbot --email $EMAIL --agree-tos --no-eff-email --force-renewal -d $DOMAIN -d "www.$DOMAIN"
    
    if (-not (Test-Path ".\certbot\conf\live\$DOMAIN\fullchain.pem")) {
        throw "Certificate files not found after generation"
    }
    
    Write-Host "SUCCESS: SSL certificate generated!" -ForegroundColor Green
} catch {
    Write-Host "ERROR: SSL certificate generation failed" -ForegroundColor Red
    Write-Host "Error details: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host ""
    Write-Host "Troubleshooting steps:" -ForegroundColor Yellow
    Write-Host "1. Ensure external DNS points to your public IP" -ForegroundColor Yellow
    Write-Host "2. Check firewall allows port 80 from internet" -ForegroundColor Yellow
    Write-Host "3. Verify port forwarding is configured" -ForegroundColor Yellow
    Write-Host "4. Check certbot logs in .\logs\certbot\" -ForegroundColor Yellow
    exit 1
}

# Create SSL nginx configuration
Write-Host "Creating SSL-enabled nginx configuration..." -ForegroundColor Yellow

# Backup current configuration
Copy-Item ".\nginx\conf.d\production.conf" ".\nginx\conf.d\production.conf.backup" -Force

# Create SSL configuration content
$sslConfig = @"
# Production Nginx Configuration with Let's Encrypt SSL

# HTTP Server - Redirects to HTTPS
server {
    listen 80;
    server_name $DOMAIN www.$DOMAIN;

    # Let's Encrypt ACME challenge
    location /.well-known/acme-challenge/ {
        root /var/www/certbot;
        try_files `$uri =404;
    }

    # Redirect all other HTTP traffic to HTTPS
    location / {
        return 301 https://`$server_name`$request_uri;
    }
}

# HTTPS Server Configuration
server {
    listen 443 ssl http2;
    server_name $DOMAIN www.$DOMAIN;

    # SSL Configuration
    ssl_certificate /etc/letsencrypt/live/$DOMAIN/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/$DOMAIN/privkey.pem;

    # SSL Security Settings
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512:ECDHE-RSA-AES256-GCM-SHA384;
    ssl_prefer_server_ciphers off;
    ssl_session_cache shared:SSL:10m;
    ssl_session_timeout 10m;

    # Security Headers
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;
    add_header X-Frame-Options DENY always;
    add_header X-Content-Type-Options nosniff always;

    # Root directory
    root /usr/share/nginx/html;
    index index.html index.htm;

    # Logging
    access_log /var/log/nginx/access.log;
    error_log /var/log/nginx/error.log;

    # Backend API routes
    location /api/ {
        proxy_pass http://backend:8000;
        proxy_set_header Host `$host;
        proxy_set_header X-Real-IP `$remote_addr;
        proxy_set_header X-Forwarded-For `$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto `$scheme;
        proxy_connect_timeout 300s;
        proxy_read_timeout 300s;
        proxy_send_timeout 300s;
    }

    # Django Admin
    location /admin/ {
        proxy_pass http://backend:8000;
        proxy_set_header Host `$host;
        proxy_set_header X-Real-IP `$remote_addr;
        proxy_set_header X-Forwarded-For `$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto `$scheme;
        proxy_connect_timeout 300s;
        proxy_read_timeout 300s;
        proxy_send_timeout 300s;
    }

    # Health check
    location /health/ {
        proxy_pass http://backend:8000;
        proxy_set_header Host `$host;
        proxy_set_header X-Real-IP `$remote_addr;
        proxy_set_header X-Forwarded-For `$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto `$scheme;
        proxy_connect_timeout 300s;
        proxy_read_timeout 300s;
        proxy_send_timeout 300s;
    }

    # Static files
    location /static/ {
        root /usr/share/nginx/html;
        expires 1y;
        add_header Cache-Control "public, immutable";
    }

    # Django static files
    location /django-static/ {
        alias /var/www/static/;
        expires 1y;
        add_header Cache-Control "public, immutable";
    }

    # Media files
    location /media/ {
        alias /var/www/media/;
        expires 1M;
        add_header Cache-Control "public";
    }

    # Frontend application
    location / {
        try_files `$uri `$uri/ /index.html;
    }
}
"@

# Write SSL configuration
$sslConfig | Out-File -FilePath ".\nginx\conf.d\production.conf" -Encoding UTF8

Write-Host "SUCCESS: SSL configuration created" -ForegroundColor Green

# Restart services with SSL
Write-Host "Restarting services with SSL enabled..." -ForegroundColor Yellow
docker-compose -f $COMPOSE_FILE down
docker-compose -f $COMPOSE_FILE up -d

# Wait for services to start
Write-Host "Waiting for services to start..." -ForegroundColor Yellow
Start-Sleep -Seconds 30

# Test HTTPS connection
Write-Host "Testing HTTPS connection..." -ForegroundColor Yellow
try {
    $response = Invoke-WebRequest -Uri "https://$DOMAIN" -Method Head -TimeoutSec 15
    Write-Host "SUCCESS: HTTPS is working correctly!" -ForegroundColor Green
} catch {
    Write-Host "WARNING: HTTPS test failed, but this might be normal during startup" -ForegroundColor Yellow
    Write-Host "Please wait a few minutes and test manually" -ForegroundColor Yellow
}

Write-Host ""
Write-Host "Let's Encrypt SSL setup completed!" -ForegroundColor Green
Write-Host "======================================" -ForegroundColor Green
Write-Host "Your application is now secured with SSL" -ForegroundColor Green
Write-Host "Access your application at: https://$DOMAIN" -ForegroundColor Green
Write-Host "SSL certificate will auto-renew every 12 hours" -ForegroundColor Green
Write-Host ""
Write-Host "Important Notes:" -ForegroundColor Blue
Write-Host "- Internal DNS: A record for $DOMAIN -> ************ (DONE)" -ForegroundColor Blue
Write-Host "- External DNS: A record for $DOMAIN -> ************* (DONE)" -ForegroundColor Blue
Write-Host "- Firewall: Ensure ports 80 and 443 are open from internet" -ForegroundColor Blue
Write-Host "- Auto-renewal: Configured via the certbot container" -ForegroundColor Blue
Write-Host ""
Write-Host "SSL setup complete!" -ForegroundColor Green
