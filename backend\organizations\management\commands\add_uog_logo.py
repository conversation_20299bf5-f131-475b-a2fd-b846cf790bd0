"""
Management command to add a logo to University of Gondar organization
"""
import os
from django.core.management.base import BaseCommand
from django.core.files.base import ContentFile
from django.conf import settings
from organizations.models import Organization
import base64


class Command(BaseCommand):
    help = 'Add a logo to University of Gondar organization'

    def handle(self, *args, **options):
        self.stdout.write('Adding logo to University of Gondar organization...')
        
        # Get the primary organization
        try:
            organization = Organization.objects.get(is_primary=True)
        except Organization.DoesNotExist:
            self.stdout.write(
                self.style.ERROR('No primary organization found. Run setup_uog first.')
            )
            return
        
        # Create a simple SVG logo for University of Gondar
        svg_logo = '''<?xml version="1.0" encoding="UTF-8"?>
<svg width="200" height="200" viewBox="0 0 200 200" xmlns="http://www.w3.org/2000/svg">
  <!-- Background Circle -->
  <circle cx="100" cy="100" r="95" fill="#1e40af" stroke="#ffffff" stroke-width="3"/>
  
  <!-- University Building -->
  <rect x="60" y="120" width="80" height="40" fill="#ffffff"/>
  <rect x="65" y="125" width="70" height="30" fill="#1e40af"/>
  
  <!-- Columns -->
  <rect x="70" y="110" width="6" height="20" fill="#ffffff"/>
  <rect x="85" y="110" width="6" height="20" fill="#ffffff"/>
  <rect x="100" y="110" width="6" height="20" fill="#ffffff"/>
  <rect x="115" y="110" width="6" height="20" fill="#ffffff"/>
  <rect x="130" y="110" width="6" height="20" fill="#ffffff"/>
  
  <!-- Roof -->
  <polygon points="55,120 100,95 145,120" fill="#ffffff"/>
  
  <!-- Text -->
  <text x="100" y="50" text-anchor="middle" fill="#ffffff" font-family="serif" font-size="16" font-weight="bold">UoG</text>
  <text x="100" y="180" text-anchor="middle" fill="#ffffff" font-family="serif" font-size="12">University of Gondar</text>
  
  <!-- Academic Symbol -->
  <circle cx="100" cy="75" r="8" fill="#fbbf24"/>
  <text x="100" y="80" text-anchor="middle" fill="#1e40af" font-family="serif" font-size="12">🎓</text>
</svg>'''
        
        # Save the SVG logo
        logo_content = ContentFile(svg_logo.encode('utf-8'))
        organization.logo.save('uog_logo.svg', logo_content, save=True)
        
        self.stdout.write(
            self.style.SUCCESS(f'Successfully added logo to: {organization.name}')
        )
        self.stdout.write(
            self.style.SUCCESS(f'Logo path: {organization.logo.url}')
        )
