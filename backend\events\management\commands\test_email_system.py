from django.core.management.base import BaseCommand
from django.core.mail import send_mail
from django.conf import settings
from events.models import EmailConfiguration, EmailTemplate
from events.email_service import get_email_service


class Command(BaseCommand):
    help = 'Test the email system configuration'
    
    def add_arguments(self, parser):
        parser.add_argument(
            '--to',
            type=str,
            required=True,
            help='Email address to send test email to',
        )
        parser.add_argument(
            '--template-test',
            action='store_true',
            help='Test email templates instead of basic SMTP',
        )
    
    def handle(self, *args, **options):
        recipient_email = options['to']
        
        self.stdout.write(
            self.style.SUCCESS(f'Testing email system...')
        )
        self.stdout.write(f'Recipient: {recipient_email}')
        
        # Check email configuration
        try:
            config = EmailConfiguration.objects.get(is_active=True)
            self.stdout.write(
                self.style.SUCCESS(f'✓ Active email configuration found: {config.name}')
            )
            self.stdout.write(f'  Host: {config.email_host}')
            self.stdout.write(f'  User: {config.email_host_user}')
            self.stdout.write(f'  Port: {config.email_port}')
            self.stdout.write(f'  TLS: {config.email_use_tls}')
        except EmailConfiguration.DoesNotExist:
            self.stdout.write(
                self.style.WARNING('⚠ No active email configuration found')
            )
            self.stdout.write('Using settings from environment variables')
        
        # Check email templates
        template_count = EmailTemplate.objects.filter(is_active=True).count()
        self.stdout.write(
            self.style.SUCCESS(f'✓ Found {template_count} active email templates')
        )
        
        if options['template_test']:
            self.test_email_templates(recipient_email)
        else:
            self.test_basic_smtp(recipient_email)
    
    def test_basic_smtp(self, recipient_email):
        """Test basic SMTP functionality"""
        self.stdout.write('\n--- Testing Basic SMTP ---')
        
        try:
            send_mail(
                subject='UoG Events - SMTP Test',
                message='This is a test email from the University of Gondar Events system.\n\nIf you receive this email, your SMTP configuration is working correctly!',
                from_email=settings.DEFAULT_FROM_EMAIL,
                recipient_list=[recipient_email],
                fail_silently=False,
            )
            
            self.stdout.write(
                self.style.SUCCESS('✓ Basic SMTP test email sent successfully!')
            )
            
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f'✗ SMTP test failed: {str(e)}')
            )
            self.stdout.write('\nTroubleshooting tips:')
            self.stdout.write('1. Check your SMTP credentials in .env.dev')
            self.stdout.write('2. Verify email configuration in Django admin')
            self.stdout.write('3. For Gmail, use App Password instead of regular password')
            self.stdout.write('4. Check firewall/network settings')
    
    def test_email_templates(self, recipient_email):
        """Test email templates and service"""
        self.stdout.write('\n--- Testing Email Templates ---')
        
        email_service = get_email_service()
        
        if not email_service.config:
            self.stdout.write(
                self.style.ERROR('✗ No active email configuration found')
            )
            return
        
        # Test each template type
        template_types = [
            'registration_confirmation',
            'event_details',
            'badge_notification',
            'schedule_update',
            'daily_gallery',
            'event_reminder'
        ]
        
        for template_type in template_types:
            self.stdout.write(f'\nTesting {template_type} template...')
            
            template = email_service._get_template(template_type)
            if not template:
                self.stdout.write(
                    self.style.WARNING(f'⚠ No active template found for {template_type}')
                )
                continue
            
            # Test context
            context = {
                'participant_name': 'Test User',
                'event_name': 'Test Event 2024',
                'event_description': 'A comprehensive academic conference featuring research presentations, workshops, and networking opportunities for students, faculty, and professionals.',
                'event_date': 'December 15, 2024',
                'event_location': 'University of Gondar Main Campus',
                'registration_date': 'November 20, 2024',
                'event_start_date': 'December 15, 2024 at 9:00 AM',
                'event_end_date': 'December 15, 2024 at 5:00 PM',
                'organizer_name': 'Event Organizer',
                'organizer_email': '<EMAIL>',
                'organizer_phone': '+251-911-123456',
                'days_before': 1,
                'date': 'December 15, 2024',
                'image_count': 25,
                'site_name': 'University of Gondar Events',
                'current_year': 2024,
            }
            
            try:
                # Test template rendering
                subject = template.subject.format(**context)
                html_content = template.html_content.format(**context)
                
                self.stdout.write(f'  Subject: {subject}')
                
                # Send test email
                success = email_service.send_email(
                    template_type,
                    recipient_email,
                    'Test User',
                    context
                )
                
                if success:
                    self.stdout.write(
                        self.style.SUCCESS(f'  ✓ {template_type} email sent successfully')
                    )
                else:
                    self.stdout.write(
                        self.style.ERROR(f'  ✗ {template_type} email failed to send')
                    )
                    
            except KeyError as e:
                self.stdout.write(
                    self.style.ERROR(f'  ✗ Template error: Missing variable {e}')
                )
            except Exception as e:
                self.stdout.write(
                    self.style.ERROR(f'  ✗ Error: {str(e)}')
                )
        
        # Show email log summary
        from events.models import EmailLog
        recent_logs = EmailLog.objects.filter(
            recipient_email=recipient_email
        ).order_by('-created_at')[:10]
        
        if recent_logs:
            self.stdout.write('\n--- Recent Email Logs ---')
            for log in recent_logs:
                status_icon = '✓' if log.status == 'sent' else '✗'
                self.stdout.write(
                    f'{status_icon} {log.template_type}: {log.subject} ({log.status})'
                )
        
        self.stdout.write('\n--- Test Complete ---')
        self.stdout.write('Check your email inbox for test messages.')
        self.stdout.write('You can view detailed logs in Django admin: /admin/events/emaillog/')
