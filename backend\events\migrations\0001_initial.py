# Generated by Django 4.2.7 on 2025-07-26 04:21

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = []

    operations = [
        migrations.CreateModel(
            name="Event",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("name", models.CharField(max_length=200)),
                ("description", models.TextField()),
                ("start_date", models.DateTimeField()),
                ("end_date", models.DateTimeField()),
                ("location", models.Char<PERSON>ield(max_length=300)),
                ("city", models.Char<PERSON>ield(max_length=100)),
                ("country", models.CharField(max_length=100)),
                (
                    "latitude",
                    models.DecimalField(
                        blank=True, decimal_places=6, max_digits=9, null=True
                    ),
                ),
                (
                    "longitude",
                    models.DecimalField(
                        blank=True, decimal_places=6, max_digits=9, null=True
                    ),
                ),
                ("organizer_name", models.<PERSON>r<PERSON>ield(max_length=200)),
                ("organizer_email", models.Email<PERSON>ield(max_length=254)),
                ("organizer_phone", models.CharField(max_length=20)),
                (
                    "logo",
                    models.ImageField(blank=True, null=True, upload_to="event_logos/"),
                ),
                (
                    "banner",
                    models.ImageField(
                        blank=True, null=True, upload_to="event_banners/"
                    ),
                ),
                ("is_active", models.BooleanField(default=True)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
            ],
            options={
                "ordering": ["-start_date"],
            },
        ),
        migrations.CreateModel(
            name="EventSchedule",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("title", models.CharField(max_length=200)),
                ("description", models.TextField()),
                ("start_time", models.DateTimeField()),
                ("end_time", models.DateTimeField()),
                ("location", models.CharField(max_length=200)),
                ("speaker", models.CharField(blank=True, max_length=200)),
                ("is_break", models.BooleanField(default=False)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                (
                    "event",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="schedules",
                        to="events.event",
                    ),
                ),
            ],
            options={
                "ordering": ["start_time"],
            },
        ),
        migrations.CreateModel(
            name="EventGallery",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("title", models.CharField(max_length=200)),
                ("description", models.TextField(blank=True)),
                ("image", models.ImageField(upload_to="event_gallery/")),
                ("uploaded_at", models.DateTimeField(auto_now_add=True)),
                ("is_featured", models.BooleanField(default=False)),
                (
                    "event",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="gallery",
                        to="events.event",
                    ),
                ),
            ],
            options={
                "verbose_name_plural": "Event Galleries",
                "ordering": ["-uploaded_at"],
            },
        ),
    ]
