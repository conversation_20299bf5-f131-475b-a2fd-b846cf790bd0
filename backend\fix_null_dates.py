#!/usr/bin/env python3

import os
import sys
import django
from datetime import datetime

# Add the project root to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'event_management.settings')
django.setup()

from participants.models import Participant, ParticipantType
from events.models import Event
from django.db import connection

def fix_null_dates():
    """Add remaining participants with NULL dates by providing default dates"""
    
    print("=== FIXING PARTICIPANTS WITH NULL DATES ===")
    
    # Get Event ID 1
    try:
        event = Event.objects.get(id=1)
    except Event.DoesNotExist:
        print("Error: Event with ID 1 not found")
        return
    
    # Get participant types
    participant_types = {}
    for pt in ParticipantType.objects.all():
        participant_types[pt.id] = pt
    
    # Participants that failed due to NULL dates
    remaining_participants = [
        (595, "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<EMAIL>", "091 0153087", "Universtiy of Gondar", 32, "2025-08-03 08:00:00", "2025-08-07 18:00:00", "event-registration/profiles/profile_2217eeef-85f5-", "", "deleted"),
        (596, "<PERSON>", "<PERSON>", "<PERSON> <PERSON>", "<EMAIL>", "094 3616975", "<PERSON> <PERSON> Associates", 4, "2025-08-03 08:00:00", "2025-08-07 18:00:00", None, "Anim tempore neque", "deleted"),
        (597, "Solomon", "Gebretsadik", "Fantaw", "<EMAIL>", "093 7402246", "University of Gondar", 25, "2025-08-03 08:00:00", "2025-08-07 18:00:00", "event-registration/profiles/profile_bc54bbed-dc8f-", "", "active"),
        (598, "Dr. Yitayal", "Mengistu", "Alemu", "<EMAIL>", "091 1601812", "University of Gondar", 25, "2025-08-03 08:00:00", "2025-08-07 18:00:00", None, "", "active"),
        (599, "Chanie", "Faris", "Adefris", "<EMAIL>", "+251 911167250", "Ministry of education", 25, "2025-08-03 08:00:00", "2025-08-07 18:00:00", None, "", "active"),
        (600, "Demiss", "Geberu", "Mulatu", "<EMAIL>", "092 4110714", "University of Gondar", 25, "2025-08-03 08:00:00", "2025-08-07 18:00:00", "event-registration/profiles/profile_328dcfd2-59e7-", "", "active"),
        (601, "Tadesse", "Baymot", "Weldegebreal", "<EMAIL>", "091 8778670", "University of Gondar", 25, "2025-08-03 08:00:00", "2025-08-07 18:00:00", None, "", "active"),
        (602, "Getasew", "Ayalew", "Abebaw", "<EMAIL>", "913336448", "UoG", 25, "2025-08-03 08:00:00", "2025-08-07 18:00:00", "event-registration/profiles/profile_835e9bae-6366-", "", "active"),
    ]
    
    added_count = 0
    
    with connection.cursor() as cursor:
        for p_data in remaining_participants:
            # Check if participant already exists
            if Participant.objects.filter(id=p_data[0]).exists():
                print(f"Participant with ID {p_data[0]} already exists, skipping...")
                continue
            
            # Get participant type
            participant_type = participant_types.get(p_data[7])
            if not participant_type:
                print(f"Warning: Participant type {p_data[7]} not found for {p_data[1]} {p_data[2]}")
                continue
            
            # Parse dates (now with default values)
            arrival_date = datetime.strptime(p_data[8], "%Y-%m-%d %H:%M:%S")
            departure_date = datetime.strptime(p_data[9], "%Y-%m-%d %H:%M:%S")
            
            # Create participant
            try:
                participant = Participant(
                    first_name=p_data[1],
                    last_name=p_data[2],
                    middle_name=p_data[3] or "",
                    email=p_data[4],
                    phone=p_data[5],
                    institution_name=p_data[6],
                    position="Participant",
                    participant_type=participant_type,
                    arrival_date=arrival_date,
                    departure_date=departure_date,
                    profile_photo=p_data[10],
                    remarks=p_data[11] or "",
                    status=p_data[12],
                    event=event
                )
                participant.save()
                
                # Set the exact ID using raw SQL
                cursor.execute(
                    "UPDATE participants_participant SET id = %s WHERE id = %s",
                    [p_data[0], participant.id]
                )
                
                added_count += 1
                print(f"Added: {p_data[1]} {p_data[2]} with ID {p_data[0]}")
                
            except Exception as e:
                print(f"Error adding {p_data[1]} {p_data[2]}: {str(e)}")
    
    print(f"\nSummary:")
    print(f"Added {added_count} new participants")
    print(f"Total participants now: {Participant.objects.count()}")

if __name__ == "__main__":
    fix_null_dates()
