<!DOCTYPE html>
<html>
<head>
    <title>API Endpoints Test</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; background: #f5f5f5; }
        .container { max-width: 800px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; }
        .endpoint { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .success { background: #d4edda; border-color: #c3e6cb; }
        .error { background: #f8d7da; border-color: #f5c6cb; }
        .loading { background: #fff3cd; border-color: #ffeaa7; }
        button { background: #007bff; color: white; border: none; padding: 8px 16px; border-radius: 4px; cursor: pointer; margin: 5px; }
        button:hover { background: #0056b3; }
        pre { background: #f8f9fa; padding: 10px; border-radius: 4px; overflow-x: auto; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 API Endpoints Test</h1>
        <p>Testing the badge preview API endpoints</p>
        
        <div class="endpoint" id="badges-test">
            <h3>📊 Badges API</h3>
            <button onclick="testBadges()">Test /api/badges/</button>
            <div id="badges-result"></div>
        </div>
        
        <div class="endpoint" id="events-test">
            <h3>🎪 Events API</h3>
            <button onclick="testEvents()">Test /api/events/</button>
            <div id="events-result"></div>
        </div>
        
        <div class="endpoint" id="sponsors-test">
            <h3>🤝 Sponsors API</h3>
            <button onclick="testSponsors()">Test /api/events/6/sponsors/</button>
            <div id="sponsors-result"></div>
        </div>
        
        <div class="endpoint" id="organizers-test">
            <h3>👥 Organizers API</h3>
            <button onclick="testOrganizers()">Test /api/events/6/organizers/</button>
            <div id="organizers-result"></div>
        </div>
        
        <div class="endpoint">
            <h3>🎉 Test All</h3>
            <button onclick="testAll()">Test All Endpoints</button>
        </div>
    </div>

    <script>
        async function testEndpoint(url, resultId) {
            const resultDiv = document.getElementById(resultId);
            resultDiv.innerHTML = '<p>🔄 Testing...</p>';
            
            try {
                const response = await fetch(url);
                const data = await response.json();
                
                if (response.ok) {
                    resultDiv.innerHTML = `
                        <p>✅ <strong>Success!</strong> Status: ${response.status}</p>
                        <p>📊 Data count: ${Array.isArray(data) ? data.length : (data.results ? data.results.length : 'N/A')}</p>
                        <pre>${JSON.stringify(data, null, 2).substring(0, 500)}${JSON.stringify(data, null, 2).length > 500 ? '...' : ''}</pre>
                    `;
                    document.getElementById(resultId.replace('-result', '-test')).className = 'endpoint success';
                } else {
                    resultDiv.innerHTML = `
                        <p>❌ <strong>Error!</strong> Status: ${response.status}</p>
                        <pre>${JSON.stringify(data, null, 2)}</pre>
                    `;
                    document.getElementById(resultId.replace('-result', '-test')).className = 'endpoint error';
                }
            } catch (error) {
                resultDiv.innerHTML = `
                    <p>❌ <strong>Network Error!</strong></p>
                    <pre>${error.message}</pre>
                `;
                document.getElementById(resultId.replace('-result', '-test')).className = 'endpoint error';
            }
        }
        
        function testBadges() {
            testEndpoint('/api/badges/', 'badges-result');
        }
        
        function testEvents() {
            testEndpoint('/api/events/', 'events-result');
        }
        
        function testSponsors() {
            testEndpoint('/api/events/6/sponsors/', 'sponsors-result');
        }
        
        function testOrganizers() {
            testEndpoint('/api/events/6/organizers/', 'organizers-result');
        }
        
        function testAll() {
            testBadges();
            setTimeout(testEvents, 500);
            setTimeout(testSponsors, 1000);
            setTimeout(testOrganizers, 1500);
        }
        
        // Auto-test on page load
        window.onload = function() {
            setTimeout(testAll, 1000);
        };
    </script>
</body>
</html>
