# Generated migration to add session_type field to EventSchedule

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('events', '0003_eventgallery_is_active_eventgallery_uploaded_by'),
    ]

    operations = [
        migrations.AddField(
            model_name='eventschedule',
            name='session_type',
            field=models.CharField(
                choices=[
                    ('presentation', 'Presentation'),
                    ('workshop', 'Workshop'),
                    ('break', 'Break'),
                    ('networking', 'Networking'),
                    ('other', 'Other')
                ],
                default='presentation',
                max_length=20
            ),
        ),
    ]
