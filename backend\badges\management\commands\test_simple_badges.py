from django.core.management.base import BaseCommand
from badges.models import Badge
from participants.models import Participant


class Command(BaseCommand):
    help = 'Test the simplified SVG-style badge generation'

    def add_arguments(self, parser):
        parser.add_argument(
            '--participant-id',
            type=int,
            help='Specific participant ID to generate badge for',
        )
        parser.add_argument(
            '--regenerate',
            action='store_true',
            help='Regenerate existing badges',
        )

    def handle(self, *args, **options):
        self.stdout.write(
            self.style.SUCCESS('🎨 Testing Simplified SVG Badge Generation')
        )
        self.stdout.write('=' * 50)

        # Get participants
        if options['participant_id']:
            try:
                participants = [Participant.objects.get(id=options['participant_id'])]
                self.stdout.write(f'📋 Testing with specific participant ID: {options["participant_id"]}')
            except Participant.DoesNotExist:
                self.stdout.write(
                    self.style.ERROR(f'❌ Participant with ID {options["participant_id"]} not found')
                )
                return
        else:
            participants = Participant.objects.all()[:3]  # Test with first 3 participants
            self.stdout.write(f'📋 Testing with first {len(participants)} participants')

        if not participants:
            self.stdout.write(
                self.style.WARNING('⚠️  No participants found. Please create participants first.')
            )
            return

        generated_count = 0
        updated_count = 0
        error_count = 0

        for participant in participants:
            try:
                self.stdout.write(f'\n👤 Processing: {participant.first_name} {participant.last_name}')
                
                # Get or create badge
                badge, created = Badge.objects.get_or_create(
                    participant=participant
                )
                
                # Generate or regenerate badge
                if not badge.is_generated or options['regenerate']:
                    self.stdout.write('   🎨 Generating badge...')
                    badge_img = badge.generate_badge()
                    
                    if created:
                        generated_count += 1
                        status = '✅ CREATED'
                    else:
                        updated_count += 1
                        status = '🔄 UPDATED'
                    
                    self.stdout.write(f'      {status} Badge generated successfully')
                    self.stdout.write(f'      📏 Dimensions: {badge_img.size}')
                    if badge.badge_image:
                        self.stdout.write(f'      📁 Saved to: {badge.badge_image.name}')
                else:
                    self.stdout.write('   ⏭️  Badge already exists (use --regenerate to update)')
                    
            except Exception as e:
                error_count += 1
                self.stdout.write(
                    self.style.ERROR(f'   ❌ Error generating badge: {str(e)}')
                )
                # Print traceback for debugging
                import traceback
                self.stdout.write(traceback.format_exc())

        # Summary
        self.stdout.write('\n' + '=' * 50)
        self.stdout.write(self.style.SUCCESS('📊 SUMMARY'))
        self.stdout.write(f'✅ Created: {generated_count}')
        self.stdout.write(f'🔄 Updated: {updated_count}')
        self.stdout.write(f'❌ Errors: {error_count}')
        self.stdout.write(f'📋 Total processed: {len(participants)}')
        
        if generated_count > 0 or updated_count > 0:
            self.stdout.write(
                self.style.SUCCESS('\n🎉 Badge generation test completed successfully!')
            )
        elif error_count > 0:
            self.stdout.write(
                self.style.ERROR('\n💥 Badge generation test completed with errors!')
            )
        else:
            self.stdout.write(
                self.style.WARNING('\n⚠️  No badges were generated (all already exist)')
            )
