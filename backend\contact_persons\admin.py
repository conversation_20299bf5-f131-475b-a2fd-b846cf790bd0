from django.contrib import admin
from django.http import HttpResponse
from django.shortcuts import render, redirect
from django.contrib import messages
from django.urls import path, reverse
from django.utils.html import format_html
import csv
import io
from .models import Contact<PERSON>erson


@admin.register(ContactPerson)
class ContactPersonAdmin(admin.ModelAdmin):
    list_display = ['full_name', 'phone', 'email', 'position', 'organization', 'event', 'is_available', 'assigned_participants_count']
    list_filter = ['event', 'is_available', 'organization', 'created_at']
    search_fields = ['first_name', 'middle_name', 'last_name', 'phone', 'email', 'position', 'organization']
    readonly_fields = ['created_at', 'updated_at']

    fieldsets = (
        ('Personal Information', {
            'fields': ('first_name', 'middle_name', 'last_name', 'phone', 'email', 'photo')
        }),
        ('Professional Information', {
            'fields': ('position', 'organization')
        }),
        ('Availability & Event', {
            'fields': ('event', 'is_available', 'notes')
        }),
        ('System Information', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        })
    )

    actions = ['export_to_csv', 'mark_available', 'mark_unavailable', 'generate_badges']

    def assigned_participants_count(self, obj):
        count = obj.assigned_participants.count()
        if count > 0:
            url = reverse('admin:participants_participant_changelist') + f'?assigned_contact_person__id__exact={obj.id}'
            return format_html('<a href="{}">{} participants</a>', url, count)
        return '0 participants'
    assigned_participants_count.short_description = 'Assigned Participants'

    def mark_available(self, request, queryset):
        updated = queryset.update(is_available=True)
        self.message_user(request, f'{updated} contact persons marked as available.')
    mark_available.short_description = "Mark selected contact persons as available"

    def mark_unavailable(self, request, queryset):
        updated = queryset.update(is_available=False)
        self.message_user(request, f'{updated} contact persons marked as unavailable.')
    mark_unavailable.short_description = "Mark selected contact persons as unavailable"

    def generate_badges(self, request, queryset):
        """Generate badges for selected contact persons"""
        updated = 0
        for contact_person in queryset:
            try:
                # Import here to avoid circular imports
                from badges.models import ContactPersonBadge
                badge, created = ContactPersonBadge.objects.get_or_create(contact_person=contact_person)
                badge.generate_badge()
                updated += 1
            except Exception as e:
                messages.error(request, f'Error generating badge for {contact_person.full_name}: {e}')
        self.message_user(request, f'{updated} contact person badges generated successfully.')
    generate_badges.short_description = "Generate badges for selected contact persons"

    def export_to_csv(self, request, queryset):
        response = HttpResponse(content_type='text/csv')
        response['Content-Disposition'] = 'attachment; filename="contact_persons.csv"'

        writer = csv.writer(response)
        writer.writerow([
            'First Name', 'Middle Name', 'Last Name', 'Phone', 'Email', 'Position',
            'Organization', 'Event', 'Available', 'Notes'
        ])

        for contact_person in queryset:
            writer.writerow([
                contact_person.first_name,
                contact_person.middle_name,
                contact_person.last_name,
                contact_person.phone,
                contact_person.email,
                contact_person.position,
                contact_person.organization,
                contact_person.event.name,
                'Yes' if contact_person.is_available else 'No',
                contact_person.notes
            ])

        return response
    export_to_csv.short_description = "Export selected contact persons to CSV"
