from django.contrib import admin
from django.contrib.auth.admin import UserAdmin
from .models import CustomUser, UserRole, UserSession, LoginAttempt, Developer

@admin.register(UserRole)
class UserRoleAdmin(admin.ModelAdmin):
    list_display = ['display_name', 'name', 'color', 'created_at']
    list_filter = ['created_at']
    search_fields = ['name', 'display_name']
    readonly_fields = ['created_at', 'updated_at']
    
    fieldsets = (
        ('Basic Information', {
            'fields': ('name', 'display_name', 'description', 'color')
        }),
        ('Permissions', {
            'fields': (
                'can_manage_users', 'can_manage_events', 'can_manage_participants',
                'can_take_attendance', 'can_upload_gallery', 'can_manage_schedule',
                'can_generate_badges', 'can_manage_hotels', 'can_manage_drivers',
                'can_view_reports', 'can_export_data'
            )
        }),
        ('Timestamps', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        })
    )

@admin.register(CustomUser)
class CustomUserAdmin(UserAdmin):
    list_display = ['username', 'email', 'first_name', 'last_name', 'role', 'is_active', 'date_joined']
    list_filter = ['role', 'is_active', 'is_staff', 'date_joined']
    search_fields = ['username', 'email', 'first_name', 'last_name']
    readonly_fields = ['date_joined', 'last_login', 'created_at', 'updated_at']
    
    fieldsets = UserAdmin.fieldsets + (
        ('Extended Information', {
            'fields': ('role', 'phone', 'position', 'institution', 'profile_picture')
        }),
        ('Security', {
            'fields': ('last_login_ip',)
        }),
        ('Timestamps', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        })
    )
    
    add_fieldsets = UserAdmin.add_fieldsets + (
        ('Extended Information', {
            'fields': ('email', 'first_name', 'last_name', 'role', 'phone', 'position', 'institution')
        }),
    )

@admin.register(UserSession)
class UserSessionAdmin(admin.ModelAdmin):
    list_display = ['user', 'ip_address', 'is_active', 'created_at', 'last_activity']
    list_filter = ['is_active', 'created_at']
    search_fields = ['user__username', 'ip_address']
    readonly_fields = ['session_key', 'created_at', 'last_activity']
    
    def has_add_permission(self, request):
        return False

@admin.register(LoginAttempt)
class LoginAttemptAdmin(admin.ModelAdmin):
    list_display = ['username', 'ip_address', 'success', 'timestamp']
    list_filter = ['success', 'timestamp']
    search_fields = ['username', 'ip_address']
    readonly_fields = ['username', 'ip_address', 'user_agent', 'success', 'timestamp']
    
    def has_add_permission(self, request):
        return False
    
    def has_change_permission(self, request, obj=None):
        return False

@admin.register(Developer)
class DeveloperAdmin(admin.ModelAdmin):
    list_display = ['full_name', 'profession', 'order', 'is_active', 'created_at']
    list_filter = ['is_active', 'created_at']
    search_fields = ['full_name', 'profession']
    list_editable = ['order', 'is_active']
    readonly_fields = ['created_at', 'updated_at']

    fieldsets = (
        ('Basic Information', {
            'fields': ('full_name', 'profession', 'bio')
        }),
        ('Contact & Links', {
            'fields': ('linkedin_link', 'photo')
        }),
        ('Display Settings', {
            'fields': ('order', 'is_active')
        }),
        ('Timestamps', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )
