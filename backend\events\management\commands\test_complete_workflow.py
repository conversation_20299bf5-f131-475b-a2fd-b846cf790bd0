from django.core.management.base import BaseCommand
from participants.models import Participant, ParticipantType
from events.models import Event, EmailLog
from badges.models import Badge
from events.email_service import get_email_service
from django.utils import timezone
import os


class Command(BaseCommand):
    help = 'Test complete email workflow from registration to approval'

    def handle(self, *args, **options):
        self.stdout.write("🚀 Testing complete email workflow...")
        
        # Get or create test data
        try:
            event = Event.objects.first()
            if not event:
                self.stdout.write("❌ No events found for testing")
                return
                
            participant_type = ParticipantType.objects.first()
            if not participant_type:
                self.stdout.write("❌ No participant types found for testing")
                return
                
            self.stdout.write(f"📅 Using event: {event.name}")
            self.stdout.write(f"👥 Using participant type: {participant_type.name}")
            
        except Exception as e:
            self.stdout.write(f"❌ Error getting test data: {e}")
            return
        
        # Create a test participant
        test_email = "<EMAIL>"
        self.stdout.write(f"\n👤 Creating test participant with email: {test_email}")
        
        # Clean up any existing test participant
        Participant.objects.filter(email=test_email).delete()
        EmailLog.objects.filter(recipient_email=test_email).delete()
        
        try:
            participant = Participant.objects.create(
                first_name="Test",
                last_name="Workflow",
                email=test_email,
                phone="+251-911-123456",
                institution_name="Test University",
                position="Test Position",
                event=event,
                participant_type=participant_type,
                arrival_date=timezone.now() + timezone.timedelta(days=7),
                departure_date=timezone.now() + timezone.timedelta(days=10),
                status='pending'
            )
            self.stdout.write(f"✅ Test participant created: {participant.full_name}")
            
        except Exception as e:
            self.stdout.write(f"❌ Error creating participant: {e}")
            return
        
        # Step 1: Test registration confirmation email
        self.stdout.write("\n📧 Step 1: Testing registration confirmation email...")
        email_service = get_email_service()
        
        try:
            result = email_service.send_registration_confirmation(participant)
            if result:
                self.stdout.write("✅ Registration confirmation email sent successfully!")
            else:
                self.stdout.write("❌ Registration confirmation email failed")
                
        except Exception as e:
            self.stdout.write(f"❌ Error sending registration email: {e}")
        
        # Step 2: Approve participant and test approval email
        self.stdout.write("\n✅ Step 2: Approving participant...")
        
        try:
            # Change status to approved (this should trigger the signal)
            participant.status = 'approved'
            participant.approved_at = timezone.now()
            participant.save()
            
            self.stdout.write(f"✅ Participant approved: {participant.status}")
            self.stdout.write("📧 Approval email should be sent automatically via signal...")
            
        except Exception as e:
            self.stdout.write(f"❌ Error approving participant: {e}")
        
        # Step 3: Check if badge was generated
        self.stdout.write("\n🎫 Step 3: Checking badge generation...")
        
        try:
            badge = Badge.objects.get(participant=participant)
            self.stdout.write(f"✅ Badge found: ID={badge.id}")
            self.stdout.write(f"   Generated: {badge.is_generated}")
            
            if badge.badge_image:
                badge_path = badge.badge_image.path
                self.stdout.write(f"   Badge file: {badge.badge_image.name}")
                self.stdout.write(f"   File exists: {os.path.exists(badge_path)}")
                if os.path.exists(badge_path):
                    file_size = os.path.getsize(badge_path)
                    self.stdout.write(f"   File size: {file_size} bytes")
            else:
                self.stdout.write("   ⚠️ No badge image file")
                
        except Badge.DoesNotExist:
            self.stdout.write("❌ No badge found - this should have been created automatically")
        except Exception as e:
            self.stdout.write(f"❌ Error checking badge: {e}")
        
        # Step 4: Manually test approval email with badge
        self.stdout.write("\n📤 Step 4: Testing manual approval email with badge...")
        
        try:
            result = email_service.send_event_details(participant)
            if result:
                self.stdout.write("✅ Approval email with badge sent successfully!")
            else:
                self.stdout.write("❌ Approval email failed")
                
        except Exception as e:
            self.stdout.write(f"❌ Error sending approval email: {e}")
        
        # Step 5: Check email logs
        self.stdout.write("\n📋 Step 5: Checking email logs...")
        
        logs = EmailLog.objects.filter(
            recipient_email=test_email
        ).order_by('-created_at')
        
        self.stdout.write(f"Found {logs.count()} email logs:")
        for log in logs:
            status_icon = "✅" if log.status == 'sent' else "❌"
            self.stdout.write(f"   {status_icon} {log.template_type}: {log.status}")
            if log.error_message:
                self.stdout.write(f"      Error: {log.error_message}")
            self.stdout.write(f"      Time: {log.created_at}")
        
        # Summary
        self.stdout.write("\n📊 WORKFLOW TEST SUMMARY:")
        registration_emails = logs.filter(template_type='registration_confirmation', status='sent').count()
        approval_emails = logs.filter(template_type='event_details', status='sent').count()
        
        self.stdout.write(f"   📧 Registration confirmation emails sent: {registration_emails}")
        self.stdout.write(f"   📧 Approval emails sent: {approval_emails}")
        
        if registration_emails > 0 and approval_emails > 0:
            self.stdout.write("   🎉 COMPLETE WORKFLOW SUCCESS! Both emails working!")
        else:
            self.stdout.write("   ⚠️ Some emails failed - check logs above")
        
        # Cleanup
        self.stdout.write(f"\n🧹 Cleaning up test participant...")
        participant.delete()
        EmailLog.objects.filter(recipient_email=test_email).delete()
        self.stdout.write("✅ Cleanup completed!")
        
        self.stdout.write("\n✅ Complete workflow test finished!")
