#!/usr/bin/env python
"""
Test API endpoints for badge preview
"""
import os
import sys
import django

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'event_management.settings')
django.setup()

from badges.models import Badge
from events.models import Event

def test_api_endpoints():
    """Test API endpoints"""
    print("🔍 TESTING API ENDPOINTS")
    print("=" * 50)
    
    try:
        # Test badges
        badge_count = Badge.objects.count()
        print(f"📊 Total badges: {badge_count}")
        
        if badge_count > 0:
            badge = Badge.objects.first()
            print(f"✅ First badge: {badge.participant.full_name}")
            print(f"   Generated: {badge.is_generated}")
            print(f"   File: {badge.badge_image.name if badge.badge_image else 'None'}")
        
        # Test events
        event_count = Event.objects.count()
        print(f"📊 Total events: {event_count}")
        
        if event_count > 0:
            event = Event.objects.first()
            print(f"✅ First event: {event.name}")
            print(f"   ID: {event.id}")
            
            # Test sponsors
            try:
                sponsors = event.sponsors.filter(is_active=True)
                print(f"   Sponsors: {sponsors.count()}")
            except:
                print("   Sponsors: No sponsors model")
            
            # Test organizers
            try:
                organizers = event.organizers.filter(is_active=True)
                print(f"   Organizers: {organizers.count()}")
            except:
                print("   Organizers: No organizers model")
        
        print(f"\n🎉 API endpoints should work now!")
        print(f"✅ Badges API: /api/badges/ (public access)")
        print(f"✅ Events API: /api/events/ (public access)")
        print(f"✅ Sponsors API: /api/events/{event.id if event_count > 0 else 1}/sponsors/ (public access)")
        print(f"✅ Organizers API: /api/events/{event.id if event_count > 0 else 1}/organizers/ (public access)")
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_api_endpoints()
