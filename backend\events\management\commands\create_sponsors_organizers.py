from django.core.management.base import BaseCommand
from events.models import Event, EventSponsor, EventOrganizer


class Command(BaseCommand):
    help = 'Create sample sponsors and organizers for testing the enhanced badge system'
    
    def handle(self, *args, **options):
        self.stdout.write('🎨 Creating sample sponsors and organizers...')
        
        # Get the event
        try:
            event = Event.objects.get(name='University of Gondar Annual Conference 2025')
        except Event.DoesNotExist:
            self.stdout.write(
                self.style.ERROR('❌ Event not found. Please create the event first.')
            )
            return
        
        # Create MULTIPLE sponsors for enhanced showcase
        sponsors_data = [
            {'name': 'Tech Solutions Inc', 'sponsor_type': 'platinum'},
            {'name': 'Innovation Partners', 'sponsor_type': 'gold'},
            {'name': 'Future Systems', 'sponsor_type': 'silver'},
            {'name': 'Digital Dynamics', 'sponsor_type': 'bronze'},
            {'name': 'Smart Technologies', 'sponsor_type': 'partner'},
            {'name': 'Global Networks', 'sponsor_type': 'supporter'},
            {'name': 'Advanced Computing', 'sponsor_type': 'gold'},
            {'name': 'NextGen Solutions', 'sponsor_type': 'silver'},
        ]
        
        for sponsor_data in sponsors_data:
            sponsor, created = EventSponsor.objects.get_or_create(
                event=event,
                name=sponsor_data['name'],
                defaults={
                    'sponsor_type': sponsor_data['sponsor_type'],
                    'description': f'{sponsor_data["name"]} - Supporting innovation in education',
                    'display_order': len(sponsors_data) - sponsors_data.index(sponsor_data),
                    'is_active': True
                }
            )
            
            status = 'created' if created else 'exists'
            self.stdout.write(f'✅ Sponsor: {sponsor.name} ({status})')
        
        # Create MULTIPLE organizers for enhanced showcase
        organizers_data = [
            {'name': 'Dr. Alemayehu Teshome', 'title': 'Conference Chair', 'is_primary': True},
            {'name': 'Prof. Birtukan Assefa', 'title': 'Program Director', 'is_primary': False},
            {'name': 'Dr. Meseret Tadesse', 'title': 'Technical Lead', 'is_primary': False},
            {'name': 'Prof. Yohannes Haile', 'title': 'Academic Coordinator', 'is_primary': False},
        ]
        
        for org_data in organizers_data:
            organizer, created = EventOrganizer.objects.get_or_create(
                event=event,
                name=org_data['name'],
                defaults={
                    'title': org_data['title'],
                    'email': f'{org_data["name"].lower().replace(" ", ".").replace("dr.", "").replace("prof.", "")}@uog.edu.et',
                    'phone': f'+251-911-{100000 + organizers_data.index(org_data) * 111111:06d}',
                    'bio': f'{org_data["title"]} for the University of Gondar Annual Conference',
                    'is_primary': org_data['is_primary'],
                    'display_order': organizers_data.index(org_data),
                    'is_active': True
                }
            )
            
            status = 'created' if created else 'exists'
            primary = ' (PRIMARY)' if organizer.is_primary else ''
            self.stdout.write(f'👤 Organizer: {organizer.name}{primary} ({status})')
        
        self.stdout.write(
            self.style.SUCCESS('✅ Sample sponsors and organizers created successfully!')
        )
        self.stdout.write(f'📊 Total sponsors: {EventSponsor.objects.filter(event=event).count()}')
        self.stdout.write(f'📊 Total organizers: {EventOrganizer.objects.filter(event=event).count()}')
