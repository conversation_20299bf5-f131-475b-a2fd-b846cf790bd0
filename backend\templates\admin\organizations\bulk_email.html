{% extends "admin/base_site.html" %}
{% load i18n admin_urls static admin_modify %}

{% block title %}Bulk Email Announcements{% endblock %}

{% block extrahead %}
{{ block.super }}
<style>
    .bulk-email-container {
        max-width: 1000px;
        margin: 20px auto;
        background: white;
        border-radius: 8px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        overflow: hidden;
    }
    
    .bulk-email-header {
        background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
        color: white;
        padding: 30px;
        text-align: center;
    }
    
    .bulk-email-header h1 {
        margin: 0;
        font-size: 28px;
        font-weight: 300;
    }
    
    .bulk-email-header p {
        margin: 10px 0 0 0;
        opacity: 0.9;
    }
    
    .bulk-email-form {
        padding: 40px;
    }
    
    .form-group {
        margin-bottom: 25px;
    }
    
    .form-group label {
        display: block;
        margin-bottom: 8px;
        font-weight: 600;
        color: #333;
    }
    
    .form-group input,
    .form-group select,
    .form-group textarea {
        width: 100%;
        padding: 12px;
        border: 2px solid #e9ecef;
        border-radius: 6px;
        font-size: 14px;
        transition: border-color 0.3s ease;
    }
    
    .form-group input:focus,
    .form-group select:focus,
    .form-group textarea:focus {
        outline: none;
        border-color: #2a5298;
        box-shadow: 0 0 0 3px rgba(42, 82, 152, 0.1);
    }
    
    .form-group textarea {
        min-height: 150px;
        resize: vertical;
        font-family: inherit;
    }
    
    .recipient-options {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 15px;
        margin-top: 10px;
    }
    
    .recipient-option {
        padding: 15px;
        border: 2px solid #e9ecef;
        border-radius: 6px;
        cursor: pointer;
        transition: all 0.3s ease;
        text-align: center;
    }
    
    .recipient-option:hover {
        border-color: #2a5298;
        background-color: #f8f9fa;
    }
    
    .recipient-option.selected {
        border-color: #2a5298;
        background-color: #e3f2fd;
    }
    
    .participant-types {
        display: none;
        margin-top: 15px;
        padding: 15px;
        background-color: #f8f9fa;
        border-radius: 6px;
    }
    
    .participant-types.show {
        display: block;
    }
    
    .participant-type-item {
        display: flex;
        align-items: center;
        margin-bottom: 10px;
    }
    
    .participant-type-item input[type="checkbox"] {
        width: auto;
        margin-right: 10px;
    }
    
    .btn-primary {
        background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
        border: none;
        color: white;
        padding: 15px 30px;
        border-radius: 6px;
        font-size: 16px;
        font-weight: 600;
        cursor: pointer;
        transition: transform 0.2s ease;
    }
    
    .btn-primary:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 15px rgba(42, 82, 152, 0.3);
    }
    
    .btn-primary:disabled {
        opacity: 0.6;
        cursor: not-allowed;
        transform: none;
    }
    
    .alert {
        padding: 15px;
        border-radius: 6px;
        margin-bottom: 20px;
    }
    
    .alert-success {
        background-color: #d4edda;
        border: 1px solid #c3e6cb;
        color: #155724;
    }
    
    .alert-error {
        background-color: #f8d7da;
        border: 1px solid #f5c6cb;
        color: #721c24;
    }
    
    .loading {
        display: none;
        text-align: center;
        padding: 20px;
    }
    
    .spinner {
        border: 4px solid #f3f3f3;
        border-top: 4px solid #2a5298;
        border-radius: 50%;
        width: 40px;
        height: 40px;
        animation: spin 1s linear infinite;
        margin: 0 auto 10px;
    }
    
    @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }
    
    .help-text {
        font-size: 12px;
        color: #6c757d;
        margin-top: 5px;
    }
    
    .preview-section {
        margin-top: 30px;
        padding: 20px;
        background-color: #f8f9fa;
        border-radius: 6px;
        border: 1px solid #e9ecef;
    }
    
    .preview-section h3 {
        margin-top: 0;
        color: #1e3c72;
    }
</style>
{% endblock %}

{% block content %}
<div class="bulk-email-container">
    <div class="bulk-email-header">
        <h1>📢 Bulk Email Announcements</h1>
        <p>Send professional announcements to event participants</p>
    </div>
    
    <div class="bulk-email-form">
        <div id="alert-container"></div>
        
        <form id="bulk-email-form">
            {% csrf_token %}
            
            <div class="form-group">
                <label for="event">Select Event *</label>
                <select id="event" name="event" required>
                    <option value="">Choose an event...</option>
                    {% for event in events %}
                        <option value="{{ event.id }}">{{ event.name }} ({{ event.start_date|date:"M d, Y" }})</option>
                    {% endfor %}
                </select>
                <div class="help-text">Select the event whose participants will receive the announcement</div>
            </div>
            
            <div class="form-group">
                <label>Recipients *</label>
                <div class="recipient-options">
                    <div class="recipient-option" data-value="all">
                        <strong>All Participants</strong>
                        <div class="help-text">Send to all confirmed participants</div>
                    </div>
                    <div class="recipient-option" data-value="participant_type">
                        <strong>By Participant Type</strong>
                        <div class="help-text">Send to specific participant types</div>
                    </div>
                </div>
                
                <div class="participant-types" id="participant-types">
                    <label>Select Participant Types:</label>
                    {% for pt in participant_types %}
                        <div class="participant-type-item">
                            <input type="checkbox" id="pt_{{ pt.id }}" name="participant_types" value="{{ pt.id }}">
                            <label for="pt_{{ pt.id }}">{{ pt.name }}</label>
                        </div>
                    {% endfor %}
                </div>
            </div>
            
            <div class="form-group">
                <label for="subject">Email Subject *</label>
                <input type="text" id="subject" name="subject" required placeholder="Enter email subject...">
                <div class="help-text">You can use {participant_name}, {event_name}, {event_date}, {event_location} as placeholders</div>
            </div>
            
            <div class="form-group">
                <label for="content">Email Content *</label>
                <textarea id="content" name="content" required placeholder="Enter your announcement message..."></textarea>
                <div class="help-text">Write your announcement message. You can use the same placeholders as in the subject. Line breaks will be preserved.</div>
            </div>
            
            <div class="preview-section">
                <h3>📋 Email Preview</h3>
                <p><strong>Subject:</strong> <span id="preview-subject">Your subject will appear here...</span></p>
                <p><strong>Content Preview:</strong></p>
                <div id="preview-content" style="border: 1px solid #ddd; padding: 15px; background: white; border-radius: 4px; min-height: 100px;">
                    Your content will appear here...
                </div>
            </div>
            
            <div style="margin-top: 30px; text-align: center;">
                <button type="submit" class="btn-primary" id="send-btn">
                    📧 Send Bulk Email
                </button>
            </div>
        </form>
        
        <div class="loading" id="loading">
            <div class="spinner"></div>
            <p>Sending emails, please wait...</p>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const form = document.getElementById('bulk-email-form');
    const recipientOptions = document.querySelectorAll('.recipient-option');
    const participantTypesDiv = document.getElementById('participant-types');
    const subjectInput = document.getElementById('subject');
    const contentInput = document.getElementById('content');
    const previewSubject = document.getElementById('preview-subject');
    const previewContent = document.getElementById('preview-content');
    const sendBtn = document.getElementById('send-btn');
    const loading = document.getElementById('loading');
    const alertContainer = document.getElementById('alert-container');
    
    let selectedRecipientType = 'all';
    
    // Handle recipient type selection
    recipientOptions.forEach(option => {
        option.addEventListener('click', function() {
            recipientOptions.forEach(opt => opt.classList.remove('selected'));
            this.classList.add('selected');
            selectedRecipientType = this.dataset.value;
            
            if (selectedRecipientType === 'participant_type') {
                participantTypesDiv.classList.add('show');
            } else {
                participantTypesDiv.classList.remove('show');
            }
        });
    });
    
    // Set default selection
    recipientOptions[0].classList.add('selected');
    
    // Preview functionality
    function updatePreview() {
        const subject = subjectInput.value || 'Your subject will appear here...';
        const content = contentInput.value || 'Your content will appear here...';
        
        // Replace placeholders with sample data
        const sampleData = {
            participant_name: 'John Doe',
            event_name: 'Sample Event',
            event_date: 'January 15, 2024',
            event_location: 'University Campus'
        };
        
        let previewSubjectText = subject;
        let previewContentText = content;
        
        Object.keys(sampleData).forEach(key => {
            const placeholder = `{${key}}`;
            previewSubjectText = previewSubjectText.replace(new RegExp(placeholder, 'g'), sampleData[key]);
            previewContentText = previewContentText.replace(new RegExp(placeholder, 'g'), sampleData[key]);
        });
        
        previewSubject.textContent = previewSubjectText;
        previewContent.innerHTML = previewContentText.replace(/\n/g, '<br>');
    }
    
    subjectInput.addEventListener('input', updatePreview);
    contentInput.addEventListener('input', updatePreview);
    
    // Form submission
    form.addEventListener('submit', function(e) {
        e.preventDefault();
        
        const formData = new FormData(form);
        const eventId = formData.get('event');
        const subject = formData.get('subject');
        const content = formData.get('content');
        
        if (!eventId || !subject || !content) {
            showAlert('Please fill in all required fields.', 'error');
            return;
        }
        
        let participantTypeIds = [];
        if (selectedRecipientType === 'participant_type') {
            const checkedTypes = document.querySelectorAll('input[name="participant_types"]:checked');
            if (checkedTypes.length === 0) {
                showAlert('Please select at least one participant type.', 'error');
                return;
            }
            participantTypeIds = Array.from(checkedTypes).map(cb => cb.value);
        }
        
        // Show loading
        form.style.display = 'none';
        loading.style.display = 'block';
        
        // Send request
        fetch('{% url "admin:organizations_send_bulk_email" %}', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value
            },
            body: JSON.stringify({
                event_id: eventId,
                subject: subject,
                content: content,
                recipient_type: selectedRecipientType,
                participant_type_ids: participantTypeIds
            })
        })
        .then(response => response.json())
        .then(data => {
            form.style.display = 'block';
            loading.style.display = 'none';
            
            if (data.success) {
                showAlert(data.message, 'success');
                form.reset();
                recipientOptions.forEach(opt => opt.classList.remove('selected'));
                recipientOptions[0].classList.add('selected');
                participantTypesDiv.classList.remove('show');
                updatePreview();
            } else {
                showAlert(data.error || 'An error occurred while sending emails.', 'error');
            }
        })
        .catch(error => {
            form.style.display = 'block';
            loading.style.display = 'none';
            showAlert('Network error. Please try again.', 'error');
            console.error('Error:', error);
        });
    });
    
    function showAlert(message, type) {
        alertContainer.innerHTML = `<div class="alert alert-${type}">${message}</div>`;
        alertContainer.scrollIntoView({ behavior: 'smooth' });
        
        if (type === 'success') {
            setTimeout(() => {
                alertContainer.innerHTML = '';
            }, 5000);
        }
    }
});
</script>
{% endblock %}
