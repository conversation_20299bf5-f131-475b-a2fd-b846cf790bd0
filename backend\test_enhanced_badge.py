#!/usr/bin/env python3
"""
Test script for enhanced badge generation with improved text visibility
"""

import os
import sys
import django
from PIL import Image, ImageDraw, ImageFont, ImageEnhance
import qrcode

# Add the backend directory to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Set up Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'event_management.settings')
django.setup()

def create_test_badge():
    """Create a test badge with enhanced visibility"""
    print("🎨 Creating enhanced test badge...")
    
    # Badge dimensions - keeping original
    width = 600
    height = 1200
    
    # Create badge with blue gradient background
    badge_img = Image.new('RGB', (width, height), color='#002D72')
    draw = ImageDraw.Draw(badge_img)
    
    # Enhanced fonts for better visibility
    try:
        ministry_font = ImageFont.truetype("arial.ttf", 32)  # Increased from 28
        sector_font = ImageFont.truetype("arial.ttf", 26)    # Increased from 22
        theme_font = ImageFont.truetype("arial.ttf", 24)     # Increased from 20
        name_font = ImageFont.truetype("arial.ttf", 42)      # Much larger for visibility
        position_font = ImageFont.truetype("arial.ttf", 32)  # Much larger for visibility
        institution_font = ImageFont.truetype("arial.ttf", 28) # Much larger for visibility
        type_font = ImageFont.truetype("arial.ttf", 30)      # Larger for prominence
        qr_font = ImageFont.truetype("arial.ttf", 22)        # Larger for visibility
        footer_font = ImageFont.truetype("arial.ttf", 20)    # Larger for visibility
    except OSError:
        # Fallback to default font
        ministry_font = ImageFont.load_default()
        sector_font = ImageFont.load_default()
        theme_font = ImageFont.load_default()
        name_font = ImageFont.load_default()
        position_font = ImageFont.load_default()
        institution_font = ImageFont.load_default()
        type_font = ImageFont.load_default()
        qr_font = ImageFont.load_default()
        footer_font = ImageFont.load_default()
    
    # Header text with enhanced visibility
    draw.text((300, 70), "MINISTRY OF EDUCATION OF FDRE", fill='white', font=ministry_font, anchor='mm')
    
    # Gold line
    draw.line([(180, 80), (420, 80)], fill='#FFD700', width=3)
    draw.ellipse([177, 78, 183, 82], fill='#FFD700')
    draw.ellipse([417, 78, 423, 82], fill='#FFD700')
    
    # Sector and theme text
    draw.text((300, 120), "HIGHER EDUCATION DEVELOPMENT SECTOR", fill='white', font=sector_font, anchor='mm')
    draw.text((300, 160), "HIGHER EDUCATION FOR HIGHER IMPACT", fill='#FFD700', font=theme_font, anchor='mm')
    
    # Photo area
    draw.rounded_rectangle([150, 240, 450, 540], radius=20, fill=None, outline='#FFD700', width=3)
    draw.text((300, 400), "PARTICIPANT PHOTO", fill=(255, 255, 255, 144), font=qr_font, anchor='mm')
    
    # Participant info with ENHANCED visibility
    draw.text((300, 570), "TEDDY CHELOT ABACHEW", fill='white', font=name_font, anchor='mm')
    
    # Gold dashed line
    for x in range(200, 400, 8):
        draw.line([(x, 580), (x+5, 580)], fill='#FFD700', width=2)
    
    draw.text((300, 600), "Senior Software Programmer", fill=(255, 255, 255, 221), font=position_font, anchor='mm')
    draw.text((300, 630), "University of Gondar", fill=(255, 255, 255, 170), font=institution_font, anchor='mm')
    
    # Participant type with enhanced styling
    draw.rounded_rectangle([140, 640, 460, 710], radius=30, fill=(255, 215, 0, 30), outline='#FFD700', width=4)
    draw.rounded_rectangle([150, 650, 450, 700], radius=25, fill=(255, 215, 0, 50), outline='#FFEC8B', width=2)
    
    # Enhanced gold waves
    for i in range(3):
        alpha = 255 - (i * 60)
        draw.line([(160, 675+i), (180, 665+i), (200, 675+i)], fill=(255, 215, 0, alpha), width=3)
        draw.line([(420, 675+i), (440, 665+i), (460, 675+i)], fill=(255, 215, 0, alpha), width=3)
    
    # Type text with shadow for prominence
    draw.text((302, 677), "EXTERNAL PARTICIPANT", fill=(0, 0, 0, 100), font=type_font, anchor='mm')
    draw.text((300, 675), "EXTERNAL PARTICIPANT", fill='#FFD700', font=type_font, anchor='mm')
    
    # QR code area
    draw.rounded_rectangle([175, 720, 425, 970], radius=15, fill=(255, 255, 255, 16), outline='#FFD700', width=2)
    draw.rectangle([200, 745, 400, 945], fill='white')
    
    # Create and add QR code
    qr = qrcode.QRCode(version=1, box_size=10, border=5)
    qr.add_data("https://example.com/participant/123")
    qr.make(fit=True)
    qr_img = qr.make_image(fill_color="black", back_color="white")
    qr_resized = qr_img.resize((200, 200), Image.Resampling.LANCZOS)
    badge_img.paste(qr_resized, (200, 745))
    
    # Sponsor section wave
    for i in range(3):
        y_pos = 990 + i
        alpha = 200 - (i * 40)
        draw.line([(100, y_pos), (300, y_pos-10), (500, y_pos)], fill=(255, 215, 0, alpha), width=2)
    draw.line([(100, 990), (300, 980), (500, 990)], fill='#FFD700', width=3)
    
    # Sponsor boxes (placeholder)
    sponsor_positions = [(100, 1020, 220, 1100), (240, 1020, 360, 1100), (380, 1020, 500, 1100)]
    for x1, y1, x2, y2 in sponsor_positions:
        draw.rounded_rectangle([x1-2, y1-2, x2+2, y2+2], radius=12, fill=(255, 215, 0, 20), outline='#FFD700', width=2)
        draw.rounded_rectangle([x1, y1, x2, y2], radius=10, fill=(255, 255, 255, 15), outline='#FFEC8B', width=1)
        draw.text(((x1+x2)//2, (y1+y2)//2), "SPONSOR", fill='white', font=footer_font, anchor='mm')
    
    # Footer wave
    for i in range(2):
        y_pos = 1160 + i
        alpha = 200 - (i * 50)
        draw.line([(100, y_pos), (300, y_pos+10), (500, y_pos)], fill=(255, 215, 0, alpha), width=2)
    draw.line([(100, 1160), (300, 1170), (500, 1160)], fill='#FFD700', width=3)
    
    # Footer text
    draw.text((300, 1190), "www.moe.gov.et | <EMAIL>", fill='white', font=footer_font, anchor='mm')
    
    # Save with high quality
    output_path = "enhanced_test_badge.png"
    badge_img.save(output_path, 'PNG', optimize=False, compress_level=0, dpi=(1200, 1200))
    
    print(f"✅ Enhanced test badge created: {output_path}")
    print(f"   📏 Dimensions: {width}x{height}")
    print(f"   🎯 Enhanced fonts for better visibility")
    print(f"   🔤 Larger text sizes while keeping original layout")
    print(f"   📱 High DPI (1200x1200) for print quality")
    
    return badge_img

if __name__ == "__main__":
    create_test_badge()
