# Generated manually for admin workflow fields

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('auth', '0012_alter_user_first_name_max_length'),
        ('hotels', '0001_initial'),
        ('drivers', '0001_initial'),
        ('participants', '0001_initial'),
    ]

    operations = [
        migrations.AddField(
            model_name='participant',
            name='status',
            field=models.CharField(
                choices=[
                    ('pending', 'Pending Review'),
                    ('approved', 'Approved'),
                    ('rejected', 'Rejected'),
                    ('assigned', 'Hotel & Driver Assigned'),
                    ('badge_generated', 'Badge Generated'),
                    ('email_sent', 'Details Email Sent'),
                ],
                default='pending',
                max_length=20
            ),
        ),
        migrations.AddField(
            model_name='participant',
            name='assigned_hotel',
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                related_name='assigned_participants',
                to='hotels.hotel'
            ),
        ),
        migrations.AddField(
            model_name='participant',
            name='assigned_room',
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                related_name='assigned_participants',
                to='hotels.hotelroom'
            ),
        ),
        migrations.AddField(
            model_name='participant',
            name='assigned_driver',
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                related_name='assigned_participants',
                to='drivers.driver'
            ),
        ),
        migrations.AddField(
            model_name='participant',
            name='admin_notes',
            field=models.TextField(
                blank=True,
                help_text='Internal admin notes for evaluation'
            ),
        ),
        migrations.AddField(
            model_name='participant',
            name='approved_by',
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                related_name='approved_participants',
                to=settings.AUTH_USER_MODEL
            ),
        ),
        migrations.AddField(
            model_name='participant',
            name='approved_at',
            field=models.DateTimeField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='participant',
            name='rejection_reason',
            field=models.TextField(blank=True),
        ),
        migrations.AddField(
            model_name='participant',
            name='details_email_sent',
            field=models.BooleanField(default=False),
        ),
        migrations.AddField(
            model_name='participant',
            name='details_email_sent_at',
            field=models.DateTimeField(blank=True, null=True),
        ),
    ]
