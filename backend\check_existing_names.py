#!/usr/bin/env python3

import os
import sys
import django

# Add the project root to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'event_management.settings')
django.setup()

from participants.models import Participant

def check_existing_names():
    """Check if participants already exist by name"""
    
    print("=== CHECKING EXISTING PARTICIPANTS BY NAME ===")
    
    # Test some names from the user's request
    test_names = [
        ('<PERSON><PERSON>', '<PERSON>'),
        ('<PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>'), 
        ('Andargachew', 'De<PERSON>'),
        ('Pal', 'Dol'),
        ('<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>'),
        ('<PERSON><PERSON>', 'Agwa'),
        ('<PERSON>', '<PERSON>'),
        ('<PERSON><PERSON><PERSON>', '<PERSON><PERSON>'),
        ('<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>'),
        ('<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>')
    ]
    
    found_count = 0
    for first, last in test_names:
        participants = Participant.objects.filter(first_name=first, last_name=last)
        if participants.exists():
            p = participants.first()
            print(f"Found: {first} {last} (ID: {p.id}, Email: {p.email})")
            found_count += 1
        else:
            print(f"Not found: {first} {last}")
    
    print(f"\nFound {found_count}/{len(test_names)} test participants by name")
    
    # Show total count
    print(f"Total participants in database: {Participant.objects.count()}")

if __name__ == "__main__":
    check_existing_names()
