#!/usr/bin/env python
"""
Script to create participants data with exact IDs.
"""

import os
import sys
import django
from datetime import datetime
from django.utils import timezone

# Setup Django
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'event_management.settings')
django.setup()

from participants.models import ParticipantType, Participant
from events.models import Event

def create_participants():
    """Create participants with exact IDs"""
    print("Creating participants...")
    
    # Get the first event to associate participants with
    event = Event.objects.first()
    if not event:
        print("No events found. Please create an event first.")
        return
    
    # Get participant types
    participant_types = {pt.id: pt for pt in ParticipantType.objects.all()}
    
    participants_data = [
        # (id, first_name, last_name, middle_name, email, phone, institution_name, participant_type_id, arrival_date, departure_date, profile_photo, remarks, status)
        (2, "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<EMAIL>", "097 9883653", "Fischer and Sampson Traders", 4, "2025-07-29 20:41:00", "2025-07-31 17:09:00", None, "Nulla ut deserunt si", "pending"),
        (3, "Tewodros", "Chekol", "Abebaw", "<EMAIL>", "091 0153087", "University of Gondar", 4, "2025-07-31 05:54:00", "2025-08-04 05:54:00", "event-registration/profile_6886e69e84ab6.jpg", "", "pending"),
        (4, "Asrat", "Andargie", "Atsedeweyn", "<EMAIL>", "+251 930001197", "University of Gondar", 2, "2025-09-03 06:58:00", "2025-09-07 06:59:00", None, "", "approved"),
        (5, "Chirotaw", "Gizaw", "Ayele", "<EMAIL>", "091 6581479", "Hawassa University", 2, "2025-08-03 13:35:00", "2025-08-07 08:30:00", "event-registration/profile_6886fb1bddfec.jpg", "", "approved"),
        (6, "Yonas", "Workneh", "Kefialew", "<EMAIL>", "095 6529252", "Gambella University", 23, "2025-08-03 08:20:00", "2025-08-06 02:00:00", "event-registration/profile_6886fb8e154b2.jpg", "", "approved"),
        (7, "Fisiha", "Argaw", "Getachew", "<EMAIL>", "091 9682266", "Hawassa University", 23, "2025-07-28 07:22:00", "2025-07-31 07:22:00", "event-registration/profile_6886fbc405514.jpg", "", "approved"),
        (8, "Awol", "Ebrahim", "Seid", "<EMAIL>", "097 9018485", "Wollo university", 2, "2025-08-03 07:25:00", "2025-08-07 07:25:00", None, "", "approved"),
        (9, "Teshome", "Ababu", "Mulugeta", "<EMAIL>", "091 2381518", "Salale University", 4, "2025-08-04 11:26:00", "2025-08-07 21:27:00", None, "thanks for hosting", "approved"),
        (10, "Dr.Mustefa", "Geda", "Bati", "<EMAIL>", "092 0397305", "Arsi University", 23, "2025-08-03 01:35:00", "2025-08-07 06:35:00", None, "", "approved"),
        (11, "Getachew", "Dagnew", "Gashaw", "<EMAIL>", "091 1182683", "Oda Bultum University", 8, "2025-08-05 00:00:00", "2025-09-07 10:00:00", "event-registration/profile_688700baa12f1.jpg", "", "approved"),
        (12, "Muktar", "Yusuf", "Mohammed", "<EMAIL>", "093 0313003", "Oda Bultun University", 2, "2025-07-28 19:42:00", "2025-07-29 19:35:00", "event-registration/profile_688700fb0a7c9.jpg", "", "approved"),
        (13, "Tafesse", "Karo", "Matewos", "<EMAIL>", "+251 916580266", "Hawassa University", 23, "2025-08-03 13:30:00", "2025-08-07 08:00:00", "event-registration/profile_6887019e9192a.jpg", "", "approved"),
        (14, "Essey", "Muluneh", "Kebede", "<EMAIL>", "093 2808002", "Bahir Dar university", 23, "2025-08-03 14:50:00", "2025-08-06 11:00:00", "event-registration/profile_688702e7c3962.jpg", "", "approved"),
        (15, "Jemal", "A", "Abafita", "<EMAIL>", "096 7876050", "Jimma University", 2, "2025-08-03 14:01:00", "2025-09-05 12:02:00", "event-registration/profile_68870504143f5.jpg", "", "approved"),
        (16, "Dr. Feyera", "Hundessa", "Dinsa", "<EMAIL>", "091 1235189", "Salale University", 2, "2025-08-03 14:00:00", "2025-08-07 09:00:00", "event-registration/profile_688706a543075.png", "Dr. Feyera Dinsa Hundessa President of Salale Un...", "approved"),
        (17, "Alemu", "Mulleta", "Disassa", "<EMAIL>", "091 1159465", "Mattu University", 2, "2025-07-28 08:10:00", "2025-08-07 19:10:00", "event-registration/profile_688707c50d0f7.jpg", "", "approved"),
        (18, "Fisiha", "Argaw", "Getachew", "<EMAIL>", "091 6580261", "Hawassa University", 23, "2025-08-03 08:31:00", "2025-08-07 08:31:00", "event-registration/profile_68870bc9791db.jpg", "", "approved"),
        (19, "Berhanemeskel", "Zemenfes", "Tena", "<EMAIL>", "+251 911487816", "Kotebe University of Education", 2, "2025-07-28 08:36:00", "2025-07-30 10:05:00", None, "", "approved"),
        (20, "Hawa", "Yimer", "Wolie", "<EMAIL>", "094 5023757", "Wollo University", 23, "2025-08-03 16:00:00", "2025-08-07 08:30:00", None, "", "approved"),
        (21, "Tsegaye", "W.medihin", "Deyou", "<EMAIL>", "093 0364625", "Salale University", 23, "2025-08-03 12:30:00", "2025-08-06 03:30:00", None, "", "approved"),
        (22, "Dr Lemi", "Enyadene", "Guta", "<EMAIL>", "091 1491772", "ASTU", 2, "2025-08-03 14:07:00", "2025-08-06 14:09:00", "event-registration/profile_688713d090249.jpg", "", "approved"),
        (23, "Dr Mohammed", "Darasa", "Udman", "<EMAIL>", "099 1277901", "Samara University", 2, "2025-08-03 15:30:00", "2025-08-07 09:30:00", None, "", "approved"),
        (24, "Dr lemma", "Gudata", "Beressa", "<EMAIL>", "091 1263952", "ASTU", 23, "2025-08-03 14:15:00", "2025-08-06 14:16:00", "event-registration/profile_688718e38e0fc.jpg", "", "approved"),
        (25, "Dr Ibsa", "Hassen", "Ahmed", "<EMAIL>", "093 5085977", "Oda Bultum Unvirsity", 23, "2025-08-03 14:30:00", "2025-08-06 12:31:00", None, "", "approved"),
        (26, "Alemayehu", "Abdi", "Beyene", "<EMAIL>", "093 3775511", "Oda Bultum University", 23, "2025-08-03 14:30:00", "2025-08-06 14:30:00", None, "", "approved"),
        (27, "Teramaj", "Kabtimer", "Abebe", "<EMAIL>", "091 3554201", "Mekdela Amba University Educational quality improv...", 8, "2026-09-05 15:31:00", "2026-09-09 14:31:00", None, "", "approved"),
        (28, "Tadesse", "Mamo", "Regassa", "<EMAIL>", "091 7804372", "Dembi Dollo University", 2, "2025-08-04 06:56:00", "2025-08-09 09:57:00", "event-registration/profile_68872041d36eb.jpg", "", "approved"),
        (29, "Dr Teshome", "Segne", "Abdo", "<EMAIL>", "093 0107537", "ASTU", 23, "2025-08-03 19:51:00", "2025-08-06 13:46:00", "event-registration/profile_68872096ae4c1.jpg", "", "approved"),
        (30, "Dr Solomon", "Gebrekirstos", "Abrha", "<EMAIL>", "091 1766278", "CEO for governance and infrastructure, Ministry of...", 8, "2025-08-03 10:06:00", "2025-08-07 10:07:00", "event-registration/profile_68872217300a8.jpeg", "", "approved"),
        (31, "Dr Solomon", "Dibeba", "Tiruneh", "<EMAIL>", "097 0113003", "ASTU", 23, "2025-08-03 14:23:00", "2025-08-06 14:23:00", "event-registration/profile_6887265de27db.jpg", "", "approved"),
        (32, "BESHIR", "MAHAMMOUD", "ABDULLAHI", "<EMAIL>", "093 3004455", "Jigjiga University", 2, "2025-08-03 12:15:00", "2025-08-30 06:00:00", None, "", "approved"),
        (33, "Wohabie", "Bitew", "Birhan", "<EMAIL>", "091 1305606", "Injibara University", 23, "2025-08-03 13:15:00", "2025-08-08 13:15:00", "event-registration/profile_68872ca040915.jpg", "", "approved"),
        (34, "Kindie", "Fenta", "Birhan", "<EMAIL>", "091 8779183", "Injibara University", 23, "2025-08-03 13:15:00", "2025-08-08 13:15:00", "event-registration/profile_68872ceaa7fbc.jpg", "", "approved"),
        (36, "Prof Daniel", "Azene", "Kitaw", "<EMAIL>", "094 3294343", "ASTU", 1, "2025-08-03 14:35:00", "2025-08-06 14:35:00", "event-registration/profile_68872dd945c52.jpg", "", "approved"),
        (37, "Aemero", "Tiku", "Tadesse", "<EMAIL>", "093 0416920", "Injibara university", 23, "2025-08-03 19:15:00", "2025-08-08 19:15:00", "event-registration/profile_68872eb37faaa.jpg", "", "approved"),
        (38, "Fikadu", "Abdissa", "Mitiku", "<EMAIL>", "092 3612548", "Arsi University", 2, "2025-08-03 11:08:00", "2025-08-07 11:08:00", None, "", "approved"),
        (39, "Esayas", "Mengistu", "Lakew", "<EMAIL>", "091 3103634", "Mettu University", 25, "2025-07-28 10:39:00", "2025-07-31 10:39:00", "event-registration/profile_68873185245a4.jpg", "I am Strategic affair executive but the is no opti...", "approved"),
        (40, "Dr. Hailu", "Demessie", "Fekadu", "<EMAIL>", "091 1717608", "Arsi University", 23, "2025-08-03 01:35:00", "2025-08-07 12:45:00", None, "", "approved"),
        (41, "Lingerew", "Zegeye", "Atinkut", "<EMAIL>", "091 8768242", "Injibara University", 8, "2025-08-03 03:40:00", "2025-08-07 08:30:00", "event-registration/profile_6887351e7d26d.jpg", "I am Strategic Affairs Executive  In Injibara Univ...", "approved"),
        (42, "Galma", "Huka", "Bonaya", "<EMAIL>", "092 4690022", "Borana University", 23, "2025-08-03 10:50:00", "2025-08-07 10:20:00", None, "", "approved"),
        (43, "Gebrekidan", "Weldeslasse", "Tesfay", "<EMAIL>", "+251 914314743", "Adigrat University", 23, "2025-08-03 11:52:00", "2025-08-07 11:52:00", None, "", "approved"),
        (44, "Kula", "Tache", "Jilo", "<EMAIL>", "091 6323883", "Borana University", 23, "2025-08-03 22:30:00", "2025-09-06 09:30:00", None, "", "approved"),
        (45, "Alemayehu", "Haye", "Teklemariam", "<EMAIL>", "091 1436898", "Denbidolo University", 29, "2025-08-03 12:29:00", "2025-08-07 12:29:00", "event-registration/profile_68874401bae0e.jpg", "", "approved"),
        (46, "Samuel", "Dira", "Jilo", "<EMAIL>", "091 6580260", "Hawassa University", 23, "2025-08-03 13:34:00", "2025-08-07 20:35:00", None, "", "approved"),
        (47, "Befekadu", "Beyenssa", "Chemere", "<EMAIL>", "091 1840705", "Borana University", 2, "2025-08-03 10:17:00", "2025-08-07 10:17:00", None, "", "approved"),
        (48, "Solomon", "Mamo", "Yifru", "<EMAIL>", "091 3272172", "Arba Minch University", 8, "2025-08-03 07:09:00", "2025-08-07 07:09:00", None, "", "approved"),
        (49, "Dr. Abdlmuhsin", "Alajib", "Hassen", "<EMAIL>", "091 1284201", "Assosa University", 23, "2025-08-03 14:00:00", "2025-08-07 10:30:00", None, "", "approved"),
        (50, "Mengesha", "Ejigu", "Ayene", "<EMAIL>", "093 0377642", "Bahir Dar University", 2, "2025-08-03 16:01:00", "2025-08-05 17:01:00", "event-registration/profile_68874ddc84180.jpg", "", "approved"),
        (51, "YARED", "Gelaw", "Mulu", "<EMAIL>", "093 0317480", "DEBARK UNIVERSITY", 23, "2025-09-03 16:30:00", "2025-09-07 14:30:00", None, "", "approved"),
        (52, "Shimeles", "Tessema", "Bekele", "<EMAIL>", "091 8708091", "BDU", 8, "2025-08-02 09:49:00", "2025-08-04 10:30:00", None, "", "approved"),
        (53, "Dr Kemal Abdurahim", "Ahmed", "Abdurahim", "<EMAIL>", "097 9600008", "Assosa University", 2, "2025-08-03 12:15:00", "2025-08-07 10:20:00", "event-registration/profile_688755e902704.jpg", "", "approved"),
        (54, "Mrs Mesay", "Assefa", "Mesfin", "<EMAIL>", "091 1741789", "ASTU", 4, "2025-08-03 14:09:00", "2025-08-06 14:09:00", "event-registration/profile_68875756a5455.jpg", "", "approved"),
        (55, "Dr. Kassa", "Retta", "Shawle", "<EMAIL>", "090 0975369", "Mekdela Amba University", 2, "2025-08-03 11:23:00", "2025-08-07 11:23:00", "event-registration/profile_688757dde3d77.jpg", "", "approved"),
        (56, "Dr. Diriba", "Gemeda", "Diba", "<EMAIL>", "091 1895155", "Wollega University", 23, "2025-08-03 10:01:00", "2025-08-07 08:04:00", "event-registration/profile_688759b40ce6d.jpg", "See you in Gondor, a historical city!", "approved"),
        (57, "Duresa", "Geleto", "Deksiso", "<EMAIL>", "921477136", "Arsi University", 1, "2025-08-05 08:00:00", "2025-09-07 08:00:00", None, "", "approved"),
        (58, "Degela", "Done", "Ergano", "<EMAIL>", "091 1030822", "Bonga University", 2, "2025-07-28 14:13:00", "2025-08-27 11:12:00", None, "", "approved"),
        (59, "Towfik", "Ali", "Jemal", "<EMAIL>", "096 0344784", "Werabe University", 2, "2025-08-03 16:15:00", "2025-08-06 17:20:00", None, "", "approved"),
        (60, "Dr. Reda", "Hora", "Nemo", "<EMAIL>", "+251 917842053", "Dambi Dollo University", 23, "2025-08-03 12:00:00", "2025-08-07 08:30:00", "event-registration/profile_68875f8d72cbe.jpg", "", "approved"),
        (61, "Dr Gemechu", "Kerorsa", "Berhanu", "<EMAIL>", "091 7491514", "Dambi Dollo University", 23, "2025-08-03 12:20:00", "2025-08-07 08:30:00", "event-registration/profile_68875fca471b8.jpg", "", "approved"),
        (62, "Harbora", "Racha", "Bule", "<EMAIL>", "091 6178794", "Borana University", 4, "2025-08-03 12:15:00", "2025-08-03 22:50:00", "event-registration/profile_688760c9538c3.jpg", "", "approved"),
        # Additional participants
        (154, "Paulos", "Shibeshi", "Taddesse", "<EMAIL>", "093 7458433", "Arba Minch University", 23, "2025-08-03 15:30:00", "2025-08-07 08:30:00", None, "", "approved"),
        (155, "Dr Tolossa", "Wedajo", "Dadi", "<EMAIL>", "093 0364635", "Salale University", 23, "2025-08-03 01:35:00", "2025-08-07 10:20:00", None, "", "approved"),
        (156, "Teferi", "Jibicho", "Gishe", "<EMAIL>", "091 6358173", "Ethiopian civil service university", 27, "2025-08-03 10:55:00", "2025-08-07 10:58:00", None, "", "approved"),
        (157, "Tadewos", "Wogasso", "Mentta", "<EMAIL>", "093 7809795", "Ethiopian Civil Service University", 23, "2025-08-03 15:00:00", "2025-08-07 14:00:00", None, "", "approved"),
        (158, "engidaw", "awoke", "", "<EMAIL>", "091 8030593", "UoG", 4, "2025-07-31 03:48:00", "2025-08-01 03:48:00", None, "", "pending"),
        (159, "Dr. Abdi", "Hasan", "Ahmed", "<EMAIL>", "091 1042563", "Jigjiga University", 23, "2025-08-03 06:43:00", "2025-08-06 10:00:00", "event-registration/profile_688ae70354260.jpeg", "", "approved"),
        (160, "Awoke", "Melese", "Azanaw", "<EMAIL>", "091 8305854", "Debark University", 27, "2025-08-03 08:31:00", "2025-08-03 08:36:00", "event-registration/profile_688b014d0746c.jpg", "", "approved"),
        (161, "Eba", "Negero", "Mijena", "<EMAIL>", "091 1110148", "MoE", 8, "2025-08-03 03:01:00", "2025-08-08 09:02:00", None, "", "approved"),
        (162, "Dr Zakaria", "Mohamed", "Abdiwali", "<EMAIL>", "091 3551873", "University of Kabridahar", 27, "2025-08-03 10:15:00", "2025-08-06 09:15:00", "event-registration/profile_688b0a3346015.jpg", "", "approved"),
        (163, "Abebaw", "Mekonnen", "Demssie", "<EMAIL>", "092 3656322", "Wollo university", 27, "2025-08-03 22:30:00", "2025-08-06 07:30:00", None, "", "approved"),
        (164, "Kelelew", "Hailemichael", "Addisu", "<EMAIL>", "093 0318063", "Bonga University", 23, "2025-08-03 12:15:00", "2025-08-06 10:05:00", None, "", "approved"),
        (165, "Prof. Bizunesh", "Borena", "Mideksa", "<EMAIL>", "+251 944741626", "Ambo University", 23, "2025-08-03 08:40:00", "2025-08-07 08:30:00", "event-registration/profile_688b1b86f0095.jpg", "", "approved"),
        (166, "Temam", "Bartuga", "Argaw", "<EMAIL>", "091 5666492", "Werabe university", 25, "2025-08-03 00:55:00", "2025-08-03 05:30:00", None, "", "approved"),
        (167, "Yesihak", "Mummed", "Yusuf", "<EMAIL>", "093 0760076", "Haramaya University", 23, "2025-08-03 14:30:00", "2025-08-06 08:00:00", None, "", "approved"),
        (168, "Eng. Abdifetah", "Rabi", "Ahmed", "<EMAIL>", "091 1303733", "University", 2, "2025-08-03 03:26:00", "2025-08-04 03:50:00", "event-registration/profile_688b2acc8d99c.jpg", "", "approved"),
        (169, "Teklu", "Zara", "Wegatehu", "<EMAIL>", "+251 911004186", "Arba Minch University", 23, "2025-08-03 15:30:00", "2025-09-07 02:30:00", "event-registration/profile_688b2af749fb6.jpg", "", "approved"),
        (170, "Tamirat", "Bekele", "Mulugeta", "<EMAIL>", "092 1335855", "Ethiopian Police University", 2, "2025-08-03 12:30:00", "2025-09-06 12:32:00", None, "Acting President", "approved"),
        (171, "Ahmed", "Abdinasir", "Muhumed", "<EMAIL>", "091 5751700", "University of Kabridahar", 23, "2025-08-03 22:49:00", "2025-08-06 12:50:00", None, "", "approved"),
        (172, "Bedilu", "Geleto", "Teka", "<EMAIL>", "093 7003501", "Mattu University", 23, "2025-08-03 12:56:00", "2025-08-07 12:57:00", "event-registration/profiles/profile_3e619b1d-7f31-", "My arrival and return date is filled in GeG.C not", "approved"),
        (173, "Berhanu", "Ali", "Shibeshi", "<EMAIL>", "094 1741764", "WACHEMO UNIVERSITY", 27, "2025-08-04 14:29:00", "2025-08-07 14:29:00", "event-registration/profiles/profile_5868e969-a1d6-", "", "approved"),
        (328, "Prof Ajebu", "Nurfeta", "", "<EMAIL>", "091 6032359", "Hawassa University", 29, "2025-08-03 16:41:00", "2025-08-06 16:42:00", None, "None", "approved"),
        (329, "Mulugeta", "Gebremedhin", "Berihu", "<EMAIL>", "+251 932314972", "Aksun University", 23, "2025-08-03 21:00:00", "2025-08-06 21:00:00", None, "", "approved"),
        (330, "Dr Dawit", "Borsamo", "Hayeso", "<EMAIL>", "093 8984001", "Wachemo university", 2, "2025-08-03 03:29:00", "2025-08-07 09:30:00", None, "", "approved"),
        (331, "Dr. Tadesse", "Tessema", "Habtamu", "<EMAIL>", "091 1962721", "Jimma University", 23, "2025-08-03 16:22:00", "2025-08-07 10:24:00", None, "Nothing special", "approved"),
        (332, "Nigus", "Bshe", "Tadesse", "<EMAIL>", "094 2523422", "Ethiopian Civi Service University", 2, "2025-08-04 13:44:00", "2025-08-04 13:45:00", None, "", "approved"),
        (333, "Jema", "Mohammed", "Haji", "<EMAIL>", "091 1780518", "Haramaya University", 29, "2025-08-03 15:55:00", "2025-08-07 10:20:00", None, "", "approved"),
        (334, "Argaw", "Bayih", "Ambelu", "<EMAIL>", "091 1826218", "Addis Ababa University", 29, "2025-08-03 13:25:00", "2025-08-06 13:25:00", None, "", "approved"),
        (335, "Sara", "Muzein", "Shikur", "<EMAIL>", "091 1891235", "Wachemo University", 23, "2025-08-03 15:30:00", "2025-08-07 03:30:00", None, "", "approved"),
        (336, "Ebrahim", "Mohammed", "Jemal", "<EMAIL>", "+251 910990926", "Jinka University", 23, "2025-08-03 11:01:00", "2025-08-07 11:10:00", None, "", "approved"),
        (337, "Abdella", "Mohammed", "Kemal", "<EMAIL>", "093 3585793", "Arbaminch University", 2, "2025-08-03 15:39:00", "2025-08-07 08:30:00", None, "", "approved"),
        (338, "Gemechis", "Duressa", "", "<EMAIL>", "094 2565096", "Jimma University", 23, "2025-08-03 16:48:00", "2025-08-07 20:48:00", None, "NA", "approved"),
        (339, "Andargachew", "Deata", "Baylie", "<EMAIL>", "092 8558827", "Debre Markos University", 23, "2025-08-03 23:50:00", "2025-09-03 22:50:00", None, "", "approved"),
        (340, "Pal", "Dol", "Both", "<EMAIL>", "091 7303194", "Gambella University", 23, "2025-09-03 17:54:00", "2025-09-06 05:55:00", None, "", "approved"),
        (342, "Yohannes", "Ejigu", "Yitbarek", "<EMAIL>", "+251 920437380", "Jinka University", 2, "2025-08-03 15:40:00", "2025-08-07 08:35:00", None, "", "approved"),
        (343, "Tadiyose", "Arba", "Arba", "<EMAIL>", "091 0153087", "Prof Kindy's Security", 9, "2025-07-31 17:47:00", "2025-08-06 17:47:00", None, "", "approved"),
        (344, "Mengesha", "Mersha", "Admasu", "<EMAIL>", "091 8350278", "Raya University", 29, "2025-08-03 15:55:00", "2025-08-07 08:30:00", None, "", "approved"),
        (346, "Tilahun", "Retta", "Teshome", "<EMAIL>", "091 1684491", "AAU", 29, "2025-08-03 17:34:00", "2025-08-07 17:36:00", None, "", "pending"),
        (347, "Tesfahun", "Yilma", "Melese", "<EMAIL>", "091 8779820", "University of Gondar", 25, "2025-07-31 17:30:00", "2025-07-31 22:30:00", None, "", "pending"),
        (348, "Endris", "Ahmed", "Seid", "<EMAIL>", "091 1034154", "Samara University", 27, "2025-08-02 14:30:00", "2025-08-07 18:45:00", None, "", "approved"),
        (349, "Zerihun", "Assefa", "", "<EMAIL>", "098 3001916", "Jimma University", 23, "2025-08-03 12:10:00", "2025-08-06 12:00:00", None, "The arrival and departure times may change.", "approved"),
        (350, "Engidaw", "Muche", "Awoke", "<EMAIL>", "+251 918030593", "UoG", 4, "2025-07-30 17:11:00", "2025-07-31 17:11:00", None, "", "pending"),
        (351, "Tesfaye", "Feyissa", "Tolu", "<EMAIL>", "+251 911677704", "Ethiopian Defence University", 23, "2025-08-03 02:45:00", "2025-08-07 08:45:00", None, "", "approved"),
        (352, "Lemma", "Angessa", "Gudissa", "<EMAIL>", "091 1968772", "Ethiopian Civil Service University", 23, "2025-08-03 11:56:00", "2025-08-07 11:56:00", None, "", "approved"),
        (353, "Dr. Shimelis", "Admassie", "Zewdie", "<EMAIL>", "091 1718823", "Kotebe University of Education", 23, "2025-08-03 10:50:00", "2025-08-06 04:25:00", None, "", "approved"),
        (354, "Meba Tadesse", "Delle", "", "<EMAIL>", "091 1114759", "Addis Ababa University", 27, "2025-08-03 11:38:00", "2025-08-07 11:38:00", None, "", "approved"),
        (355, "Dr. Samuel", "Kidane", "Kifle", "<EMAIL>", "+251 911244079", "Addis Ababa University", 2, "2025-08-03 11:27:00", "2025-08-06 11:26:00", None, "", "approved"),
        (356, "Kebede", "Gerbi", "Regassa", "<EMAIL>", "+251 932180457", "Ethiopian Defence University", 2, "2025-08-03 02:45:00", "2025-08-07 08:45:00", None, "", "approved"),
        (511, "Carly", "Gentry", "Alice Ortiz", "<EMAIL>", "099 0542178", "Anderson Maddox Associates", 6, "2025-08-01 16:28:00", "2025-08-08 23:16:00", "event-registration/profiles/profile_9c38531b-9305-", "Ipsam reiciendis ali", "pending"),
        (512, "Adem", "Kabo", "Ali", "<EMAIL>", "091 2127133", "Samara university", 23, "2025-08-03 15:39:00", "2025-08-06 15:40:00", "event-registration/profiles/profile_d1974986-bdd2-", "Ok", "approved"),
        (513, "Matebu", "Jabessa", "Gezahegn", "<EMAIL>", "094 2632924", "Jimma University", 27, "2025-08-03 16:31:00", "2025-08-07 16:31:00", None, "", "approved"),
        (514, "Asmamaw", "Workneh", "Zegeye", "<EMAIL>", "096 0100825", "Debark university", 2, "2025-08-03 17:16:00", "2025-08-07 17:16:00", "event-registration/profiles/profile_5d540ecb-6723-", "", "approved"),
        (515, "Dr.Megersa", "Hussen", "Kasim", "<EMAIL>", "099 3440333", "Dire Dawa University", 23, "2025-08-03 00:30:00", "2025-09-06 16:25:00", "event-registration/profiles/profile_797e48f7-ae85-", "", "approved"),
        (516, "LAMESGIN", "TIZAZU", "AYELE", "<EMAIL>", "091 2768313", "Dire dawa University", 27, "2025-08-03 14:03:00", "2025-08-06 04:25:00", None, "", "approved"),
        (517, "Woldeamlak", "Alemayehu", "Bewket", "<EMAIL>", "091 1608122", "Ambo University", 29, "2025-08-03 13:35:00", "2025-08-07 10:20:00", None, "", "approved"),
        (518, "Derbew", "Yohannes", "Belew", "<EMAIL>", "+251 946511627", "Wollega University", 29, "2025-08-03 13:25:00", "2025-08-07 10:20:00", "event-registration/profiles/profile_a5240abe-4745-", "", "approved"),
        (519, "Bizunesh", "Borena", "Mideksa", "<EMAIL>", "+251 944741626", "Ambo University", 23, "2025-08-03 08:40:00", "2025-09-07 08:30:00", "event-registration/profiles/profile_58750392-4301-", "", "approved"),
        (520, "LIDETU", "GOBENA", "GIZAW", "<EMAIL>", "+251 913307451", "BONGA UNIVERSITY", 27, "2025-08-03 12:15:00", "2025-08-06 10:05:00", None, "Thanks!", "approved"),
        (521, "Alemayehu", "Mekonnen", "Debebe", "<EMAIL>", "+251 935998680", "Ethiopian Civil Service University", 23, "2025-08-03 13:00:00", "2025-08-07 10:00:00", None, "None", "approved"),
        (522, "Alemayehu", "Mekonnen", "Debebe", "<EMAIL>", "+251 935998680", "Ethiopian Civil Service University", 23, "2025-08-03 13:00:00", "2025-08-07 10:00:00", None, "None", "approved"),
        (523, "Asnake", "Ede", "Gudisa", "<EMAIL>", "096 5568511", "Kotebe University of Education", 27, "2025-08-03 12:15:00", "2025-08-06 05:45:00", "event-registration/profiles/profile_c441fb03-985b-", "", "approved"),
    ]
    
    for p_data in participants_data:
        # Check if participant type exists
        if p_data[7] not in participant_types:
            print(f"Warning: Participant type {p_data[7]} not found, skipping participant {p_data[1]} {p_data[2]}")
            continue

        # Check if participant already exists
        if Participant.objects.filter(id=p_data[0]).exists():
            print(f"Participant with ID {p_data[0]} already exists, skipping {p_data[1]} {p_data[2]}")
            continue

        participant = Participant(
            first_name=p_data[1],
            last_name=p_data[2],
            middle_name=p_data[3],
            email=p_data[4],
            phone=p_data[5],
            institution_name=p_data[6],
            position="Participant",  # Default position
            participant_type=participant_types[p_data[7]],
            arrival_date=p_data[8],
            departure_date=p_data[9],
            profile_photo=p_data[10],
            remarks=p_data[11] or "",
            status=p_data[12],
            event=event
        )
        participant.save()

        # Set the ID after saving to preserve exact IDs
        participant.id = p_data[0]
        participant.save()
    
    print(f"Created {len(participants_data)} participants.")

if __name__ == "__main__":
    create_participants()
