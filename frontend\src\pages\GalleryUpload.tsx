import React, { useState, useEffect, useCallback } from 'react';
import { Container, Row, Col, Card, Button, Alert, Form, Modal, Badge, ProgressBar } from 'react-bootstrap';
import { useAuth } from '../contexts/AuthContext';
import { galleryService, GalleryCategory, GalleryImage } from '../services/api';
import { useDropzone } from 'react-dropzone';

interface UploadFile extends File {
  id: string;
  preview: string;
  progress: number;
  status: 'pending' | 'uploading' | 'success' | 'error';
  error?: string;
}

const GalleryUpload: React.FC = () => {
  const { isAuthenticated } = useAuth();
  const [categories, setCategories] = useState<GalleryCategory[]>([]);
  const [selectedCategory, setSelectedCategory] = useState<number | null>(null);
  const [files, setFiles] = useState<UploadFile[]>([]);
  const [uploading, setUploading] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');

  // Form data for each image
  const [globalSettings, setGlobalSettings] = useState({
    photographer: '',
    location: '',
    is_featured: false,
    is_slider: false,
    is_campus: false,
    tags: ''
  });

  useEffect(() => {
    if (isAuthenticated) {
      fetchCategories();
    }
  }, [isAuthenticated]);

  const fetchCategories = async () => {
    try {
      const response = await galleryService.getCategories();
      setCategories(response.data.results || response.data);
    } catch (err: any) {
      setError('Failed to fetch categories');
    }
  };

  const onDrop = useCallback((acceptedFiles: File[]) => {
    const newFiles: UploadFile[] = acceptedFiles.map(file => ({
      ...file,
      id: Math.random().toString(36).substr(2, 9),
      preview: URL.createObjectURL(file),
      progress: 0,
      status: 'pending'
    }));
    
    setFiles(prev => [...prev, ...newFiles]);
  }, []);

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: {
      'image/*': ['.jpeg', '.jpg', '.png', '.gif', '.webp']
    },
    multiple: true
  });

  const removeFile = (fileId: string) => {
    setFiles(prev => {
      const file = prev.find(f => f.id === fileId);
      if (file) {
        URL.revokeObjectURL(file.preview);
      }
      return prev.filter(f => f.id !== fileId);
    });
  };

  const uploadFiles = async () => {
    if (!selectedCategory) {
      setError('Please select a category');
      return;
    }

    if (files.length === 0) {
      setError('Please select files to upload');
      return;
    }

    setUploading(true);
    setError('');
    setSuccess('');

    const pendingFiles = files.filter(f => f.status === 'pending');
    
    for (const file of pendingFiles) {
      try {
        // Update file status to uploading
        setFiles(prev => prev.map(f => 
          f.id === file.id ? { ...f, status: 'uploading', progress: 0 } : f
        ));

        const formData = new FormData();
        formData.append('image', file);
        formData.append('title', file.name.split('.')[0]);
        formData.append('description', `Uploaded image: ${file.name}`);
        formData.append('category', selectedCategory.toString());
        formData.append('photographer', globalSettings.photographer);
        formData.append('location', globalSettings.location);
        formData.append('is_featured', globalSettings.is_featured.toString());
        formData.append('is_slider', globalSettings.is_slider.toString());
        formData.append('is_campus', globalSettings.is_campus.toString());
        formData.append('tags', globalSettings.tags);
        formData.append('alt_text', file.name);

        // Simulate progress (since we can't track real progress easily)
        const progressInterval = setInterval(() => {
          setFiles(prev => prev.map(f => 
            f.id === file.id && f.progress < 90 
              ? { ...f, progress: f.progress + 10 } 
              : f
          ));
        }, 200);

        await galleryService.createImage(formData);

        clearInterval(progressInterval);
        
        // Update file status to success
        setFiles(prev => prev.map(f => 
          f.id === file.id ? { ...f, status: 'success', progress: 100 } : f
        ));

      } catch (err: any) {
        // Update file status to error
        setFiles(prev => prev.map(f => 
          f.id === file.id ? { 
            ...f, 
            status: 'error', 
            error: err.response?.data?.detail || 'Upload failed' 
          } : f
        ));
      }
    }

    setUploading(false);
    
    const successCount = files.filter(f => f.status === 'success').length;
    const errorCount = files.filter(f => f.status === 'error').length;
    
    if (successCount > 0) {
      setSuccess(`Successfully uploaded ${successCount} image(s)`);
    }
    if (errorCount > 0) {
      setError(`Failed to upload ${errorCount} image(s)`);
    }
  };

  const clearAll = () => {
    files.forEach(file => URL.revokeObjectURL(file.preview));
    setFiles([]);
    setError('');
    setSuccess('');
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'pending': return 'fas fa-clock text-warning';
      case 'uploading': return 'fas fa-spinner fa-spin text-info';
      case 'success': return 'fas fa-check-circle text-success';
      case 'error': return 'fas fa-times-circle text-danger';
      default: return 'fas fa-question-circle text-muted';
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending': return 'warning';
      case 'uploading': return 'info';
      case 'success': return 'success';
      case 'error': return 'danger';
      default: return 'secondary';
    }
  };

  return (
    <Container className="py-4">
      {/* Header */}
      <Row className="mb-4">
        <Col>
          <div className="d-flex justify-content-between align-items-center">
            <div>
              <h1 className="display-6 fw-bold text-primary">
                <i className="fas fa-cloud-upload-alt me-3"></i>
                Gallery Upload
              </h1>
              <p className="lead text-muted">Upload multiple images to your gallery</p>
            </div>
            <div className="d-flex gap-2">
              <Button 
                variant="outline-secondary" 
                onClick={clearAll}
                disabled={uploading || files.length === 0}
              >
                <i className="fas fa-trash me-2"></i>
                Clear All
              </Button>
              <Button 
                variant="primary" 
                onClick={uploadFiles}
                disabled={uploading || files.length === 0 || !selectedCategory}
              >
                <i className="fas fa-upload me-2"></i>
                {uploading ? 'Uploading...' : `Upload ${files.length} Files`}
              </Button>
            </div>
          </div>
        </Col>
      </Row>

      {/* Alerts */}
      {error && (
        <Alert variant="danger" onClose={() => setError('')} dismissible>
          {error}
        </Alert>
      )}
      {success && (
        <Alert variant="success" onClose={() => setSuccess('')} dismissible>
          {success}
        </Alert>
      )}

      <Row>
        {/* Upload Settings */}
        <Col lg={4}>
          <Card className="mb-4">
            <Card.Header>
              <h5 className="mb-0">
                <i className="fas fa-cog me-2"></i>
                Upload Settings
              </h5>
            </Card.Header>
            <Card.Body>
              <Form.Group className="mb-3">
                <Form.Label>Category *</Form.Label>
                <Form.Select
                  value={selectedCategory || ''}
                  onChange={(e) => setSelectedCategory(Number(e.target.value) || null)}
                  required
                >
                  <option value="">Select a category</option>
                  {categories.map(category => (
                    <option key={category.id} value={category.id}>
                      {category.name}
                    </option>
                  ))}
                </Form.Select>
              </Form.Group>

              <Form.Group className="mb-3">
                <Form.Label>Photographer</Form.Label>
                <Form.Control
                  type="text"
                  value={globalSettings.photographer}
                  onChange={(e) => setGlobalSettings(prev => ({ ...prev, photographer: e.target.value }))}
                  placeholder="Photographer name"
                />
              </Form.Group>

              <Form.Group className="mb-3">
                <Form.Label>Location</Form.Label>
                <Form.Control
                  type="text"
                  value={globalSettings.location}
                  onChange={(e) => setGlobalSettings(prev => ({ ...prev, location: e.target.value }))}
                  placeholder="Photo location"
                />
              </Form.Group>

              <Form.Group className="mb-3">
                <Form.Label>Tags</Form.Label>
                <Form.Control
                  type="text"
                  value={globalSettings.tags}
                  onChange={(e) => setGlobalSettings(prev => ({ ...prev, tags: e.target.value }))}
                  placeholder="Comma-separated tags"
                />
              </Form.Group>

              <div className="mb-3">
                <Form.Check
                  type="checkbox"
                  label="Featured Image"
                  checked={globalSettings.is_featured}
                  onChange={(e) => setGlobalSettings(prev => ({ ...prev, is_featured: e.target.checked }))}
                />
                <Form.Check
                  type="checkbox"
                  label="Slider Image"
                  checked={globalSettings.is_slider}
                  onChange={(e) => setGlobalSettings(prev => ({ ...prev, is_slider: e.target.checked }))}
                />
                <Form.Check
                  type="checkbox"
                  label="Campus Image"
                  checked={globalSettings.is_campus}
                  onChange={(e) => setGlobalSettings(prev => ({ ...prev, is_campus: e.target.checked }))}
                />
              </div>
            </Card.Body>
          </Card>
        </Col>

        {/* Upload Area and File List */}
        <Col lg={8}>
          {/* Dropzone */}
          <Card className="mb-4">
            <Card.Body>
              <div
                {...getRootProps()}
                className={`border-2 border-dashed rounded p-5 text-center ${
                  isDragActive ? 'border-primary bg-light' : 'border-secondary'
                }`}
                style={{ cursor: 'pointer' }}
              >
                <input {...getInputProps()} />
                <i className="fas fa-cloud-upload-alt fa-3x text-muted mb-3"></i>
                <h5>
                  {isDragActive
                    ? 'Drop the files here...'
                    : 'Drag & drop images here, or click to select'}
                </h5>
                <p className="text-muted">
                  Supports: JPEG, PNG, GIF, WebP
                </p>
              </div>
            </Card.Body>
          </Card>

          {/* File List */}
          {files.length > 0 && (
            <Card>
              <Card.Header>
                <h5 className="mb-0">
                  <i className="fas fa-list me-2"></i>
                  Files to Upload ({files.length})
                </h5>
              </Card.Header>
              <Card.Body className="p-0">
                <div className="list-group list-group-flush">
                  {files.map(file => (
                    <div key={file.id} className="list-group-item">
                      <div className="d-flex align-items-center">
                        <img
                          src={file.preview}
                          alt={file.name}
                          className="rounded me-3"
                          style={{ width: '60px', height: '60px', objectFit: 'cover' }}
                        />
                        <div className="flex-grow-1">
                          <h6 className="mb-1">{file.name}</h6>
                          <small className="text-muted">
                            {(file.size / 1024 / 1024).toFixed(2)} MB
                          </small>
                          {file.status === 'uploading' && (
                            <ProgressBar 
                              now={file.progress} 
                              className="mt-2" 
                              style={{ height: '4px' }}
                            />
                          )}
                          {file.error && (
                            <div className="text-danger small mt-1">{file.error}</div>
                          )}
                        </div>
                        <div className="d-flex align-items-center gap-2">
                          <Badge bg={getStatusColor(file.status)}>
                            <i className={getStatusIcon(file.status)}></i>
                          </Badge>
                          <Button
                            variant="outline-danger"
                            size="sm"
                            onClick={() => removeFile(file.id)}
                            disabled={uploading}
                          >
                            <i className="fas fa-times"></i>
                          </Button>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </Card.Body>
            </Card>
          )}
        </Col>
      </Row>
    </Container>
  );
};

export default GalleryUpload;
