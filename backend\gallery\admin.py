from django.contrib import admin
from django.utils.html import format_html
from django.utils.safestring import mark_safe
from .models import GalleryCategory, GalleryImage, EventSlider


@admin.register(GalleryCategory)
class GalleryCategoryAdmin(admin.ModelAdmin):
    list_display = ['name', 'slug', 'image_count', 'color_preview', 'is_active', 'order']
    list_filter = ['is_active', 'created_at']
    search_fields = ['name', 'description']
    prepopulated_fields = {'slug': ('name',)}
    list_editable = ['is_active', 'order']
    ordering = ['order', 'name']

    def color_preview(self, obj):
        return format_html(
            '<div style="width: 30px; height: 20px; background-color: {}; border: 1px solid #ccc; border-radius: 3px;"></div>',
            obj.color
        )
    color_preview.short_description = 'Color'

    def image_count(self, obj):
        count = obj.image_count
        return format_html(
            '<span style="background: #e3f2fd; padding: 2px 8px; border-radius: 10px; font-size: 12px;">{}</span>',
            count
        )
    image_count.short_description = 'Images'


@admin.register(GalleryImage)
class GalleryImageAdmin(admin.ModelAdmin):
    list_display = ['title', 'category', 'image_preview', 'is_featured', 'is_slider', 'is_campus', 'is_active', 'order']
    list_filter = ['category', 'is_featured', 'is_slider', 'is_campus', 'is_active', 'created_at']
    search_fields = ['title', 'description', 'tags', 'photographer', 'location']
    list_editable = ['is_featured', 'is_slider', 'is_campus', 'is_active', 'order']
    readonly_fields = ['image_preview', 'thumbnail_preview']
    
    fieldsets = (
        ('Basic Information', {
            'fields': ('title', 'description', 'image', 'image_preview')
        }),
        ('Categorization', {
            'fields': ('category', 'tags')
        }),
        ('Display Options', {
            'fields': ('is_featured', 'is_slider', 'is_campus', 'is_active', 'order'),
            'description': 'Control where and how this image appears on the website'
        }),
        ('Metadata', {
            'fields': ('photographer', 'date_taken', 'location', 'alt_text'),
            'classes': ('collapse',)
        }),
        ('Thumbnail', {
            'fields': ('thumbnail_preview',),
            'classes': ('collapse',)
        })
    )

    def image_preview(self, obj):
        if obj.image:
            return format_html(
                '<img src="{}" style="max-width: 200px; max-height: 150px; border-radius: 5px;" />',
                obj.image.url
            )
        return "No image"
    image_preview.short_description = 'Preview'

    def thumbnail_preview(self, obj):
        if obj.thumbnail:
            return format_html(
                '<img src="{}" style="max-width: 150px; max-height: 100px; border-radius: 5px;" />',
                obj.thumbnail.url
            )
        return "No thumbnail"
    thumbnail_preview.short_description = 'Thumbnail'

    actions = ['make_featured', 'remove_featured', 'add_to_slider', 'remove_from_slider']

    def make_featured(self, request, queryset):
        queryset.update(is_featured=True)
        self.message_user(request, f"{queryset.count()} images marked as featured.")
    make_featured.short_description = "Mark selected images as featured"

    def remove_featured(self, request, queryset):
        queryset.update(is_featured=False)
        self.message_user(request, f"{queryset.count()} images removed from featured.")
    remove_featured.short_description = "Remove selected images from featured"

    def add_to_slider(self, request, queryset):
        queryset.update(is_slider=True)
        self.message_user(request, f"{queryset.count()} images added to slider.")
    add_to_slider.short_description = "Add selected images to slider"

    def remove_from_slider(self, request, queryset):
        queryset.update(is_slider=False)
        self.message_user(request, f"{queryset.count()} images removed from slider.")
    remove_from_slider.short_description = "Remove selected images from slider"


@admin.register(EventSlider)
class EventSliderAdmin(admin.ModelAdmin):
    list_display = ['event', 'display_title', 'background_preview', 'is_active', 'order']
    list_filter = ['is_active', 'created_at']
    search_fields = ['event__name', 'slider_title', 'slider_subtitle']
    list_editable = ['is_active', 'order']
    readonly_fields = ['background_preview']
    
    fieldsets = (
        ('Event', {
            'fields': ('event',)
        }),
        ('Slider Content', {
            'fields': ('slider_title', 'slider_subtitle', 'slider_description'),
            'description': 'Leave blank to use event details'
        }),
        ('Visual Design', {
            'fields': ('background_image', 'background_preview', 'background_color', 'background_color_end')
        }),
        ('Call to Action', {
            'fields': ('cta_text', 'cta_url'),
            'description': 'Customize the action button'
        }),
        ('Display Settings', {
            'fields': ('is_active', 'order')
        })
    )

    def background_preview(self, obj):
        if obj.background_image:
            return format_html(
                '<img src="{}" style="max-width: 200px; max-height: 100px; border-radius: 5px;" />',
                obj.background_image.url
            )
        else:
            return format_html(
                '<div style="width: 200px; height: 100px; background: {}; border-radius: 5px; display: flex; align-items: center; justify-content: center; color: white; font-size: 12px;">Gradient Preview</div>',
                obj.background_gradient
            )
    background_preview.short_description = 'Background Preview'
