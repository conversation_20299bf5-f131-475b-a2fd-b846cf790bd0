#!/usr/bin/env python
"""
Verify that the new SVG badges are working
"""
import os
import sys
import django

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'event_management.settings')
django.setup()

from badges.models import Badge
from participants.models import Participant

def verify_badges():
    """Verify the new SVG badges"""
    print("🔍 VERIFYING NEW SVG BADGES")
    print("=" * 50)
    
    try:
        # Get badge count
        total_badges = Badge.objects.count()
        print(f"📊 Total badges in database: {total_badges}")
        
        if total_badges == 0:
            print("❌ No badges found")
            return
        
        # Get latest badges
        recent_badges = Badge.objects.filter(is_generated=True).order_by('-generated_at')[:5]
        print(f"🎨 Recent generated badges: {len(recent_badges)}")
        
        for badge in recent_badges:
            print(f"\n👤 {badge.participant.full_name}")
            print(f"   📅 Generated: {badge.generated_at}")
            print(f"   🎫 Badge file: {badge.badge_image.name if badge.badge_image else 'None'}")
            
            if badge.badge_image:
                try:
                    # Try to get image dimensions
                    from PIL import Image
                    img = Image.open(badge.badge_image.path)
                    dimensions = img.size
                    print(f"   📏 Dimensions: {dimensions}")
                    
                    if dimensions == (600, 1200):
                        print("   ✅ CONFIRMED: Using new SVG template!")
                    else:
                        print(f"   ⚠️  Old dimensions: {dimensions}")
                        
                except Exception as e:
                    print(f"   ❌ Error reading image: {e}")
            else:
                print("   ❌ No badge image")
        
        # Check if we have the new SVG methods
        badge = Badge.objects.first()
        svg_methods = [
            '_add_svg_gradient_background',
            '_add_svg_ministry_logo',
            '_add_svg_participant_info',
            '_add_svg_qr_code',
            '_add_svg_sponsors',
            '_add_svg_footer'
        ]
        
        print(f"\n🔧 SVG Methods Check:")
        for method in svg_methods:
            if hasattr(badge, method):
                print(f"   ✅ {method}")
            else:
                print(f"   ❌ {method}")
        
        print(f"\n🎉 Verification complete!")
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    verify_badges()
