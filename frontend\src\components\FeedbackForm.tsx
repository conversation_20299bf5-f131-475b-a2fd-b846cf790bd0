import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON><PERSON>, <PERSON>, Col, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, Spin<PERSON>, ProgressBar } from 'react-bootstrap';
import { feedbackService, EventFeedback, Event } from '../services/api';
import { useToast } from '../contexts/ToastContext';

interface FeedbackFormProps {
  event: Event;
  participantId?: number;
  onSubmitSuccess?: (feedback: EventFeedback) => void;
  onCancel?: () => void;
  className?: string;
}

interface FeedbackFormData {
  // Participant information
  participant_name: string;
  participant_email: string;
  institution_name: string;
  position_title: string;
  
  // Overall experience
  overall_satisfaction: number;
  met_expectations: 'yes' | 'somewhat' | 'no';
  most_valuable_aspect: string;
  improvement_suggestions: string;
  
  // Sessions & content
  session_relevance: number;
  most_valuable_sessions: string;
  future_topics_suggestions: string;
  
  // Speakers & moderators
  speaker_quality: number;
  speaker_comments: string;
  
  // Logistics & organization
  venue_facilities: number;
  technical_setup: number;
  time_management: number;
  transportation_accessibility: number;
  pre_event_communication: number;
  logistics_comments: string;
  
  // Networking
  sufficient_networking: 'yes' | 'somewhat' | 'no';
  networking_improvements: string;
  
  // Future suggestions
  future_topics: string;
  additional_feedback: string;
  
  // Consent
  consent_given: boolean;
  is_anonymous: boolean;
}

const FeedbackForm: React.FC<FeedbackFormProps> = ({
  event,
  participantId,
  onSubmitSuccess,
  onCancel,
  className = ''
}) => {
  const { showToast } = useToast();
  const [formData, setFormData] = useState<FeedbackFormData>({
    participant_name: '',
    participant_email: '',
    institution_name: '',
    position_title: '',
    overall_satisfaction: 5,
    met_expectations: 'yes',
    most_valuable_aspect: '',
    improvement_suggestions: '',
    session_relevance: 5,
    most_valuable_sessions: '',
    future_topics_suggestions: '',
    speaker_quality: 5,
    speaker_comments: '',
    venue_facilities: 5,
    technical_setup: 5,
    time_management: 5,
    transportation_accessibility: 5,
    pre_event_communication: 5,
    logistics_comments: '',
    sufficient_networking: 'yes',
    networking_improvements: '',
    future_topics: '',
    additional_feedback: '',
    consent_given: false,
    is_anonymous: false,
  });
  
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [currentStep, setCurrentStep] = useState(1);
  const [uploadedFile, setUploadedFile] = useState<File | null>(null);
  const [existingFeedback, setExistingFeedback] = useState<EventFeedback | null>(null);

  const totalSteps = 6;

  useEffect(() => {
    checkExistingFeedback();
  }, [event.id, participantId]);

  const checkExistingFeedback = async () => {
    if (!participantId && !formData.participant_email) return;
    
    try {
      const response = await feedbackService.checkExistingFeedback(
        event.id,
        participantId,
        formData.participant_email
      );
      
      if (response.data.exists) {
        const feedback = await feedbackService.getFeedback(response.data.feedback_id);
        setExistingFeedback(feedback.data);
        showToast({
          type: 'info',
          title: 'Feedback Already Submitted',
          message: 'You have already submitted feedback for this event.'
        });
      }
    } catch (error) {
      console.error('Error checking existing feedback:', error);
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value, type } = e.target;
    
    if (type === 'checkbox') {
      const checked = (e.target as HTMLInputElement).checked;
      setFormData(prev => ({ ...prev, [name]: checked }));
    } else if (type === 'range' || name.includes('satisfaction') || name.includes('quality') || name.includes('facilities') || name.includes('setup') || name.includes('management') || name.includes('accessibility') || name.includes('communication') || name.includes('relevance')) {
      setFormData(prev => ({ ...prev, [name]: parseInt(value) }));
    } else {
      setFormData(prev => ({ ...prev, [name]: value }));
    }
  };

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0] || null;
    setUploadedFile(file);
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!formData.consent_given) {
      setError('Please provide consent to submit your feedback.');
      return;
    }

    if (!formData.most_valuable_aspect.trim()) {
      setError('Please provide information about the most valuable aspect of the event.');
      return;
    }

    setLoading(true);
    setError('');

    try {
      const submitData = new FormData();
      
      // Add event and participant data
      submitData.append('event', event.id.toString());
      if (participantId) {
        submitData.append('participant', participantId.toString());
      }
      
      // Add form data
      Object.entries(formData).forEach(([key, value]) => {
        if (value !== null && value !== undefined) {
          submitData.append(key, value.toString());
        }
      });
      
      // Add file if uploaded
      if (uploadedFile) {
        submitData.append('uploaded_file', uploadedFile);
      }

      const response = await feedbackService.createFeedback(submitData);
      
      showToast({
        type: 'success',
        title: 'Feedback Submitted',
        message: 'Thank you! Your feedback has been submitted successfully.'
      });
      
      if (onSubmitSuccess) {
        onSubmitSuccess(response.data);
      }
      
    } catch (error: any) {
      console.error('Error submitting feedback:', error);
      setError(error.response?.data?.detail || 'Failed to submit feedback. Please try again.');
      showToast({
        type: 'error',
        title: 'Submission Failed',
        message: 'Failed to submit feedback. Please try again.'
      });
    } finally {
      setLoading(false);
    }
  };

  const nextStep = () => {
    if (currentStep < totalSteps) {
      setCurrentStep(currentStep + 1);
    }
  };

  const prevStep = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1);
    }
  };

  const renderStarRating = (name: string, value: number, label: string) => (
    <Form.Group className="mb-3">
      <Form.Label>{label} *</Form.Label>
      <div className="d-flex align-items-center">
        <Form.Range
          name={name}
          min={1}
          max={5}
          value={value}
          onChange={handleInputChange}
          className="me-3"
        />
        <div className="star-display">
          {'⭐'.repeat(value)} ({value}/5)
        </div>
      </div>
    </Form.Group>
  );

  if (existingFeedback) {
    return (
      <Container className={className}>
        <Row className="justify-content-center">
          <Col lg={8}>
            <Card className="shadow-sm">
              <Card.Body className="text-center p-5">
                <div className="mb-4">
                  <i className="fas fa-check-circle text-success" style={{ fontSize: '4rem' }}></i>
                </div>
                <h3 className="text-success mb-3">Feedback Already Submitted</h3>
                <p className="text-muted mb-4">
                  You have already provided feedback for <strong>{event.name}</strong>.
                  Thank you for your valuable input!
                </p>
                <div className="d-flex justify-content-center gap-3">
                  {onCancel && (
                    <Button variant="outline-primary" onClick={onCancel}>
                      <i className="fas fa-arrow-left me-2"></i>
                      Back to Event
                    </Button>
                  )}
                </div>
              </Card.Body>
            </Card>
          </Col>
        </Row>
      </Container>
    );
  }

  return (
    <Container className={className}>
      <Row className="justify-content-center">
        <Col lg={10}>
          <Card className="shadow-sm">
            <Card.Header className="bg-primary text-white">
              <div className="d-flex justify-content-between align-items-center">
                <div>
                  <h4 className="mb-1">
                    <i className="fas fa-comment-alt me-2"></i>
                    Conference Feedback Form
                  </h4>
                  <p className="mb-0">Event: {event.name}</p>
                </div>
                <div className="text-end">
                  <small>Step {currentStep} of {totalSteps}</small>
                </div>
              </div>
              <ProgressBar 
                now={(currentStep / totalSteps) * 100} 
                className="mt-3"
                style={{ height: '4px' }}
              />
            </Card.Header>
            
            <Card.Body className="p-4">
              {error && (
                <Alert variant="danger" className="mb-4">
                  <i className="fas fa-exclamation-triangle me-2"></i>
                  {error}
                </Alert>
              )}

              <Form onSubmit={handleSubmit}>
                {/* Step 1: Introduction & Participant Information */}
                {currentStep === 1 && (
                  <div>
                    <div className="mb-4 p-4 bg-light rounded">
                      <h5 className="text-primary mb-3">
                        <i className="fas fa-info-circle me-2"></i>
                        Introduction
                      </h5>
                      <p className="mb-2">
                        Thank you for attending the <strong>{event.name}</strong>. 
                        Your feedback is essential to help us improve future events.
                      </p>
                      <p className="mb-2">
                        <strong>Dates:</strong> {new Date(event.start_date).toLocaleDateString()} - {new Date(event.end_date).toLocaleDateString()}
                      </p>
                      <p className="mb-2">
                        <strong>Location:</strong> {event.location}, {event.city}, {event.country}
                      </p>
                      <p className="mb-0 text-muted">
                        This survey will take <strong>3–5 minutes</strong> to complete. 
                        All responses are confidential unless you opt to share your contact details.
                      </p>
                    </div>

                    <h5 className="text-primary mb-3">
                      <i className="fas fa-user me-2"></i>
                      Participant Information (Optional)
                    </h5>
                    
                    <Row>
                      <Col md={6}>
                        <Form.Group className="mb-3">
                          <Form.Label>Name</Form.Label>
                          <Form.Control
                            type="text"
                            name="participant_name"
                            value={formData.participant_name}
                            onChange={handleInputChange}
                            placeholder="Your full name"
                          />
                        </Form.Group>
                      </Col>
                      <Col md={6}>
                        <Form.Group className="mb-3">
                          <Form.Label>Email (for follow-up or event updates)</Form.Label>
                          <Form.Control
                            type="email"
                            name="participant_email"
                            value={formData.participant_email}
                            onChange={handleInputChange}
                            placeholder="<EMAIL>"
                          />
                        </Form.Group>
                      </Col>
                    </Row>
                    
                    <Row>
                      <Col md={6}>
                        <Form.Group className="mb-3">
                          <Form.Label>Institution/Organization</Form.Label>
                          <Form.Control
                            type="text"
                            name="institution_name"
                            value={formData.institution_name}
                            onChange={handleInputChange}
                            placeholder="Your institution or organization"
                          />
                        </Form.Group>
                      </Col>
                      <Col md={6}>
                        <Form.Group className="mb-3">
                          <Form.Label>Position/Title</Form.Label>
                          <Form.Control
                            type="text"
                            name="position_title"
                            value={formData.position_title}
                            onChange={handleInputChange}
                            placeholder="Your position or title"
                          />
                        </Form.Group>
                      </Col>
                    </Row>

                    <Form.Group className="mb-3">
                      <Form.Check
                        type="checkbox"
                        name="is_anonymous"
                        checked={formData.is_anonymous}
                        onChange={handleInputChange}
                        label="Submit this feedback anonymously"
                      />
                    </Form.Group>
                  </div>
                )}

                {/* Step 2: Overall Experience */}
                {currentStep === 2 && (
                  <div>
                    <h5 className="text-primary mb-4">
                      <i className="fas fa-star me-2"></i>
                      Overall Experience
                    </h5>

                    {renderStarRating('overall_satisfaction', formData.overall_satisfaction, 'How would you rate your overall satisfaction with the conference?')}

                    <Form.Group className="mb-3">
                      <Form.Label>Did the conference meet your expectations? *</Form.Label>
                      <div>
                        {['yes', 'somewhat', 'no'].map((option) => (
                          <Form.Check
                            key={option}
                            type="radio"
                            name="met_expectations"
                            value={option}
                            checked={formData.met_expectations === option}
                            onChange={handleInputChange}
                            label={option === 'yes' ? '✅ Yes' : option === 'somewhat' ? '🤔 Somewhat' : '❌ No'}
                            className="mb-2"
                          />
                        ))}
                      </div>
                    </Form.Group>

                    <Form.Group className="mb-3">
                      <Form.Label>What was the most valuable aspect of the event? *</Form.Label>
                      <Form.Control
                        as="textarea"
                        rows={3}
                        name="most_valuable_aspect"
                        value={formData.most_valuable_aspect}
                        onChange={handleInputChange}
                        placeholder="Please describe what you found most valuable about this conference..."
                        required
                      />
                    </Form.Group>

                    <Form.Group className="mb-3">
                      <Form.Label>What could be improved for future conferences?</Form.Label>
                      <Form.Control
                        as="textarea"
                        rows={3}
                        name="improvement_suggestions"
                        value={formData.improvement_suggestions}
                        onChange={handleInputChange}
                        placeholder="Please share your suggestions for improvement..."
                      />
                    </Form.Group>
                  </div>
                )}

                {/* Step 3: Sessions & Content */}
                {currentStep === 3 && (
                  <div>
                    <h5 className="text-primary mb-4">
                      <i className="fas fa-chalkboard-teacher me-2"></i>
                      Sessions & Content
                    </h5>

                    {renderStarRating('session_relevance', formData.session_relevance, 'How relevant were the sessions to your professional interests?')}

                    <Form.Group className="mb-3">
                      <Form.Label>Which session(s) did you find most valuable?</Form.Label>
                      <Form.Control
                        as="textarea"
                        rows={3}
                        name="most_valuable_sessions"
                        value={formData.most_valuable_sessions}
                        onChange={handleInputChange}
                        placeholder="Please list the sessions that were most valuable to you..."
                      />
                    </Form.Group>

                    <Form.Group className="mb-3">
                      <Form.Label>Suggestions for future topics or session formats:</Form.Label>
                      <Form.Control
                        as="textarea"
                        rows={3}
                        name="future_topics_suggestions"
                        value={formData.future_topics_suggestions}
                        onChange={handleInputChange}
                        placeholder="What topics or formats would you like to see in future events?"
                      />
                    </Form.Group>
                  </div>
                )}

                {/* Step 4: Speakers & Moderators */}
                {currentStep === 4 && (
                  <div>
                    <h5 className="text-primary mb-4">
                      <i className="fas fa-microphone me-2"></i>
                      Speakers & Moderators
                    </h5>

                    {renderStarRating('speaker_quality', formData.speaker_quality, 'How would you rate the quality of speakers/moderators?')}

                    <Form.Group className="mb-3">
                      <Form.Label>Comments on speaker expertise, engagement, or delivery:</Form.Label>
                      <Form.Control
                        as="textarea"
                        rows={4}
                        name="speaker_comments"
                        value={formData.speaker_comments}
                        onChange={handleInputChange}
                        placeholder="Please share your thoughts on the speakers and moderators..."
                      />
                    </Form.Group>
                  </div>
                )}

                {/* Step 5: Logistics & Organization */}
                {currentStep === 5 && (
                  <div>
                    <h5 className="text-primary mb-4">
                      <i className="fas fa-cogs me-2"></i>
                      Logistics & Organization
                    </h5>

                    <p className="text-muted mb-4">Please rate the following aspects (1-5 scale):</p>

                    {renderStarRating('venue_facilities', formData.venue_facilities, 'Venue & Facilities')}
                    {renderStarRating('technical_setup', formData.technical_setup, 'Technical Setup (if hybrid/virtual)')}
                    {renderStarRating('time_management', formData.time_management, 'Time Management & Scheduling')}
                    {renderStarRating('transportation_accessibility', formData.transportation_accessibility, 'Transportation & Accessibility')}
                    {renderStarRating('pre_event_communication', formData.pre_event_communication, 'Pre-event Communication')}

                    <Form.Group className="mb-3">
                      <Form.Label>Additional comments on logistics:</Form.Label>
                      <Form.Control
                        as="textarea"
                        rows={3}
                        name="logistics_comments"
                        value={formData.logistics_comments}
                        onChange={handleInputChange}
                        placeholder="Any additional feedback on the event organization and logistics..."
                      />
                    </Form.Group>
                  </div>
                )}

                {/* Step 6: Networking & Final Thoughts */}
                {currentStep === 6 && (
                  <div>
                    <h5 className="text-primary mb-4">
                      <i className="fas fa-users me-2"></i>
                      Networking & Future Suggestions
                    </h5>

                    <Form.Group className="mb-3">
                      <Form.Label>Did you have sufficient networking opportunities?</Form.Label>
                      <div>
                        {['yes', 'somewhat', 'no'].map((option) => (
                          <Form.Check
                            key={option}
                            type="radio"
                            name="sufficient_networking"
                            value={option}
                            checked={formData.sufficient_networking === option}
                            onChange={handleInputChange}
                            label={option === 'yes' ? '✅ Yes' : option === 'somewhat' ? '🤔 Somewhat' : '❌ No'}
                            className="mb-2"
                          />
                        ))}
                      </div>
                    </Form.Group>

                    <Form.Group className="mb-3">
                      <Form.Label>How could networking be improved in future events?</Form.Label>
                      <Form.Control
                        as="textarea"
                        rows={3}
                        name="networking_improvements"
                        value={formData.networking_improvements}
                        onChange={handleInputChange}
                        placeholder="Suggestions for improving networking opportunities..."
                      />
                    </Form.Group>

                    <Form.Group className="mb-3">
                      <Form.Label>What topics would you like to see in future conferences?</Form.Label>
                      <Form.Control
                        as="textarea"
                        rows={3}
                        name="future_topics"
                        value={formData.future_topics}
                        onChange={handleInputChange}
                        placeholder="Topics or themes for future events..."
                      />
                    </Form.Group>

                    <Form.Group className="mb-3">
                      <Form.Label>Any other feedback or recommendations?</Form.Label>
                      <Form.Control
                        as="textarea"
                        rows={3}
                        name="additional_feedback"
                        value={formData.additional_feedback}
                        onChange={handleInputChange}
                        placeholder="Any additional thoughts, suggestions, or feedback..."
                      />
                    </Form.Group>

                    <Form.Group className="mb-3">
                      <Form.Label>Optional: Upload relevant materials (e.g., session notes, photos)</Form.Label>
                      <Form.Control
                        type="file"
                        onChange={handleFileChange}
                        accept=".pdf,.doc,.docx,.jpg,.jpeg,.png,.txt"
                      />
                      <Form.Text className="text-muted">
                        Accepted formats: PDF, DOC, DOCX, JPG, PNG, TXT (Max 10MB)
                      </Form.Text>
                    </Form.Group>

                    <div className="border rounded p-3 bg-light">
                      <h6 className="text-primary mb-3">Consent & Submission</h6>
                      <Form.Group className="mb-0">
                        <Form.Check
                          type="checkbox"
                          name="consent_given"
                          checked={formData.consent_given}
                          onChange={handleInputChange}
                          label="I consent to my feedback being used for event improvement and reporting."
                          required
                        />
                      </Form.Group>
                    </div>

                    <div className="mt-4 p-3 bg-success bg-opacity-10 border border-success rounded">
                      <h6 className="text-success mb-2">
                        <i className="fas fa-heart me-2"></i>
                        Thank You!
                      </h6>
                      <p className="mb-0 text-success">
                        Your feedback will help shape future conferences and strengthen higher education in Ethiopia.
                        For further inquiries, contact the event organizers.
                      </p>
                    </div>
                  </div>
                )}

                {/* Navigation buttons */}
                <div className="d-flex justify-content-between mt-4">
                  <div>
                    {currentStep > 1 && (
                      <Button variant="outline-secondary" onClick={prevStep}>
                        <i className="fas fa-arrow-left me-2"></i>
                        Previous
                      </Button>
                    )}
                  </div>
                  
                  <div className="d-flex gap-2">
                    {onCancel && (
                      <Button variant="outline-danger" onClick={onCancel}>
                        <i className="fas fa-times me-2"></i>
                        Cancel
                      </Button>
                    )}
                    
                    {currentStep < totalSteps ? (
                      <Button variant="primary" onClick={nextStep}>
                        Next
                        <i className="fas fa-arrow-right ms-2"></i>
                      </Button>
                    ) : (
                      <Button 
                        type="submit" 
                        variant="success" 
                        disabled={loading || !formData.consent_given}
                      >
                        {loading ? (
                          <>
                            <Spinner size="sm" className="me-2" />
                            Submitting...
                          </>
                        ) : (
                          <>
                            <i className="fas fa-paper-plane me-2"></i>
                            Submit Feedback
                          </>
                        )}
                      </Button>
                    )}
                  </div>
                </div>
              </Form>
            </Card.Body>
          </Card>
        </Col>
      </Row>
    </Container>
  );
};

export default FeedbackForm;
