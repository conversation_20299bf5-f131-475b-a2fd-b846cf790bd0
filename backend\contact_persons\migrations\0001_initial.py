# Generated by Django 4.2.7 on 2025-07-27 15:23

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('events', '0004_auto_20250727_0048'),
    ]

    operations = [
        migrations.CreateModel(
            name='ContactPerson',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('first_name', models.CharField(max_length=100)),
                ('middle_name', models.CharField(blank=True, max_length=100)),
                ('last_name', models.CharField(max_length=100)),
                ('phone', models.Char<PERSON>ield(max_length=20)),
                ('email', models.EmailField(max_length=254)),
                ('photo', models.ImageField(blank=True, null=True, upload_to='contact_person_photos/')),
                ('position', models.CharField(blank=True, help_text='Job title or position', max_length=200)),
                ('organization', models.Char<PERSON>ield(blank=True, help_text='Organization or company', max_length=200)),
                ('is_available', models.BooleanField(default=True)),
                ('notes', models.TextField(blank=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('event', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='contact_persons', to='events.event')),
            ],
            options={
                'verbose_name': 'Contact Person',
                'verbose_name_plural': 'Contact Persons',
                'ordering': ['first_name', 'last_name'],
            },
        ),
    ]
