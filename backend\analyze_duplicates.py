#!/usr/bin/env python3

import os
import sys
import django

# Add the project root to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'event_management.settings')
django.setup()

from participants.models import Participant

def analyze_duplicates():
    """Analyze duplicate participants and their emails"""
    
    print("=== ANALYZING DUPLICATE PARTICIPANTS ===")
    
    # Test some names that had issues
    test_names = [
        ('<PERSON><PERSON>', '<PERSON>'),
        ('<PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>'), 
        ('<PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>'),
        ('<PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON>'),
        ('<PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>'),
        ('<PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>')
    ]
    
    for first_name, last_name in test_names:
        participants = Participant.objects.filter(first_name=first_name, last_name=last_name)
        
        if participants.count() > 1:
            print(f"\n{first_name} {last_name} - {participants.count()} duplicates:")
            for p in participants:
                print(f"  ID: {p.id}, Email: {p.email}, Status: {p.status}")
        elif participants.count() == 1:
            p = participants.first()
            print(f"\n{first_name} {last_name} - Single participant:")
            print(f"  ID: {p.id}, Email: {p.email}, Status: {p.status}")
        else:
            print(f"\n{first_name} {last_name} - Not found")
    
    # Check for email duplicates across all participants
    print(f"\n=== EMAIL DUPLICATE ANALYSIS ===")
    from django.db.models import Count
    
    email_duplicates = Participant.objects.values('email').annotate(
        count=Count('email')
    ).filter(count__gt=1)
    
    print(f"Found {email_duplicates.count()} emails with duplicates:")
    for item in email_duplicates[:10]:  # Show first 10
        email = item['email']
        count = item['count']
        participants = Participant.objects.filter(email=email)
        print(f"\nEmail: {email} ({count} participants)")
        for p in participants:
            print(f"  ID: {p.id}, Name: {p.first_name} {p.last_name}, Status: {p.status}")

if __name__ == "__main__":
    analyze_duplicates()
