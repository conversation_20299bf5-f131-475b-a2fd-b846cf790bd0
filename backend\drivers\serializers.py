from rest_framework import serializers
from .models import Driver, DriverAssignment


class DriverSerializer(serializers.ModelSerializer):
    event_name = serializers.CharField(source='event.name', read_only=True)

    class Meta:
        model = Driver
        fields = '__all__'


class DriverAssignmentSerializer(serializers.ModelSerializer):
    driver_name = serializers.CharField(source='driver.name', read_only=True)
    driver_phone = serializers.CharField(source='driver.phone', read_only=True)
    driver_car_plate = serializers.CharField(source='driver.car_plate', read_only=True)
    participant_name = serializers.CharField(source='participant.full_name', read_only=True)
    participant_phone = serializers.CharField(source='participant.phone', read_only=True)

    class Meta:
        model = DriverAssignment
        fields = '__all__'
