from django.core.management.base import BaseCommand
from events.models import EmailTemplate


class Command(BaseCommand):
    help = 'Create default email templates'
    
    def handle(self, *args, **options):
        templates = [
            {
                'name': 'Registration Confirmation',
                'template_type': 'registration_confirmation',
                'subject': '✅ Registration Confirmed - {event_name}',
                'html_content': '''
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Registration Confirmed</title>
    <style>
        body {{ font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; margin: 0; padding: 0; background-color: #f8f9fa; }}
        .container {{ max-width: 600px; margin: 0 auto; background-color: #ffffff; }}
        .header {{ background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%); color: white; padding: 30px; text-align: center; }}
        .header h1 {{ margin: 0; font-size: 28px; font-weight: 300; }}
        .header .subtitle {{ margin: 10px 0 0 0; opacity: 0.9; font-size: 16px; }}
        .content {{ padding: 40px 30px; }}
        .welcome {{ font-size: 24px; color: #1e3c72; margin-bottom: 20px; font-weight: 300; }}
        .highlight-box {{ background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 25px; border-radius: 10px; margin: 25px 0; }}
        .highlight-box h3 {{ margin: 0 0 15px 0; font-size: 20px; }}
        .detail-row {{ margin: 8px 0; }}
        .detail-label {{ font-weight: 600; }}
        .success-badge {{ background: #28a745; color: white; padding: 8px 16px; border-radius: 20px; display: inline-block; margin: 20px 0; font-weight: 600; }}
        .next-steps {{ background: #f8f9fa; padding: 20px; border-radius: 8px; margin: 25px 0; border-left: 4px solid #1e3c72; }}
        .footer {{ background: #f8f9fa; padding: 30px; text-align: center; color: #6c757d; }}
        .contact-info {{ margin: 20px 0; }}
        .btn {{ background: #1e3c72; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; display: inline-block; margin: 15px 0; }}
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🇪🇹 University of Gondar</h1>
            <div class="subtitle">Excellence in Education & Research</div>
        </div>

        <div class="content">
            <div class="success-badge">✅ Registration Confirmed</div>
            <div class="welcome">Welcome, {participant_name}!</div>

            <p>Thank you for registering for <strong>{event_name}</strong>. We're excited to have you join us for this important event.</p>

            <div class="highlight-box">
                <h3>📅 Event Details</h3>
                <div class="detail-row"><span class="detail-label">Event:</span> {event_name}</div>
                <div class="detail-row"><span class="detail-label">Date & Time:</span> {event_date}</div>
                <div class="detail-row"><span class="detail-label">Location:</span> {event_location}</div>
                <div class="detail-row"><span class="detail-label">Registration Date:</span> {registration_date}</div>
            </div>

            <div class="next-steps">
                <h3>📋 What's Next?</h3>
                <ul>
                    <li>You will receive your event badge via email within 24 hours</li>
                    <li>Event schedule and detailed information will be sent soon</li>
                    <li>Please arrive 15 minutes early for check-in</li>
                    <li>Bring a valid ID and your printed badge</li>
                </ul>
            </div>

            <p>If you have any questions or need assistance, please don't hesitate to contact our events team.</p>
        </div>

        <div class="footer">
            <div class="contact-info">
                <strong>University of Gondar Events Team</strong><br>
                📧 <EMAIL> | 📞 +251-58-114-1240<br>
                🌐 www.uog.edu.et
            </div>

            <div class="developers" style="margin-top: 20px; padding: 20px; background: #ffffff; border-radius: 8px;">
                <h4 style="margin: 0 0 15px 0; color: #495057;">Developed by:</h4>
                <div style="display: flex; justify-content: center; gap: 20px; flex-wrap: wrap;">
                    <a href="https://www.linkedin.com/in/tewodros-abebaw-chekol/" target="_blank" style="color: #0077b5; text-decoration: none; font-weight: 500;">Tewodros Abebaw</a>
                    <a href="https://www.linkedin.com/in/aragaw-mebratu-a3096514a/" target="_blank" style="color: #0077b5; text-decoration: none; font-weight: 500;">Aragaw Mebratu</a>
                    <a href="https://www.linkedin.com/in/meseret-teshale-bb24b767/" target="_blank" style="color: #0077b5; text-decoration: none; font-weight: 500;">Meseret Teshale</a>
                </div>
            </div>

            <p style="margin-top: 20px; font-size: 12px;">
                © 2025 University of Gondar. All rights reserved.<br>
                This email was sent to {participant_email}. If you have any concerns, please contact us immediately.
            </p>
        </div>
    </div>
</body>
</html>
                ''',
                'text_content': '''
✅ REGISTRATION CONFIRMED

Welcome {participant_name}!

Thank you for registering for {event_name}. We're excited to have you join us for this important event.

📅 EVENT DETAILS:
• Event: {event_name}
• Date & Time: {event_date}
• Location: {event_location}
• Registration Date: {registration_date}

📋 WHAT'S NEXT?
• You will receive your event badge via email within 24 hours
• Event schedule and detailed information will be sent soon
• Please arrive 15 minutes early for check-in
• Bring a valid ID and your printed badge

If you have any questions or need assistance, please contact our events team.

---
University of Gondar Events Team
📧 <EMAIL> | 📞 +251-58-114-1240
🌐 www.uog.edu.et

Developed by:
• Tewodros Abebaw (https://www.linkedin.com/in/tewodros-abebaw-chekol/)
• Aragaw Mebratu (https://www.linkedin.com/in/aragaw-mebratu-a3096514a/)
• Meseret Teshale (https://www.linkedin.com/in/meseret-teshale-bb24b767/)

© 2025 University of Gondar. All rights reserved.
This email was sent to {participant_email}. If you have any concerns, please contact us immediately.
                '''
            },
            {
                'name': 'Event Approval',
                'template_type': 'event_approval',
                'subject': '🎉 Registration Approved - {event_name}',
                'html_content': '''
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Registration Approved</title>
    <style>
        body {{ font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; margin: 0; padding: 0; background-color: #f8f9fa; }}
        .container {{ max-width: 600px; margin: 0 auto; background-color: #ffffff; }}
        .header {{ background: linear-gradient(135deg, #28a745 0%, #20c997 100%); color: white; padding: 30px; text-align: center; }}
        .header h1 {{ margin: 0; font-size: 28px; font-weight: 300; }}
        .header .subtitle {{ margin: 10px 0 0 0; opacity: 0.9; font-size: 16px; }}
        .content {{ padding: 40px 30px; }}
        .approval-badge {{ background: #28a745; color: white; padding: 12px 24px; border-radius: 25px; display: inline-block; margin: 20px 0; font-weight: 600; text-align: center; }}
        .welcome-msg {{ font-size: 24px; color: #28a745; margin-bottom: 20px; font-weight: 300; }}
        .event-info {{ background: #f8f9fa; padding: 20px; border-radius: 8px; margin: 25px 0; border-left: 4px solid #28a745; }}
        .event-info h3 {{ margin: 0 0 15px 0; color: #28a745; }}
        .detail-row {{ margin: 8px 0; }}
        .detail-label {{ font-weight: 600; }}
        .hotel-section {{ background: linear-gradient(135deg, #007bff 0%, #0056b3 100%); color: white; padding: 25px; border-radius: 10px; margin: 25px 0; }}
        .hotel-section h3 {{ margin: 0 0 15px 0; font-size: 20px; }}
        .contact-card {{ background: rgba(255,255,255,0.1); padding: 15px; border-radius: 8px; margin: 10px 0; }}
        .footer {{ background: #f8f9fa; padding: 30px; text-align: center; color: #6c757d; }}
        .developers {{ margin-top: 20px; padding: 20px; background: #ffffff; border-radius: 8px; }}
        .developers h4 {{ margin: 0 0 15px 0; color: #495057; }}
        .dev-links {{ display: flex; justify-content: center; gap: 20px; flex-wrap: wrap; }}
        .dev-links a {{ color: #0077b5; text-decoration: none; font-weight: 500; }}
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🇪🇹 University of Gondar</h1>
            <div class="subtitle">Excellence in Education & Research</div>
        </div>

        <div class="content">
            <div class="approval-badge">✅ Registration Approved!</div>
            <div class="welcome-msg">Congratulations {participant_name}!</div>

            <p>We are pleased to inform you that your registration for <strong>{event_name}</strong> has been approved. Below you will find all the essential information for your participation.</p>

            <div class="event-info">
                <h3>📅 Event Information</h3>
                <div class="detail-row"><span class="detail-label">Event Name:</span> {event_name}</div>
                <div class="detail-row"><span class="detail-label">Start Date & Time:</span> {event_start_date}</div>
                <div class="detail-row"><span class="detail-label">End Date & Time:</span> {event_end_date}</div>
                <div class="detail-row"><span class="detail-label">Location:</span> {event_location}</div>
                <div class="detail-row"><span class="detail-label">Event Description:</span> {event_description}</div>
            </div>

            <div class="hotel-section">
                <h3>🏨 Your Hotel Assignment</h3>
                <div class="contact-card">
                    <div class="detail-row"><span class="detail-label">Hotel Name:</span> {hotel_name}</div>
                    <div class="detail-row"><span class="detail-label">Address:</span> {hotel_address}</div>
                    <div class="detail-row"><span class="detail-label">Phone:</span> {hotel_phone}</div>
                    <div class="detail-row"><span class="detail-label">Contact Person:</span> {hotel_contact_person}</div>
                    <div class="detail-row"><span class="detail-label">Email:</span> {hotel_email}</div>
                </div>

                <h3>🚗 Driver Contact Information</h3>
                <div class="contact-card">
                    <div class="detail-row"><span class="detail-label">Driver Name:</span> {driver_name}</div>
                    <div class="detail-row"><span class="detail-label">Phone:</span> {driver_phone}</div>
                    <div class="detail-row"><span class="detail-label">Vehicle:</span> {driver_vehicle}</div>
                    <div class="detail-row"><span class="detail-label">License Plate:</span> {driver_license_plate}</div>
                </div>
            </div>

            <p>Your event badge and detailed schedule will be sent to you separately. Please keep this information handy for your trip.</p>
        </div>

        <div class="footer">
            <div class="contact-info">
                <strong>University of Gondar Events Team</strong><br>
                📧 <EMAIL> | 📞 +251-58-114-1240<br>
                🌐 www.uog.edu.et
            </div>

            <div class="developers">
                <h4>Developed by:</h4>
                <div class="dev-links">
                    <a href="https://www.linkedin.com/in/tewodros-abebaw-chekol/" target="_blank">Tewodros Abebaw</a>
                    <a href="https://www.linkedin.com/in/aragaw-mebratu-a3096514a/" target="_blank">Aragaw Mebratu</a>
                    <a href="https://www.linkedin.com/in/meseret-teshale-bb24b767/" target="_blank">Meseret Teshale</a>
                </div>
            </div>

            <p style="margin-top: 20px; font-size: 12px;">
                © 2025 University of Gondar. All rights reserved.<br>
                This email was sent to {participant_email}. If you have any concerns, please contact us immediately.
            </p>
        </div>
    </div>
</body>
</html>
                ''',
                'text_content': '''
🎉 REGISTRATION APPROVED!

Congratulations {participant_name}!

We are pleased to inform you that your registration for {event_name} has been approved. Below you will find all the essential information for your participation.

📅 EVENT INFORMATION:
• Event Name: {event_name}
• Start Date & Time: {event_start_date}
• End Date & Time: {event_end_date}
• Location: {event_location}
• Event Description: {event_description}

🏨 YOUR HOTEL ASSIGNMENT:
• Hotel Name: {hotel_name}
• Address: {hotel_address}
• Phone: {hotel_phone}
• Contact Person: {hotel_contact_person}
• Email: {hotel_email}

🚗 DRIVER CONTACT INFORMATION:
• Driver Name: {driver_name}
• Phone: {driver_phone}
• Vehicle: {driver_vehicle}
• License Plate: {driver_license_plate}

Your event badge and detailed schedule will be sent to you separately. Please keep this information handy for your trip.

---
University of Gondar Events Team
📧 <EMAIL> | 📞 +251-58-114-1240
🌐 www.uog.edu.et

Developed by:
• Tewodros Abebaw (https://www.linkedin.com/in/tewodros-abebaw-chekol/)
• Aragaw Mebratu (https://www.linkedin.com/in/aragaw-mebratu-a3096514a/)
• Meseret Teshale (https://www.linkedin.com/in/meseret-teshale-bb24b767/)

© 2025 University of Gondar. All rights reserved.
This email was sent to {participant_email}. If you have any concerns, please contact us immediately.
                '''
            },
            {
                'name': 'Event Details',
                'template_type': 'event_details',
                'subject': 'Event Details - {event_name}',
                'html_content': '''
                <h2>Event Details for {participant_name}</h2>
                <p>Here are the complete details for <strong>{event_name}</strong>:</p>
                
                <div class="highlight-box">
                    <h3>{event_name}</h3>
                    <p>{event_description}</p>
                    <p><strong>Start:</strong> {event_start_date}</p>
                    <p><strong>End:</strong> {event_end_date}</p>
                    <p><strong>Location:</strong> {event_location}</p>
                </div>
                
                <h3>Organizer Information:</h3>
                <p><strong>Name:</strong> {organizer_name}</p>
                <p><strong>Email:</strong> {organizer_email}</p>
                <p><strong>Phone:</strong> {organizer_phone}</p>
                
                <p>We look forward to seeing you at the event!</p>
                
                <p>Best regards,<br>University of Gondar Events Team</p>
                ''',
                'text_content': '''
                Event Details for {participant_name}
                
                {event_name}
                {event_description}
                
                Start: {event_start_date}
                End: {event_end_date}
                Location: {event_location}
                
                Organizer: {organizer_name}
                Email: {organizer_email}
                Phone: {organizer_phone}
                
                Best regards,
                University of Gondar Events Team
                '''
            },
            {
                'name': 'Badge Notification',
                'template_type': 'badge_notification',
                'subject': '🎫 Your Event Badge is Ready - {event_name}',
                'html_content': '''
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Your Event Badge</title>
    <style>
        body {{ font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; margin: 0; padding: 0; background-color: #f8f9fa; }}
        .container {{ max-width: 600px; margin: 0 auto; background-color: #ffffff; }}
        .header {{ background: linear-gradient(135deg, #28a745 0%, #20c997 100%); color: white; padding: 30px; text-align: center; }}
        .header h1 {{ margin: 0; font-size: 28px; font-weight: 300; }}
        .header .subtitle {{ margin: 10px 0 0 0; opacity: 0.9; font-size: 16px; }}
        .content {{ padding: 40px 30px; }}
        .badge-ready {{ font-size: 24px; color: #28a745; margin-bottom: 20px; font-weight: 300; text-align: center; }}
        .badge-icon {{ font-size: 48px; text-align: center; margin: 20px 0; }}
        .instructions-box {{ background: linear-gradient(135deg, #fd7e14 0%, #e83e8c 100%); color: white; padding: 25px; border-radius: 10px; margin: 25px 0; }}
        .instructions-box h3 {{ margin: 0 0 15px 0; font-size: 20px; }}
        .instructions-box ul {{ margin: 10px 0; padding-left: 20px; }}
        .instructions-box li {{ margin: 8px 0; }}
        .important-note {{ background: #fff3cd; border: 1px solid #ffeaa7; padding: 20px; border-radius: 8px; margin: 25px 0; }}
        .footer {{ background: #f8f9fa; padding: 30px; text-align: center; color: #6c757d; }}
        .contact-info {{ margin: 20px 0; }}
        .attachment-note {{ background: #d1ecf1; border: 1px solid #bee5eb; padding: 15px; border-radius: 6px; margin: 20px 0; }}
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🇪🇹 University of Gondar</h1>
            <div class="subtitle">Excellence in Education & Research</div>
        </div>

        <div class="content">
            <div class="badge-icon">🎫</div>
            <div class="badge-ready">Your Event Badge is Ready!</div>

            <p>Dear <strong>{participant_name}</strong>,</p>

            <p>Great news! Your personalized badge for <strong>{event_name}</strong> has been generated and is attached to this email.</p>

            <div class="attachment-note">
                <strong>📎 Attachment:</strong> Your event badge is attached as a PDF file. Please download and print it before the event.
            </div>

            <div class="instructions-box">
                <h3>📋 Important Badge Instructions</h3>
                <ul>
                    <li><strong>Print Quality:</strong> Use high-quality paper (preferably cardstock) for durability</li>
                    <li><strong>Bring to Event:</strong> Your badge is required for entry and identification</li>
                    <li><strong>Keep Visible:</strong> Wear your badge prominently throughout the event</li>
                    <li><strong>QR Code:</strong> Your badge contains a unique QR code for quick check-in</li>
                    <li><strong>Backup:</strong> Save a digital copy on your phone as backup</li>
                </ul>
            </div>

            <div class="important-note">
                <strong>⚠️ Important:</strong> If you cannot print your badge, please contact us at least 24 hours before the event. We can arrange for on-site printing with a small fee.
            </div>

            <p>We're excited to welcome you to <strong>{event_name}</strong> and look forward to seeing you there!</p>
        </div>

        <div class="footer">
            <div class="contact-info">
                <strong>University of Gondar Events Team</strong><br>
                📧 <EMAIL> | 📞 +251-58-114-1240<br>
                🌐 www.uog.edu.et
            </div>

            <div style="margin-top: 20px; padding: 20px; background: #ffffff; border-radius: 8px;">
                <h4 style="margin: 0 0 15px 0; color: #495057;">Developed by:</h4>
                <div style="display: flex; justify-content: center; gap: 20px; flex-wrap: wrap;">
                    <a href="https://www.linkedin.com/in/tewodros-abebaw-chekol/" target="_blank" style="color: #0077b5; text-decoration: none; font-weight: 500;">Tewodros Abebaw</a>
                    <a href="https://www.linkedin.com/in/aragaw-mebratu-a3096514a/" target="_blank" style="color: #0077b5; text-decoration: none; font-weight: 500;">Aragaw Mebratu</a>
                    <a href="https://www.linkedin.com/in/meseret-teshale-bb24b767/" target="_blank" style="color: #0077b5; text-decoration: none; font-weight: 500;">Meseret Teshale</a>
                </div>
            </div>

            <p style="margin-top: 20px; font-size: 12px;">
                © 2025 University of Gondar. All rights reserved.<br>
                This email was sent to {participant_email}. If you have any concerns, please contact us immediately.
            </p>
        </div>
    </div>
</body>
</html>
                ''',
                'text_content': '''
🎫 YOUR EVENT BADGE IS READY!

Dear {participant_name},

Great news! Your personalized badge for {event_name} has been generated and is attached to this email.

📎 ATTACHMENT: Your event badge is attached as a PDF file. Please download and print it before the event.

📋 IMPORTANT BADGE INSTRUCTIONS:
• Print Quality: Use high-quality paper (preferably cardstock) for durability
• Bring to Event: Your badge is required for entry and identification
• Keep Visible: Wear your badge prominently throughout the event
• QR Code: Your badge contains a unique QR code for quick check-in
• Backup: Save a digital copy on your phone as backup

⚠️ IMPORTANT: If you cannot print your badge, please contact us at least 24 hours before the event. We can arrange for on-site printing with a small fee.

We're excited to welcome you to {event_name} and look forward to seeing you there!

---
University of Gondar Events Team
📧 <EMAIL> | 📞 +251-58-114-1240
🌐 www.uog.edu.et

Developed by:
• Tewodros Abebaw (https://www.linkedin.com/in/tewodros-abebaw-chekol/)
• Aragaw Mebratu (https://www.linkedin.com/in/aragaw-mebratu-a3096514a/)
• Meseret Teshale (https://www.linkedin.com/in/meseret-teshale-bb24b767/)

© 2025 University of Gondar. All rights reserved.
This email was sent to {participant_email}. If you have any concerns, please contact us immediately.
                '''
            },
            {
                'name': 'Schedule Update',
                'template_type': 'schedule_update',
                'subject': 'Updated Schedule - {event_name}',
                'html_content': '''
                <h2>Schedule Update</h2>
                <p>Dear {participant_name},</p>
                
                <p>The schedule for <strong>{event_name}</strong> has been updated. Please review the latest schedule below:</p>
                
                <div class="highlight-box">
                    <h3>Event Schedule - {event_date}</h3>
                    <p>Please check the event portal for the most up-to-date schedule information.</p>
                </div>
                
                <p>Make sure to arrive on time for all sessions you plan to attend.</p>
                
                <p>Best regards,<br>University of Gondar Events Team</p>
                ''',
                'text_content': '''
                Schedule Update
                
                Dear {participant_name},
                
                The schedule for {event_name} has been updated.
                Event Date: {event_date}
                
                Please check the event portal for the most up-to-date schedule information.
                
                Best regards,
                University of Gondar Events Team
                '''
            },
            {
                'name': 'Daily Gallery',
                'template_type': 'daily_gallery',
                'subject': 'Daily Event Photos - {event_name}',
                'html_content': '''
                <h2>Daily Event Photos</h2>
                <p>Dear {participant_name},</p>
                
                <p>Here are the photos from <strong>{event_name}</strong> for {date}.</p>
                
                <div class="highlight-box">
                    <h3>Photo Package</h3>
                    <p>📸 <strong>{image_count}</strong> photos from {date}</p>
                    <p>All photos are included in the attached ZIP file.</p>
                </div>
                
                <p>Feel free to download, share, and enjoy these memories from the event!</p>
                
                <p>Best regards,<br>University of Gondar Events Team</p>
                ''',
                'text_content': '''
                Daily Event Photos
                
                Dear {participant_name},
                
                Here are the photos from {event_name} for {date}.
                
                Photo Package: {image_count} photos from {date}
                All photos are included in the attached ZIP file.
                
                Best regards,
                University of Gondar Events Team
                '''
            },
            {
                'name': 'Event Reminder',
                'template_type': 'event_reminder',
                'subject': '⏰ Reminder: {event_name} in {days_before} day(s)',
                'html_content': '''
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Event Reminder</title>
    <style>
        body {{ font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; margin: 0; padding: 0; background-color: #f8f9fa; }}
        .container {{ max-width: 600px; margin: 0 auto; background-color: #ffffff; }}
        .header {{ background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%); color: white; padding: 30px; text-align: center; }}
        .header h1 {{ margin: 0; font-size: 28px; font-weight: 300; }}
        .header .subtitle {{ margin: 10px 0 0 0; opacity: 0.9; font-size: 16px; }}
        .content {{ padding: 40px 30px; }}
        .reminder-title {{ font-size: 24px; color: #ff6b6b; margin-bottom: 20px; font-weight: 300; text-align: center; }}
        .countdown {{ background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%); color: white; padding: 25px; border-radius: 10px; margin: 25px 0; text-align: center; }}
        .countdown .days {{ font-size: 48px; font-weight: bold; display: block; }}
        .countdown .label {{ font-size: 18px; opacity: 0.9; }}
        .event-info {{ background: #f8f9fa; padding: 20px; border-radius: 8px; margin: 25px 0; border-left: 4px solid #ff6b6b; }}
        .event-info h3 {{ margin: 0 0 15px 0; color: #ff6b6b; }}
        .detail-row {{ margin: 8px 0; }}
        .detail-label {{ font-weight: 600; }}
        .checklist {{ background: #e8f5e8; padding: 20px; border-radius: 8px; margin: 25px 0; }}
        .checklist h3 {{ margin: 0 0 15px 0; color: #28a745; }}
        .checklist ul {{ margin: 10px 0; padding-left: 20px; }}
        .checklist li {{ margin: 8px 0; }}
        .footer {{ background: #f8f9fa; padding: 30px; text-align: center; color: #6c757d; }}
        .contact-info {{ margin: 20px 0; }}
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🇪🇹 University of Gondar</h1>
            <div class="subtitle">Excellence in Education & Research</div>
        </div>

        <div class="content">
            <div class="reminder-title">⏰ Event Reminder</div>

            <p>Dear <strong>{participant_name}</strong>,</p>

            <p>This is a friendly reminder that <strong>{event_name}</strong> is coming up soon!</p>

            <div class="countdown">
                <span class="days">{days_before}</span>
                <span class="label">Day(s) Remaining</span>
            </div>

            <div class="event-info">
                <h3>📅 Event Information</h3>
                <div class="detail-row"><span class="detail-label">Event:</span> {event_name}</div>
                <div class="detail-row"><span class="detail-label">Date & Time:</span> {event_date}</div>
                <div class="detail-row"><span class="detail-label">Location:</span> {event_location}</div>
            </div>

            <div class="checklist">
                <h3>✅ Pre-Event Checklist</h3>
                <ul>
                    <li><strong>Print your badge:</strong> Download and print your event badge on quality paper</li>
                    <li><strong>Arrive early:</strong> Plan to arrive 15-30 minutes before the event starts</li>
                    <li><strong>Bring essentials:</strong> Valid ID, printed badge, and any required materials</li>
                    <li><strong>Check updates:</strong> Review the latest schedule and announcements</li>
                    <li><strong>Plan transportation:</strong> Confirm your route and parking arrangements</li>
                </ul>
            </div>

            <p>We're excited to welcome you to this important event and look forward to your participation!</p>

            <p>If you have any last-minute questions or concerns, please don't hesitate to contact our events team.</p>
        </div>

        <div class="footer">
            <div class="contact-info">
                <strong>University of Gondar Events Team</strong><br>
                📧 <EMAIL> | 📞 +251-58-114-1240<br>
                🌐 www.uog.edu.et
            </div>

            <div style="margin-top: 20px; padding: 20px; background: #ffffff; border-radius: 8px;">
                <h4 style="margin: 0 0 15px 0; color: #495057;">Developed by:</h4>
                <div style="display: flex; justify-content: center; gap: 20px; flex-wrap: wrap;">
                    <a href="https://www.linkedin.com/in/tewodros-abebaw-chekol/" target="_blank" style="color: #0077b5; text-decoration: none; font-weight: 500;">Tewodros Abebaw</a>
                    <a href="https://www.linkedin.com/in/aragaw-mebratu-a3096514a/" target="_blank" style="color: #0077b5; text-decoration: none; font-weight: 500;">Aragaw Mebratu</a>
                    <a href="https://www.linkedin.com/in/meseret-teshale-bb24b767/" target="_blank" style="color: #0077b5; text-decoration: none; font-weight: 500;">Meseret Teshale</a>
                </div>
            </div>

            <p style="margin-top: 20px; font-size: 12px;">
                © 2025 University of Gondar. All rights reserved.<br>
                This email was sent to {participant_email}. If you have any concerns, please contact us immediately.
            </p>
        </div>
    </div>
</body>
</html>
                ''',
                'text_content': '''
⏰ EVENT REMINDER

Dear {participant_name},

This is a friendly reminder that {event_name} is coming up soon!

🗓️ COUNTDOWN: {days_before} day(s) remaining

📅 EVENT INFORMATION:
• Event: {event_name}
• Date & Time: {event_date}
• Location: {event_location}

✅ PRE-EVENT CHECKLIST:
• Print your badge: Download and print your event badge on quality paper
• Arrive early: Plan to arrive 15-30 minutes before the event starts
• Bring essentials: Valid ID, printed badge, and any required materials
• Check updates: Review the latest schedule and announcements
• Plan transportation: Confirm your route and parking arrangements

We're excited to welcome you to this important event and look forward to your participation!

If you have any last-minute questions or concerns, please don't hesitate to contact our events team.

---
University of Gondar Events Team
📧 <EMAIL> | 📞 +251-58-114-1240
🌐 www.uog.edu.et

Developed by:
• Tewodros Abebaw (https://www.linkedin.com/in/tewodros-abebaw-chekol/)
• Aragaw Mebratu (https://www.linkedin.com/in/aragaw-mebratu-a3096514a/)
• Meseret Teshale (https://www.linkedin.com/in/meseret-teshale-bb24b767/)

© 2025 University of Gondar. All rights reserved.
This email was sent to {participant_email}. If you have any concerns, please contact us immediately.
                '''
            }
        ]
        
        created_count = 0
        updated_count = 0
        
        for template_data in templates:
            template, created = EmailTemplate.objects.get_or_create(
                template_type=template_data['template_type'],
                defaults=template_data
            )
            
            if created:
                created_count += 1
                self.stdout.write(
                    self.style.SUCCESS(f'Created template: {template.name}')
                )
            else:
                # Update existing template
                for key, value in template_data.items():
                    setattr(template, key, value)
                template.save()
                updated_count += 1
                self.stdout.write(
                    self.style.WARNING(f'Updated template: {template.name}')
                )
        
        self.stdout.write(
            self.style.SUCCESS(
                f'Successfully processed {created_count} new templates and updated {updated_count} existing templates'
            )
        )
