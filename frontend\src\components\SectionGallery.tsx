import React, { useState, useEffect } from 'react';
import { Con<PERSON><PERSON>, Row, Col, Spinner, Alert } from 'react-bootstrap';
import Gallery from './Gallery';
import { EventGallery } from '../services/api';
import api from '../services/api';

interface SectionGalleryProps {
  sectionId?: number;
  sectionType?: string;
  title?: string;
  showDateGrouping?: boolean;
  showDownloadButton?: boolean;
  gridColumns?: {
    lg?: number;
    md?: number;
    sm?: number;
  };
  imageHeight?: string;
  className?: string;
}

/**
 * A reusable section gallery component that can fetch and display images
 * for any section of the website (news, about, departments, etc.)
 */
const SectionGallery: React.FC<SectionGalleryProps> = ({
  sectionId,
  sectionType = 'general',
  title = 'Gallery',
  showDateGrouping = false,
  showDownloadButton = true,
  gridColumns = { lg: 4, md: 6, sm: 12 },
  imageHeight = '200px',
  className = ''
}) => {
  const [images, setImages] = useState<EventGallery[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');

  useEffect(() => {
    if (sectionId) {
      fetchSectionImages();
    }
  }, [sectionId, sectionType]);

  const fetchSectionImages = async () => {
    setLoading(true);
    setError('');
    
    try {
      // This is a placeholder - you would implement the actual API endpoint
      // for fetching section-specific images
      const response = await api.get(`/site-gallery/images/?section=${sectionType}&section_id=${sectionId}`);
      setImages(response.data.results || response.data);
    } catch (err: any) {
      setError('Failed to load images');
      console.error('Error fetching section images:', err);
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <div className="text-center py-5">
        <Spinner animation="border" role="status">
          <span className="visually-hidden">Loading images...</span>
        </Spinner>
      </div>
    );
  }

  if (error) {
    return (
      <Alert variant="danger" className="text-center">
        <i className="fas fa-exclamation-triangle me-2"></i>
        {error}
      </Alert>
    );
  }

  return (
    <div className={className}>
      <Gallery
        images={images}
        title={title}
        showDateGrouping={showDateGrouping}
        showDownloadButton={showDownloadButton}
        showFeaturedBadge={true}
        gridColumns={gridColumns}
        imageHeight={imageHeight}
        downloadPrefix={sectionType}
        emptyStateMessage={`No ${sectionType} images available`}
        emptyStateIcon="fas fa-images"
      />
    </div>
  );
};

export default SectionGallery;

// Example usage components for different sections:

export const NewsGallery: React.FC<{ newsId: number }> = ({ newsId }) => (
  <SectionGallery
    sectionId={newsId}
    sectionType="news"
    title="News Images"
    showDateGrouping={false}
    showDownloadButton={false}
    gridColumns={{ lg: 4, md: 6, sm: 12 }}
    imageHeight="180px"
  />
);

export const AboutGallery: React.FC = () => (
  <SectionGallery
    sectionType="about"
    title="About University of Gondar"
    showDateGrouping={false}
    gridColumns={{ lg: 3, md: 4, sm: 6 }}
    imageHeight="250px"
  />
);

export const DepartmentGallery: React.FC<{ departmentId: number }> = ({ departmentId }) => (
  <SectionGallery
    sectionId={departmentId}
    sectionType="department"
    title="Department Gallery"
    showDateGrouping={false}
    gridColumns={{ lg: 4, md: 6, sm: 12 }}
    imageHeight="200px"
  />
);

export const CampusGallery: React.FC = () => (
  <SectionGallery
    sectionType="campus"
    title="Campus Life"
    showDateGrouping={false}
    gridColumns={{ lg: 3, md: 4, sm: 6 }}
    imageHeight="220px"
  />
);

export const FacilitiesGallery: React.FC = () => (
  <SectionGallery
    sectionType="facilities"
    title="University Facilities"
    showDateGrouping={false}
    gridColumns={{ lg: 4, md: 6, sm: 12 }}
    imageHeight="180px"
  />
);
