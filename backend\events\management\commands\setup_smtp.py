from django.core.management.base import BaseCommand
from events.models import EmailConfiguration


class Command(BaseCommand):
    help = 'Setup SMTP configuration for University of Gondar'
    
    def handle(self, *args, **options):
        # Create or update the SMTP configuration
        config, created = EmailConfiguration.objects.get_or_create(
            name='UoG Event Email',
            defaults={
                'email_backend': 'django.core.mail.backends.smtp.EmailBackend',
                'email_host': 'smtp.office365.com',
                'email_port': 587,
                'email_use_tls': True,
                'email_use_ssl': False,
                'email_host_user': '<EMAIL>',
                'email_host_password': 'Eve@456!!',
                'default_from_email': 'Event <<EMAIL>>',
                'is_active': True
            }
        )
        
        if created:
            self.stdout.write(
                self.style.SUCCESS('✓ Created new SMTP configuration: UoG Event Email')
            )
        else:
            # Update existing configuration
            config.email_backend = 'django.core.mail.backends.smtp.EmailBackend'
            config.email_host = 'smtp.office365.com'
            config.email_port = 587
            config.email_use_tls = True
            config.email_use_ssl = False
            config.email_host_user = '<EMAIL>'
            config.email_host_password = 'Eve@456!!'
            config.default_from_email = 'Event <<EMAIL>>'
            config.is_active = True
            config.save()
            
            self.stdout.write(
                self.style.SUCCESS('✓ Updated existing SMTP configuration: UoG Event Email')
            )
        
        # Deactivate all other configurations
        EmailConfiguration.objects.exclude(pk=config.pk).update(is_active=False)
        
        self.stdout.write(
            self.style.SUCCESS(f'✓ SMTP configuration is now active')
        )
        self.stdout.write(f'  Host: {config.email_host}')
        self.stdout.write(f'  Port: {config.email_port}')
        self.stdout.write(f'  User: {config.email_host_user}')
        self.stdout.write(f'  TLS: {config.email_use_tls}')
        self.stdout.write(f'  From: {config.default_from_email}')
        
        self.stdout.write(
            self.style.WARNING('\nNote: Make sure the email account has:')
        )
        self.stdout.write('  - 2-factor authentication enabled')
        self.stdout.write('  - App password generated (if using Gmail)')
        self.stdout.write('  - "Less secure app access" enabled (if not using app password)')
        
        self.stdout.write(
            self.style.SUCCESS('\n✓ Ready to test email sending!')
        )