from django.urls import path, include
from rest_framework.routers import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from .views import OrganizationViewSet, get_primary_organization, bulk_email_send

router = DefaultRouter()
router.register(r'organizations', OrganizationViewSet)

urlpatterns = [
    path('organizations/primary/', get_primary_organization, name='primary-organization'),
    path('organizations/bulk-email/send/', bulk_email_send, name='bulk-email-send'),
    path('', include(router.urls)),
]
