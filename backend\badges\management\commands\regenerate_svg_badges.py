from django.core.management.base import BaseCommand
from badges.models import Badge
from participants.models import Participant


class Command(BaseCommand):
    help = 'Regenerate all badges with the new SVG design'

    def add_arguments(self, parser):
        parser.add_argument(
            '--participant-id',
            type=int,
            help='Regenerate badge for specific participant ID',
        )
        parser.add_argument(
            '--force',
            action='store_true',
            help='Force regenerate even if badge already exists',
        )
        parser.add_argument(
            '--count',
            type=int,
            default=10,
            help='Number of badges to regenerate (default: 10)',
        )

    def handle(self, *args, **options):
        self.stdout.write(
            self.style.SUCCESS('🎨 Regenerating Badges with New SVG Design')
        )
        self.stdout.write('=' * 60)

        try:
            if options['participant_id']:
                # Regenerate specific participant
                try:
                    participant = Participant.objects.get(id=options['participant_id'])
                    participants = [participant]
                    self.stdout.write(f'🎯 Targeting specific participant: {participant.full_name}')
                except Participant.DoesNotExist:
                    self.stdout.write(
                        self.style.ERROR(f'❌ Participant with ID {options["participant_id"]} not found')
                    )
                    return
            else:
                # Get participants
                participants = Participant.objects.all()[:options['count']]
                self.stdout.write(f'🎯 Targeting first {len(participants)} participants')

            if not participants:
                self.stdout.write(
                    self.style.WARNING('⚠️  No participants found')
                )
                return

            regenerated_count = 0
            skipped_count = 0
            error_count = 0

            for participant in participants:
                try:
                    self.stdout.write(f'\n👤 Processing: {participant.full_name}')
                    
                    # Get or create badge
                    badge, created = Badge.objects.get_or_create(participant=participant)
                    
                    if created:
                        self.stdout.write('   ✨ Created new badge record')
                    else:
                        self.stdout.write('   🔄 Found existing badge record')
                    
                    # Check if regeneration is needed
                    should_regenerate = (
                        options['force'] or 
                        not badge.is_generated or 
                        not badge.badge_image or
                        badge.badge_image.width != 600 or  # Check if old dimensions
                        badge.badge_image.height != 1200
                    )
                    
                    if should_regenerate:
                        self.stdout.write('   🎨 Regenerating with SVG design...')
                        
                        # Clear old files if they exist
                        if badge.badge_image:
                            badge.badge_image.delete(save=False)
                        if badge.qr_code_image:
                            badge.qr_code_image.delete(save=False)
                        
                        # Reset generation status
                        badge.is_generated = False
                        badge.generated_at = None
                        badge.save()
                        
                        # Generate new SVG badge
                        badge_img = badge.generate_badge()
                        
                        regenerated_count += 1
                        self.stdout.write(f'   ✅ REGENERATED - Dimensions: {badge_img.size}')
                        
                        # Verify SVG dimensions
                        if badge_img.size == (600, 1200):
                            self.stdout.write('   🎯 CONFIRMED: Using new SVG template!')
                        else:
                            self.stdout.write(f'   ⚠️  WARNING: Unexpected dimensions {badge_img.size}')
                            
                    else:
                        skipped_count += 1
                        self.stdout.write('   ⏭️  Skipped (already has SVG badge)')
                        
                except Exception as e:
                    error_count += 1
                    self.stdout.write(
                        self.style.ERROR(f'   ❌ Error: {str(e)}')
                    )

            # Summary
            self.stdout.write('\n' + '=' * 60)
            self.stdout.write(self.style.SUCCESS('📊 REGENERATION SUMMARY'))
            self.stdout.write(f'✅ Regenerated: {regenerated_count}')
            self.stdout.write(f'⏭️  Skipped: {skipped_count}')
            self.stdout.write(f'❌ Errors: {error_count}')
            self.stdout.write(f'📋 Total processed: {len(participants)}')
            
            if regenerated_count > 0:
                self.stdout.write(
                    self.style.SUCCESS(f'\n🎉 Successfully regenerated {regenerated_count} badges with new SVG design!')
                )
                self.stdout.write('💡 All regenerated badges now use:')
                self.stdout.write('   • 600x1200 pixel dimensions')
                self.stdout.write('   • Ministry branding and logos')
                self.stdout.write('   • Professional SVG template layout')
                self.stdout.write('   • QR codes for verification')
                self.stdout.write('   • Sponsor acknowledgments')
            else:
                self.stdout.write(
                    self.style.WARNING('\n⚠️  No badges were regenerated')
                )
                
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f'❌ Command failed: {str(e)}')
            )
            import traceback
            self.stdout.write(traceback.format_exc())
