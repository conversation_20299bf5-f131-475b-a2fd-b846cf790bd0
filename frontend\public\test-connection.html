<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Frontend-Backend Connection Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .success {
            background-color: #d4edda;
            border-color: #c3e6cb;
            color: #155724;
        }
        .error {
            background-color: #f8d7da;
            border-color: #f5c6cb;
            color: #721c24;
        }
        .loading {
            background-color: #fff3cd;
            border-color: #ffeaa7;
            color: #856404;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        pre {
            background-color: #f8f9fa;
            padding: 10px;
            border-radius: 3px;
            overflow-x: auto;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔗 Frontend-Backend Connection Test</h1>
        <p>This page tests the connection between the React frontend and Django backend.</p>
        
        <div class="test-section">
            <h3>Test 1: Organization API (Public)</h3>
            <button onclick="testOrganizationAPI()">Test Organization API</button>
            <div id="org-result"></div>
        </div>

        <div class="test-section">
            <h3>Test 2: Events API (Public)</h3>
            <button onclick="testEventsAPI()">Test Events API</button>
            <div id="events-result"></div>
        </div>

        <div class="test-section">
            <h3>Test 3: Authentication API</h3>
            <button onclick="testAuthAPI()">Test Auth API</button>
            <div id="auth-result"></div>
        </div>

        <div class="test-section">
            <h3>Test 4: CORS Headers</h3>
            <button onclick="testCORS()">Test CORS</button>
            <div id="cors-result"></div>
        </div>

        <div class="test-section">
            <h3>Run All Tests</h3>
            <button onclick="runAllTests()">Run All Tests</button>
        </div>
    </div>

    <script>
        const API_BASE_URL = 'http://localhost:8000/api';

        function showLoading(elementId) {
            document.getElementById(elementId).innerHTML = '<div class="loading">⏳ Testing...</div>';
        }

        function showSuccess(elementId, message, data = null) {
            let content = `<div class="success">✅ ${message}</div>`;
            if (data) {
                content += `<pre>${JSON.stringify(data, null, 2)}</pre>`;
            }
            document.getElementById(elementId).innerHTML = content;
        }

        function showError(elementId, message, error = null) {
            let content = `<div class="error">❌ ${message}</div>`;
            if (error) {
                content += `<pre>${error}</pre>`;
            }
            document.getElementById(elementId).innerHTML = content;
        }

        async function testOrganizationAPI() {
            showLoading('org-result');
            try {
                const response = await fetch(`${API_BASE_URL}/organizations/primary/`);
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                const data = await response.json();
                showSuccess('org-result', 'Organization API working!', data);
            } catch (error) {
                showError('org-result', 'Organization API failed', error.message);
            }
        }

        async function testEventsAPI() {
            showLoading('events-result');
            try {
                const response = await fetch(`${API_BASE_URL}/public/events/`);
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                const data = await response.json();
                showSuccess('events-result', 'Events API working!', data);
            } catch (error) {
                showError('events-result', 'Events API failed', error.message);
            }
        }

        async function testAuthAPI() {
            showLoading('auth-result');
            try {
                const response = await fetch(`${API_BASE_URL}/auth/profile/`);
                // We expect this to fail with 401 since we're not authenticated
                if (response.status === 401) {
                    const data = await response.json();
                    showSuccess('auth-result', 'Auth API working (401 expected for unauthenticated request)', data);
                } else {
                    throw new Error(`Unexpected status: ${response.status}`);
                }
            } catch (error) {
                showError('auth-result', 'Auth API test failed', error.message);
            }
        }

        async function testCORS() {
            showLoading('cors-result');
            try {
                const response = await fetch(`${API_BASE_URL}/organizations/primary/`);
                const corsHeaders = {
                    'Access-Control-Allow-Origin': response.headers.get('Access-Control-Allow-Origin'),
                    'Access-Control-Allow-Methods': response.headers.get('Access-Control-Allow-Methods'),
                    'Access-Control-Allow-Headers': response.headers.get('Access-Control-Allow-Headers')
                };
                showSuccess('cors-result', 'CORS headers present!', corsHeaders);
            } catch (error) {
                showError('cors-result', 'CORS test failed', error.message);
            }
        }

        async function runAllTests() {
            await testOrganizationAPI();
            await new Promise(resolve => setTimeout(resolve, 500));
            await testEventsAPI();
            await new Promise(resolve => setTimeout(resolve, 500));
            await testAuthAPI();
            await new Promise(resolve => setTimeout(resolve, 500));
            await testCORS();
        }

        // Auto-run tests on page load
        window.addEventListener('load', () => {
            setTimeout(runAllTests, 1000);
        });
    </script>
</body>
</html>
