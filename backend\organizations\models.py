from django.db import models
from django.core.validators import EmailValidator, URLValidator


class Organization(models.Model):
    """Organization model for managing organization details"""
    
    name = models.Char<PERSON>ield(
        max_length=200,
        help_text="Official name of the organization"
    )
    
    short_name = models.Char<PERSON>ield(
        max_length=50,
        blank=True,
        help_text="Short name or abbreviation"
    )
    
    logo = models.ImageField(
        upload_to='organizations/logos/',
        blank=True,
        null=True,
        help_text="Organization logo"
    )
    
    motto = models.TextField(
        blank=True,
        help_text="Organization motto or tagline"
    )
    
    description = models.TextField(
        blank=True,
        help_text="Brief description of the organization"
    )
    
    # Contact Information
    email = models.EmailField(
        validators=[EmailValidator()],
        help_text="Primary contact email"
    )
    
    phone = models.CharField(
        max_length=20,
        blank=True,
        help_text="Primary contact phone number"
    )
    
    website = models.URLField(
        blank=True,
        validators=[URLValidator()],
        help_text="Organization website URL"
    )
    
    # Address Information
    address_line_1 = models.Char<PERSON><PERSON>(
        max_length=255,
        blank=True,
        help_text="Street address line 1"
    )
    
    address_line_2 = models.CharField(
        max_length=255,
        blank=True,
        help_text="Street address line 2"
    )
    
    city = models.CharField(
        max_length=100,
        blank=True,
        help_text="City"
    )
    
    state_province = models.CharField(
        max_length=100,
        blank=True,
        help_text="State or Province"
    )
    
    postal_code = models.CharField(
        max_length=20,
        blank=True,
        help_text="Postal or ZIP code"
    )
    
    country = models.CharField(
        max_length=100,
        blank=True,
        help_text="Country"
    )
    
    # Social Media
    facebook_url = models.URLField(
        blank=True,
        help_text="Facebook page URL"
    )
    
    twitter_url = models.URLField(
        blank=True,
        help_text="Twitter profile URL"
    )
    
    linkedin_url = models.URLField(
        blank=True,
        help_text="LinkedIn page URL"
    )
    
    instagram_url = models.URLField(
        blank=True,
        help_text="Instagram profile URL"
    )
    
    # Settings
    is_active = models.BooleanField(
        default=True,
        help_text="Whether this organization is active"
    )
    
    is_primary = models.BooleanField(
        default=False,
        help_text="Whether this is the primary organization"
    )
    
    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        ordering = ['-is_primary', 'name']
        verbose_name = "Organization"
        verbose_name_plural = "Organizations"
    
    def __str__(self):
        return self.name
    
    def save(self, *args, **kwargs):
        # Ensure only one primary organization
        if self.is_primary:
            Organization.objects.filter(is_primary=True).exclude(pk=self.pk).update(is_primary=False)
        super().save(*args, **kwargs)
    
    @property
    def full_address(self):
        """Return formatted full address"""
        address_parts = [
            self.address_line_1,
            self.address_line_2,
            self.city,
            self.state_province,
            self.postal_code,
            self.country
        ]
        return ', '.join([part for part in address_parts if part])
    
    @property
    def display_name(self):
        """Return display name (short name if available, otherwise full name)"""
        return self.short_name if self.short_name else self.name


class OrganizationSettings(models.Model):
    """Additional settings for organization"""
    
    organization = models.OneToOneField(
        Organization,
        on_delete=models.CASCADE,
        related_name='settings'
    )
    
    # Event Settings
    default_event_duration_hours = models.PositiveIntegerField(
        default=8,
        help_text="Default duration for events in hours"
    )
    
    default_registration_fee = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        default=0.00,
        help_text="Default registration fee for events"
    )
    
    # Email Settings
    email_signature = models.TextField(
        blank=True,
        help_text="Default email signature for communications"
    )
    
    # Branding Settings
    primary_color = models.CharField(
        max_length=7,
        default='#007bff',
        help_text="Primary brand color (hex code)"
    )
    
    secondary_color = models.CharField(
        max_length=7,
        default='#6c757d',
        help_text="Secondary brand color (hex code)"
    )
    
    # Notification Settings
    send_welcome_emails = models.BooleanField(
        default=True,
        help_text="Send welcome emails to new participants"
    )
    
    send_confirmation_emails = models.BooleanField(
        default=True,
        help_text="Send confirmation emails for registrations"
    )
    
    send_reminder_emails = models.BooleanField(
        default=True,
        help_text="Send reminder emails before events"
    )
    
    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        verbose_name = "Organization Settings"
        verbose_name_plural = "Organization Settings"
    
    def __str__(self):
        return f"Settings for {self.organization.name}"
