import React, { useState, useEffect } from 'react';
import { Con<PERSON><PERSON>, <PERSON>, Col, <PERSON>, Badge, <PERSON><PERSON>, Spinner, Button, Modal, Tab, Tabs } from 'react-bootstrap';
import { useParams, useNavigate, Link } from 'react-router-dom';
import { hotelService, Hotel } from '../services/api';

const HotelDetail: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  
  const [hotel, setHotel] = useState<Hotel | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [deleting, setDeleting] = useState(false);

  useEffect(() => {
    if (id) {
      fetchHotel(parseInt(id));
    }
  }, [id]);

  const fetchHotel = async (hotelId: number) => {
    try {
      setLoading(true);
      const response = await hotelService.getHotel(hotelId);
      setHotel(response.data);
      setError(null);
    } catch (err) {
      console.error('Error fetching hotel:', err);
      setError('Failed to load hotel details. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const handleDelete = async () => {
    if (!hotel) return;
    
    try {
      setDeleting(true);
      await hotelService.deleteHotel(hotel.id);
      navigate('/hotels');
    } catch (err) {
      console.error('Error deleting hotel:', err);
      setError('Failed to delete hotel. Please try again.');
      setDeleting(false);
    }
  };

  const getStarRating = (rating?: number) => {
    if (!rating) return 'Not Rated';
    return '★'.repeat(rating) + '☆'.repeat(5 - rating);
  };

  if (loading) {
    return (
      <Container className="text-center mt-5">
        <Spinner animation="border" role="status">
          <span className="visually-hidden">Loading...</span>
        </Spinner>
        <p className="mt-3">Loading hotel details...</p>
      </Container>
    );
  }

  if (error && !hotel) {
    return (
      <Container className="py-4">
        <Alert variant="danger">
          <i className="fas fa-exclamation-triangle me-2"></i>
          {error}
        </Alert>
        <Button variant="primary" onClick={() => navigate('/hotels')}>
          <i className="fas fa-arrow-left me-2"></i>
          Back to Hotels
        </Button>
      </Container>
    );
  }

  if (!hotel) {
    return (
      <Container className="py-4">
        <Alert variant="warning">
          <i className="fas fa-exclamation-triangle me-2"></i>
          Hotel not found.
        </Alert>
        <Button variant="primary" onClick={() => navigate('/hotels')}>
          <i className="fas fa-arrow-left me-2"></i>
          Back to Hotels
        </Button>
      </Container>
    );
  }

  return (
    <Container className="py-4">
      {error && (
        <Alert variant="danger" className="mb-4">
          <i className="fas fa-exclamation-triangle me-2"></i>
          {error}
        </Alert>
      )}

      {/* Header */}
      <Row className="mb-4">
        <Col>
          <div className="d-flex justify-content-between align-items-center">
            <div>
              <Button 
                variant="outline-secondary" 
                onClick={() => navigate('/hotels')}
                className="mb-2"
              >
                <i className="fas fa-arrow-left me-2"></i>
                Back to Hotels
              </Button>
              <h1 className="display-6 fw-bold text-primary mb-0">
                <i className="fas fa-hotel me-3"></i>
                {hotel.name}
              </h1>
              <p className="lead text-muted">Hotel Details & Information</p>
            </div>
            <div>
              <Badge 
                bg={hotel.is_active ? 'success' : 'secondary'} 
                className="fs-6 me-2"
              >
                {hotel.is_active ? 'Active' : 'Inactive'}
              </Badge>
              <div className="text-warning">
                {getStarRating(hotel.star_rating)}
                {hotel.star_rating && (
                  <span className="text-muted ms-2">({hotel.star_rating} stars)</span>
                )}
              </div>
            </div>
          </div>
        </Col>
      </Row>

      <Tabs defaultActiveKey="details" className="mb-4">
        <Tab eventKey="details" title={<><i className="fas fa-info-circle me-2"></i>Details</>}>
          <Row>
            <Col lg={8}>
              {/* Basic Information */}
              <Card className="mb-4">
                <Card.Header>
                  <h5 className="mb-0">
                    <i className="fas fa-info-circle me-2"></i>
                    Basic Information
                  </h5>
                </Card.Header>
                <Card.Body>
                  <Row>
                    <Col md={6}>
                      <div className="mb-3">
                        <label className="form-label fw-bold">Hotel Name</label>
                        <p className="mb-0 fs-5">{hotel.name}</p>
                      </div>
                    </Col>
                    <Col md={6}>
                      <div className="mb-3">
                        <label className="form-label fw-bold">Star Rating</label>
                        <p className="mb-0">
                          <span className="text-warning fs-5">
                            {getStarRating(hotel.star_rating)}
                          </span>
                          {hotel.star_rating && (
                            <span className="text-muted ms-2">({hotel.star_rating} stars)</span>
                          )}
                        </p>
                      </div>
                    </Col>
                    <Col md={12}>
                      <div className="mb-3">
                        <label className="form-label fw-bold">Address</label>
                        <p className="mb-0">
                          <i className="fas fa-map-marker-alt me-2 text-primary"></i>
                          {hotel.address}
                        </p>
                      </div>
                    </Col>
                  </Row>
                </Card.Body>
              </Card>

              {/* Contact Information */}
              <Card className="mb-4">
                <Card.Header>
                  <h5 className="mb-0">
                    <i className="fas fa-address-card me-2"></i>
                    Contact Information
                  </h5>
                </Card.Header>
                <Card.Body>
                  <Row>
                    <Col md={6}>
                      <div className="mb-3">
                        <label className="form-label fw-bold">Phone Number</label>
                        <p className="mb-0">
                          <i className="fas fa-phone me-2 text-primary"></i>
                          <a href={`tel:${hotel.phone}`} className="text-decoration-none">
                            {hotel.phone}
                          </a>
                        </p>
                      </div>
                    </Col>
                    <Col md={6}>
                      <div className="mb-3">
                        <label className="form-label fw-bold">Email Address</label>
                        <p className="mb-0">
                          <i className="fas fa-envelope me-2 text-primary"></i>
                          <a href={`mailto:${hotel.email}`} className="text-decoration-none">
                            {hotel.email}
                          </a>
                        </p>
                      </div>
                    </Col>
                    <Col md={6}>
                      <div className="mb-3">
                        <label className="form-label fw-bold">Contact Person</label>
                        <p className="mb-0">
                          <i className="fas fa-user me-2 text-primary"></i>
                          {hotel.contact_person}
                        </p>
                      </div>
                    </Col>
                    {hotel.website && (
                      <Col md={6}>
                        <div className="mb-3">
                          <label className="form-label fw-bold">Website</label>
                          <p className="mb-0">
                            <i className="fas fa-globe me-2 text-primary"></i>
                            <a 
                              href={hotel.website} 
                              target="_blank" 
                              rel="noopener noreferrer"
                              className="text-decoration-none"
                            >
                              Visit Website
                            </a>
                          </p>
                        </div>
                      </Col>
                    )}
                  </Row>
                </Card.Body>
              </Card>

              {/* Description */}
              {hotel.description && (
                <Card className="mb-4">
                  <Card.Header>
                    <h5 className="mb-0">
                      <i className="fas fa-file-alt me-2"></i>
                      Description
                    </h5>
                  </Card.Header>
                  <Card.Body>
                    <p className="mb-0" style={{ whiteSpace: 'pre-wrap' }}>
                      {hotel.description}
                    </p>
                  </Card.Body>
                </Card>
              )}

              {/* Location */}
              {(hotel.latitude && hotel.longitude) && (
                <Card className="mb-4">
                  <Card.Header>
                    <h5 className="mb-0">
                      <i className="fas fa-map-marker-alt me-2"></i>
                      Location
                    </h5>
                  </Card.Header>
                  <Card.Body>
                    <Row>
                      <Col md={6}>
                        <div className="mb-3">
                          <label className="form-label fw-bold">Latitude</label>
                          <p className="mb-0">{hotel.latitude}</p>
                        </div>
                      </Col>
                      <Col md={6}>
                        <div className="mb-3">
                          <label className="form-label fw-bold">Longitude</label>
                          <p className="mb-0">{hotel.longitude}</p>
                        </div>
                      </Col>
                    </Row>
                    <Button 
                      variant="outline-primary" 
                      size="sm"
                      onClick={() => window.open(`https://maps.google.com/?q=${hotel.latitude},${hotel.longitude}`, '_blank')}
                    >
                      <i className="fas fa-external-link-alt me-2"></i>
                      View on Google Maps
                    </Button>
                  </Card.Body>
                </Card>
              )}

              {/* System Information */}
              <Card>
                <Card.Header>
                  <h5 className="mb-0">
                    <i className="fas fa-info me-2"></i>
                    System Information
                  </h5>
                </Card.Header>
                <Card.Body>
                  <Row>
                    <Col md={6}>
                      <div className="mb-3">
                        <label className="form-label fw-bold">Created</label>
                        <p className="mb-0">
                          <i className="fas fa-calendar-plus me-2 text-success"></i>
                          {new Date(hotel.created_at).toLocaleString()}
                        </p>
                      </div>
                    </Col>
                    <Col md={6}>
                      <div className="mb-3">
                        <label className="form-label fw-bold">Last Updated</label>
                        <p className="mb-0">
                          <i className="fas fa-calendar-edit me-2 text-info"></i>
                          {new Date(hotel.updated_at).toLocaleString()}
                        </p>
                      </div>
                    </Col>
                  </Row>
                </Card.Body>
              </Card>
            </Col>

            <Col lg={4}>
              {/* Quick Actions */}
              <Card className="mb-4">
                <Card.Header>
                  <h5 className="mb-0">
                    <i className="fas fa-cogs me-2"></i>
                    Quick Actions
                  </h5>
                </Card.Header>
                <Card.Body>
                  <div className="d-grid gap-2">
                    <Link 
                      to={`/hotels/${hotel.id}/edit`} 
                      className="btn btn-primary"
                    >
                      <i className="fas fa-edit me-2"></i>
                      Edit Hotel
                    </Link>
                    
                    <Link 
                      to={`/hotels/${hotel.id}/rooms`} 
                      className="btn btn-outline-secondary"
                    >
                      <i className="fas fa-bed me-2"></i>
                      Manage Rooms
                    </Link>
                    
                    <Link 
                      to={`/hotels/${hotel.id}/reservations`} 
                      className="btn btn-outline-info"
                    >
                      <i className="fas fa-calendar-check me-2"></i>
                      View Reservations
                    </Link>
                    
                    <Button 
                      variant="danger" 
                      onClick={() => setShowDeleteModal(true)}
                    >
                      <i className="fas fa-trash me-2"></i>
                      Delete Hotel
                    </Button>
                  </div>
                </Card.Body>
              </Card>

              {/* Statistics */}
              <Card>
                <Card.Header>
                  <h5 className="mb-0">
                    <i className="fas fa-chart-bar me-2"></i>
                    Statistics
                  </h5>
                </Card.Header>
                <Card.Body>
                  <div className="text-center">
                    <div className="mb-3">
                      <h4 className="text-info">{hotel.available_rooms_count}</h4>
                      <p className="text-muted mb-0">Available Rooms</p>
                    </div>
                    <div className="mb-3">
                      <h4 className="text-success">0</h4>
                      <p className="text-muted mb-0">Current Reservations</p>
                    </div>
                    <div>
                      <h4 className="text-warning">0</h4>
                      <p className="text-muted mb-0">Assigned Participants</p>
                    </div>
                  </div>
                </Card.Body>
              </Card>
            </Col>
          </Row>
        </Tab>

        <Tab eventKey="rooms" title={<><i className="fas fa-bed me-2"></i>Rooms</>}>
          <Card>
            <Card.Body className="text-center py-5">
              <i className="fas fa-bed fa-4x text-muted mb-3"></i>
              <h4 className="text-muted">Room Management</h4>
              <p className="text-muted">
                Hotel room management functionality will be implemented here.
              </p>
              <Button variant="primary" disabled>
                <i className="fas fa-plus me-2"></i>
                Add Room
              </Button>
            </Card.Body>
          </Card>
        </Tab>

        <Tab eventKey="reservations" title={<><i className="fas fa-calendar-check me-2"></i>Reservations</>}>
          <Card>
            <Card.Body className="text-center py-5">
              <i className="fas fa-calendar-check fa-4x text-muted mb-3"></i>
              <h4 className="text-muted">Reservation Management</h4>
              <p className="text-muted">
                Hotel reservation management functionality will be implemented here.
              </p>
              <Button variant="primary" disabled>
                <i className="fas fa-plus me-2"></i>
                New Reservation
              </Button>
            </Card.Body>
          </Card>
        </Tab>
      </Tabs>

      {/* Delete Confirmation Modal */}
      <Modal show={showDeleteModal} onHide={() => setShowDeleteModal(false)}>
        <Modal.Header closeButton>
          <Modal.Title>
            <i className="fas fa-exclamation-triangle text-danger me-2"></i>
            Confirm Delete
          </Modal.Title>
        </Modal.Header>
        <Modal.Body>
          <p>
            Are you sure you want to delete <strong>{hotel.name}</strong>?
          </p>
          <div className="alert alert-warning">
            <i className="fas fa-exclamation-triangle me-2"></i>
            This action cannot be undone. All rooms, reservations, and related data will be lost.
          </div>
        </Modal.Body>
        <Modal.Footer>
          <Button variant="secondary" onClick={() => setShowDeleteModal(false)}>
            Cancel
          </Button>
          <Button 
            variant="danger" 
            onClick={handleDelete}
            disabled={deleting}
          >
            {deleting ? (
              <>
                <Spinner animation="border" size="sm" className="me-2" />
                Deleting...
              </>
            ) : (
              <>
                <i className="fas fa-trash me-2"></i>
                Delete Hotel
              </>
            )}
          </Button>
        </Modal.Footer>
      </Modal>
    </Container>
  );
};

export default HotelDetail;
