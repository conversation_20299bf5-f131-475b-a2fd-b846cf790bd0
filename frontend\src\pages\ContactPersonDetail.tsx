import React, { useState, useEffect } from 'react';
import { use<PERSON><PERSON><PERSON>, <PERSON> } from 'react-router-dom';
import { Container, Row, Col, Card, Button, Badge, <PERSON><PERSON>, Spinner } from 'react-bootstrap';
import { contactPersonService, ContactPerson } from '../services/api';

const ContactPersonDetail: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const [contactPerson, setContactPerson] = useState<ContactPerson | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (id) {
      fetchContactPerson(parseInt(id));
    }
  }, [id]);

  const fetchContactPerson = async (contactPersonId: number) => {
    try {
      setLoading(true);
      const response = await contactPersonService.getContactPerson(contactPersonId);
      setContactPerson(response.data);
    } catch (err) {
      console.error('Error fetching contact person:', err);
      setError('Failed to fetch contact person details. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <Container className="d-flex justify-content-center align-items-center" style={{ minHeight: '400px' }}>
        <Spinner animation="border" role="status">
          <span className="visually-hidden">Loading...</span>
        </Spinner>
      </Container>
    );
  }

  if (error || !contactPerson) {
    return (
      <Container className="py-4">
        <Alert variant="danger">
          {error || 'Contact person not found'}
        </Alert>
        <Link to="/contact-persons" className="btn btn-primary">
          <i className="fas fa-arrow-left me-1"></i>
          Back to Contact Persons
        </Link>
      </Container>
    );
  }

  return (
    <Container fluid className="py-4">
      <Row className="mb-4">
        <Col>
          <div className="d-flex justify-content-between align-items-center">
            <div>
              <h2 className="mb-0">
                <i className="fas fa-address-book me-2"></i>
                {contactPerson.full_name}
              </h2>
              <p className="text-muted mb-0">Contact Person Details</p>
            </div>
            <div>
              <Link to="/contact-persons" className="btn btn-outline-secondary me-2">
                <i className="fas fa-arrow-left me-1"></i>
                Back to List
              </Link>
              <Link to={`/contact-persons/${id}/edit`} className="btn btn-primary">
                <i className="fas fa-edit me-1"></i>
                Edit
              </Link>
            </div>
          </div>
        </Col>
      </Row>

      <Row>
        <Col lg={4}>
          <Card className="mb-4">
            <Card.Header>
              <h5 className="mb-0">Contact Information</h5>
            </Card.Header>
            <Card.Body className="text-center">
              {contactPerson.photo ? (
                <img
                  src={contactPerson.photo}
                  alt={contactPerson.full_name}
                  className="rounded-circle mb-3"
                  style={{ width: '120px', height: '120px', objectFit: 'cover' }}
                />
              ) : (
                <div
                  className="rounded-circle bg-secondary d-flex align-items-center justify-content-center text-white mb-3 mx-auto"
                  style={{ width: '120px', height: '120px' }}
                >
                  <i className="fas fa-user fa-3x"></i>
                </div>
              )}

              <h4>{contactPerson.full_name}</h4>

              <Badge bg={contactPerson.is_available ? 'success' : 'secondary'} className="mb-3">
                {contactPerson.is_available ? 'Available' : 'Unavailable'}
              </Badge>

              <div className="text-start">
                <div className="mb-2">
                  <i className="fas fa-phone text-primary me-2"></i>
                  <strong>Phone:</strong> {contactPerson.phone}
                </div>
                <div className="mb-2">
                  <i className="fas fa-envelope text-primary me-2"></i>
                  <strong>Email:</strong> {contactPerson.email}
                </div>
                {contactPerson.position && (
                  <div className="mb-2">
                    <i className="fas fa-briefcase text-primary me-2"></i>
                    <strong>Position:</strong> {contactPerson.position}
                  </div>
                )}
                {contactPerson.organization && (
                  <div className="mb-2">
                    <i className="fas fa-building text-primary me-2"></i>
                    <strong>Organization:</strong> {contactPerson.organization}
                  </div>
                )}
              </div>

              <div className="mt-3">
                <Button
                  variant="outline-primary"
                  href={`mailto:${contactPerson.email}`}
                  className="me-2"
                >
                  <i className="fas fa-envelope me-1"></i>
                  Send Email
                </Button>
                <Button
                  variant="outline-success"
                  href={`tel:${contactPerson.phone}`}
                >
                  <i className="fas fa-phone me-1"></i>
                  Call
                </Button>
              </div>
            </Card.Body>
          </Card>

          <Card>
            <Card.Header>
              <h5 className="mb-0">Additional Information</h5>
            </Card.Header>
            <Card.Body>
              <div className="mb-2">
                <i className="fas fa-calendar text-primary me-2"></i>
                <strong>Created:</strong> {new Date(contactPerson.created_at).toLocaleDateString()}
              </div>
              <div className="mb-2">
                <i className="fas fa-clock text-primary me-2"></i>
                <strong>Last Updated:</strong> {new Date(contactPerson.updated_at).toLocaleDateString()}
              </div>
              <div className="mb-2">
                <i className="fas fa-calendar-check text-primary me-2"></i>
                <strong>Event:</strong> {contactPerson.event_name}
              </div>
              {contactPerson.notes && (
                <div className="mb-2">
                  <i className="fas fa-sticky-note text-primary me-2"></i>
                  <strong>Notes:</strong> {contactPerson.notes}
                </div>
              )}
            </Card.Body>
          </Card>
        </Col>
      </Row>
    </Container>
  );
};

export default ContactPersonDetail;
