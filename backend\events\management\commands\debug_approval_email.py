from django.core.management.base import BaseCommand
from participants.models import Participant
from badges.models import Badge
from events.email_service import get_email_service
from events.models import EmailLog
import os


class Command(BaseCommand):
    help = 'Debug approval email and badge attachment issues'

    def handle(self, *args, **options):
        self.stdout.write("🔍 Debugging approval email and badge attachments...")
        
        # Get a test participant
        participant = Participant.objects.first()
        if not participant:
            self.stdout.write("❌ No participants found for testing")
            return
            
        self.stdout.write(f"📋 Testing with participant: {participant.full_name} ({participant.email})")
        self.stdout.write(f"   Status: {participant.status}")
        
        # Check if participant has a badge
        try:
            badge = Badge.objects.get(participant=participant)
            self.stdout.write(f"🎫 Badge found: ID={badge.id}")
            self.stdout.write(f"   Generated: {badge.is_generated}")
            self.stdout.write(f"   Badge file: {badge.badge_image.name if badge.badge_image else 'None'}")
            
            if badge.badge_image:
                badge_path = badge.badge_image.path
                self.stdout.write(f"   Badge path: {badge_path}")
                self.stdout.write(f"   File exists: {os.path.exists(badge_path)}")
                if os.path.exists(badge_path):
                    file_size = os.path.getsize(badge_path)
                    self.stdout.write(f"   File size: {file_size} bytes")
            else:
                self.stdout.write("   ⚠️ No badge image file")
                
        except Badge.DoesNotExist:
            self.stdout.write("🎫 No badge found - creating one...")
            try:
                badge = Badge.objects.create(participant=participant)
                self.stdout.write(f"   ✓ Badge created: ID={badge.id}")
            except Exception as e:
                self.stdout.write(f"   ❌ Error creating badge: {e}")
                return
        
        # Generate badge if not generated
        if not badge.is_generated or not badge.badge_image:
            self.stdout.write("🎨 Generating badge...")
            try:
                badge.generate_badge()
                self.stdout.write("   ✓ Badge generated successfully")
                self.stdout.write(f"   Badge file: {badge.badge_image.name if badge.badge_image else 'None'}")
                
                if badge.badge_image:
                    badge_path = badge.badge_image.path
                    self.stdout.write(f"   Badge path: {badge_path}")
                    self.stdout.write(f"   File exists: {os.path.exists(badge_path)}")
                    if os.path.exists(badge_path):
                        file_size = os.path.getsize(badge_path)
                        self.stdout.write(f"   File size: {file_size} bytes")
                        
            except Exception as e:
                self.stdout.write(f"   ❌ Error generating badge: {e}")
                import traceback
                traceback.print_exc()
        
        # Test email service
        self.stdout.write("\n📧 Testing email service...")
        email_service = get_email_service()
        self.stdout.write(f"   Email service: {email_service.config}")
        
        # Test approval email with badge attachment
        self.stdout.write("\n📤 Sending approval email with badge...")
        try:
            result = email_service.send_event_details(participant)
            self.stdout.write(f"   Result: {result}")
            
            if result:
                self.stdout.write("   ✅ Email sent successfully!")
            else:
                self.stdout.write("   ❌ Email failed to send")
                
        except Exception as e:
            self.stdout.write(f"   ❌ Error sending email: {e}")
            import traceback
            traceback.print_exc()
        
        # Check email logs
        self.stdout.write("\n📋 Recent email logs:")
        recent_logs = EmailLog.objects.filter(
            recipient_email=participant.email
        ).order_by('-created_at')[:3]
        
        for log in recent_logs:
            self.stdout.write(f"   - {log.template_type}: {log.status}")
            if log.error_message:
                self.stdout.write(f"     Error: {log.error_message}")
            self.stdout.write(f"     Time: {log.created_at}")
        
        # Test manual approval process
        self.stdout.write("\n🔄 Testing manual approval process...")
        if participant.status != 'approved':
            old_status = participant.status
            participant.status = 'approved'
            participant.save()
            self.stdout.write(f"   Status changed from {old_status} to {participant.status}")
            self.stdout.write("   ✓ Approval signal should have triggered email")
        else:
            self.stdout.write("   Participant already approved")
        
        self.stdout.write("\n✅ Debug completed!")
