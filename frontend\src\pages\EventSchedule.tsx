import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, Badge } from 'react-bootstrap';
import { useParams, Link } from 'react-router-dom';
import { eventService, Event, EventSchedule as EventScheduleType } from '../services/api';
import DailySchedule from '../components/DailySchedule';
import ModernLayout from '../components/ModernLayout';

const EventSchedule: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const [event, setEvent] = useState<Event | null>(null);
  const [schedules, setSchedules] = useState<EventScheduleType[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');

  useEffect(() => {
    if (id) {
      fetchEventData();
    }
  }, [id]);

  const fetchEventData = async () => {
    try {
      const [eventResponse, scheduleResponse] = await Promise.all([
        eventService.getEvent(parseInt(id!)),
        eventService.getEventSchedule(parseInt(id!)),
      ]);

      setEvent(eventResponse.data);
      setSchedules(scheduleResponse.data);
    } catch (error) {
      console.error('Error fetching event data:', error);
      setError('Failed to load event schedule');
    } finally {
      setLoading(false);
    }
  };

  const handleDownloadPDF = async () => {
    try {
      const response = await fetch(`/api/events/${id}/schedule/pdf/`);
      if (!response.ok) {
        throw new Error('Failed to download PDF');
      }

      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `${event?.name.replace(/\s+/g, '_')}_Schedule.pdf` || 'Event_Schedule.pdf';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);
    } catch (error) {
      console.error('Error downloading PDF:', error);
      alert('Failed to download schedule PDF. Please try again.');
    }
  };



  if (loading) {
    return (
      <ModernLayout showHero={false}>
        <div className="container py-5">
          <div className="text-center py-5">
            <div className="spinner-border text-primary" role="status" style={{ width: '3rem', height: '3rem' }}>
              <span className="visually-hidden">Loading...</span>
            </div>
            <h4 className="mt-4 text-muted">Loading Schedule...</h4>
            <p className="text-muted">Please wait while we fetch the event schedule</p>
          </div>
        </div>
      </ModernLayout>
    );
  }

  if (error) {
    return (
      <ModernLayout showHero={false}>
        <div className="py-5">
          <div className="text-center py-5">
            <i className="fas fa-exclamation-triangle fa-4x text-danger mb-4"></i>
            <h2 className="text-dark mb-3">Error Loading Schedule</h2>
            <p className="text-muted mb-4 lead">{error}</p>
            <Link to="/#events" className="btn btn-primary btn-lg">
              <i className="fas fa-arrow-left me-2"></i>
              Back to Events
            </Link>
          </div>
        </div>
      </ModernLayout>
    );
  }

  if (!event) {
    return (
      <ModernLayout showHero={false}>
        <div className="py-5">
          <div className="text-center py-5">
            <i className="fas fa-question-circle fa-4x text-warning mb-4"></i>
            <h2 className="text-dark mb-3">Event Not Found</h2>
            <p className="text-muted mb-4 lead">The requested event could not be found</p>
            <Link to="/#events" className="btn btn-primary btn-lg">
              <i className="fas fa-arrow-left me-2"></i>
              Back to Events
            </Link>
          </div>
        </div>
      </ModernLayout>
    );
  }

  return (
    <ModernLayout showHero={false} className="event-schedule-page">
      <div className="schedule-hero-section py-5 bg-gradient-primary text-white">
        <div className="row align-items-center">
            <div className="col-lg-8">
              <Link to={`/events/${event.id}`} className="btn btn-outline-light btn-sm mb-3">
                <i className="fas fa-arrow-left me-2"></i>
                Back to Event Details
              </Link>
              <h1 className="display-4 fw-bold mb-3">{event.name}</h1>
              <h2 className="h3 mb-4 opacity-75">Complete Event Schedule</h2>
              <div className="d-flex flex-wrap gap-3">
                <Badge bg="light" text="dark" className="fs-6 px-3 py-2">
                  <i className="fas fa-calendar me-1"></i>
                  {new Date(event.start_date).toLocaleDateString()} - {new Date(event.end_date).toLocaleDateString()}
                </Badge>
                <Badge bg="light" text="dark" className="fs-6 px-3 py-2">
                  <i className="fas fa-clock me-1"></i>
                  {schedules.length} Sessions
                </Badge>
                <Badge bg="light" text="dark" className="fs-6 px-3 py-2">
                  <i className="fas fa-map-marker-alt me-1"></i>
                  {event.location}
                </Badge>
              </div>
            </div>
          </div>
      </div>

      <div className="py-5">
        {/* Quick Navigation */}
        <div className="row mb-5">
          <div className="col-12">
            <div className="d-flex flex-wrap gap-3 justify-content-center">
              <Link to={`/events/${event.id}`} className="btn btn-outline-primary">
                <i className="fas fa-info-circle me-2"></i>
                Event Details
              </Link>
              <Link to={`/events/${event.id}/gallery`} className="btn btn-outline-primary">
                <i className="fas fa-images me-2"></i>
                Event Gallery
              </Link>
              <Link to="/#events" className="btn btn-outline-secondary">
                <i className="fas fa-list me-2"></i>
                All Events
              </Link>
            </div>
          </div>
        </div>

        {/* Schedule Overview Card */}
        <div className="row mb-5">
          <div className="col-12">
            <Card className="border-0 shadow-lg">
              <Card.Header className="bg-gradient-primary text-white">
                <div className="d-flex justify-content-between align-items-center">
                  <h3 className="mb-0">
                    <i className="fas fa-calendar-alt me-2"></i>
                    Schedule Overview
                  </h3>
                  <Badge bg="light" text="dark" className="fs-6">
                    {schedules.length} Total Sessions
                  </Badge>
                </div>
              </Card.Header>
              <Card.Body className="p-4">
                <div className="row g-4 text-center">
                  <div className="col-md-3">
                    <div className="schedule-stat">
                      <i className="fas fa-calendar-day text-primary fa-2x mb-2"></i>
                      <h5 className="fw-bold text-dark">
                        {Math.ceil((new Date(event.end_date).getTime() - new Date(event.start_date).getTime()) / (1000 * 60 * 60 * 24)) + 1}
                      </h5>
                      <small className="text-muted">Days</small>
                    </div>
                  </div>
                  <div className="col-md-3">
                    <div className="schedule-stat">
                      <i className="fas fa-clock text-success fa-2x mb-2"></i>
                      <h5 className="fw-bold text-dark">{schedules.length}</h5>
                      <small className="text-muted">Sessions</small>
                    </div>
                  </div>
                  <div className="col-md-3">
                    <div className="schedule-stat">
                      <i className="fas fa-users text-info fa-2x mb-2"></i>
                      <h5 className="fw-bold text-dark">{Array.from(new Set(schedules.map(s => s.speaker).filter(Boolean))).length}</h5>
                      <small className="text-muted">Speakers</small>
                    </div>
                  </div>
                  <div className="col-md-3">
                    <div className="schedule-stat">
                      <i className="fas fa-map-marker-alt text-warning fa-2x mb-2"></i>
                      <h5 className="fw-bold text-dark">{Array.from(new Set(schedules.map(s => s.location).filter(Boolean))).length}</h5>
                      <small className="text-muted">Venues</small>
                    </div>
                  </div>
                </div>
              </Card.Body>
            </Card>
          </div>
        </div>

        {/* Daily Schedule */}
        <DailySchedule schedules={schedules} eventName={event?.name || 'Event'} />

        {/* Download Schedule Button */}
        {schedules.length > 0 && (
          <div className="row mt-5">
            <div className="col-12 text-center">
              <Card className="border-0 shadow-sm">
                <Card.Body className="p-4">
                  <h5 className="text-dark mb-3">
                    <i className="fas fa-download me-2"></i>
                    Download Schedule
                  </h5>
                  <p className="text-muted mb-4">
                    Get a copy of the complete event schedule for offline viewing
                  </p>
                  <Button
                    variant="success"
                    size="lg"
                    className="px-4"
                    onClick={handleDownloadPDF}
                  >
                    <i className="fas fa-download me-2"></i>
                    Download Schedule (PDF)
                  </Button>
                </Card.Body>
              </Card>
            </div>
          </div>
        )}
      </div>
    </ModernLayout>
  );
};

export default EventSchedule;
