import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON> } from 'react-bootstrap';
import { Link } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext';
import { authService, DashboardStats } from '../services/auth';

const Dashboard: React.FC = () => {
  const { user } = useAuth();
  const [stats, setStats] = useState<DashboardStats>({});
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');

  useEffect(() => {
    fetchDashboardStats();
  }, []);

  const fetchDashboardStats = async () => {
    try {
      const dashboardStats = await authService.getDashboardStats();
      setStats(dashboardStats);
    } catch (error) {
      console.error('Error fetching dashboard stats:', error);
      setError('Failed to load dashboard statistics');
    } finally {
      setLoading(false);
    }
  };

  const getGreeting = () => {
    const hour = new Date().getHours();
    if (hour < 12) return 'Good Morning';
    if (hour < 17) return 'Good Afternoon';
    return 'Good Evening';
  };

  const getQuickActions = () => {
    const actions = [];

    if (user?.role_name === 'administrator') {
      actions.push({
        title: 'Manage Events',
        description: 'Create and manage events',
        icon: 'fas fa-calendar-plus',
        color: 'primary',
        link: '/events'
      });
    }

    if (user?.role_name === 'administrator') {
      actions.push({
        title: 'Manage Participants',
        description: 'View and manage participants',
        icon: 'fas fa-users',
        color: 'info',
        link: '/participants'
      });
    }

    if (user?.role_name === 'administrator') {
      actions.push({
        title: 'Take Attendance',
        description: 'Scan QR codes for attendance',
        icon: 'fas fa-qrcode',
        color: 'success',
        link: '/scan'
      });
    }

    if (user?.role_name === 'administrator') {
      actions.push({
        title: 'Upload Photos',
        description: 'Manage event gallery',
        icon: 'fas fa-images',
        color: 'warning',
        link: '/gallery/upload'
      });
    }

    if (user?.role_name === 'administrator') {
      actions.push({
        title: 'Manage Schedule',
        description: 'Create event schedules',
        icon: 'fas fa-clock',
        color: 'secondary',
        link: '/schedule/manage'
      });
    }

    if (user?.role_name === 'administrator') {
      actions.push({
        title: 'Generate Badges',
        description: 'Create participant badges',
        icon: 'fas fa-id-badge',
        color: 'dark',
        link: '/badges'
      });
    }

    return actions;
  };

  if (loading) {
    return (
      <Container className="text-center mt-5">
        <Spinner animation="border" role="status">
          <span className="visually-hidden">Loading...</span>
        </Spinner>
        <p className="mt-3">Loading dashboard...</p>
      </Container>
    );
  }

  return (
    <Container className="py-4">
      {/* Welcome Header */}
      <Row className="mb-4">
        <Col>
          <div className="d-flex justify-content-between align-items-center">
            <div>
              <h1 className="display-6 fw-bold mb-2">
                {getGreeting()}, {user?.first_name}!
              </h1>
              <p className="lead text-muted mb-0">
                Welcome to your {user?.role_display_name} dashboard
              </p>
            </div>
            <div className="text-end">
              <Badge 
                bg="primary" 
                className="fs-6 px-3 py-2"
                style={{ backgroundColor: user?.role_color }}
              >
                <i className="fas fa-user-tag me-2"></i>
                {user?.role_display_name}
              </Badge>
            </div>
          </div>
        </Col>
      </Row>

      {error && (
        <Alert variant="danger" className="mb-4">
          <i className="fas fa-exclamation-triangle me-2"></i>
          {error}
        </Alert>
      )}

      {/* Statistics Cards */}
      <Row className="mb-5">
        {user?.role_name === 'administrator' && (
          <>
            <Col lg={3} md={6} className="mb-4">
              <Card className="border-0 shadow-sm h-100 stat-card">
                <Card.Body className="text-center">
                  <div className="stat-icon text-primary mb-3">
                    <i className="fas fa-users fa-2x"></i>
                  </div>
                  <h3 className="fw-bold mb-1">{stats.total_users || 0}</h3>
                  <p className="text-muted mb-0">Total Users</p>
                  <small className="text-success">
                    <i className="fas fa-check-circle me-1"></i>
                    {stats.active_users || 0} active
                  </small>
                </Card.Body>
              </Card>
            </Col>
            <Col lg={3} md={6} className="mb-4">
              <Card className="border-0 shadow-sm h-100 stat-card">
                <Card.Body className="text-center">
                  <div className="stat-icon text-success mb-3">
                    <i className="fas fa-sign-in-alt fa-2x"></i>
                  </div>
                  <h3 className="fw-bold mb-1">{stats.total_sessions || 0}</h3>
                  <p className="text-muted mb-0">Active Sessions</p>
                  <small className="text-info">
                    <i className="fas fa-clock me-1"></i>
                    {stats.recent_logins || 0} recent logins
                  </small>
                </Card.Body>
              </Card>
            </Col>
          </>
        )}

        {user?.role_name === 'administrator' && (
          <Col lg={3} md={6} className="mb-4">
            <Card className="border-0 shadow-sm h-100 stat-card">
              <Card.Body className="text-center">
                <div className="stat-icon text-warning mb-3">
                  <i className="fas fa-calendar fa-2x"></i>
                </div>
                <h3 className="fw-bold mb-1">{stats.total_events || 0}</h3>
                <p className="text-muted mb-0">Total Events</p>
                <small className="text-primary">
                  <i className="fas fa-play me-1"></i>
                  {stats.active_events || 0} active
                </small>
              </Card.Body>
            </Card>
          </Col>
        )}

        {user?.role_name === 'administrator' && (
          <Col lg={3} md={6} className="mb-4">
            <Card className="border-0 shadow-sm h-100 stat-card">
              <Card.Body className="text-center">
                <div className="stat-icon text-info mb-3">
                  <i className="fas fa-user-friends fa-2x"></i>
                </div>
                <h3 className="fw-bold mb-1">{stats.total_participants || 0}</h3>
                <p className="text-muted mb-0">Participants</p>
                <small className="text-success">
                  <i className="fas fa-check me-1"></i>
                  {stats.confirmed_participants || 0} confirmed
                </small>
              </Card.Body>
            </Card>
          </Col>
        )}

        {user?.role_name === 'administrator' && (
          <Col lg={3} md={6} className="mb-4">
            <Card className="border-0 shadow-sm h-100 stat-card">
              <Card.Body className="text-center">
                <div className="stat-icon text-success mb-3">
                  <i className="fas fa-check-circle fa-2x"></i>
                </div>
                <h3 className="fw-bold mb-1">{stats.today_attendance || 0}</h3>
                <p className="text-muted mb-0">Today's Attendance</p>
                <small className="text-muted">
                  <i className="fas fa-history me-1"></i>
                  {stats.total_attendance || 0} total
                </small>
              </Card.Body>
            </Card>
          </Col>
        )}

        {user?.role_name === 'administrator' && (
          <Col lg={3} md={6} className="mb-4">
            <Card className="border-0 shadow-sm h-100 stat-card">
              <Card.Body className="text-center">
                <div className="stat-icon text-danger mb-3">
                  <i className="fas fa-images fa-2x"></i>
                </div>
                <h3 className="fw-bold mb-1">{stats.total_photos || 0}</h3>
                <p className="text-muted mb-0">Gallery Photos</p>
                <small className="text-warning">
                  <i className="fas fa-star me-1"></i>
                  {stats.featured_photos || 0} featured
                </small>
              </Card.Body>
            </Card>
          </Col>
        )}
      </Row>

      {/* Quick Actions */}
      <Row className="mb-5">
        <Col>
          <h3 className="fw-bold mb-4">
            <i className="fas fa-bolt me-2 text-warning"></i>
            Quick Actions
          </h3>
        </Col>
      </Row>

      <Row>
        {getQuickActions().map((action, index) => (
          <Col lg={4} md={6} key={index} className="mb-4">
            <Card className="border-0 shadow-sm h-100 action-card">
              <Card.Body className="p-4">
                <div className="d-flex align-items-start">
                  <div className={`action-icon text-${action.color} me-3`}>
                    <i className={`${action.icon} fa-2x`}></i>
                  </div>
                  <div className="flex-grow-1">
                    <h5 className="fw-bold mb-2">{action.title}</h5>
                    <p className="text-muted mb-3">{action.description}</p>
                    <Link to={action.link} className={`btn btn-${action.color} btn-sm`}>
                      Get Started
                      <i className="fas fa-arrow-right ms-2"></i>
                    </Link>
                  </div>
                </div>
              </Card.Body>
            </Card>
          </Col>
        ))}
      </Row>

      {/* Custom Styles */}
      <style>{`
        .stat-card {
          transition: transform 0.2s ease, box-shadow 0.2s ease;
        }
        
        .stat-card:hover {
          transform: translateY(-5px);
          box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1) !important;
        }
        
        .action-card {
          transition: transform 0.2s ease, box-shadow 0.2s ease;
        }
        
        .action-card:hover {
          transform: translateY(-3px);
          box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1) !important;
        }
        
        .stat-icon {
          opacity: 0.8;
        }
        
        .action-icon {
          opacity: 0.9;
        }
      `}</style>
    </Container>
  );
};

export default Dashboard;
