from django.core.management.base import BaseCommand
from participants.models import Participant, ParticipantType
from events.models import Event, EmailLog, EventGallery
from events.email_service import get_email_service
from badges.models import Badge
from django.utils import timezone
from django.core.files.uploadedfile import SimpleUploadedFile
from PIL import Image
import io
import os


class Command(BaseCommand):
    help = 'Test all email types to ensure they work perfectly'

    def handle(self, *args, **options):
        self.stdout.write("🧪 Testing all email types...")
        
        # Get test data
        try:
            event = Event.objects.first()
            participant = Participant.objects.first()
            
            if not event or not participant:
                self.stdout.write("❌ No test data available")
                return
                
            self.stdout.write(f"📅 Using event: {event.name}")
            self.stdout.write(f"👤 Using participant: {participant.full_name}")
            
        except Exception as e:
            self.stdout.write(f"❌ Error getting test data: {e}")
            return
        
        email_service = get_email_service()
        test_results = {}
        
        # Test 1: Registration Confirmation Email
        self.stdout.write("\n📧 Test 1: Registration Confirmation Email")
        try:
            result = email_service.send_registration_confirmation(participant)
            test_results['registration_confirmation'] = result
            status = "✅ PASS" if result else "❌ FAIL"
            self.stdout.write(f"   {status}")
        except Exception as e:
            test_results['registration_confirmation'] = False
            self.stdout.write(f"   ❌ FAIL: {e}")
        
        # Test 2: Approval Email with Badge
        self.stdout.write("\n📧 Test 2: Approval Email with Badge")
        try:
            # Ensure participant has a badge
            badge, created = Badge.objects.get_or_create(participant=participant)
            if not badge.is_generated:
                badge.generate_badge()
            
            result = email_service.send_event_details(participant)
            test_results['event_details'] = result
            status = "✅ PASS" if result else "❌ FAIL"
            self.stdout.write(f"   {status}")
        except Exception as e:
            test_results['event_details'] = False
            self.stdout.write(f"   ❌ FAIL: {e}")
        
        # Test 3: Event Reminder Email
        self.stdout.write("\n📧 Test 3: Event Reminder Email")
        try:
            participants = [participant]
            result = email_service.send_event_reminder(participants, event, days_before=3)
            test_results['event_reminder'] = result > 0
            status = "✅ PASS" if result > 0 else "❌ FAIL"
            self.stdout.write(f"   {status} (sent to {result} participants)")
        except Exception as e:
            test_results['event_reminder'] = False
            self.stdout.write(f"   ❌ FAIL: {e}")
        
        # Test 4: Daily Gallery Email (create test gallery images)
        self.stdout.write("\n📧 Test 4: Daily Gallery Email")
        try:
            # Create test gallery images if none exist
            gallery_count = EventGallery.objects.filter(event=event).count()
            if gallery_count == 0:
                self.stdout.write("   📸 Creating test gallery images...")
                
                # Create a simple test image
                img = Image.new('RGB', (100, 100), color='blue')
                img_io = io.BytesIO()
                img.save(img_io, format='JPEG')
                img_io.seek(0)
                
                # Create test gallery entries
                for i in range(3):
                    gallery = EventGallery.objects.create(
                        event=event,
                        title=f"Test Image {i+1}",
                        description=f"Test gallery image {i+1}",
                        image=SimpleUploadedFile(
                            f"test_image_{i+1}.jpg",
                            img_io.getvalue(),
                            content_type="image/jpeg"
                        )
                    )
                
                self.stdout.write(f"   ✓ Created {3} test gallery images")
            
            participants = [participant]
            result = email_service.send_daily_gallery(participants, event, timezone.now().date())
            test_results['daily_gallery'] = result > 0
            status = "✅ PASS" if result > 0 else "❌ FAIL"
            self.stdout.write(f"   {status} (sent to {result} participants)")
            
        except Exception as e:
            test_results['daily_gallery'] = False
            self.stdout.write(f"   ❌ FAIL: {e}")
            import traceback
            traceback.print_exc()
        
        # Test 5: Schedule Update Email
        self.stdout.write("\n📧 Test 5: Schedule Update Email")
        try:
            participants = [participant]
            result = email_service.send_schedule_update(participants, event)
            test_results['schedule_update'] = result > 0
            status = "✅ PASS" if result > 0 else "❌ FAIL"
            self.stdout.write(f"   {status} (sent to {result} participants)")
        except Exception as e:
            test_results['schedule_update'] = False
            self.stdout.write(f"   ❌ FAIL: {e}")
        
        # Test 6: Badge Notification Email
        self.stdout.write("\n📧 Test 6: Badge Notification Email")
        try:
            result = email_service.send_badge_notification(participant)
            test_results['badge_notification'] = result
            status = "✅ PASS" if result else "❌ FAIL"
            self.stdout.write(f"   {status}")
        except Exception as e:
            test_results['badge_notification'] = False
            self.stdout.write(f"   ❌ FAIL: {e}")
        
        # Test Admin Approval Process
        self.stdout.write("\n🔧 Test 7: Admin Approval Process")
        try:
            # Create a test participant for approval
            test_participant = Participant.objects.create(
                first_name="Test",
                last_name="Admin",
                email="<EMAIL>",
                phone="+251-911-000000",
                institution_name="Test Institution",
                event=event,
                participant_type=ParticipantType.objects.first(),
                arrival_date=timezone.now() + timezone.timedelta(days=1),
                departure_date=timezone.now() + timezone.timedelta(days=3),
                status='pending'
            )
            
            # Simulate admin approval (this should trigger email)
            old_status = test_participant.status
            test_participant.status = 'approved'
            test_participant.approved_at = timezone.now()
            test_participant.save()  # This should trigger the signal
            
            # Check if email was logged
            approval_log = EmailLog.objects.filter(
                recipient_email=test_participant.email,
                template_type='event_details'
            ).order_by('-created_at').first()
            
            admin_approval_works = approval_log is not None
            test_results['admin_approval'] = admin_approval_works
            status = "✅ PASS" if admin_approval_works else "❌ FAIL"
            self.stdout.write(f"   {status} (signal-based approval)")
            
            # Cleanup test participant
            test_participant.delete()
            
        except Exception as e:
            test_results['admin_approval'] = False
            self.stdout.write(f"   ❌ FAIL: {e}")
        
        # Check email logs
        self.stdout.write("\n📋 Recent Email Logs:")
        recent_logs = EmailLog.objects.filter(
            recipient_email=participant.email
        ).order_by('-created_at')[:10]
        
        for log in recent_logs:
            status_icon = "✅" if log.status == 'sent' else "❌"
            self.stdout.write(f"   {status_icon} {log.template_type}: {log.status} at {log.created_at}")
        
        # Summary
        self.stdout.write("\n📊 EMAIL SYSTEM TEST SUMMARY:")
        self.stdout.write("=" * 50)
        
        total_tests = len(test_results)
        passed_tests = sum(1 for result in test_results.values() if result)
        
        for email_type, result in test_results.items():
            status = "✅ PASS" if result else "❌ FAIL"
            self.stdout.write(f"   {email_type.replace('_', ' ').title()}: {status}")
        
        self.stdout.write("=" * 50)
        self.stdout.write(f"📈 OVERALL RESULT: {passed_tests}/{total_tests} tests passed")
        
        if passed_tests == total_tests:
            self.stdout.write("🎉 ALL EMAIL TYPES WORKING PERFECTLY!")
        else:
            failed_tests = total_tests - passed_tests
            self.stdout.write(f"⚠️ {failed_tests} email type(s) need attention")
        
        # Email configuration status
        self.stdout.write(f"\n⚙️ Email Configuration: {email_service.config}")
        
        self.stdout.write("\n✅ Email system test completed!")
