from django.core.management.base import BaseCommand
from django.utils import timezone
from datetime import datetime, timedelta
from events.models import Event, EventGallery
from events.email_service import get_email_service
from participants.models import Participant


class Command(BaseCommand):
    help = 'Send daily gallery emails to event participants'
    
    def add_arguments(self, parser):
        parser.add_argument(
            '--date',
            type=str,
            help='Date to send gallery for (YYYY-MM-DD format). Defaults to yesterday.',
        )
        parser.add_argument(
            '--event-id',
            type=int,
            help='Specific event ID to send gallery for. If not provided, sends for all active events.',
        )
        parser.add_argument(
            '--dry-run',
            action='store_true',
            help='Show what would be sent without actually sending emails.',
        )
    
    def handle(self, *args, **options):
        # Parse date
        if options['date']:
            try:
                target_date = datetime.strptime(options['date'], '%Y-%m-%d').date()
            except ValueError:
                self.stdout.write(
                    self.style.ERROR('Invalid date format. Use YYYY-MM-DD')
                )
                return
        else:
            # Default to yesterday
            target_date = (timezone.now() - timedelta(days=1)).date()
        
        self.stdout.write(f'Processing gallery emails for date: {target_date}')
        
        # Get events to process
        if options['event_id']:
            try:
                events = [Event.objects.get(id=options['event_id'])]
            except Event.DoesNotExist:
                self.stdout.write(
                    self.style.ERROR(f'Event with ID {options["event_id"]} not found')
                )
                return
        else:
            # Get events that were active on the target date
            events = Event.objects.filter(
                start_date__date__lte=target_date,
                end_date__date__gte=target_date,
                is_active=True
            )
        
        if not events:
            self.stdout.write(
                self.style.WARNING('No active events found for the specified date')
            )
            return
        
        email_service = get_email_service()
        total_sent = 0
        
        for event in events:
            self.stdout.write(f'\nProcessing event: {event.name}')
            
            # Check if there are gallery images for this date
            gallery_images = EventGallery.objects.filter(
                event=event,
                uploaded_at__date=target_date
            )
            
            if not gallery_images.exists():
                self.stdout.write(
                    self.style.WARNING(f'  No gallery images found for {target_date}')
                )
                continue
            
            # Get participants for this event
            participants = Participant.objects.filter(event=event)
            
            if not participants.exists():
                self.stdout.write(
                    self.style.WARNING('  No participants found for this event')
                )
                continue
            
            self.stdout.write(
                f'  Found {gallery_images.count()} images and {participants.count()} participants'
            )
            
            if options['dry_run']:
                self.stdout.write(
                    self.style.SUCCESS(
                        f'  [DRY RUN] Would send gallery email to {participants.count()} participants'
                    )
                )
                continue
            
            # Send gallery emails
            try:
                sent_count = email_service.send_daily_gallery(participants, event, target_date)
                total_sent += sent_count
                
                self.stdout.write(
                    self.style.SUCCESS(
                        f'  Successfully sent gallery to {sent_count}/{participants.count()} participants'
                    )
                )
                
                if sent_count < participants.count():
                    self.stdout.write(
                        self.style.WARNING(
                            f'  {participants.count() - sent_count} emails failed to send'
                        )
                    )
                
            except Exception as e:
                self.stdout.write(
                    self.style.ERROR(f'  Error sending gallery emails: {str(e)}')
                )
        
        if options['dry_run']:
            self.stdout.write(
                self.style.SUCCESS('\n[DRY RUN] No emails were actually sent')
            )
        else:
            self.stdout.write(
                self.style.SUCCESS(f'\nTotal emails sent: {total_sent}')
            )
        
        # Summary of email logs
        if not options['dry_run']:
            from events.models import EmailLog
            recent_logs = EmailLog.objects.filter(
                template_type='daily_gallery',
                created_at__date=timezone.now().date()
            )
            
            sent_today = recent_logs.filter(status='sent').count()
            failed_today = recent_logs.filter(status='failed').count()
            
            self.stdout.write(f'\nToday\'s email summary:')
            self.stdout.write(f'  Sent: {sent_today}')
            self.stdout.write(f'  Failed: {failed_today}')
            
            if failed_today > 0:
                self.stdout.write(
                    self.style.WARNING(
                        f'Check email logs for details on failed emails'
                    )
                )
