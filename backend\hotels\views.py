from rest_framework import viewsets, status
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework.permissions import AllowAny, IsAuthenticated
from django.http import HttpResponse, JsonResponse
from django.core.files.storage import default_storage
import csv
import io
from .models import Hotel, HotelRoom, HotelReservation
from .serializers import (
    HotelSerializer, HotelListSerializer, HotelRoomSerializer,
    HotelReservationSerializer
)


class HotelViewSet(viewsets.ModelViewSet):
    queryset = Hotel.objects.all()

    def get_permissions(self):
        """Allow public access for list, retrieve, create, update, rooms, and export actions"""
        if self.action in ['list', 'retrieve', 'create', 'update', 'partial_update', 'rooms', 'export_csv', 'download_sample', 'import_csv']:
            permission_classes = [AllowAny]
        else:
            permission_classes = [IsAuthenticated]
        return [permission() for permission in permission_classes]

    def get_serializer_class(self):
        if self.action == 'list':
            return HotelListSerializer
        return HotelSerializer

    def get_queryset(self):
        queryset = super().get_queryset()
        event_id = self.request.query_params.get('event', None)
        is_active = self.request.query_params.get('active', None)

        if event_id is not None:
            queryset = queryset.filter(event=event_id)
        if is_active is not None:
            queryset = queryset.filter(is_active=is_active.lower() == 'true')

        return queryset

    @action(detail=True, methods=['get'])
    def rooms(self, request, pk=None):
        """Get hotel rooms"""
        hotel = self.get_object()
        rooms = hotel.rooms.all()
        available_only = request.query_params.get('available', None)

        if available_only and available_only.lower() == 'true':
            rooms = rooms.filter(is_available=True)

        serializer = HotelRoomSerializer(rooms, many=True)
        return Response(serializer.data)

    @action(detail=False, methods=['get'])
    def export_csv(self, request):
        """Export hotels to CSV"""
        response = HttpResponse(content_type='text/csv')
        response['Content-Disposition'] = 'attachment; filename="hotels_export.csv"'

        writer = csv.writer(response)
        writer.writerow([
            'Name', 'Address', 'Phone', 'Email', 'Contact Person', 'Star Rating',
            'Website', 'Description', 'Latitude', 'Longitude', 'Event ID', 'Is Active'
        ])

        for hotel in self.get_queryset():
            writer.writerow([
                hotel.name, hotel.address, hotel.phone, hotel.email,
                hotel.contact_person, hotel.star_rating or '', hotel.website or '',
                hotel.description, hotel.latitude or '', hotel.longitude or '',
                hotel.event.id if hotel.event else '', hotel.is_active
            ])

        return response

    @action(detail=False, methods=['get'])
    def download_sample(self, request):
        """Download sample CSV template"""
        response = HttpResponse(content_type='text/csv')
        response['Content-Disposition'] = 'attachment; filename="hotels_sample.csv"'

        writer = csv.writer(response)
        writer.writerow([
            'Name', 'Address', 'Phone', 'Email', 'Contact Person', 'Star Rating',
            'Website', 'Description', 'Latitude', 'Longitude', 'Event ID', 'Is Active'
        ])
        # Add sample rows (replace with actual data)
        writer.writerow([
            'Hotel Name', 'Hotel Address, Gondar, Ethiopia', '+251-911-123456',
            '<EMAIL>', 'Hotel Manager', '4', 'https://hotel.com',
            'Hotel description', '12.6090', '37.4692', '1', 'True'
        ])

        return response

    @action(detail=False, methods=['post'])
    def import_csv(self, request):
        """Import hotels from CSV"""
        if 'file' not in request.FILES:
            return Response({'error': 'No file provided'}, status=status.HTTP_400_BAD_REQUEST)

        csv_file = request.FILES['file']
        if not csv_file.name.endswith('.csv'):
            return Response({'error': 'File must be a CSV'}, status=status.HTTP_400_BAD_REQUEST)

        try:
            decoded_file = csv_file.read().decode('utf-8')
            io_string = io.StringIO(decoded_file)
            reader = csv.DictReader(io_string)

            created_count = 0
            errors = []

            for row_num, row in enumerate(reader, start=2):
                try:
                    # Get or create event
                    event = None
                    if row.get('Event ID'):
                        from events.models import Event
                        try:
                            event = Event.objects.get(id=int(row['Event ID']))
                        except Event.DoesNotExist:
                            errors.append(f"Row {row_num}: Event with ID {row['Event ID']} not found")
                            continue

                    # Create hotel
                    hotel_data = {
                        'name': row['Name'],
                        'address': row['Address'],
                        'phone': row['Phone'],
                        'email': row['Email'],
                        'contact_person': row['Contact Person'],
                        'star_rating': int(row['Star Rating']) if row.get('Star Rating') else None,
                        'website': row.get('Website', ''),
                        'description': row.get('Description', ''),
                        'latitude': float(row['Latitude']) if row.get('Latitude') else None,
                        'longitude': float(row['Longitude']) if row.get('Longitude') else None,
                        'event': event,
                        'is_active': row.get('Is Active', 'True').lower() == 'true'
                    }

                    hotel = Hotel.objects.create(**hotel_data)
                    created_count += 1

                except Exception as e:
                    errors.append(f"Row {row_num}: {str(e)}")

            return Response({
                'message': f'Successfully imported {created_count} hotels',
                'created_count': created_count,
                'errors': errors
            })

        except Exception as e:
            return Response({'error': f'Error processing file: {str(e)}'},
                          status=status.HTTP_400_BAD_REQUEST)

    @action(detail=True, methods=['get'])
    def reservations(self, request, pk=None):
        """Get hotel reservations"""
        hotel = self.get_object()
        reservations = hotel.reservations.all()
        serializer = HotelReservationSerializer(reservations, many=True)
        return Response(serializer.data)


class HotelRoomViewSet(viewsets.ModelViewSet):
    queryset = HotelRoom.objects.all()
    serializer_class = HotelRoomSerializer

    def get_permissions(self):
        """Allow public access for list and retrieve actions"""
        if self.action in ['list', 'retrieve']:
            permission_classes = [AllowAny]
        else:
            permission_classes = [IsAuthenticated]
        return [permission() for permission in permission_classes]

    def get_queryset(self):
        queryset = super().get_queryset()
        hotel_id = self.request.query_params.get('hotel', None)
        is_available = self.request.query_params.get('available', None)
        room_type = self.request.query_params.get('type', None)

        if hotel_id is not None:
            queryset = queryset.filter(hotel=hotel_id)
        if is_available is not None:
            queryset = queryset.filter(is_available=is_available.lower() == 'true')
        if room_type is not None:
            queryset = queryset.filter(room_type__icontains=room_type)

        return queryset

    @action(detail=True, methods=['post'])
    def toggle_availability(self, request, pk=None):
        """Toggle room availability"""
        room = self.get_object()
        room.is_available = not room.is_available
        room.save()

        serializer = self.get_serializer(room)
        return Response(serializer.data)


class HotelReservationViewSet(viewsets.ModelViewSet):
    queryset = HotelReservation.objects.all()
    serializer_class = HotelReservationSerializer

    def get_queryset(self):
        queryset = super().get_queryset()
        hotel_id = self.request.query_params.get('hotel', None)
        participant_id = self.request.query_params.get('participant', None)
        status_filter = self.request.query_params.get('status', None)

        if hotel_id is not None:
            queryset = queryset.filter(hotel=hotel_id)
        if participant_id is not None:
            queryset = queryset.filter(participant=participant_id)
        if status_filter is not None:
            queryset = queryset.filter(status=status_filter)

        return queryset

    @action(detail=True, methods=['post'])
    def update_status(self, request, pk=None):
        """Update reservation status"""
        reservation = self.get_object()
        new_status = request.data.get('status')

        if new_status in dict(HotelReservation.STATUS_CHOICES):
            reservation.status = new_status
            reservation.save()

            serializer = self.get_serializer(reservation)
            return Response(serializer.data)
        else:
            return Response({'error': 'Invalid status'}, status=status.HTTP_400_BAD_REQUEST)

    @action(detail=True, methods=['post'])
    def check_in(self, request, pk=None):
        """Check in guest"""
        reservation = self.get_object()
        if reservation.status == 'confirmed':
            reservation.status = 'checked_in'
            reservation.save()

            serializer = self.get_serializer(reservation)
            return Response(serializer.data)
        else:
            return Response({'error': 'Reservation must be confirmed to check in'},
                          status=status.HTTP_400_BAD_REQUEST)

    @action(detail=True, methods=['post'])
    def check_out(self, request, pk=None):
        """Check out guest"""
        reservation = self.get_object()
        if reservation.status == 'checked_in':
            reservation.status = 'checked_out'
            reservation.save()

            # Make room available again
            if reservation.room:
                reservation.room.is_available = True
                reservation.room.save()

            serializer = self.get_serializer(reservation)
            return Response(serializer.data)
        else:
            return Response({'error': 'Guest must be checked in to check out'},
                          status=status.HTTP_400_BAD_REQUEST)
