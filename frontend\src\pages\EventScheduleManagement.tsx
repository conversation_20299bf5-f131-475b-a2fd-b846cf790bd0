import React, { useState, useEffect } from 'react';
import { Con<PERSON><PERSON>, <PERSON>, Col, Card, But<PERSON>, Table, Modal, Form, Alert, Badge, Spinner } from 'react-bootstrap';
import { eventService, Event } from '../services/api';
import api from '../services/api';

interface EventSchedule {
  id?: number;
  event: number;
  title: string;
  description: string;
  start_time: string;
  end_time: string;
  location: string;
  speaker: string;
  session_type: 'presentation' | 'workshop' | 'break' | 'networking' | 'other';
  is_break: boolean;
  created_at?: string;
  updated_at?: string;
}

const EventScheduleManagement: React.FC = () => {
  const [events, setEvents] = useState<Event[]>([]);
  const [selectedEvent, setSelectedEvent] = useState<number | null>(null);
  const [schedules, setSchedules] = useState<EventSchedule[]>([]);
  const [loading, setLoading] = useState(false);
  const [showModal, setShowModal] = useState(false);
  const [editingSchedule, setEditingSchedule] = useState<EventSchedule | null>(null);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');

  const [formData, setFormData] = useState<Partial<EventSchedule & { schedule_date: string }>>({
    title: '',
    description: '',
    schedule_date: '',
    start_time: '',
    end_time: '',
    location: '',
    speaker: '',
    session_type: 'presentation',
    is_break: false,
  });

  useEffect(() => {
    fetchEvents();
  }, []);

  // Get selected event details
  const getSelectedEventDetails = () => {
    return events.find(event => event.id === selectedEvent);
  };

  // Get available dates for the selected event
  const getAvailableDates = () => {
    const event = getSelectedEventDetails();
    if (!event) return [];

    const dates = [];
    const startDate = new Date(event.start_date);
    const endDate = new Date(event.end_date);

    // Generate all dates between start and end date
    for (let date = new Date(startDate); date <= endDate; date.setDate(date.getDate() + 1)) {
      dates.push(new Date(date).toISOString().split('T')[0]);
    }

    return dates;
  };

  useEffect(() => {
    if (selectedEvent) {
      fetchSchedules();
    }
  }, [selectedEvent]);

  const fetchEvents = async () => {
    try {
      const response = await eventService.getEvents();
      setEvents(response.data.results || response.data);
    } catch (error) {
      console.error('Error fetching events:', error);
      setError('Failed to load events');
    }
  };

  const fetchSchedules = async () => {
    if (!selectedEvent) return;
    
    setLoading(true);
    try {
      const response = await api.get(`/events/${selectedEvent}/schedule/`);
      setSchedules(response.data.results || response.data);
    } catch (error) {
      console.error('Error fetching schedules:', error);
      setError('Failed to load schedules');
    } finally {
      setLoading(false);
    }
  };

  const validateSchedule = (schedule: Partial<EventSchedule & { schedule_date: string }>): string[] => {
    const errors: string[] = [];

    if (!schedule.title?.trim()) {
      errors.push('Title is required');
    }

    if (!schedule.schedule_date) {
      errors.push('Schedule date is required');
    }

    // Validate date is within event range
    if (schedule.schedule_date) {
      const availableDates = getAvailableDates();
      if (!availableDates.includes(schedule.schedule_date)) {
        errors.push('Selected date is not within the event date range');
      }
    }

    if (!schedule.start_time) {
      errors.push('Start time is required');
    }

    if (!schedule.end_time) {
      errors.push('End time is required');
    }

    if (schedule.start_time && schedule.end_time) {
      const startTime = new Date(`2000-01-01T${schedule.start_time}`);
      const endTime = new Date(`2000-01-01T${schedule.end_time}`);

      if (startTime >= endTime) {
        errors.push('End time must be after start time');
      }
    }

    if (!schedule.location?.trim()) {
      errors.push('Location is required');
    }

    return errors;
  };

  const checkTimeOverlap = (newSchedule: Partial<EventSchedule & { schedule_date: string }>): string[] => {
    const errors: string[] = [];

    if (!newSchedule.start_time || !newSchedule.end_time || !newSchedule.schedule_date) return errors;

    const newStart = new Date(`2000-01-01T${newSchedule.start_time}`);
    const newEnd = new Date(`2000-01-01T${newSchedule.end_time}`);

    for (const existing of schedules) {
      // Skip if editing the same schedule
      if (editingSchedule && existing.id === editingSchedule.id) continue;

      // Only check overlap for schedules on the same date
      const existingDate = new Date(existing.start_time).toISOString().split('T')[0];
      if (existingDate !== newSchedule.schedule_date) continue;

      const existingStartTime = new Date(existing.start_time).toTimeString().slice(0, 5);
      const existingEndTime = new Date(existing.end_time).toTimeString().slice(0, 5);
      const existingStart = new Date(`2000-01-01T${existingStartTime}`);
      const existingEnd = new Date(`2000-01-01T${existingEndTime}`);

      // Check for overlap
      if (
        (newStart >= existingStart && newStart < existingEnd) ||
        (newEnd > existingStart && newEnd <= existingEnd) ||
        (newStart <= existingStart && newEnd >= existingEnd)
      ) {
        errors.push(`Time conflicts with "${existing.title}" (${existingStartTime} - ${existingEndTime}) on ${newSchedule.schedule_date}`);
      }
    }

    return errors;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError('');
    setSuccess('');

    // Validate form
    const validationErrors = validateSchedule(formData);
    const overlapErrors = checkTimeOverlap(formData);
    const allErrors = [...validationErrors, ...overlapErrors];

    if (allErrors.length > 0) {
      setError(allErrors.join('. '));
      return;
    }

    try {
      // Combine date and time into proper datetime strings
      const startDateTime = `${formData.schedule_date}T${formData.start_time}:00`;
      const endDateTime = `${formData.schedule_date}T${formData.end_time}:00`;

      const scheduleData = {
        event: selectedEvent,
        title: formData.title,
        description: formData.description,
        start_time: startDateTime,
        end_time: endDateTime,
        location: formData.location,
        speaker: formData.speaker,
        session_type: formData.session_type,
        is_break: formData.is_break,
      };

      if (editingSchedule) {
        await api.put(`/schedules/${editingSchedule.id}/`, scheduleData);
        setSuccess('Schedule updated successfully');
      } else {
        await api.post('/schedules/', scheduleData);
        setSuccess('Schedule created successfully');
      }

      setShowModal(false);
      resetForm();
      fetchSchedules();
    } catch (error: any) {
      console.error('Error saving schedule:', error);
      setError(error.response?.data?.detail || 'Failed to save schedule');
    }
  };

  const handleDelete = async (id: number) => {
    if (!window.confirm('Are you sure you want to delete this schedule item?')) return;

    try {
      await api.delete(`/schedules/${id}/`);
      setSuccess('Schedule deleted successfully');
      fetchSchedules();
    } catch (error: any) {
      console.error('Error deleting schedule:', error);
      setError(error.response?.data?.detail || 'Failed to delete schedule');
    }
  };

  const resetForm = () => {
    const availableDates = getAvailableDates();
    setFormData({
      title: '',
      description: '',
      schedule_date: availableDates.length > 0 ? availableDates[0] : '',
      start_time: '',
      end_time: '',
      location: '',
      speaker: '',
      session_type: 'presentation',
      is_break: false,
    });
    setEditingSchedule(null);
  };

  const openModal = (schedule?: EventSchedule) => {
    if (schedule) {
      setEditingSchedule(schedule);
      // Extract date from the start_time datetime
      const scheduleDate = new Date(schedule.start_time).toISOString().split('T')[0];
      // Extract time from the start_time and end_time
      const startTime = new Date(schedule.start_time).toTimeString().slice(0, 5);
      const endTime = new Date(schedule.end_time).toTimeString().slice(0, 5);

      setFormData({
        title: schedule.title,
        description: schedule.description,
        schedule_date: scheduleDate,
        start_time: startTime,
        end_time: endTime,
        location: schedule.location,
        speaker: schedule.speaker,
        session_type: schedule.session_type,
        is_break: schedule.is_break,
      });
    } else {
      resetForm();
    }
    setShowModal(true);
  };

  const getSessionTypeColor = (type: string) => {
    const colors = {
      presentation: 'primary',
      workshop: 'success',
      break: 'warning',
      networking: 'info',
      other: 'secondary',
    };
    return colors[type as keyof typeof colors] || 'secondary';
  };

  const formatTime = (timeString: string) => {
    try {
      // Handle time format like "14:30" or "14:30:00"
      if (timeString && timeString.includes(':')) {
        // If it's just time format (HH:MM or HH:MM:SS), create a date object for today
        const timeParts = timeString.split(':');
        if (timeParts.length >= 2) {
          const hours = parseInt(timeParts[0]);
          const minutes = parseInt(timeParts[1]);

          if (!isNaN(hours) && !isNaN(minutes) && hours >= 0 && hours <= 23 && minutes >= 0 && minutes <= 59) {
            // Create a date object for formatting
            const date = new Date();
            date.setHours(hours, minutes, 0, 0);
            return date.toLocaleTimeString('en-US', {
              hour: 'numeric',
              minute: '2-digit',
              hour12: true,
            });
          }
        }
      }

      // Fallback: try to parse as full date string
      const date = new Date(timeString);
      if (isNaN(date.getTime())) {
        return 'Invalid Time';
      }
      return date.toLocaleTimeString('en-US', {
        hour: 'numeric',
        minute: '2-digit',
        hour12: true,
      });
    } catch (error) {
      return 'Invalid Time';
    }
  };

  return (
    <Container fluid className="py-4">
      <Row className="mb-4">
        <Col>
          <h2 className="mb-0">
            <i className="fas fa-calendar-alt me-2 text-primary"></i>
            Event Schedule Management
          </h2>
          <p className="text-muted">Manage event schedules with time validation</p>
        </Col>
      </Row>

      {error && (
        <Alert variant="danger" dismissible onClose={() => setError('')}>
          {error}
        </Alert>
      )}

      {success && (
        <Alert variant="success" dismissible onClose={() => setSuccess('')}>
          {success}
        </Alert>
      )}

      <Row className="mb-4">
        <Col md={6}>
          <Card>
            <Card.Header>
              <h5 className="mb-0">Select Event</h5>
            </Card.Header>
            <Card.Body>
              <Form.Select
                value={selectedEvent || ''}
                onChange={(e) => setSelectedEvent(Number(e.target.value) || null)}
              >
                <option value="">Choose an event...</option>
                {events.map((event) => (
                  <option key={event.id} value={event.id}>
                    {event.name}
                  </option>
                ))}
              </Form.Select>
            </Card.Body>
          </Card>
        </Col>
        <Col md={6}>
          {selectedEvent && (
            <div className="d-flex justify-content-end">
              <Button
                variant="primary"
                onClick={() => openModal()}
                className="d-flex align-items-center"
              >
                <i className="fas fa-plus me-2"></i>
                Add Schedule Item
              </Button>
            </div>
          )}
        </Col>
      </Row>

      {selectedEvent && (
        <Card>
          <Card.Header>
            <h5 className="mb-0">Event Schedule</h5>
          </Card.Header>
          <Card.Body>
            {loading ? (
              <div className="text-center py-4">
                <Spinner animation="border" />
                <p className="mt-2">Loading schedules...</p>
              </div>
            ) : schedules.length === 0 ? (
              <div className="text-center py-4">
                <p className="text-muted">No schedule items found. Add your first schedule item!</p>
              </div>
            ) : (
              <Table responsive hover>
                <thead>
                  <tr>
                    <th>Time</th>
                    <th>Title</th>
                    <th>Type</th>
                    <th>Location</th>
                    <th>Speaker</th>
                    <th>Actions</th>
                  </tr>
                </thead>
                <tbody>
                  {schedules
                    .sort((a, b) => a.start_time.localeCompare(b.start_time))
                    .map((schedule) => (
                      <tr key={schedule.id}>
                        <td>
                          <div className="d-flex align-items-center">
                            <i className="fas fa-clock me-2 text-muted"></i>
                            <div>
                              <div className="fw-bold">
                                {formatTime(schedule.start_time)} - {formatTime(schedule.end_time)}
                              </div>
                              <small className="text-muted">
                                {new Date(schedule.start_time).toLocaleDateString('en-US', {
                                  weekday: 'short',
                                  month: 'short',
                                  day: 'numeric'
                                })}
                              </small>
                            </div>
                          </div>
                        </td>
                        <td>
                          <div>
                            <div className="fw-bold">{schedule.title}</div>
                            {schedule.description && (
                              <small className="text-muted">{schedule.description}</small>
                            )}
                          </div>
                        </td>
                        <td>
                          <Badge bg={getSessionTypeColor(schedule.session_type)}>
                            {schedule.session_type.charAt(0).toUpperCase() + schedule.session_type.slice(1)}
                          </Badge>
                        </td>
                        <td>
                          <div className="d-flex align-items-center">
                            <i className="fas fa-map-marker-alt me-2 text-muted"></i>
                            {schedule.location}
                          </div>
                        </td>
                        <td>{schedule.speaker || '-'}</td>
                        <td>
                          <div className="d-flex gap-2">
                            <Button
                              variant="outline-primary"
                              size="sm"
                              onClick={() => openModal(schedule)}
                            >
                              <i className="fas fa-edit"></i>
                            </Button>
                            <Button
                              variant="outline-danger"
                              size="sm"
                              onClick={() => schedule.id && handleDelete(schedule.id)}
                            >
                              <i className="fas fa-trash"></i>
                            </Button>
                          </div>
                        </td>
                      </tr>
                    ))}
                </tbody>
              </Table>
            )}
          </Card.Body>
        </Card>
      )}

      {/* Schedule Modal */}
      <Modal show={showModal} onHide={() => setShowModal(false)} size="lg">
        <Modal.Header closeButton>
          <Modal.Title>
            {editingSchedule ? 'Edit Schedule Item' : 'Add Schedule Item'}
          </Modal.Title>
        </Modal.Header>
        <Form onSubmit={handleSubmit}>
          <Modal.Body>
            <Row>
              <Col md={6}>
                <Form.Group className="mb-3">
                  <Form.Label>Title *</Form.Label>
                  <Form.Control
                    type="text"
                    value={formData.title || ''}
                    onChange={(e) => setFormData({ ...formData, title: e.target.value })}
                    required
                  />
                </Form.Group>
              </Col>
              <Col md={6}>
                <Form.Group className="mb-3">
                  <Form.Label>Session Type</Form.Label>
                  <Form.Select
                    value={formData.session_type || 'presentation'}
                    onChange={(e) => setFormData({ ...formData, session_type: e.target.value as any })}
                  >
                    <option value="presentation">Presentation</option>
                    <option value="workshop">Workshop</option>
                    <option value="break">Break</option>
                    <option value="networking">Networking</option>
                    <option value="other">Other</option>
                  </Form.Select>
                </Form.Group>
              </Col>
            </Row>

            <Form.Group className="mb-3">
              <Form.Label>Description</Form.Label>
              <Form.Control
                as="textarea"
                rows={3}
                value={formData.description || ''}
                onChange={(e) => setFormData({ ...formData, description: e.target.value })}
              />
            </Form.Group>

            <Row>
              <Col md={4}>
                <Form.Group className="mb-3">
                  <Form.Label>Schedule Date *</Form.Label>
                  <Form.Select
                    value={formData.schedule_date || ''}
                    onChange={(e) => setFormData({ ...formData, schedule_date: e.target.value })}
                    required
                  >
                    <option value="">Select a date...</option>
                    {getAvailableDates().map((date) => (
                      <option key={date} value={date}>
                        {new Date(date + 'T00:00:00').toLocaleDateString('en-US', {
                          weekday: 'long',
                          year: 'numeric',
                          month: 'long',
                          day: 'numeric'
                        })}
                      </option>
                    ))}
                  </Form.Select>
                </Form.Group>
              </Col>
              <Col md={4}>
                <Form.Group className="mb-3">
                  <Form.Label>Start Time *</Form.Label>
                  <Form.Control
                    type="time"
                    value={formData.start_time || ''}
                    onChange={(e) => setFormData({ ...formData, start_time: e.target.value })}
                    required
                  />
                </Form.Group>
              </Col>
              <Col md={4}>
                <Form.Group className="mb-3">
                  <Form.Label>End Time *</Form.Label>
                  <Form.Control
                    type="time"
                    value={formData.end_time || ''}
                    onChange={(e) => setFormData({ ...formData, end_time: e.target.value })}
                    required
                  />
                </Form.Group>
              </Col>
            </Row>

            <Row>
              <Col md={6}>
                <Form.Group className="mb-3">
                  <Form.Label>Location *</Form.Label>
                  <Form.Control
                    type="text"
                    value={formData.location || ''}
                    onChange={(e) => setFormData({ ...formData, location: e.target.value })}
                    required
                  />
                </Form.Group>
              </Col>
              <Col md={6}>
                <Form.Group className="mb-3">
                  <Form.Label>Speaker</Form.Label>
                  <Form.Control
                    type="text"
                    value={formData.speaker || ''}
                    onChange={(e) => setFormData({ ...formData, speaker: e.target.value })}
                  />
                </Form.Group>
              </Col>
            </Row>

            <Form.Group className="mb-3">
              <Form.Check
                type="checkbox"
                label="This is a break/intermission"
                checked={formData.is_break || false}
                onChange={(e) => setFormData({ ...formData, is_break: e.target.checked })}
              />
            </Form.Group>
          </Modal.Body>
          <Modal.Footer>
            <Button variant="secondary" onClick={() => setShowModal(false)}>
              Cancel
            </Button>
            <Button variant="primary" type="submit">
              {editingSchedule ? 'Update' : 'Create'} Schedule
            </Button>
          </Modal.Footer>
        </Form>
      </Modal>
    </Container>
  );
};

export default EventScheduleManagement;
