import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { Container, Row, Col, Card, Form, Button, Alert, Spinner } from 'react-bootstrap';
import { eventService, Event } from '../services/api';

interface EventFormData {
  name: string;
  description: string;
  location: string;
  city: string;
  country: string;
  start_date: string;
  end_date: string;
  organizer_name: string;
  organizer_email: string;
  organizer_phone: string;
  is_active: boolean;
  banner?: File | null;
}

const EventForm: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const isEdit = Boolean(id);

  const [formData, setFormData] = useState<EventFormData>({
    name: '',
    description: '',
    location: '',
    city: '',
    country: '',
    start_date: '',
    end_date: '',
    organizer_name: '',
    organizer_email: '',
    organizer_phone: '',
    is_active: true,
    banner: null
  });

  const [loading, setLoading] = useState(false);
  const [submitLoading, setSubmitLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);

  useEffect(() => {
    if (isEdit && id) {
      fetchEvent();
    }
  }, [isEdit, id]);

  const fetchEvent = async () => {
    try {
      setLoading(true);
      const response = await eventService.getEvent(parseInt(id!));
      const event = response.data;
      
      setFormData({
        name: event.name || '',
        description: event.description || '',
        location: event.location || '',
        city: event.city || '',
        country: event.country || '',
        start_date: event.start_date ? new Date(event.start_date).toISOString().slice(0, 16) : '',
        end_date: event.end_date ? new Date(event.end_date).toISOString().slice(0, 16) : '',
        organizer_name: event.organizer_name || '',
        organizer_email: event.organizer_email || '',
        organizer_phone: event.organizer_phone || '',
        is_active: event.is_active ?? true,
        banner: null
      });
    } catch (err) {
      console.error('Error fetching event:', err);
      setError('Failed to load event details. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value, type } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? (e.target as HTMLInputElement).checked : value
    }));
  };

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0] || null;
    setFormData(prev => ({
      ...prev,
      banner: file
    }));
  };

  const validateForm = (): string | null => {
    if (!formData.name.trim()) return 'Event name is required';
    if (!formData.description.trim()) return 'Event description is required';
    if (!formData.location.trim()) return 'Event location is required';
    if (!formData.city.trim()) return 'City is required';
    if (!formData.country.trim()) return 'Country is required';
    if (!formData.organizer_name.trim()) return 'Organizer name is required';
    if (!formData.organizer_email.trim()) return 'Organizer email is required';
    if (!formData.organizer_phone.trim()) return 'Organizer phone is required';
    if (!formData.start_date) return 'Start date is required';
    if (!formData.end_date) return 'End date is required';

    // Email validation
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(formData.organizer_email)) return 'Please enter a valid email address';

    const startDate = new Date(formData.start_date);
    const endDate = new Date(formData.end_date);
    if (endDate <= startDate) return 'End date must be after start date';

    return null;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    const validationError = validateForm();
    if (validationError) {
      setError(validationError);
      return;
    }

    try {
      setSubmitLoading(true);
      setError(null);

      const submitData = new FormData();
      submitData.append('name', formData.name);
      submitData.append('description', formData.description);
      submitData.append('location', formData.location);
      submitData.append('city', formData.city);
      submitData.append('country', formData.country);
      submitData.append('start_date', formData.start_date);
      submitData.append('end_date', formData.end_date);
      submitData.append('organizer_name', formData.organizer_name);
      submitData.append('organizer_email', formData.organizer_email);
      submitData.append('organizer_phone', formData.organizer_phone);
      submitData.append('is_active', formData.is_active.toString());

      if (formData.banner) {
        submitData.append('banner', formData.banner);
      }

      if (isEdit) {
        await eventService.updateEvent(parseInt(id!), submitData);
        setSuccess('Event updated successfully!');
      } else {
        await eventService.createEvent(submitData);
        setSuccess('Event created successfully!');
      }

      // Redirect after a short delay
      setTimeout(() => {
        navigate('/events');
      }, 2000);

    } catch (err) {
      console.error('Error saving event:', err);
      setError('Failed to save event. Please try again.');
    } finally {
      setSubmitLoading(false);
    }
  };

  if (loading) {
    return (
      <Container className="text-center mt-5">
        <Spinner animation="border" role="status">
          <span className="visually-hidden">Loading...</span>
        </Spinner>
        <p className="mt-3">Loading event details...</p>
      </Container>
    );
  }

  return (
    <Container className="py-4">
      <Row className="justify-content-center">
        <Col lg={8}>
          <Card className="shadow">
            <Card.Header className="bg-primary text-white">
              <h3 className="mb-0">
                <i className={`fas fa-${isEdit ? 'edit' : 'plus'} me-2`}></i>
                {isEdit ? 'Edit Event' : 'Create New Event'}
              </h3>
            </Card.Header>
            <Card.Body className="p-4">
              {error && (
                <Alert variant="danger" className="mb-4">
                  <i className="fas fa-exclamation-triangle me-2"></i>
                  {error}
                </Alert>
              )}

              {success && (
                <Alert variant="success" className="mb-4">
                  <i className="fas fa-check-circle me-2"></i>
                  {success}
                </Alert>
              )}

              <Form onSubmit={handleSubmit}>
                <Row>
                  <Col md={6}>
                    <Form.Group className="mb-3">
                      <Form.Label className="fw-semibold">
                        <i className="fas fa-tag me-2"></i>
                        Event Name *
                      </Form.Label>
                      <Form.Control
                        type="text"
                        name="name"
                        value={formData.name}
                        onChange={handleInputChange}
                        placeholder="Enter event name"
                      />
                    </Form.Group>
                  </Col>
                  <Col md={6}>
                    <Form.Group className="mb-3">
                      <Form.Label className="fw-semibold">
                        <i className="fas fa-map-marker-alt me-2"></i>
                        Location *
                      </Form.Label>
                      <Form.Control
                        type="text"
                        name="location"
                        value={formData.location}
                        onChange={handleInputChange}
                        placeholder="Enter event location"
                      />
                    </Form.Group>
                  </Col>
                </Row>

                <Form.Group className="mb-3">
                  <Form.Label className="fw-semibold">
                    <i className="fas fa-align-left me-2"></i>
                    Description *
                  </Form.Label>
                  <Form.Control
                    as="textarea"
                    rows={4}
                    name="description"
                    value={formData.description}
                    onChange={handleInputChange}
                    placeholder="Enter event description"
                  />
                </Form.Group>

                <Row>
                  <Col md={6}>
                    <Form.Group className="mb-3">
                      <Form.Label className="fw-semibold">
                        <i className="fas fa-calendar-start me-2"></i>
                        Start Date & Time *
                      </Form.Label>
                      <Form.Control
                        type="datetime-local"
                        name="start_date"
                        value={formData.start_date}
                        onChange={handleInputChange}
                      />
                    </Form.Group>
                  </Col>
                  <Col md={6}>
                    <Form.Group className="mb-3">
                      <Form.Label className="fw-semibold">
                        <i className="fas fa-calendar-end me-2"></i>
                        End Date & Time *
                      </Form.Label>
                      <Form.Control
                        type="datetime-local"
                        name="end_date"
                        value={formData.end_date}
                        onChange={handleInputChange}
                      />
                    </Form.Group>
                  </Col>
                </Row>

                <Row>
                  <Col md={6}>
                    <Form.Group className="mb-3">
                      <Form.Label className="fw-semibold">
                        <i className="fas fa-city me-2"></i>
                        City *
                      </Form.Label>
                      <Form.Control
                        type="text"
                        name="city"
                        value={formData.city}
                        onChange={handleInputChange}
                        placeholder="Enter city"
                      />
                    </Form.Group>
                  </Col>
                  <Col md={6}>
                    <Form.Group className="mb-3">
                      <Form.Label className="fw-semibold">
                        <i className="fas fa-globe me-2"></i>
                        Country *
                      </Form.Label>
                      <Form.Control
                        type="text"
                        name="country"
                        value={formData.country}
                        onChange={handleInputChange}
                        placeholder="Enter country"
                      />
                    </Form.Group>
                  </Col>
                </Row>

                <h5 className="mb-3 text-primary">Organizer Information</h5>
                <Row>
                  <Col md={6}>
                    <Form.Group className="mb-3">
                      <Form.Label className="fw-semibold">
                        <i className="fas fa-user me-2"></i>
                        Organizer Name *
                      </Form.Label>
                      <Form.Control
                        type="text"
                        name="organizer_name"
                        value={formData.organizer_name}
                        onChange={handleInputChange}
                        placeholder="Enter organizer name"
                      />
                    </Form.Group>
                  </Col>
                  <Col md={6}>
                    <Form.Group className="mb-3">
                      <Form.Label className="fw-semibold">
                        <i className="fas fa-envelope me-2"></i>
                        Organizer Email *
                      </Form.Label>
                      <Form.Control
                        type="email"
                        name="organizer_email"
                        value={formData.organizer_email}
                        onChange={handleInputChange}
                        placeholder="Enter organizer email"
                      />
                    </Form.Group>
                  </Col>
                </Row>

                <Row>
                  <Col md={6}>
                    <Form.Group className="mb-3">
                      <Form.Label className="fw-semibold">
                        <i className="fas fa-phone me-2"></i>
                        Organizer Phone *
                      </Form.Label>
                      <Form.Control
                        type="tel"
                        name="organizer_phone"
                        value={formData.organizer_phone}
                        onChange={handleInputChange}
                        placeholder="Enter organizer phone"
                      />
                    </Form.Group>
                  </Col>
                  <Col md={6}>
                    <Form.Group className="mb-3">
                      <Form.Label className="fw-semibold">
                        <i className="fas fa-image me-2"></i>
                        Event Banner Image
                      </Form.Label>
                      <Form.Control
                        type="file"
                        name="banner"
                        onChange={handleFileChange}
                        accept="image/*"
                      />
                      <Form.Text className="text-muted">
                        Upload a banner image for your event (optional)
                      </Form.Text>
                    </Form.Group>
                  </Col>
                </Row>

                <Form.Group className="mb-4">
                  <Form.Check
                    type="checkbox"
                    name="is_active"
                    checked={formData.is_active}
                    onChange={handleInputChange}
                    label="Event is active and accepting registrations"
                  />
                </Form.Group>

                <div className="d-flex gap-3">
                  <Button
                    variant="secondary"
                    onClick={() => navigate('/events')}
                    disabled={submitLoading}
                  >
                    <i className="fas fa-times me-2"></i>
                    Cancel
                  </Button>
                  <Button
                    type="submit"
                    variant="primary"
                    disabled={submitLoading}
                    className="flex-fill"
                  >
                    {submitLoading ? (
                      <>
                        <Spinner animation="border" size="sm" className="me-2" />
                        {isEdit ? 'Updating...' : 'Creating...'}
                      </>
                    ) : (
                      <>
                        <i className={`fas fa-${isEdit ? 'save' : 'plus'} me-2`}></i>
                        {isEdit ? 'Update Event' : 'Create Event'}
                      </>
                    )}
                  </Button>
                </div>
              </Form>
            </Card.Body>
          </Card>
        </Col>
      </Row>
    </Container>
  );
};

export default EventForm;
