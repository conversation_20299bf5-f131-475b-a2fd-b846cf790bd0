#!/usr/bin/env python
"""
<PERSON><PERSON><PERSON> to create University of Gondar organization and the Ethiopian Public Higher Education 
Institutions Reform Council Annual Meeting event with detailed schedule.
"""

import os
import sys
import django
from datetime import datetime, time
from django.utils import timezone

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'event_management.settings')
django.setup()

from organizations.models import Organization
from events.models import Event, EventSchedule
from authentication.models import CustomUser
from django.contrib.auth.models import Group

def create_organization():
    """Create University of Gondar organization"""
    print("Creating University of Gondar organization...")
    
    org, created = Organization.objects.get_or_create(
        name="University of Gondar",
        defaults={
            'description': 'University of Gondar is one of the oldest and most prestigious universities in Ethiopia, located in the historic city of Gondar.',
            'address': 'Gondar, Ethiopia',
            'phone': '+251-58-114-1240',
            'email': '<EMAIL>',
            'website': 'https://www.uog.edu.et',
            'logo': None,
            'is_active': True
        }
    )
    
    if created:
        print(f"✅ Created organization: {org.name}")
    else:
        print(f"ℹ️  Organization already exists: {org.name}")
    
    return org

def create_admin_user():
    """Create or get admin user"""
    print("Creating admin user...")
    
    admin_user, created = CustomUser.objects.get_or_create(
        username='admin',
        defaults={
            'email': '<EMAIL>',
            'first_name': 'Admin',
            'last_name': 'User',
            'is_staff': True,
            'is_superuser': True,
            'is_active': True
        }
    )
    
    if created:
        admin_user.set_password('admin123')
        admin_user.save()
        print(f"✅ Created admin user: {admin_user.username}")
    else:
        print(f"ℹ️  Admin user already exists: {admin_user.username}")
    
    # Add to administrator group
    admin_group, _ = Group.objects.get_or_create(name='administrator')
    admin_user.groups.add(admin_group)
    
    return admin_user

def create_event(organization, admin_user):
    """Create the Ethiopian Public Higher Education Institutions Reform Council Annual Meeting"""
    print("Creating Ethiopian Public Higher Education event...")
    
    # Delete existing event with ID 6 if it exists
    try:
        existing_event = Event.objects.get(id=6)
        existing_event.delete()
        print("🗑️  Deleted existing event with ID 6")
    except Event.DoesNotExist:
        pass
    
    # Create the event
    event_data = {
        'title': 'ETHIOPIAN PUBLIC HIGHER EDUCATION INSTITUTIONS REFORM COUNCIL ANNUAL MEETING',
        'description': '''The Ethiopian Public Higher Education Institutions Reform Council Annual Meeting brings together leaders, administrators, and stakeholders from across Ethiopia's higher education sector to discuss reforms, policies, and the future direction of higher education in the country.

This three-day meeting will cover:
- Higher Education Reform Updates
- Policy Papers, Directives and Guidelines
- Thematic Research Handbook and BTIC Guidelines
- Differentiation Roadmap Progress
- Performance Baseline Data Analysis
- University Performance Reviews

The meeting aims to enhance collaboration, share best practices, and drive forward the transformation of Ethiopia's higher education landscape.''',
        'start_date': datetime(2025, 8, 4).date(),
        'end_date': datetime(2025, 8, 6).date(),
        'start_time': time(8, 30),
        'end_time': time(17, 30),
        'location': 'University of Gondar, Gondar, Ethiopia',
        'max_participants': 200,
        'registration_deadline': datetime(2025, 7, 30).date(),
        'is_active': True,
        'organization': organization,
        'created_by': admin_user,
        'event_type': 'conference',
        'website': 'www.moe.gov.et'
    }
    
    event = Event.objects.create(**event_data)
    
    # Force the event ID to be 6
    if event.id != 6:
        # Update the ID to 6
        Event.objects.filter(id=event.id).update(id=6)
        event.id = 6
        event.save()
    
    print(f"✅ Created event: {event.title} (ID: {event.id})")
    return event

def create_schedule(event):
    """Create detailed schedule for the event"""
    print("Creating event schedule...")
    
    # Clear existing schedules
    EventSchedule.objects.filter(event=event).delete()
    
    # Monday, 04 August schedule
    monday_schedules = [
        {
            'title': 'Registration',
            'start_time': time(8, 30),
            'end_time': time(9, 0),
            'description': 'Participant registration and welcome materials distribution',
            'speaker': '',
            'location': 'Main Hall Entrance'
        },
        {
            'title': 'Welcoming Speech',
            'start_time': time(9, 0),
            'end_time': time(9, 25),
            'description': 'Official welcome to all participants',
            'speaker': 'Dr. Asrat Atsedeweyn',
            'location': 'Main Conference Hall'
        },
        {
            'title': 'Opening Remarks',
            'start_time': time(9, 25),
            'end_time': time(9, 40),
            'description': 'Opening remarks and meeting objectives',
            'speaker': 'Ato Kora Tushune',
            'location': 'Main Conference Hall'
        },
        {
            'title': 'Updates on the Higher Education Reform',
            'start_time': time(9, 40),
            'end_time': time(10, 15),
            'description': 'Comprehensive updates on ongoing higher education reforms in Ethiopia',
            'speaker': 'Ato Kora Tushune',
            'location': 'Main Conference Hall'
        },
        {
            'title': 'Discussion on Policy Papers, Directives and Guidelines',
            'start_time': time(10, 15),
            'end_time': time(10, 40),
            'description': 'Review and discussion of new policy papers and guidelines',
            'speaker': 'Dr. Solomon Abraha',
            'location': 'Main Conference Hall'
        },
        {
            'title': 'Tea Break',
            'start_time': time(10, 40),
            'end_time': time(11, 0),
            'description': 'Networking break with refreshments',
            'speaker': '',
            'location': 'Lobby Area'
        },
        {
            'title': 'Thematic Research Handbook and BTIC Guideline',
            'start_time': time(11, 0),
            'end_time': time(11, 25),
            'description': 'Presentation of thematic research handbook and BTIC guidelines',
            'speaker': 'Dr. Serawit Handiso',
            'location': 'Main Conference Hall'
        },
        {
            'title': 'Updates on Differentiation Roadmap and Progress So Far',
            'start_time': time(11, 25),
            'end_time': time(12, 0),
            'description': 'Progress report on university differentiation initiatives',
            'speaker': 'Dr. Eba Mijena',
            'location': 'Main Conference Hall'
        },
        {
            'title': 'Overview of the Findings from Performance Baseline Data Analysis',
            'start_time': time(12, 0),
            'end_time': time(12, 30),
            'description': 'Analysis of performance baseline data across institutions',
            'speaker': 'Ato Tesfaye Negewo',
            'location': 'Main Conference Hall'
        },
        {
            'title': 'Lunch Time',
            'start_time': time(12, 30),
            'end_time': time(14, 0),
            'description': 'Lunch break and informal networking',
            'speaker': '',
            'location': 'University Restaurant'
        },
        {
            'title': 'Plenary Discussion',
            'start_time': time(14, 0),
            'end_time': time(16, 0),
            'description': 'Open discussion on morning presentations and reform initiatives',
            'speaker': 'All Participants',
            'location': 'Main Conference Hall'
        },
        {
            'title': 'Tea Break',
            'start_time': time(16, 0),
            'end_time': time(16, 20),
            'description': 'Afternoon refreshment break',
            'speaker': '',
            'location': 'Lobby Area'
        },
        {
            'title': 'Discussion and Closing Remark',
            'start_time': time(16, 20),
            'end_time': time(17, 15),
            'description': 'Final discussions and closing remarks for day one',
            'speaker': 'Session Chairs',
            'location': 'Main Conference Hall'
        },
        {
            'title': 'Announcements and Reminders',
            'start_time': time(17, 15),
            'end_time': time(17, 30),
            'description': 'Important announcements and reminders for next day',
            'speaker': 'Organizing Committee',
            'location': 'Main Conference Hall'
        }
    ]
    
    # Create Monday schedules
    for schedule_data in monday_schedules:
        schedule_data.update({
            'event': event,
            'date': datetime(2025, 8, 4).date(),
            'is_active': True
        })
        EventSchedule.objects.create(**schedule_data)
    
    print(f"✅ Created {len(monday_schedules)} schedule items for Monday")
    
    return event

def main():
    """Main function to set up all data"""
    print("🚀 Setting up University of Gondar data...")
    print("=" * 60)
    
    try:
        # Create organization
        organization = create_organization()
        
        # Create admin user
        admin_user = create_admin_user()
        
        # Create event
        event = create_event(organization, admin_user)
        
        # Create schedule
        create_schedule(event)
        
        print("=" * 60)
        print("✅ Successfully created all data!")
        print(f"📍 Organization: {organization.name}")
        print(f"🎉 Event: {event.title}")
        print(f"🆔 Event ID: {event.id}")
        print(f"📅 Event Date: {event.start_date} to {event.end_date}")
        print(f"👤 Admin User: {admin_user.username}")
        print("=" * 60)
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == '__main__':
    main()
