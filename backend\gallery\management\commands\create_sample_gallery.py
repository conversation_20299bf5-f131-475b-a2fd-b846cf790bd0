"""
Management command to create sample gallery data for University of Gondar
"""
import os
from django.core.management.base import BaseCommand
from django.core.files.base import ContentFile
from django.conf import settings
from gallery.models import GalleryCategory, GalleryImage
from events.models import Event, EventGallery
from organizations.models import Organization
import base64


class Command(BaseCommand):
    help = 'Create sample gallery data for University of Gondar'

    def handle(self, *args, **options):
        self.stdout.write('Creating sample gallery data for University of Gondar...')
        
        # Get the primary organization
        try:
            organization = Organization.objects.get(is_primary=True)
        except Organization.DoesNotExist:
            self.stdout.write(
                self.style.ERROR('No primary organization found. Run setup_uog first.')
            )
            return

        # Create gallery categories
        categories_data = [
            {
                'name': 'Campus Life',
                'slug': 'campus-life',
                'description': 'Daily life and activities on the University of Gondar campus',
                'color': '#28a745',
                'icon': 'fas fa-university',
                'is_active': True,
                'order': 1
            },
            {
                'name': 'Academic Events',
                'slug': 'academic-events',
                'description': 'Conferences, seminars, and academic gatherings',
                'color': '#007bff',
                'icon': 'fas fa-graduation-cap',
                'is_active': True,
                'order': 2
            },
            {
                'name': 'Research & Innovation',
                'slug': 'research-innovation',
                'description': 'Research activities and innovative projects',
                'color': '#6f42c1',
                'icon': 'fas fa-microscope',
                'is_active': True,
                'order': 3
            },
            {
                'name': 'Student Activities',
                'slug': 'student-activities',
                'description': 'Student clubs, sports, and extracurricular activities',
                'color': '#fd7e14',
                'icon': 'fas fa-users',
                'is_active': True,
                'order': 4
            },
            {
                'name': 'Facilities',
                'slug': 'facilities',
                'description': 'University buildings, laboratories, and infrastructure',
                'color': '#20c997',
                'icon': 'fas fa-building',
                'is_active': True,
                'order': 5
            }
        ]

        created_categories = []
        for cat_data in categories_data:
            category, created = GalleryCategory.objects.get_or_create(
                slug=cat_data['slug'],
                defaults=cat_data
            )
            if created:
                self.stdout.write(f'Created category: {category.name}')
            else:
                self.stdout.write(f'Category already exists: {category.name}')
            created_categories.append(category)

        # Create sample SVG images for each category
        sample_images = [
            # Campus Life
            {
                'title': 'University Main Gate',
                'description': 'The iconic entrance to University of Gondar',
                'category': 'campus-life',
                'is_featured': True,
                'is_slider': True,
                'is_campus': True,
                'photographer': 'UoG Photography Team',
                'location': 'Main Campus Entrance'
            },
            {
                'title': 'Students Walking on Campus',
                'description': 'Students enjoying the beautiful campus environment',
                'category': 'campus-life',
                'is_featured': False,
                'is_slider': False,
                'is_campus': True,
                'photographer': 'Campus Media',
                'location': 'Central Campus'
            },
            # Academic Events
            {
                'title': 'Annual Academic Conference',
                'description': 'Distinguished speakers at the annual academic conference',
                'category': 'academic-events',
                'is_featured': True,
                'is_slider': True,
                'is_campus': False,
                'photographer': 'Event Documentation Team',
                'location': 'Conference Hall'
            },
            {
                'title': 'Graduation Ceremony',
                'description': 'Proud graduates celebrating their achievements',
                'category': 'academic-events',
                'is_featured': True,
                'is_slider': False,
                'is_campus': False,
                'photographer': 'Graduation Committee',
                'location': 'Main Auditorium'
            },
            # Research & Innovation
            {
                'title': 'Research Laboratory',
                'description': 'State-of-the-art research facilities',
                'category': 'research-innovation',
                'is_featured': True,
                'is_slider': True,
                'is_campus': True,
                'photographer': 'Research Department',
                'location': 'Science Building'
            },
            {
                'title': 'Innovation Showcase',
                'description': 'Students presenting innovative projects',
                'category': 'research-innovation',
                'is_featured': False,
                'is_slider': False,
                'is_campus': False,
                'photographer': 'Innovation Hub',
                'location': 'Innovation Center'
            },
            # Student Activities
            {
                'title': 'Student Sports Day',
                'description': 'Annual sports competition among students',
                'category': 'student-activities',
                'is_featured': False,
                'is_slider': False,
                'is_campus': False,
                'photographer': 'Sports Committee',
                'location': 'Sports Complex'
            },
            {
                'title': 'Cultural Festival',
                'description': 'Celebrating Ethiopian culture and diversity',
                'category': 'student-activities',
                'is_featured': True,
                'is_slider': False,
                'is_campus': False,
                'photographer': 'Cultural Committee',
                'location': 'Cultural Center'
            },
            # Facilities
            {
                'title': 'Modern Library',
                'description': 'The university\'s state-of-the-art library facility',
                'category': 'facilities',
                'is_featured': True,
                'is_slider': True,
                'is_campus': True,
                'photographer': 'Facilities Management',
                'location': 'Central Library'
            },
            {
                'title': 'Computer Laboratory',
                'description': 'Modern computer lab with latest technology',
                'category': 'facilities',
                'is_featured': False,
                'is_slider': False,
                'is_campus': True,
                'photographer': 'IT Department',
                'location': 'IT Building'
            }
        ]

        # Create sample images
        for img_data in sample_images:
            category = next((cat for cat in created_categories if cat.slug == img_data['category']), None)
            if not category:
                continue

            # Create a simple SVG image
            svg_content = f'''<?xml version="1.0" encoding="UTF-8"?>
<svg width="800" height="600" viewBox="0 0 800 600" xmlns="http://www.w3.org/2000/svg">
  <!-- Background -->
  <rect width="800" height="600" fill="{category.color}"/>
  
  <!-- University Building Silhouette -->
  <rect x="200" y="300" width="400" height="200" fill="#ffffff" opacity="0.9"/>
  <rect x="220" y="320" width="360" height="160" fill="{category.color}"/>
  
  <!-- Columns -->
  <rect x="250" y="280" width="20" height="80" fill="#ffffff"/>
  <rect x="300" y="280" width="20" height="80" fill="#ffffff"/>
  <rect x="350" y="280" width="20" height="80" fill="#ffffff"/>
  <rect x="400" y="280" width="20" height="80" fill="#ffffff"/>
  <rect x="450" y="280" width="20" height="80" fill="#ffffff"/>
  <rect x="500" y="280" width="20" height="80" fill="#ffffff"/>
  <rect x="550" y="280" width="20" height="80" fill="#ffffff"/>
  
  <!-- Roof -->
  <polygon points="180,300 400,200 620,300" fill="#ffffff"/>
  
  <!-- Title -->
  <text x="400" y="100" text-anchor="middle" fill="#ffffff" font-family="Arial, sans-serif" font-size="32" font-weight="bold">
    University of Gondar
  </text>
  
  <!-- Image Title -->
  <text x="400" y="550" text-anchor="middle" fill="#ffffff" font-family="Arial, sans-serif" font-size="24">
    {img_data['title']}
  </text>
  
  <!-- Category Icon -->
  <circle cx="400" cy="150" r="30" fill="#ffffff" opacity="0.8"/>
  <text x="400" y="160" text-anchor="middle" fill="{category.color}" font-family="Arial, sans-serif" font-size="20">
    📚
  </text>
</svg>'''

            # Create the gallery image
            image_content = ContentFile(svg_content.encode('utf-8'))
            
            gallery_image, created = GalleryImage.objects.get_or_create(
                title=img_data['title'],
                defaults={
                    'description': img_data['description'],
                    'category': category,
                    'is_featured': img_data['is_featured'],
                    'is_slider': img_data['is_slider'],
                    'is_campus': img_data['is_campus'],
                    'photographer': img_data['photographer'],
                    'location': img_data['location'],
                    'is_active': True
                }
            )
            
            if created:
                gallery_image.image.save(
                    f"{img_data['category']}-{gallery_image.id}.svg",
                    image_content,
                    save=True
                )
                self.stdout.write(f'Created gallery image: {gallery_image.title}')
            else:
                self.stdout.write(f'Gallery image already exists: {gallery_image.title}')

        self.stdout.write(
            self.style.SUCCESS('Successfully created sample gallery data!')
        )
        self.stdout.write(
            self.style.SUCCESS(f'Created {len(created_categories)} categories')
        )
        self.stdout.write(
            self.style.SUCCESS(f'Created {len(sample_images)} sample images')
        )
