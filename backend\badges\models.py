from django.db import models
from django.utils import timezone
from datetime import <PERSON><PERSON><PERSON>
import qrcode
from io import BytesIO
from django.core.files import File
from PIL import Image, ImageDraw, ImageFont
from django.conf import settings
from django.core.cache import cache
import os
import math
import json


class BadgeTemplate(models.Model):
    name = models.CharField(max_length=100)
    description = models.TextField(blank=True)
    version = models.CharField(max_length=20, default='v1.0')  # Template versioning
    width = models.IntegerField(default=600)  # SVG template width
    height = models.IntegerField(default=1200)  # SVG template height
    background_color = models.CharField(max_length=7, default='#002D72')
    template_file = models.ImageField(upload_to='badge_templates/', null=True, blank=True)

    # Template configuration
    variables = models.JSONField(default=dict, help_text="Dynamic fields configuration")
    svg_content = models.TextField(blank=True, help_text="SVG template content")

    # Status
    is_default = models.Bo<PERSON>anField(default=False)
    is_active = models.BooleanField(default=True)

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        unique_together = ['name', 'version']

    def __str__(self):
        return f"{self.name} ({self.version})"

    def save(self, *args, **kwargs):
        if self.is_default:
            # Ensure only one default template
            BadgeTemplate.objects.filter(is_default=True).update(is_default=False)
        super().save(*args, **kwargs)


class Badge(models.Model):
    participant = models.OneToOneField('participants.Participant', on_delete=models.CASCADE, related_name='badge')
    template = models.ForeignKey(BadgeTemplate, on_delete=models.CASCADE, null=True, blank=True)

    # QR Code - Using JSONField for better structure
    qr_data = models.JSONField(default=dict, help_text="Structured QR code data")
    qr_code_image = models.ImageField(upload_to='qr_codes/', null=True, blank=True)

    # Badge File
    badge_image = models.ImageField(upload_to='generated_badges/', null=True, blank=True)

    # Caching and versioning
    template_version = models.CharField(max_length=20, default='v1.0')
    cached_svg = models.TextField(blank=True, help_text="Cached SVG content")
    last_rendered = models.DateTimeField(null=True, blank=True)
    content_hash = models.CharField(max_length=64, blank=True, help_text="Hash of badge content for cache invalidation")

    # Generation Status
    is_generated = models.BooleanField(default=False)
    generated_at = models.DateTimeField(null=True, blank=True)

    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"Badge for {self.participant.full_name}"

    def get_cache_key(self):
        """Generate cache key for this badge"""
        return f"badge_{self.participant.id}_{self.template_version}_{self.content_hash}"

    def should_regenerate(self):
        """Check if badge should be regenerated"""
        if not self.is_generated or not self.last_rendered:
            return True

        # Regenerate if older than 24 hours
        if self.last_rendered < timezone.now() - timedelta(hours=24):
            return True

        # Regenerate if content has changed
        current_hash = self.calculate_content_hash()
        if current_hash != self.content_hash:
            return True

        return False

    def calculate_content_hash(self):
        """Calculate hash of badge content for cache invalidation"""
        import hashlib

        content_data = {
            'participant_id': self.participant.id,
            'participant_name': self.participant.full_name,
            'participant_position': getattr(self.participant, 'position', ''),
            'participant_institution': getattr(self.participant, 'institution_name', ''),
            'event_id': self.participant.event.id if self.participant.event else None,
            'template_version': self.template_version,
        }

        # Include sponsor data if available
        if self.participant.event:
            from events.models import EventSponsor
            sponsors = EventSponsor.objects.filter(
                event=self.participant.event,
                is_active=True
            ).order_by('display_order')[:3]

            content_data['sponsors'] = [
                {
                    'id': s.id,
                    'name': s.name,
                    'has_logo': bool(s.logo),
                    'logo_modified': s.logo.file.name if s.logo else None
                }
                for s in sponsors
            ]

        content_str = json.dumps(content_data, sort_keys=True)
        return hashlib.sha256(content_str.encode()).hexdigest()

    def get_font_with_fallback(self, font_name, size):
        """Get font with proper fallback strategy"""
        font_paths = [
            f"{font_name}.ttf",
            f"C:/Windows/Fonts/{font_name}.ttf",
            f"C:/Windows/Fonts/arial.ttf",
            f"/System/Library/Fonts/Arial.ttf",  # macOS
            f"/usr/share/fonts/truetype/dejavu/DejaVuSans.ttf",  # Linux
        ]

        for font_path in font_paths:
            try:
                return ImageFont.truetype(font_path, size)
            except (OSError, IOError):
                continue

        # Final fallback to default font
        try:
            return ImageFont.load_default()
        except:
            # Create a minimal font if all else fails
            return ImageFont.load_default()

    def generate_qr_code(self):
        """Generate QR code for the participant with improved data structure"""
        # Create structured QR code data
        qr_data = {
            'participant_id': self.participant.id,
            'full_name': self.participant.full_name,
            'email': self.participant.email,
            'event_id': self.participant.event.id if self.participant.event else None,
            'event_name': self.participant.event.name if self.participant.event else None,
            'generated_at': timezone.now().isoformat(),
            'type': 'participant_badge',
            'version': '2.0'  # QR data version for future compatibility
        }

        # Add participant details if available
        if hasattr(self.participant, 'position') and self.participant.position:
            qr_data['position'] = self.participant.position

        if hasattr(self.participant, 'institution_name') and self.participant.institution_name:
            qr_data['institution'] = self.participant.institution_name

        # Store QR data in JSONField
        self.qr_data = qr_data

        # Generate QR code image with better error correction
        qr = qrcode.QRCode(
            version=1,
            error_correction=qrcode.constants.ERROR_CORRECT_M,  # Better error correction
            box_size=10,
            border=4,
        )
        # Use simple email for QR code instead of complex JSON
        qr.add_data(self.participant.email)  # Simple email only
        qr.make(fit=True)

        # Create QR code image with better quality
        qr_img = qr.make_image(fill_color="black", back_color="white")

        # Save QR code to model
        buffer = BytesIO()
        qr_img.save(buffer, format='PNG', optimize=True)
        buffer.seek(0)

        filename = f"qr_participant_{self.participant.id}_{timezone.now().strftime('%Y%m%d')}.png"
        self.qr_code_image.save(filename, File(buffer), save=False)

        return qr_img

    def generate_badge(self):
        """Generate enhanced badge with new SVG template design"""
        # Check if regeneration is needed
        if not self.should_regenerate():
            print(f"✅ Using cached badge for {self.participant.full_name}")
            return Image.open(self.badge_image.path) if self.badge_image else None

        print(f"🎨 Generating new badge for {self.participant.full_name}")

        try:
            # Update content hash
            self.content_hash = self.calculate_content_hash()

            # Get or create QR code
            if not self.qr_code_image or not self.qr_data:
                qr_img = self.generate_qr_code()
            else:
                qr_img = Image.open(self.qr_code_image.path)

            # Target design dimensions - match the reference image
            width = 600
            height = 1200

            # Create badge with blue background to match target
            badge_img = Image.new('RGB', (width, height), color='#2E5BBA')  # Blue background
            draw = ImageDraw.Draw(badge_img)

            # Load fonts to match target design proportions
            try:
                # Header fonts - sized for 600x1200 badge
                ministry_font = ImageFont.truetype("arial.ttf", 32)   # Ministry title
                sector_font = ImageFont.truetype("arial.ttf", 20)    # Sector text
                theme_font = ImageFont.truetype("arial.ttf", 18)     # Theme text
                date_font = ImageFont.truetype("arial.ttf", 24)      # Date
                anniversary_font = ImageFont.truetype("arial.ttf", 28) # Anniversary

                # Participant info fonts
                name_font = ImageFont.truetype("arial.ttf", 36)      # Participant name
                position_font = ImageFont.truetype("arial.ttf", 32)  # Position/type
                institution_font = ImageFont.truetype("arial.ttf", 24) # Institution

                # Other fonts
                qr_font = ImageFont.truetype("arial.ttf", 16)        # QR label
                footer_font = ImageFont.truetype("arial.ttf", 14)    # Footer
                small_font = ImageFont.truetype("arial.ttf", 12)     # Small text
                vertical_font = ImageFont.truetype("arial.ttf", 14)  # Vertical text

            except Exception as e:
                print(f"Font loading error: {e}")
                # Fallback to default fonts
                ministry_font = ImageFont.load_default()
                sector_font = ImageFont.load_default()
                theme_font = ImageFont.load_default()
                date_font = ImageFont.load_default()
                anniversary_font = ImageFont.load_default()
                name_font = ImageFont.load_default()
                position_font = ImageFont.load_default()
                institution_font = ImageFont.load_default()
                qr_font = ImageFont.load_default()
                footer_font = ImageFont.load_default()
                small_font = ImageFont.load_default()
                vertical_font = ImageFont.load_default()

            # 1. GOLD ANNIVERSARY BANNER at top
            anniversary_height = 60
            draw.rectangle([0, 0, width, anniversary_height], fill='#FFD700')  # Gold banner
            draw.text((width//2, anniversary_height//2), "70/100th ANNIVERSARY",
                     fill='#2E5BBA', font=anniversary_font, anchor='mm')

            # 2. BLUE HEADER SECTION with ministry info
            header_start = anniversary_height
            header_height = 200

            # Ministry title
            ministry_y = header_start + 40
            draw.text((width//2, ministry_y), "MINISTRY OF EDUCATION OF FDRE",
                     fill='#FFD700', font=ministry_font, anchor='mm')

            # Sector subtitle
            sector_y = ministry_y + 40
            draw.text((width//2, sector_y), "HIGHER EDUCATION DEVELOPMENT SECTOR",
                     fill='white', font=sector_font, anchor='mm')

            # Theme text
            theme_y = sector_y + 30
            draw.text((width//2, theme_y), "HIGHER EDUCATION FOR HIGHER IMPACT",
                     fill='#FFD700', font=theme_font, anchor='mm')

            # 3. CONFERENCE DATE in rounded rectangle
            date_y = header_start + header_height + 20
            date_rect = [100, date_y, width-100, date_y + 50]
            draw.rounded_rectangle(date_rect, radius=25, fill=None, outline='#FFD700', width=3)
            draw.text((width//2, date_y + 25), "AUGUST 4-6, 2024",
                     fill='#FFD700', font=date_font, anchor='mm')



            # 4. VERTICAL TEXT on left side
            vertical_text = "SEVEN DECADES OF EXCELLENCE AND A CENTURY OF SERVICE"
            # Draw vertical text letter by letter
            x_pos = 15
            y_start = 350
            letter_spacing = 20

            for i, char in enumerate(vertical_text):
                if char != ' ':
                    draw.text((x_pos, y_start + i * letter_spacing), char,
                             fill='#FFD700', font=vertical_font, anchor='mm')

            # 5. PARTICIPANT PHOTO - Circular
            photo_size = 180
            photo_x = (width - photo_size) // 2
            photo_y = 350

            # Draw circular photo border
            draw.ellipse([photo_x-5, photo_y-5, photo_x + photo_size + 5, photo_y + photo_size + 5],
                        outline='#FFD700', width=6)
            draw.ellipse([photo_x, photo_y, photo_x + photo_size, photo_y + photo_size],
                        fill='white')  # White background for photo

            # 6. PARTICIPANT NAME
            participant_name_y = photo_y + photo_size + 30
            full_name = self.participant.full_name
            if len(full_name) > 20:
                full_name = full_name[:17] + "..."
            draw.text((width//2, participant_name_y), full_name,
                     fill='white', font=name_font, anchor='mm')

            # 7. PARTICIPANT TYPE - Gold banner (like "AIKO ELLIS")
            type_y = participant_name_y + 50
            type_rect = [50, type_y - 20, width - 50, type_y + 30]
            draw.rounded_rectangle(type_rect, radius=25, fill='#FFD700')

            position = getattr(self.participant, 'position', None) or "KEYNOTE SPEAKER"
            if len(position) > 15:
                position = position[:12] + "..."
            draw.text((width//2, type_y + 5), position,
                     fill='#2E5BBA', font=position_font, anchor='mm')

            # 8. ORGANIZATION NAME
            org_y = type_y + 70
            institution = getattr(self.participant, 'institution_name', None) or "University of Gondar"
            if len(institution) > 25:
                institution = institution[:22] + "..."
            draw.text((width//2, org_y), institution,
                     fill='white', font=institution_font, anchor='mm')

            # 9. QR CODE SECTION
            qr_size = 170  # As specified in target design
            qr_x = (width - qr_size) // 2
            qr_y = org_y + 60

            # QR background - white with gold border
            qr_bg_rect = [qr_x - 15, qr_y - 15, qr_x + qr_size + 15, qr_y + qr_size + 15]
            draw.rounded_rectangle(qr_bg_rect, radius=15, fill='white', outline='#FFD700', width=4)

            # Resize and paste QR code
            qr_resized = qr_img.resize((qr_size, qr_size), Image.Resampling.LANCZOS)
            badge_img.paste(qr_resized, (qr_x, qr_y))

            # QR label
            qr_label_y = qr_y + qr_size + 30
            draw.text((width//2, qr_label_y), "Event Link & Attendance",
                     fill='#FFD700', font=qr_font, anchor='mm')

            # 10. EVENT SPONSORS SECTION
            sponsors_y = qr_label_y + 50

            # Dark blue background for sponsors section
            sponsors_bg_y = sponsors_y - 10
            sponsors_bg_height = 120
            draw.rectangle([0, sponsors_bg_y, width, sponsors_bg_y + sponsors_bg_height],
                          fill='#1E3A8A')  # Darker blue

            # "EVENT SPONSORS" title
            draw.text((width//2, sponsors_y + 10), "EVENT SPONSORS",
                     fill='#FFD700', font=footer_font, anchor='mm')

            # Three sponsor logo placeholders
            logo_size = 60
            logo_y = sponsors_y + 35
            logo_spacing = 120
            start_x = (width - (3 * logo_size + 2 * (logo_spacing - logo_size))) // 2

            for i in range(3):
                logo_x = start_x + i * logo_spacing
                # Circular logo placeholders
                draw.ellipse([logo_x, logo_y, logo_x + logo_size, logo_y + logo_size],
                           fill='white', outline='#FFD700', width=2)
                draw.text((logo_x + logo_size//2, logo_y + logo_size//2), f"L{i+1}",
                         fill='#2E5BBA', font=small_font, anchor='mm')

            # 11. FOOTER with contact info
            footer_y = sponsors_bg_y + sponsors_bg_height + 10
            draw.text((width//2, footer_y), "www.uog.edu.et | <EMAIL>",
                     fill='white', font=footer_font, anchor='mm')
            draw.text((width//2, footer_y + 25), "Devoted to Excellence!",
                     fill='#FFD700', font=footer_font, anchor='mm')



            # Save the badge image with high quality
            buffer = BytesIO()
            badge_img.save(buffer, format='PNG', optimize=False, compress_level=0, dpi=(300, 300))
            buffer.seek(0)

            # Generate filename
            from django.utils import timezone
            filename = f"badge_{self.participant.id}_{timezone.now().strftime('%Y%m%d_%H%M%S')}.png"

            # Save to model
            self.badge_image.save(filename, File(buffer), save=False)
            self.is_generated = True
            self.generated_at = timezone.now()
            self.save()

            print(f"✅ Badge generated successfully for {self.participant.full_name}")
            return badge_img

        except Exception as e:
            print(f"❌ Error generating badge for {self.participant.full_name}: {e}")
            import traceback
            traceback.print_exc()
            return None

    def _add_pattern_overlays(self, draw, width, height):
        """Add exact pattern overlays to match SVG template"""
        badge_img = draw._image

        # Create pattern overlay image
        pattern_img = Image.new('RGBA', (width, height), (0, 0, 0, 0))
        pattern_draw = ImageDraw.Draw(pattern_img)

        # University seal pattern - circles with opacity 0.15, rotated 30 degrees
        import math
        for y in range(-200, height + 200, 200):
            for x in range(-200, width + 200, 200):
                center_x = x + 100
                center_y = y + 100
                # Apply 30-degree rotation
                cos30 = math.cos(math.radians(30))
                sin30 = math.sin(math.radians(30))
                rotated_x = center_x * cos30 - center_y * sin30
                rotated_y = center_x * sin30 + center_y * cos30

                if -100 <= rotated_x <= width + 100 and -100 <= rotated_y <= height + 100:
                    pattern_draw.ellipse([rotated_x-80, rotated_y-80, rotated_x+80, rotated_y+80],
                                       outline=(255, 215, 0, int(255 * 0.15)), width=2)

        # Geometric pattern - crosses with opacity 0.1, 60x60 grid
        for y in range(0, height, 60):
            for x in range(0, width, 60):
                # Draw the cross pattern from SVG: M30,0 L35,0 L35,25 L60,25 L60,30 L35,30 L35,60 L30,60 L30,30 L0,30 L0,25 L30,25 Z
                cross_points = [(x+30, y+0), (x+35, y+0), (x+35, y+25), (x+60, y+25), (x+60, y+30),
                               (x+35, y+30), (x+35, y+60), (x+30, y+60), (x+30, y+30), (x+0, y+30),
                               (x+0, y+25), (x+30, y+25)]
                pattern_draw.polygon(cross_points, fill=(255, 215, 0, int(255 * 0.1)))

        # Parchment scroll pattern with opacity 0.08, 120x120 grid
        for y in range(0, height, 120):
            for x in range(0, width, 120):
                # Draw curved scroll shape: M0,10 Q60,0 120,10 L120,110 Q60,120 0,110 Z
                scroll_points = []
                # Approximate the quadratic curves with multiple points
                for t in range(0, 11):
                    t_norm = t / 10.0
                    # Top curve: Q60,0 120,10
                    curve_x = x + (1-t_norm)**2 * 0 + 2*(1-t_norm)*t_norm * 60 + t_norm**2 * 120
                    curve_y = y + (1-t_norm)**2 * 10 + 2*(1-t_norm)*t_norm * 0 + t_norm**2 * 10
                    scroll_points.append((curve_x, curve_y))

                # Right side
                scroll_points.append((x+120, y+110))

                # Bottom curve: Q60,120 0,110
                for t in range(0, 11):
                    t_norm = t / 10.0
                    curve_x = x + (1-t_norm)**2 * 120 + 2*(1-t_norm)*t_norm * 60 + t_norm**2 * 0
                    curve_y = y + (1-t_norm)**2 * 110 + 2*(1-t_norm)*t_norm * 120 + t_norm**2 * 110
                    scroll_points.append((curve_x, curve_y))

                pattern_draw.polygon(scroll_points, fill=(255, 255, 255, int(255 * 0.08)))

        # Add background text with opacity 0.07
        # "70th" at position (100, 300) with font-size 120
        try:
            bg_font = ImageFont.truetype("arial.ttf", 180)
        except:
            bg_font = ImageFont.load_default()
        pattern_draw.text((100, 300), "70th", fill=(255, 255, 255, int(255 * 0.07)), font=bg_font)

        # "100th" at position (350, 600) with font-size 120
        pattern_draw.text((350, 600), "100th", fill=(255, 255, 255, int(255 * 0.07)), font=bg_font)

        # Apply pattern overlay to main image
        badge_img.paste(pattern_img, (0, 0), pattern_img)

    def _draw_anniversary_ribbon(self, draw, width, anniversary_font=None):
        """Draw the anniversary ribbon at the top exactly as in SVG"""
        badge_img = draw._image

        # Create ribbon with gold gradient and curved bottom
        ribbon_img = Image.new('RGBA', (width, 80), (0, 0, 0, 0))
        ribbon_draw = ImageDraw.Draw(ribbon_img)

        # Draw curved path: M0,0 L600,0 L600,40 Q300,80 0,40 Z
        # Approximate the quadratic curve
        points = [(0, 0), (width, 0), (width, 40)]
        # Add curve points for Q300,80 0,40
        for t in range(0, 11):
            t_norm = t / 10.0
            curve_x = (1-t_norm)**2 * width + 2*(1-t_norm)*t_norm * (width//2) + t_norm**2 * 0
            curve_y = (1-t_norm)**2 * 40 + 2*(1-t_norm)*t_norm * 80 + t_norm**2 * 40
            points.append((curve_x, curve_y))

        # Create gold gradient effect
        for y in range(40):
            # Gold gradient from #FFD700 to #FFC000
            r = int(255 - (255-255) * (y/40))
            g = int(215 - (215-192) * (y/40))
            b = int(0 - (0-0) * (y/40))
            color = (r, g, b, int(255 * 0.9))
            ribbon_draw.line([(0, y), (width, y)], fill=color)

        # Anniversary text with exact SVG styling
        if anniversary_font is None:
            try:
                anniversary_font = ImageFont.truetype("arial.ttf", 80)  # SVG font-size="80"
            except:
                anniversary_font = ImageFont.load_default()
        ribbon_draw.text((width//2, 60), "70/100th ANNIVERSARY", fill=(26, 75, 140),
                        font=anniversary_font, anchor='mm')

        # Paste ribbon onto main image
        badge_img.paste(ribbon_img, (0, 0), ribbon_img)

    def _add_sponsor_logos(self, draw, badge_img, width, height, font):
        """Add sponsor logo placeholders (already drawn in main method)"""
        # Logo placeholders are already drawn in the main method
        # This method is kept for compatibility but doesn't need to do anything
        # since the new template has fixed logo placeholder positions
        pass

    def _add_sponsor_placeholders(self, draw, width, height, font):
        """Add sponsor placeholders when no sponsors are available"""
        sponsor_positions = [(100, 1050, 180, 1110), (210, 1050, 290, 1110), (320, 1050, 400, 1110)]
        for x1, y1, x2, y2 in sponsor_positions:
            draw.rounded_rectangle([x1, y1, x2, y2], radius=10,
                                 fill=(255, 255, 255, 15), outline='#FFEC8B', width=1)
            draw.text(((x1+x2)//2, (y1+y2)//2), "SPONSOR", fill='white', font=font, anchor='mm')


# Driver Badge Model
class DriverBadge(models.Model):
    driver = models.OneToOneField('drivers.Driver', on_delete=models.CASCADE, related_name='badge')

    # QR Code
    qr_code_data = models.TextField()
    qr_code_image = models.ImageField(upload_to='driver_qr_codes/', null=True, blank=True)

    # Badge File
    badge_image = models.ImageField(upload_to='driver_badges/', null=True, blank=True)

    # Generation Status
    is_generated = models.BooleanField(default=False)
    generated_at = models.DateTimeField(null=True, blank=True)

    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"Driver Badge for {self.driver.name}"

    def generate_qr_code(self):
        """Generate QR code for the driver"""
        import json
        from django.utils import timezone

        # Create QR code data
        qr_data = {
            'driver_id': self.driver.id,
            'name': self.driver.name,
            'phone': self.driver.phone,
            'generated_at': timezone.now().isoformat(),
            'type': 'driver_badge'
        }

        # Store QR data
        self.qr_code_data = json.dumps(qr_data)

        # Generate QR code image
        qr = qrcode.QRCode(
            version=1,
            error_correction=qrcode.constants.ERROR_CORRECT_L,
            box_size=10,
            border=4,
        )
        qr.add_data(json.dumps(qr_data))
        qr.make(fit=True)

        # Create QR code image
        qr_img = qr.make_image(fill_color="black", back_color="white")

        # Save QR code to model
        buffer = BytesIO()
        qr_img.save(buffer, format='PNG')
        buffer.seek(0)

        filename = f"qr_driver_{self.driver.id}.png"
        self.qr_code_image.save(filename, File(buffer), save=False)

        return qr_img

    def generate_badge(self):
        """Generate the complete badge image for driver"""
        from django.utils import timezone

        # Get or create QR code
        if not self.qr_code_image:
            qr_img = self.generate_qr_code()
        else:
            qr_img = Image.open(self.qr_code_image.path)

        # Create simple driver badge (smaller than participant badge)
        width = 400
        height = 600

        badge_img = Image.new('RGB', (width, height), color='#1E40AF')  # Blue background
        draw = ImageDraw.Draw(badge_img)

        # Load fonts
        try:
            title_font = ImageFont.truetype("arial.ttf", 24)
            name_font = ImageFont.truetype("arial.ttf", 32)
            info_font = ImageFont.truetype("arial.ttf", 18)
        except:
            title_font = ImageFont.load_default()
            name_font = ImageFont.load_default()
            info_font = ImageFont.load_default()

        # Title
        draw.text((width//2, 50), "DRIVER", fill='white', font=title_font, anchor='mm')

        # Driver name
        draw.text((width//2, 120), self.driver.name, fill='white', font=name_font, anchor='mm')

        # Phone
        draw.text((width//2, 160), self.driver.phone, fill='white', font=info_font, anchor='mm')

        # QR code
        qr_resized = qr_img.resize((150, 150), Image.Resampling.LANCZOS)
        qr_x = (width - 150) // 2
        qr_y = 200

        # QR background
        draw.rectangle([qr_x-5, qr_y-5, qr_x+155, qr_y+155], fill='white')
        badge_img.paste(qr_resized, (qr_x, qr_y))

        # QR label
        draw.text((width//2, 370), "SCAN FOR DETAILS", fill='white', font=info_font, anchor='mm')

        # Save the badge
        buffer = BytesIO()
        badge_img.save(buffer, format='PNG', optimize=False, compress_level=0)
        buffer.seek(0)

        filename = f"driver_badge_{self.driver.id}_{timezone.now().strftime('%Y%m%d_%H%M%S')}.png"
        self.badge_image.save(filename, File(buffer), save=False)
        self.is_generated = True
        self.generated_at = timezone.now()
        self.save()

        return badge_img


# Contact Person Badge Model
class ContactPersonBadge(models.Model):
    contact_person = models.OneToOneField('contact_persons.ContactPerson', on_delete=models.CASCADE, related_name='badge')

    # QR Code
    qr_code_data = models.TextField()
    qr_code_image = models.ImageField(upload_to='contact_qr_codes/', null=True, blank=True)

    # Badge File
    badge_image = models.ImageField(upload_to='contact_badges/', null=True, blank=True)

    # Generation Status
    is_generated = models.BooleanField(default=False)
    generated_at = models.DateTimeField(null=True, blank=True)

    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"Contact Person Badge for {self.contact_person.full_name}"

    def generate_qr_code(self):
        """Generate QR code for the contact person"""
        import json
        from django.utils import timezone

        # Create QR code data
        qr_data = {
            'contact_person_id': self.contact_person.id,
            'full_name': self.contact_person.full_name,
            'phone': self.contact_person.phone,
            'hotel': self.contact_person.hotel.name if self.contact_person.hotel else None,
            'generated_at': timezone.now().isoformat(),
            'type': 'contact_person_badge'
        }

        # Store QR data
        self.qr_code_data = json.dumps(qr_data)

        # Generate QR code image
        qr = qrcode.QRCode(
            version=1,
            error_correction=qrcode.constants.ERROR_CORRECT_L,
            box_size=10,
            border=4,
        )
        qr.add_data(json.dumps(qr_data))
        qr.make(fit=True)

        # Create QR code image
        qr_img = qr.make_image(fill_color="black", back_color="white")

        # Save QR code to model
        buffer = BytesIO()
        qr_img.save(buffer, format='PNG')
        buffer.seek(0)

        filename = f"qr_contact_person_{self.contact_person.id}.png"
        self.qr_code_image.save(filename, File(buffer), save=False)

        return qr_img

    def generate_badge(self):
        """Generate the complete badge image for contact person"""
        from django.utils import timezone

        # Get or create QR code
        if not self.qr_code_image:
            qr_img = self.generate_qr_code()
        else:
            qr_img = Image.open(self.qr_code_image.path)

        # Create contact person badge
        width = 400
        height = 600

        badge_img = Image.new('RGB', (width, height), color='#059669')  # Green background
        draw = ImageDraw.Draw(badge_img)

        # Load fonts
        try:
            title_font = ImageFont.truetype("arial.ttf", 20)
            name_font = ImageFont.truetype("arial.ttf", 28)
            info_font = ImageFont.truetype("arial.ttf", 16)
        except:
            title_font = ImageFont.load_default()
            name_font = ImageFont.load_default()
            info_font = ImageFont.load_default()

        # Title
        draw.text((width//2, 40), "CONTACT PERSON", fill='white', font=title_font, anchor='mm')

        # Contact person name
        draw.text((width//2, 100), self.contact_person.full_name, fill='white', font=name_font, anchor='mm')

        # Hotel name
        if self.contact_person.hotel:
            draw.text((width//2, 130), self.contact_person.hotel.name, fill='white', font=info_font, anchor='mm')

        # Phone
        draw.text((width//2, 160), self.contact_person.phone, fill='white', font=info_font, anchor='mm')

        # QR code
        qr_resized = qr_img.resize((150, 150), Image.Resampling.LANCZOS)
        qr_x = (width - 150) // 2
        qr_y = 200

        # QR background
        draw.rectangle([qr_x-5, qr_y-5, qr_x+155, qr_y+155], fill='white')
        badge_img.paste(qr_resized, (qr_x, qr_y))

        # QR label
        draw.text((width//2, 370), "SCAN FOR DETAILS", fill='white', font=info_font, anchor='mm')

        # Save the badge
        buffer = BytesIO()
        badge_img.save(buffer, format='PNG', optimize=False, compress_level=0)
        buffer.seek(0)

        filename = f"contact_badge_{self.contact_person.id}_{timezone.now().strftime('%Y%m%d_%H%M%S')}.png"
        self.badge_image.save(filename, File(buffer), save=False)
        self.is_generated = True
        self.generated_at = timezone.now()
        self.save()

        return badge_img
