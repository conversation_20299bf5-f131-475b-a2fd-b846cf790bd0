# Generated by Django 4.2.7 on 2025-08-04 15:14

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('events', '0007_add_map_embed'),
        ('participants', '0006_remove_participant_badge_generated_and_more'),
    ]

    operations = [
        migrations.CreateModel(
            name='EventFeedback',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('participant_name', models.CharField(blank=True, help_text='Optional: Participant name', max_length=200)),
                ('participant_email', models.EmailField(blank=True, help_text='Optional: For follow-up or event updates', max_length=254)),
                ('institution_name', models.CharField(blank=True, help_text='Optional: Institution/Organization', max_length=200)),
                ('position_title', models.CharField(blank=True, help_text='Optional: Position/Title', max_length=200)),
                ('overall_satisfaction', models.IntegerField(choices=[(1, '⭐ Poor'), (2, '⭐⭐ Below Average'), (3, '⭐⭐⭐ Average'), (4, '⭐⭐⭐⭐ Good'), (5, '⭐⭐⭐⭐⭐ Excellent')], help_text='Overall satisfaction rating')),
                ('met_expectations', models.CharField(choices=[('yes', 'Yes'), ('somewhat', 'Somewhat'), ('no', 'No')], help_text='Did the conference meet expectations?', max_length=10)),
                ('most_valuable_aspect', models.TextField(help_text='Most valuable aspect of the event')),
                ('improvement_suggestions', models.TextField(blank=True, help_text='What could be improved for future conferences?')),
                ('session_relevance', models.IntegerField(choices=[(1, '⭐ Poor'), (2, '⭐⭐ Below Average'), (3, '⭐⭐⭐ Average'), (4, '⭐⭐⭐⭐ Good'), (5, '⭐⭐⭐⭐⭐ Excellent')], help_text='How relevant were the sessions?')),
                ('most_valuable_sessions', models.TextField(blank=True, help_text='Which sessions were most valuable?')),
                ('future_topics_suggestions', models.TextField(blank=True, help_text='Suggestions for future topics or session formats')),
                ('speaker_quality', models.IntegerField(choices=[(1, '⭐ Poor'), (2, '⭐⭐ Below Average'), (3, '⭐⭐⭐ Average'), (4, '⭐⭐⭐⭐ Good'), (5, '⭐⭐⭐⭐⭐ Excellent')], help_text='Quality of speakers/moderators')),
                ('speaker_comments', models.TextField(blank=True, help_text='Comments on speaker expertise, engagement, or delivery')),
                ('venue_facilities', models.IntegerField(choices=[(1, '⭐ Poor'), (2, '⭐⭐ Below Average'), (3, '⭐⭐⭐ Average'), (4, '⭐⭐⭐⭐ Good'), (5, '⭐⭐⭐⭐⭐ Excellent')], help_text='Venue & Facilities rating')),
                ('technical_setup', models.IntegerField(choices=[(1, '⭐ Poor'), (2, '⭐⭐ Below Average'), (3, '⭐⭐⭐ Average'), (4, '⭐⭐⭐⭐ Good'), (5, '⭐⭐⭐⭐⭐ Excellent')], help_text='Technical Setup rating')),
                ('time_management', models.IntegerField(choices=[(1, '⭐ Poor'), (2, '⭐⭐ Below Average'), (3, '⭐⭐⭐ Average'), (4, '⭐⭐⭐⭐ Good'), (5, '⭐⭐⭐⭐⭐ Excellent')], help_text='Time Management & Scheduling rating')),
                ('transportation_accessibility', models.IntegerField(choices=[(1, '⭐ Poor'), (2, '⭐⭐ Below Average'), (3, '⭐⭐⭐ Average'), (4, '⭐⭐⭐⭐ Good'), (5, '⭐⭐⭐⭐⭐ Excellent')], help_text='Transportation & Accessibility rating')),
                ('pre_event_communication', models.IntegerField(choices=[(1, '⭐ Poor'), (2, '⭐⭐ Below Average'), (3, '⭐⭐⭐ Average'), (4, '⭐⭐⭐⭐ Good'), (5, '⭐⭐⭐⭐⭐ Excellent')], help_text='Pre-event Communication rating')),
                ('logistics_comments', models.TextField(blank=True, help_text='Additional comments on logistics')),
                ('sufficient_networking', models.CharField(choices=[('yes', 'Yes'), ('somewhat', 'Somewhat'), ('no', 'No')], help_text='Sufficient networking opportunities?', max_length=10)),
                ('networking_improvements', models.TextField(blank=True, help_text='How could networking be improved?')),
                ('future_topics', models.TextField(blank=True, help_text='Topics for future conferences')),
                ('additional_feedback', models.TextField(blank=True, help_text='Any other feedback or recommendations')),
                ('uploaded_file', models.FileField(blank=True, help_text='Optional: Upload relevant materials', null=True, upload_to='feedback_uploads/')),
                ('consent_given', models.BooleanField(default=False, help_text='Consent to use feedback for improvement and reporting')),
                ('is_anonymous', models.BooleanField(default=False, help_text='Whether this is anonymous feedback')),
                ('ip_address', models.GenericIPAddressField(blank=True, help_text='IP address for tracking', null=True)),
                ('user_agent', models.TextField(blank=True, help_text='Browser user agent')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('event', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='feedback', to='events.event')),
                ('participant', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='feedback', to='participants.participant')),
            ],
            options={
                'verbose_name': 'Event Feedback',
                'verbose_name_plural': 'Event Feedback',
                'ordering': ['-created_at'],
                'unique_together': {('event', 'participant')},
            },
        ),
        migrations.CreateModel(
            name='FeedbackTemplate',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(help_text='Template name', max_length=200)),
                ('template_type', models.CharField(choices=[('conference', 'Conference'), ('workshop', 'Workshop'), ('seminar', 'Seminar'), ('symposium', 'Symposium'), ('meeting', 'Meeting'), ('custom', 'Custom')], default='conference', max_length=20)),
                ('description', models.TextField(blank=True, help_text='Template description')),
                ('form_config', models.JSONField(default=dict, help_text='Form configuration in JSON format')),
                ('is_active', models.BooleanField(default=True)),
                ('is_default', models.BooleanField(default=False, help_text='Default template for this type')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'verbose_name': 'Feedback Template',
                'verbose_name_plural': 'Feedback Templates',
                'ordering': ['template_type', 'name'],
            },
        ),
        migrations.CreateModel(
            name='SessionFeedback',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('content_quality', models.IntegerField(choices=[(1, '⭐ Poor'), (2, '⭐⭐ Below Average'), (3, '⭐⭐⭐ Average'), (4, '⭐⭐⭐⭐ Good'), (5, '⭐⭐⭐⭐⭐ Excellent')], help_text='Content quality rating')),
                ('speaker_effectiveness', models.IntegerField(choices=[(1, '⭐ Poor'), (2, '⭐⭐ Below Average'), (3, '⭐⭐⭐ Average'), (4, '⭐⭐⭐⭐ Good'), (5, '⭐⭐⭐⭐⭐ Excellent')], help_text='Speaker effectiveness rating')),
                ('session_organization', models.IntegerField(choices=[(1, '⭐ Poor'), (2, '⭐⭐ Below Average'), (3, '⭐⭐⭐ Average'), (4, '⭐⭐⭐⭐ Good'), (5, '⭐⭐⭐⭐⭐ Excellent')], help_text='Session organization rating')),
                ('audience_engagement', models.IntegerField(choices=[(1, '⭐ Poor'), (2, '⭐⭐ Below Average'), (3, '⭐⭐⭐ Average'), (4, '⭐⭐⭐⭐ Good'), (5, '⭐⭐⭐⭐⭐ Excellent')], help_text='Audience engagement rating')),
                ('what_worked_well', models.TextField(blank=True, help_text='What worked well in this session?')),
                ('improvement_suggestions', models.TextField(blank=True, help_text='What could be improved?')),
                ('additional_comments', models.TextField(blank=True, help_text='Additional comments')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('event_feedback', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='session_feedback', to='feedback.eventfeedback')),
                ('session', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='feedback', to='events.eventschedule')),
            ],
            options={
                'verbose_name': 'Session Feedback',
                'verbose_name_plural': 'Session Feedback',
                'ordering': ['-created_at'],
                'unique_together': {('event_feedback', 'session')},
            },
        ),
    ]
