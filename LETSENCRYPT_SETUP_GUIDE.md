# Let's Encrypt SSL Setup Guide
## University of Gondar Event Management System

This guide provides comprehensive instructions for setting up Let's Encrypt SSL certificates for the UoG Event Management System, replacing any existing self-signed certificates.

## 🔐 Overview

Let's Encrypt provides free, automated, and secure SSL certificates that are trusted by all major browsers. This setup includes:

- Automatic SSL certificate generation
- HTTPS redirection
- Security headers optimization
- Auto-renewal configuration
- OCSP stapling for improved performance

## 📋 Prerequisites

1. **Domain Configuration**
   - DNS A record for `event.uog.edu.et` pointing to your server IP
   - DNS A record for `www.event.uog.edu.et` pointing to your server IP
   - Firewall allowing ports 80 (HTTP) and 443 (HTTPS)

2. **Server Requirements**
   - Docker and Docker Compose installed
   - Root or sudo access
   - Internet connectivity for Let's Encrypt validation

## 🚀 Quick Setup

### Step 1: Initialize Let's Encrypt Certificates

```bash
# Make the script executable
chmod +x init-letsencrypt.sh

# Run the initialization script
sudo ./init-letsencrypt.sh
```

The script will:
- Remove any existing self-signed certificates
- Create temporary nginx configuration
- Generate Let's Encrypt certificates
- Configure production nginx with SSL
- Start all services with HTTPS enabled

### Step 2: Verify SSL Setup

After initialization, verify your SSL setup:

```bash
# Test HTTPS connection
curl -I https://event.uog.edu.et

# Check certificate details
openssl s_client -connect event.uog.edu.et:443 -servername event.uog.edu.et
```

## 🔄 Certificate Renewal

### Automatic Renewal

Certificates automatically renew via the certbot container that runs every 12 hours.

### Manual Renewal

To manually renew certificates:

```bash
# Make renewal script executable
chmod +x renew-ssl.sh

# Run manual renewal
sudo ./renew-ssl.sh
```

## 📁 File Structure

```
nginx/
├── conf.d/
│   └── production.conf          # Main nginx config with Let's Encrypt SSL
├── nginx.conf                   # Base nginx configuration
certbot/
├── conf/                        # Let's Encrypt certificates
└── www/                         # ACME challenge files
logs/
├── nginx/                       # Nginx logs
└── certbot/                     # Certificate renewal logs
```

## ⚙️ Configuration Details

### Nginx SSL Configuration

The production nginx configuration includes:

- **SSL Protocols**: TLSv1.2 and TLSv1.3
- **Strong Ciphers**: ECDHE-RSA-AES256-GCM-SHA512 and others
- **OCSP Stapling**: Enabled for performance
- **Security Headers**: HSTS, X-Frame-Options, CSP, etc.
- **HTTP to HTTPS Redirect**: Automatic redirection

### Backend HTTPS Settings

The Django backend is configured with:

- `SECURE_SSL_REDIRECT=True`
- `SESSION_COOKIE_SECURE=True`
- `CSRF_COOKIE_SECURE=True`
- `SECURE_PROXY_SSL_HEADER=HTTP_X_FORWARDED_PROTO,https`

## 🔧 Troubleshooting

### Common Issues

1. **Domain Not Accessible**
   ```bash
   # Check DNS resolution
   nslookup event.uog.edu.et
   
   # Test connectivity
   ping event.uog.edu.et
   ```

2. **Certificate Generation Failed**
   ```bash
   # Check certbot logs
   docker-compose -f docker-compose.prod.yml logs certbot
   
   # Verify nginx is running
   docker-compose -f docker-compose.prod.yml ps nginx
   ```

3. **HTTPS Not Working**
   ```bash
   # Check nginx configuration
   docker-compose -f docker-compose.prod.yml exec nginx nginx -t
   
   # Check certificate files
   ls -la ./certbot/conf/live/event.uog.edu.et/
   ```

### Log Locations

- Nginx logs: `./logs/nginx/`
- Certbot logs: `./logs/certbot/`
- Certificate renewal log: `./logs/certbot/renewal.log`

## 🔒 Security Features

### SSL Security

- **Perfect Forward Secrecy**: ECDHE key exchange
- **Strong Encryption**: AES-256-GCM ciphers
- **HSTS**: HTTP Strict Transport Security enabled
- **OCSP Stapling**: Improved certificate validation

### Security Headers

- `Strict-Transport-Security`: Forces HTTPS
- `X-Frame-Options`: Prevents clickjacking
- `X-Content-Type-Options`: Prevents MIME sniffing
- `Content-Security-Policy`: Restricts resource loading
- `Referrer-Policy`: Controls referrer information

## 📧 Certificate Notifications

Let's Encrypt will send renewal notifications to `<EMAIL>`. Update the email in `init-letsencrypt.sh` if needed.

## 🔄 Maintenance

### Regular Tasks

1. **Monitor Certificate Expiry**
   ```bash
   # Check certificate status
   docker-compose -f docker-compose.prod.yml run --rm certbot certificates
   ```

2. **Review Renewal Logs**
   ```bash
   # Check renewal logs
   tail -f ./logs/certbot/renewal.log
   ```

3. **Test HTTPS Regularly**
   ```bash
   # Test SSL configuration
   curl -I https://event.uog.edu.et
   ```

## 🆘 Emergency Procedures

### Restore from Backup

If SSL setup fails, restore from backup:

```bash
# Stop services
docker-compose -f docker-compose.prod.yml down

# Restore nginx configuration
cp nginx/conf.d/production.conf.backup nginx/conf.d/production.conf

# Start services
docker-compose -f docker-compose.prod.yml up -d
```

### Force Certificate Regeneration

To force regenerate certificates:

```bash
# Remove existing certificates
sudo rm -rf ./certbot/conf/live/event.uog.edu.et/

# Re-run initialization
sudo ./init-letsencrypt.sh
```

## ✅ Verification Checklist

- [ ] DNS records point to correct IP
- [ ] Ports 80 and 443 are open
- [ ] Let's Encrypt certificates generated
- [ ] HTTPS redirects working
- [ ] Security headers present
- [ ] Auto-renewal configured
- [ ] Backup procedures tested

## 📞 Support

For issues with Let's Encrypt setup:

1. Check the troubleshooting section above
2. Review logs in `./logs/certbot/`
3. Verify DNS and firewall configuration
4. Test with Let's Encrypt staging environment first

---

**Note**: This setup completely replaces any existing self-signed certificates with trusted Let's Encrypt certificates for production use.
