import React, { useState, useEffect } from 'react';
import { Container, Row, Col, Card, Button, Alert, Form, Modal } from 'react-bootstrap';
import { Html5QrcodeScanner } from 'html5-qrcode';
import { participantService, attendanceService, eventService, Event, EventSchedule } from '../services/api';

const QRScanner: React.FC = () => {
  const [scanning, setScanning] = useState(false);
  const [scannedData, setScannedData] = useState<any>(null);
  const [events, setEvents] = useState<Event[]>([]);
  const [selectedEvent, setSelectedEvent] = useState<string>('');
  const [eventSchedules, setEventSchedules] = useState<EventSchedule[]>([]);
  const [selectedSchedule, setSelectedSchedule] = useState<string>('');
  const [checkedInBy, setCheckedInBy] = useState('');
  const [notes, setNotes] = useState('');
  const [success, setSuccess] = useState('');
  const [error, setError] = useState('');
  const [showCheckInModal, setShowCheckInModal] = useState(false);

  useEffect(() => {
    fetchEvents();
  }, []);

  useEffect(() => {
    if (selectedEvent) {
      fetchEventSchedules();
    }
  }, [selectedEvent]);

  const fetchEvents = async () => {
    try {
      const response = await eventService.getEvents();
      const eventsData = response.data.results || [];
      setEvents(eventsData.filter((event: any) => event.is_active));
    } catch (error) {
      console.error('Error fetching events:', error);
    }
  };

  const fetchEventSchedules = async () => {
    try {
      const response = await eventService.getEventSchedule(parseInt(selectedEvent));
      setEventSchedules(response.data);
    } catch (error) {
      console.error('Error fetching schedules:', error);
    }
  };

  const startScanning = () => {
    setScanning(true);
    setError('');
    setSuccess('');

    const scanner = new Html5QrcodeScanner(
      'qr-reader',
      {
        fps: 10,
        qrbox: { width: 250, height: 250 },
        aspectRatio: 1.0,
        disableFlip: false,
        // Enhanced camera configuration for mobile back camera
        videoConstraints: {
          facingMode: { exact: "environment" }, // Force back camera
          width: { ideal: 1280 },
          height: { ideal: 720 }
        },
        // Additional configuration for better mobile support
        experimentalFeatures: {
          useBarCodeDetectorIfSupported: true
        },
        rememberLastUsedCamera: true,
        showTorchButtonIfSupported: true
      },
      false
    );

    scanner.render(
      (decodedText, decodedResult) => {
        // Handle successful scan
        console.log('QR Code scanned:', decodedText);
        handleScanSuccess(decodedText);
        scanner.clear();
        setScanning(false);
      },
      (error) => {
        // Handle scan error (can be ignored for continuous scanning)
        console.warn('QR scan error:', error);
      }
    );
  };

  const stopScanning = () => {
    setScanning(false);
    // Clear the scanner
    const element = document.getElementById('qr-reader');
    if (element) {
      element.innerHTML = '';
    }
  };

  const handleScanSuccess = async (decodedText: string) => {
    try {
      console.log('Scanned QR Code:', decodedText);

      // Try to parse QR data as JSON, fallback to raw text
      let qrData = null;
      try {
        qrData = JSON.parse(decodedText);
      } catch {
        // Not JSON, use as raw text
        qrData = null;
      }

      // The enhanced backend now handles all QR formats automatically
      // We can send the raw QR data directly without frontend parsing
      console.log('Sending raw QR data to backend for processing...');

      // Verify participant using the raw QR data - backend handles all parsing
      const response = await participantService.verifyParticipant(decodedText);

      // Enhance the response data with QR code information
      const enhancedData = {
        ...response.data,
        qr_code_data: qrData,
        scanned_at: new Date().toISOString(),
        scan_method: qrData ? 'JSON' : 'URL/ID'
      };

      setScannedData(enhancedData);
      setShowCheckInModal(true);
      setError(''); // Clear any previous errors

    } catch (error: any) {
      console.error('QR Scan Error:', error);
      setError(`Invalid QR code or participant not found: ${error.response?.data?.error || error.message}`);
    }
  };

  const handleCheckIn = async () => {
    if (!selectedSchedule) {
      setError('Please select an event schedule');
      return;
    }

    try {
      await attendanceService.checkIn({
        uuid: scannedData.uuid,
        event_schedule_id: parseInt(selectedSchedule),
        checked_in_by: checkedInBy,
        notes: notes,
      });

      setSuccess(`Successfully checked in ${scannedData.full_name}`);
      setShowCheckInModal(false);
      setScannedData(null);
      setNotes('');
    } catch (error: any) {
      setError(error.response?.data?.error || 'Check-in failed');
    }
  };

  return (
    <Container className="py-4">
      <Row className="justify-content-center">
        <Col lg={8}>
          <Card className="shadow">
            <Card.Header className="bg-primary text-white">
              <h3 className="mb-0">
                <i className="fas fa-qrcode me-2"></i>
                QR Code Scanner
              </h3>
            </Card.Header>
            <Card.Body className="p-4">
              {success && (
                <Alert variant="success" className="mb-4">
                  <i className="fas fa-check-circle me-2"></i>
                  {success}
                </Alert>
              )}

              {error && (
                <Alert variant="danger" className="mb-4">
                  <i className="fas fa-exclamation-triangle me-2"></i>
                  {error}
                </Alert>
              )}

              {/* Event and Schedule Selection */}
              <Row className="mb-4">
                <Col md={6}>
                  <Form.Group className="mb-3">
                    <Form.Label>Select Event</Form.Label>
                    <Form.Select
                      value={selectedEvent}
                      onChange={(e) => setSelectedEvent(e.target.value)}
                      required
                    >
                      <option value="">Choose an event...</option>
                      {events.map(event => (
                        <option key={event.id} value={event.id}>
                          {event.name}
                        </option>
                      ))}
                    </Form.Select>
                  </Form.Group>
                </Col>
                <Col md={6}>
                  <Form.Group className="mb-3">
                    <Form.Label>Select Session</Form.Label>
                    <Form.Select
                      value={selectedSchedule}
                      onChange={(e) => setSelectedSchedule(e.target.value)}
                      disabled={!selectedEvent}
                      required
                    >
                      <option value="">Choose a session...</option>
                      {eventSchedules.map(schedule => (
                        <option key={schedule.id} value={schedule.id}>
                          {schedule.title} - {new Date(schedule.start_time).toLocaleString()}
                        </option>
                      ))}
                    </Form.Select>
                  </Form.Group>
                </Col>
              </Row>

              <Row className="mb-4">
                <Col>
                  <Form.Group>
                    <Form.Label>Checked in by (Staff Name)</Form.Label>
                    <Form.Control
                      type="text"
                      value={checkedInBy}
                      onChange={(e) => setCheckedInBy(e.target.value)}
                      placeholder="Enter your name"
                    />
                  </Form.Group>
                </Col>
              </Row>

              {/* Scanner Controls */}
              <div className="text-center mb-4">
                {!scanning ? (
                  <Button
                    variant="primary"
                    size="lg"
                    onClick={startScanning}
                    disabled={!selectedEvent || !selectedSchedule}
                    className="px-4 py-3"
                  >
                    <i className="fas fa-camera me-2"></i>
                    Start QR Scanner (Back Camera)
                  </Button>
                ) : (
                  <Button
                    variant="danger"
                    size="lg"
                    onClick={stopScanning}
                    className="px-4 py-3"
                  >
                    <i className="fas fa-stop me-2"></i>
                    Stop Scanner
                  </Button>
                )}
              </div>

              {/* Mobile-specific tips */}
              {scanning && (
                <Alert variant="warning" className="mb-3">
                  <i className="fas fa-mobile-alt me-2"></i>
                  <strong>Mobile Tips:</strong>
                  <ul className="mb-0 mt-2">
                    <li>Hold your device steady</li>
                    <li>Ensure good lighting</li>
                    <li>Keep QR code within the yellow box</li>
                    <li>Move closer or further if needed</li>
                  </ul>
                </Alert>
              )}

              {/* QR Scanner */}
              <div className="qr-scanner-container">
                <div id="qr-reader" className="mb-4"></div>
              </div>

              {/* Instructions */}
              <Alert variant="info">
                <h6><i className="fas fa-info-circle me-2"></i>Instructions:</h6>
                <ul className="mb-0">
                  <li>Select an event and session before scanning</li>
                  <li>Point your mobile device's <strong>back camera</strong> at the participant's badge QR code</li>
                  <li>Hold the device steady and ensure good lighting for best results</li>
                  <li>The system will automatically detect and process the code</li>
                  <li>Confirm the check-in details in the popup</li>
                </ul>
              </Alert>
            </Card.Body>
          </Card>
        </Col>
      </Row>

      {/* Check-in Confirmation Modal */}
      <Modal show={showCheckInModal} onHide={() => setShowCheckInModal(false)} centered>
        <Modal.Header closeButton>
          <Modal.Title>Confirm Check-in</Modal.Title>
        </Modal.Header>
        <Modal.Body>
          {scannedData && (
            <div>
              {/* Participant Information Card */}
              <div className="card border-0 shadow-sm mb-3">
                <div className="card-header bg-primary text-white">
                  <h6 className="mb-0">
                    <i className="fas fa-user me-2"></i>
                    Participant Information
                  </h6>
                </div>
                <div className="card-body">
                  <div className="row">
                    <div className="col-md-6">
                      <p className="mb-2"><strong>ID:</strong> {scannedData.id}</p>
                      <p className="mb-2"><strong>Name:</strong> {scannedData.full_name}</p>
                      <p className="mb-2"><strong>Email:</strong> {scannedData.email}</p>
                      {scannedData.phone && (
                        <p className="mb-2"><strong>Phone:</strong> {scannedData.phone}</p>
                      )}
                    </div>
                    <div className="col-md-6">
                      <p className="mb-2"><strong>Institution:</strong> {scannedData.institution_name}</p>
                      {scannedData.position && (
                        <p className="mb-2"><strong>Position:</strong> {scannedData.position}</p>
                      )}
                      <p className="mb-2"><strong>Type:</strong>
                        <span
                          className="badge ms-2"
                          style={{ backgroundColor: scannedData.participant_type_color }}
                        >
                          {scannedData.participant_type_name}
                        </span>
                      </p>
                      <p className="mb-2"><strong>Status:</strong>
                        <span className={`badge ms-2 ${scannedData.status === 'approved' ? 'bg-success' : 'bg-warning'}`}>
                          {scannedData.status?.toUpperCase()}
                        </span>
                      </p>
                    </div>
                  </div>
                </div>
              </div>

              {/* QR Code Information */}
              {scannedData.qr_code_data && (
                <div className="card border-0 shadow-sm mb-3">
                  <div className="card-header bg-info text-white">
                    <h6 className="mb-0">
                      <i className="fas fa-qrcode me-2"></i>
                      QR Code Information
                    </h6>
                  </div>
                  <div className="card-body">
                    <p className="mb-2"><strong>Scan Method:</strong> {scannedData.scan_method}</p>
                    <p className="mb-2"><strong>Scanned At:</strong> {new Date(scannedData.scanned_at).toLocaleString()}</p>
                    {scannedData.qr_code_data.generated_at && (
                      <p className="mb-2"><strong>QR Generated:</strong> {new Date(scannedData.qr_code_data.generated_at).toLocaleString()}</p>
                    )}
                    {scannedData.qr_code_data.version && (
                      <p className="mb-2"><strong>QR Version:</strong> {scannedData.qr_code_data.version}</p>
                    )}
                  </div>
                </div>
              )}

              {/* Attendance Information */}
              {scannedData.arrival_date && (
                <div className="card border-0 shadow-sm mb-3">
                  <div className="card-header bg-success text-white">
                    <h6 className="mb-0">
                      <i className="fas fa-calendar me-2"></i>
                      Event Schedule
                    </h6>
                  </div>
                  <div className="card-body">
                    {scannedData.arrival_date && (
                      <p className="mb-2"><strong>Arrival:</strong> {new Date(scannedData.arrival_date).toLocaleString()}</p>
                    )}
                    {scannedData.departure_date && (
                      <p className="mb-2"><strong>Departure:</strong> {new Date(scannedData.departure_date).toLocaleString()}</p>
                    )}
                  </div>
                </div>
              )}

              <Form.Group className="mt-3">
                <Form.Label>Check-in Notes (Optional)</Form.Label>
                <Form.Control
                  as="textarea"
                  rows={2}
                  value={notes}
                  onChange={(e) => setNotes(e.target.value)}
                  placeholder="Any additional notes about this check-in..."
                />
              </Form.Group>
            </div>
          )}
        </Modal.Body>
        <Modal.Footer>
          <Button variant="secondary" onClick={() => setShowCheckInModal(false)}>
            Cancel
          </Button>
          <Button variant="primary" onClick={handleCheckIn}>
            <i className="fas fa-check me-2"></i>
            Confirm Check-in
          </Button>
        </Modal.Footer>
      </Modal>
    </Container>
  );
};

export default QRScanner;
