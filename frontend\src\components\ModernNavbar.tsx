import React, { useState, useEffect } from 'react';
import { Link, useLocation } from 'react-router-dom';
import { useBranding } from '../hooks/useBranding';
import { getMediaUrl } from '../services/api';

const ModernNavbar: React.FC = () => {
  const location = useLocation();
  const { organization } = useBranding();
  const [isScrolled, setIsScrolled] = useState(false);
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const isHomePage = location.pathname === '/';

  useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 50);
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  const handleSectionClick = (sectionId: string) => {
    if (isHomePage) {
      const element = document.getElementById(sectionId);
      if (element) {
        element.scrollIntoView({ behavior: 'smooth' });
      }
    } else {
      // Navigate to home page with section hash
      window.location.href = `/#${sectionId}`;
    }
    setIsMobileMenuOpen(false);
  };

  const navItems = [
    { id: 'home', label: 'Home', icon: 'fas fa-home', path: '/' },
    { id: 'events', label: 'Events', icon: 'fas fa-calendar-alt', section: 'events'  },
    { id: 'gondar-city', label: 'Gondar City', icon: 'fas fa-city', section: 'gondar-city' },
    { id: 'university', label: 'University', icon: 'fas fa-university', section: 'university' },
    { id: 'gallery', label: 'Gallery', icon: 'fas fa-images', section: 'gallery' },
  ];

  return (
    <>
      <style>{`
        .modern-navbar {
          position: fixed;
          top: 0;
          left: 0;
          right: 0;
          z-index: 1000;
          transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
          backdrop-filter: blur(25px);
          -webkit-backdrop-filter: blur(25px);
          padding: 0.3rem 0;
        }

        .modern-navbar.scrolled {
          background: rgba(255, 255, 255, 0.98);
          box-shadow: 0 10px 40px rgba(0, 0, 0, 0.15);
          border-bottom: 3px solid #ffd700;
          padding: 0.2rem 0;
        }

        .modern-navbar.not-scrolled {
          background: rgba(255, 255, 255, 0.08);
          border-bottom: 3px solid rgba(255, 215, 0, 0.8);
          padding: 0.3rem 0;
          box-shadow: 0 8px 32px rgba(255, 215, 0, 0.3);
        }

        .navbar-brand-modern {
          display: flex;
          align-items: center;
          text-decoration: none;
          transition: all 0.3s ease;
          padding: 0.5rem 0;
        }

        .navbar-brand-modern:hover {
          transform: translateY(-2px);
          text-decoration: none;
        }

        .brand-logo-modern {
          height: 50px;
          border-radius: 12px;
          object-fit: contain;
          margin-right: 1rem;
          transition: all 0.3s ease;
        }

        .brand-text-modern {
          display: flex;
          flex-direction: column;
        }

        .brand-title-modern {
          font-size: 1.4rem;
          font-weight: 800;
          margin: 0;
          line-height: 1.2;
          color: #007bff;
          text-shadow: 1px 1px 3px rgba(0, 0, 0, 0.4);
          transition: all 0.4s ease;
        }

        .modern-navbar.scrolled .brand-title-modern {
          color: #b28705;
          text-shadow: 1px 1px 3px rgba(0, 0, 0, 0.2);
        }

        .brand-subtitle-modern {
          font-size: 0.75rem;
          color: #bf9107;
          margin: 0;
          line-height: 1;
          transition: all 0.4s ease;
          font-weight: 500;
        }

        .modern-navbar.scrolled .brand-subtitle-modern {
          color: #007bff;
          text-shadow: none;
        }

        .nav-menu-modern {
          display: flex;
          align-items: center;
          gap: 0.5rem;
          margin: 0;
          padding: 0;
          list-style: none;
        }

        .nav-item-modern {
          position: relative;
        }

        .nav-link-modern {
          display: flex;
          align-items: center;
          gap: 0.6rem;
          padding: 0.8rem 1.4rem;
          border-radius: 15px;
          text-decoration: none;
          color: #2c3e50;
          font-weight: 600;
          font-size: 0.95rem;
          transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
          position: relative;
          overflow: hidden;
          backdrop-filter: blur(10px);
          border: 1px solid rgba(44, 62, 80, 0.1);
        }

        .modern-navbar.scrolled .nav-link-modern {
          color: #495057;
          border-color: rgba(0, 0, 0, 0.1);
        }

        .nav-link-modern::before {
          content: '';
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          background: linear-gradient(135deg, rgba(102, 126, 234, 0.2), rgba(118, 75, 162, 0.2));
          opacity: 0;
          transition: all 0.4s ease;
          border-radius: 15px;
          transform: scale(0.8);
        }

        .nav-link-modern:hover::before,
        .nav-link-modern.active::before {
          opacity: 1;
          transform: scale(1);
        }

        .nav-link-modern:hover {
          color: #667eea;
          transform: translateY(-3px);
          text-decoration: none;
          box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
        }

        .modern-navbar.scrolled .nav-link-modern:hover {
          color: #667eea;
        }

        .nav-link-modern.active {
          color: #667eea;
          background: linear-gradient(135deg, rgba(102, 126, 234, 0.2), rgba(118, 75, 162, 0.2));
          box-shadow: 0 6px 20px rgba(102, 126, 234, 0.3);
        }

        .modern-navbar.scrolled .nav-link-modern.active {
          color: #667eea;
          background: linear-gradient(135deg, rgba(102, 126, 234, 0.2), rgba(118, 75, 162, 0.2));
        }

        .nav-icon-modern {
          font-size: 1rem;
          transition: all 0.3s ease;
        }

        .nav-link-modern:hover .nav-icon-modern {
          transform: scale(1.1);
        }

        .mobile-toggle-modern {
          display: none;
          flex-direction: column;
          gap: 4px;
          background: none;
          border: none;
          padding: 0.5rem;
          cursor: pointer;
          border-radius: 8px;
          transition: all 0.3s ease;
        }

        .mobile-toggle-modern:hover {
          background: rgba(0, 123, 255, 0.1);
        }

        .toggle-line {
          width: 25px;
          height: 3px;
          background: #495057;
          border-radius: 2px;
          transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .mobile-toggle-modern.active .toggle-line:nth-child(1) {
          transform: rotate(45deg) translate(6px, 6px);
        }

        .mobile-toggle-modern.active .toggle-line:nth-child(2) {
          opacity: 0;
        }

        .mobile-toggle-modern.active .toggle-line:nth-child(3) {
          transform: rotate(-45deg) translate(6px, -6px);
        }

        .mobile-menu-modern {
          position: absolute;
          top: 100%;
          left: 0;
          right: 0;
          background: rgba(255, 255, 255, 0.98);
          backdrop-filter: blur(20px);
          border-radius: 0 0 20px 20px;
          box-shadow: 0 10px 40px rgba(0, 0, 0, 0.1);
          padding: 1rem;
          transform: translateY(-20px);
          opacity: 0;
          visibility: hidden;
          transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .mobile-menu-modern.open {
          transform: translateY(0);
          opacity: 1;
          visibility: visible;
        }

        .mobile-nav-menu {
          display: flex;
          flex-direction: column;
          gap: 0.5rem;
        }

        @media (max-width: 768px) {
          .nav-menu-modern {
            display: none;
          }

          .mobile-toggle-modern {
            display: flex;
          }
        }

        @media (min-width: 769px) {
          .nav-menu-modern {
            display: flex !important;
          }

          .mobile-toggle-modern {
            display: none !important;
          }
        }

          .brand-title-modern {
            font-size: 1.2rem;
          }

          .brand-subtitle-modern {
            font-size: 0.7rem;
          }
        }

        .cta-button-modern {
          background: linear-gradient(135deg, #3498db, #e74c3c);
          color: white;
          padding: 0.8rem 1.8rem;
          border-radius: 25px;
          text-decoration: none;
          font-weight: 700;
          font-size: 0.95rem;
          transition: all 0.4s ease;
          box-shadow: 0 6px 20px rgba(52, 152, 219, 0.4);
          margin-left: 1.5rem;
          position: relative;
          overflow: hidden;
          border: 2px solid rgba(255, 255, 255, 0.2);
        }

        .cta-button-modern::before {
          content: '';
          position: absolute;
          top: 0;
          left: -100%;
          width: 100%;
          height: 100%;
          background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
          transition: left 0.6s ease;
        }

        .cta-button-modern:hover::before {
          left: 100%;
        }

        .cta-button-modern:hover {
          transform: translateY(-3px) scale(1.05);
          box-shadow: 0 12px 35px rgba(52, 152, 219, 0.6);
          color: white;
          text-decoration: none;
        }
      `}</style>

      <nav className={`modern-navbar ${isScrolled ? 'scrolled' : 'not-scrolled'}`}>
        <div className="container">
          <div className="d-flex align-items-center justify-content-between py-2">
            {/* Brand */}
            <Link to="/" className="navbar-brand-modern">
              {organization?.logo ? (
                <img
                  src={getMediaUrl(organization.logo)}
                  alt={organization.name}
                  className="brand-logo-modern"
                />
              ) : (
                <div className="brand-logo-modern bg-primary d-flex align-items-center justify-content-center">
                  <i className="fas fa-university text-white" style={{ fontSize: '1.5rem' }}></i>
                </div>
              )}
              <div className="brand-text-modern">
                <h1 className="brand-title-modern">
                  {organization?.short_name || 'UoG'} Events
                </h1>
                <p className="brand-subtitle-modern">
                  {organization?.name || 'University of Gondar'}
                </p>
              </div>
            </Link>

            {/* Desktop Navigation */}
            <ul className="nav-menu-modern">
              {navItems.map((item) => (
                <li key={item.id} className="nav-item-modern">
                  {item.path ? (
                    <Link
                      to={item.path}
                      className={`nav-link-modern ${location.pathname === item.path ? 'active' : ''}`}
                    >
                      <i className={`${item.icon} nav-icon-modern`}></i>
                      <span>{item.label}</span>
                    </Link>
                  ) : (
                    <button
                      onClick={() => handleSectionClick(item.section!)}
                      className="nav-link-modern"
                      style={{ background: 'none', border: 'none' }}
                    >
                      <i className={`${item.icon} nav-icon-modern`}></i>
                      <span>{item.label}</span>
                    </button>
                  )}
                </li>
              ))}
            </ul>



            {/* Mobile Toggle */}
            <button
              className={`mobile-toggle-modern ${isMobileMenuOpen ? 'active' : ''}`}
              onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
            >
              <div className="toggle-line"></div>
              <div className="toggle-line"></div>
              <div className="toggle-line"></div>
            </button>
          </div>

          {/* Mobile Menu */}
          <div className={`mobile-menu-modern ${isMobileMenuOpen ? 'open' : ''}`}>
            <ul className="mobile-nav-menu">
              {navItems.map((item) => (
                <li key={item.id}>
                  {item.path ? (
                    <Link
                      to={item.path}
                      className={`nav-link-modern ${location.pathname === item.path ? 'active' : ''}`}
                      onClick={() => setIsMobileMenuOpen(false)}
                    >
                      <i className={`${item.icon} nav-icon-modern`}></i>
                      <span>{item.label}</span>
                    </Link>
                  ) : (
                    <button
                      onClick={() => handleSectionClick(item.section!)}
                      className="nav-link-modern w-100 text-start"
                      style={{ background: 'none', border: 'none' }}
                    >
                      <i className={`${item.icon} nav-icon-modern`}></i>
                      <span>{item.label}</span>
                    </button>
                  )}
                </li>
              ))}

            </ul>
          </div>
        </div>
      </nav>
    </>
  );
};

export default ModernNavbar;
