# Test External Access for Let's Encrypt
# University of Gondar Event Management System

Write-Host "Testing External Access for Let's Encrypt SSL" -ForegroundColor Blue
Write-Host "=============================================" -ForegroundColor Blue

$DOMAIN = "event.uog.edu.et"
$PUBLIC_IP = "*************"

Write-Host "Configuration:" -ForegroundColor Yellow
Write-Host "  Domain: $DOMAIN" -ForegroundColor White
Write-Host "  Public IP: $PUBLIC_IP" -ForegroundColor White
Write-Host "  Internal IP: ************" -ForegroundColor White

Write-Host "`nTesting DNS Resolution..." -ForegroundColor Yellow

# Test external DNS
try {
    $dnsResult = Resolve-DnsName -Name $DOMAIN -Server ******* -ErrorAction Stop
    $externalIP = ($dnsResult | Where-Object {$_.Type -eq "A"}).IPAddress
    
    if ($externalIP -eq $PUBLIC_IP) {
        Write-Host "SUCCESS: External DNS resolves to $externalIP" -ForegroundColor Green
    } else {
        Write-Host "WARNING: External DNS resolves to $externalIP (expected $PUBLIC_IP)" -ForegroundColor Yellow
    }
} catch {
    Write-Host "ERROR: External DNS resolution failed" -ForegroundColor Red
    exit 1
}

# Test internal DNS
try {
    $internalResult = Resolve-DnsName -Name $DOMAIN -ErrorAction Stop
    $internalIP = ($internalResult | Where-Object {$_.Type -eq "A"}).IPAddress
    
    if ($internalIP -eq "************") {
        Write-Host "SUCCESS: Internal DNS resolves to $internalIP" -ForegroundColor Green
    } else {
        Write-Host "WARNING: Internal DNS resolves to $internalIP (expected ************)" -ForegroundColor Yellow
    }
} catch {
    Write-Host "ERROR: Internal DNS resolution failed" -ForegroundColor Red
}

Write-Host "`nTesting Local Application..." -ForegroundColor Yellow

# Test local HTTP access
try {
    $response = Invoke-WebRequest -Uri "http://localhost" -Method Head -TimeoutSec 10 -ErrorAction Stop
    Write-Host "SUCCESS: Local application is accessible" -ForegroundColor Green
} catch {
    Write-Host "ERROR: Local application is not accessible" -ForegroundColor Red
    Write-Host "Please ensure Docker containers are running" -ForegroundColor Red
    exit 1
}

Write-Host "`nTesting External Connectivity..." -ForegroundColor Yellow
Write-Host "NOTE: This test will fail until firewall is configured" -ForegroundColor Cyan

# Test external port 80 access
Write-Host "Testing port 80 accessibility from internet..." -ForegroundColor Yellow

# Try to connect to external IP (this will likely fail from internal network)
try {
    $portTest = Test-NetConnection -ComputerName $DOMAIN -Port 80 -WarningAction SilentlyContinue
    
    if ($portTest.TcpTestSucceeded) {
        Write-Host "SUCCESS: Port 80 is accessible from external network!" -ForegroundColor Green
        Write-Host "Your firewall is properly configured for Let's Encrypt" -ForegroundColor Green
        
        Write-Host "`nReady for SSL setup! Run:" -ForegroundColor Green
        Write-Host ".\setup-ssl-direct.ps1" -ForegroundColor Cyan
        
    } else {
        Write-Host "EXPECTED: Port 80 is not accessible from internal network" -ForegroundColor Yellow
        Write-Host "This is normal - external firewall configuration needed" -ForegroundColor Yellow
    }
} catch {
    Write-Host "EXPECTED: Cannot test external connectivity from internal network" -ForegroundColor Yellow
}

Write-Host "`nFirewall Configuration Required:" -ForegroundColor Yellow
Write-Host "=================================" -ForegroundColor Yellow
Write-Host "1. Configure external firewall to allow:" -ForegroundColor White
Write-Host "   - Inbound TCP port 80 from any source" -ForegroundColor White
Write-Host "   - Inbound TCP port 443 from any source" -ForegroundColor White
Write-Host ""
Write-Host "2. Configure port forwarding:" -ForegroundColor White
Write-Host "   - External port 80 -> Internal ************:80" -ForegroundColor White
Write-Host "   - External port 443 -> Internal ************:443" -ForegroundColor White

Write-Host "`nExternal Testing Required:" -ForegroundColor Yellow
Write-Host "=========================" -ForegroundColor Yellow
Write-Host "Ask someone OUTSIDE your network to test:" -ForegroundColor White
Write-Host "  telnet $DOMAIN 80" -ForegroundColor Cyan
Write-Host ""
Write-Host "Or use online port checker:" -ForegroundColor White
Write-Host "  https://www.yougetsignal.com/tools/open-ports/" -ForegroundColor Cyan
Write-Host "  Check if port 80 is open on $DOMAIN" -ForegroundColor White

Write-Host "`nOnce External Access Works:" -ForegroundColor Yellow
Write-Host "===========================" -ForegroundColor Yellow
Write-Host "Run the SSL setup script:" -ForegroundColor White
Write-Host "  .\setup-ssl-direct.ps1" -ForegroundColor Cyan

Write-Host "`nCurrent Status Summary:" -ForegroundColor Blue
Write-Host "======================" -ForegroundColor Blue
Write-Host "DNS Configuration: READY" -ForegroundColor Green
Write-Host "Application Status: READY" -ForegroundColor Green
Write-Host "Windows Firewall: READY" -ForegroundColor Green
Write-Host "External Firewall: NEEDS CONFIGURATION" -ForegroundColor Red

Write-Host "`nNext Step: Configure external firewall and port forwarding" -ForegroundColor Yellow
