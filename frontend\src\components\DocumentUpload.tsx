import React, { useState, useCallback } from 'react';
import { Card, Button, Alert, Form, Modal, Badge, ProgressBar, Row, Col } from 'react-bootstrap';
import { useDropzone } from 'react-dropzone';
import { documentService, EventDocument } from '../services/api';

interface UploadFile extends File {
  id: string;
  preview?: string;
  progress: number;
  status: 'pending' | 'uploading' | 'success' | 'error';
  error?: string;
  title?: string;
  description?: string;
  document_type?: string;
}

interface DocumentUploadProps {
  eventId: number;
  onUploadComplete?: (documents: EventDocument[]) => void;
  onError?: (error: string) => void;
}

const DocumentUpload: React.FC<DocumentUploadProps> = ({
  eventId,
  onUploadComplete,
  onError
}) => {
  const [files, setFiles] = useState<UploadFile[]>([]);
  const [uploading, setUploading] = useState(false);
  const [showModal, setShowModal] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');

  const documentTypes = [
    { value: 'presentation', label: 'Presentation' },
    { value: 'schedule', label: 'Schedule' },
    { value: 'agenda', label: 'Agenda' },
    { value: 'brochure', label: 'Brochure' },
    { value: 'certificate', label: 'Certificate' },
    { value: 'report', label: 'Report' },
    { value: 'other', label: 'Other' },
  ];

  const onDrop = useCallback((acceptedFiles: File[]) => {
    const newFiles: UploadFile[] = acceptedFiles.map(file => ({
      ...file,
      id: Math.random().toString(36).substr(2, 9),
      progress: 0,
      status: 'pending',
      title: file.name.split('.')[0],
      description: '',
      document_type: 'other'
    }));

    setFiles(prev => [...prev, ...newFiles]);
    setError('');
  }, []);

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: {
      'application/pdf': ['.pdf'],
      'application/msword': ['.doc'],
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document': ['.docx'],
      'application/vnd.ms-powerpoint': ['.ppt'],
      'application/vnd.openxmlformats-officedocument.presentationml.presentation': ['.pptx'],
      'application/vnd.ms-excel': ['.xls'],
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet': ['.xlsx'],
      'text/plain': ['.txt']
    },
    maxSize: 10 * 1024 * 1024, // 10MB
    multiple: true
  });

  const removeFile = (fileId: string) => {
    setFiles(prev => prev.filter(f => f.id !== fileId));
  };

  const updateFileMetadata = (fileId: string, field: string, value: string) => {
    setFiles(prev => prev.map(f => 
      f.id === fileId ? { ...f, [field]: value } : f
    ));
  };

  const uploadFiles = async () => {
    if (files.length === 0) {
      setError('Please select files to upload');
      return;
    }

    setUploading(true);
    setError('');
    setSuccess('');

    const pendingFiles = files.filter(f => f.status === 'pending');
    const uploadedDocuments: EventDocument[] = [];
    
    for (const file of pendingFiles) {
      try {
        // Update file status to uploading
        setFiles(prev => prev.map(f => 
          f.id === file.id ? { ...f, status: 'uploading', progress: 0 } : f
        ));

        const formData = new FormData();
        formData.append('event', eventId.toString());
        formData.append('title', file.title || file.name);
        formData.append('description', file.description || '');
        formData.append('document_type', file.document_type || 'other');
        formData.append('file', file);

        const response = await documentService.createDocument(formData);
        
        // Update file status to success
        setFiles(prev => prev.map(f => 
          f.id === file.id ? { ...f, status: 'success', progress: 100 } : f
        ));

        uploadedDocuments.push(response.data);
      } catch (error: any) {
        console.error('Error uploading file:', error);
        const errorMessage = error.response?.data?.error || `Failed to upload ${file.name}`;
        
        // Update file status to error
        setFiles(prev => prev.map(f => 
          f.id === file.id ? { ...f, status: 'error', error: errorMessage } : f
        ));
      }
    }

    setUploading(false);

    if (uploadedDocuments.length > 0) {
      setSuccess(`Successfully uploaded ${uploadedDocuments.length} document(s)`);
      if (onUploadComplete) {
        onUploadComplete(uploadedDocuments);
      }
      // Clear successful uploads
      setFiles(prev => prev.filter(f => f.status !== 'success'));
    }

    const failedUploads = files.filter(f => f.status === 'error');
    if (failedUploads.length > 0) {
      const errorMsg = `Failed to upload ${failedUploads.length} document(s)`;
      setError(errorMsg);
      if (onError) {
        onError(errorMsg);
      }
    }
  };

  const getFileIcon = (fileName: string) => {
    const extension = fileName.split('.').pop()?.toLowerCase();
    switch (extension) {
      case 'pdf': return 'fas fa-file-pdf text-danger';
      case 'doc':
      case 'docx': return 'fas fa-file-word text-primary';
      case 'ppt':
      case 'pptx': return 'fas fa-file-powerpoint text-warning';
      case 'xls':
      case 'xlsx': return 'fas fa-file-excel text-success';
      case 'txt': return 'fas fa-file-alt text-secondary';
      default: return 'fas fa-file text-muted';
    }
  };

  return (
    <>
      <Card className="mb-4">
        <Card.Header>
          <h5 className="mb-0">
            <i className="fas fa-upload me-2"></i>
            Upload Documents
          </h5>
        </Card.Header>
        <Card.Body>
          {error && <Alert variant="danger">{error}</Alert>}
          {success && <Alert variant="success">{success}</Alert>}

          {/* Dropzone */}
          <div
            {...getRootProps()}
            className={`border-2 border-dashed rounded p-4 text-center mb-4 ${
              isDragActive ? 'border-primary bg-light' : 'border-secondary'
            }`}
            style={{ cursor: 'pointer' }}
          >
            <input {...getInputProps()} />
            <i className="fas fa-cloud-upload-alt fa-3x text-muted mb-3"></i>
            <h5>
              {isDragActive
                ? 'Drop the files here...'
                : 'Drag & drop documents here, or click to select'}
            </h5>
            <p className="text-muted">
              Supports: PDF, DOC, DOCX, PPT, PPTX, XLS, XLSX, TXT (Max 10MB each)
            </p>
          </div>

          {/* File List */}
          {files.length > 0 && (
            <div className="mb-4">
              <h6 className="mb-3">Files to Upload ({files.length})</h6>
              {files.map((file) => (
                <Card key={file.id} className="mb-3">
                  <Card.Body>
                    <Row className="align-items-center">
                      <Col md={1} className="text-center">
                        <i className={getFileIcon(file.name)}></i>
                      </Col>
                      <Col md={3}>
                        <h6 className="mb-1">{file.name}</h6>
                        <small className="text-muted">
                          {(file.size / 1024 / 1024).toFixed(2)} MB
                        </small>
                        {file.status === 'uploading' && (
                          <ProgressBar
                            now={file.progress}
                            className="mt-1"
                            style={{ height: '4px' }}
                          />
                        )}
                        {file.status === 'success' && (
                          <Badge bg="success" className="mt-1">Uploaded</Badge>
                        )}
                        {file.status === 'error' && (
                          <Badge bg="danger" className="mt-1">Failed</Badge>
                        )}
                      </Col>
                      <Col md={3}>
                        <Form.Group>
                          <Form.Label className="small">Title</Form.Label>
                          <Form.Control
                            size="sm"
                            value={file.title || ''}
                            onChange={(e) => updateFileMetadata(file.id, 'title', e.target.value)}
                            disabled={file.status !== 'pending'}
                          />
                        </Form.Group>
                      </Col>
                      <Col md={3}>
                        <Form.Group>
                          <Form.Label className="small">Type</Form.Label>
                          <Form.Select
                            size="sm"
                            value={file.document_type || 'other'}
                            onChange={(e) => updateFileMetadata(file.id, 'document_type', e.target.value)}
                            disabled={file.status !== 'pending'}
                          >
                            {documentTypes.map(type => (
                              <option key={type.value} value={type.value}>
                                {type.label}
                              </option>
                            ))}
                          </Form.Select>
                        </Form.Group>
                      </Col>
                      <Col md={2} className="text-end">
                        {file.status === 'pending' && (
                          <Button
                            variant="outline-danger"
                            size="sm"
                            onClick={() => removeFile(file.id)}
                          >
                            <i className="fas fa-trash"></i>
                          </Button>
                        )}
                      </Col>
                    </Row>
                    {file.error && (
                      <Alert variant="danger" className="mt-2 mb-0">
                        {file.error}
                      </Alert>
                    )}
                  </Card.Body>
                </Card>
              ))}
            </div>
          )}

          {/* Upload Button */}
          {files.length > 0 && (
            <div className="text-end">
              <Button
                variant="primary"
                onClick={uploadFiles}
                disabled={uploading || files.filter(f => f.status === 'pending').length === 0}
              >
                {uploading ? (
                  <>
                    <i className="fas fa-spinner fa-spin me-2"></i>
                    Uploading...
                  </>
                ) : (
                  <>
                    <i className="fas fa-upload me-2"></i>
                    Upload Documents ({files.filter(f => f.status === 'pending').length})
                  </>
                )}
              </Button>
            </div>
          )}
        </Card.Body>
      </Card>
    </>
  );
};

export default DocumentUpload;
