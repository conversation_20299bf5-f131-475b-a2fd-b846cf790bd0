import React, { useState, useEffect } from 'react';
import { Container, Row, Col, Card, Form, Button, Alert, Spinner } from 'react-bootstrap';
import { useNavigate, useParams } from 'react-router-dom';
import { hotelService, eventService, Event } from '../services/api';

const HotelForm: React.FC = () => {
  const navigate = useNavigate();
  const { id } = useParams<{ id: string }>();
  const isEdit = Boolean(id);

  const [loading, setLoading] = useState(false);
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [events, setEvents] = useState<Event[]>([]);

  const [formData, setFormData] = useState({
    name: '',
    address: '',
    phone: '',
    email: '',
    contact_person: '',
    star_rating: '',
    website: '',
    description: '',
    latitude: '',
    longitude: '',
    event: '',
    is_active: true
  });

  useEffect(() => {
    fetchEvents();
    if (isEdit && id) {
      fetchHotel(parseInt(id));
    }
  }, [isEdit, id]);

  const fetchEvents = async () => {
    try {
      const response = await eventService.getEvents();
      const eventsData = Array.isArray(response.data) ? response.data : 
                        ((response.data as any)?.results ? (response.data as any).results : []);
      setEvents(eventsData);
    } catch (err) {
      console.error('Error fetching events:', err);
      setError('Failed to load events. Please try again.');
    }
  };

  const fetchHotel = async (hotelId: number) => {
    try {
      setLoading(true);
      const response = await hotelService.getHotel(hotelId);
      const hotel = response.data;
      
      setFormData({
        name: hotel.name,
        address: hotel.address,
        phone: hotel.phone,
        email: hotel.email,
        contact_person: hotel.contact_person,
        star_rating: hotel.star_rating?.toString() || '',
        website: hotel.website || '',
        description: hotel.description || '',
        latitude: hotel.latitude?.toString() || '',
        longitude: hotel.longitude?.toString() || '',
        event: hotel.event.toString(),
        is_active: hotel.is_active
      });
    } catch (err) {
      console.error('Error fetching hotel:', err);
      setError('Failed to load hotel data. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value, type } = e.target;
    
    if (type === 'checkbox') {
      const checked = (e.target as HTMLInputElement).checked;
      setFormData(prev => ({ ...prev, [name]: checked }));
    } else {
      setFormData(prev => ({ ...prev, [name]: value }));
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!formData.name || !formData.address || !formData.phone || !formData.email || !formData.contact_person || !formData.event) {
      setError('Please fill in all required fields.');
      return;
    }

    try {
      setSaving(true);
      setError(null);

      const submitData = {
        name: formData.name,
        address: formData.address,
        phone: formData.phone,
        email: formData.email,
        contact_person: formData.contact_person,
        star_rating: formData.star_rating ? parseInt(formData.star_rating) : undefined,
        website: formData.website,
        description: formData.description,
        latitude: formData.latitude ? parseFloat(formData.latitude) : undefined,
        longitude: formData.longitude ? parseFloat(formData.longitude) : undefined,
        event: parseInt(formData.event),
        is_active: formData.is_active
      };

      if (isEdit && id) {
        await hotelService.updateHotel(parseInt(id), submitData);
      } else {
        await hotelService.createHotel(submitData);
      }

      navigate('/hotels');
    } catch (err: any) {
      console.error('Error saving hotel:', err);
      if (err.response?.data) {
        const errorData = err.response.data;
        if (typeof errorData === 'object') {
          const errorMessages = Object.entries(errorData)
            .map(([field, messages]) => `${field}: ${Array.isArray(messages) ? messages.join(', ') : messages}`)
            .join('\n');
          setError(errorMessages);
        } else {
          setError('Failed to save hotel. Please check your input and try again.');
        }
      } else {
        setError('Failed to save hotel. Please try again.');
      }
    } finally {
      setSaving(false);
    }
  };

  if (loading) {
    return (
      <Container className="text-center mt-5">
        <Spinner animation="border" role="status">
          <span className="visually-hidden">Loading...</span>
        </Spinner>
        <p className="mt-3">Loading hotel data...</p>
      </Container>
    );
  }

  return (
    <Container className="py-4">
      <Row className="justify-content-center">
        <Col lg={8}>
          <Card className="shadow">
            <Card.Header className="bg-primary text-white">
              <h4 className="mb-0">
                <i className="fas fa-hotel me-2"></i>
                {isEdit ? 'Edit Hotel' : 'Add New Hotel'}
              </h4>
            </Card.Header>
            <Card.Body>
              {error && (
                <Alert variant="danger" className="mb-4">
                  <i className="fas fa-exclamation-triangle me-2"></i>
                  <pre style={{ whiteSpace: 'pre-wrap', margin: 0 }}>{error}</pre>
                </Alert>
              )}

              <Form onSubmit={handleSubmit}>
                <Row>
                  <Col md={6}>
                    <h5 className="text-primary mb-3">
                      <i className="fas fa-info-circle me-2"></i>
                      Basic Information
                    </h5>
                    
                    <Form.Group className="mb-3">
                      <Form.Label>Hotel Name *</Form.Label>
                      <Form.Control
                        type="text"
                        name="name"
                        value={formData.name}
                        onChange={handleInputChange}
                        required
                        placeholder="Enter hotel name"
                      />
                    </Form.Group>

                    <Form.Group className="mb-3">
                      <Form.Label>Address *</Form.Label>
                      <Form.Control
                        as="textarea"
                        rows={3}
                        name="address"
                        value={formData.address}
                        onChange={handleInputChange}
                        required
                        placeholder="Enter complete hotel address"
                      />
                    </Form.Group>

                    <Form.Group className="mb-3">
                      <Form.Label>Phone Number *</Form.Label>
                      <Form.Control
                        type="tel"
                        name="phone"
                        value={formData.phone}
                        onChange={handleInputChange}
                        required
                        placeholder="+92-53-1234567"
                      />
                    </Form.Group>

                    <Form.Group className="mb-3">
                      <Form.Label>Email Address *</Form.Label>
                      <Form.Control
                        type="email"
                        name="email"
                        value={formData.email}
                        onChange={handleInputChange}
                        required
                        placeholder="<EMAIL>"
                      />
                    </Form.Group>

                    <Form.Group className="mb-3">
                      <Form.Label>Contact Person *</Form.Label>
                      <Form.Control
                        type="text"
                        name="contact_person"
                        value={formData.contact_person}
                        onChange={handleInputChange}
                        required
                        placeholder="Manager or contact person name"
                      />
                    </Form.Group>
                  </Col>

                  <Col md={6}>
                    <h5 className="text-primary mb-3">
                      <i className="fas fa-star me-2"></i>
                      Hotel Details
                    </h5>

                    <Form.Group className="mb-3">
                      <Form.Label>Star Rating</Form.Label>
                      <Form.Select
                        name="star_rating"
                        value={formData.star_rating}
                        onChange={handleInputChange}
                      >
                        <option value="">Select rating</option>
                        <option value="1">1 Star</option>
                        <option value="2">2 Stars</option>
                        <option value="3">3 Stars</option>
                        <option value="4">4 Stars</option>
                        <option value="5">5 Stars</option>
                      </Form.Select>
                    </Form.Group>

                    <Form.Group className="mb-3">
                      <Form.Label>Website</Form.Label>
                      <Form.Control
                        type="url"
                        name="website"
                        value={formData.website}
                        onChange={handleInputChange}
                        placeholder="https://hotel-website.com"
                      />
                    </Form.Group>

                    <Form.Group className="mb-3">
                      <Form.Label>Event *</Form.Label>
                      <Form.Select
                        name="event"
                        value={formData.event}
                        onChange={handleInputChange}
                        required
                      >
                        <option value="">Select an event</option>
                        {events.map(event => (
                          <option key={event.id} value={event.id}>
                            {event.name}
                          </option>
                        ))}
                      </Form.Select>
                    </Form.Group>

                    <Form.Group className="mb-3">
                      <Form.Check
                        type="checkbox"
                        name="is_active"
                        checked={formData.is_active}
                        onChange={handleInputChange}
                        label="Hotel is active and available"
                      />
                    </Form.Group>

                    <h6 className="text-secondary mb-3">
                      <i className="fas fa-map-marker-alt me-2"></i>
                      Location (Optional)
                    </h6>

                    <Row>
                      <Col md={6}>
                        <Form.Group className="mb-3">
                          <Form.Label>Latitude</Form.Label>
                          <Form.Control
                            type="number"
                            step="any"
                            name="latitude"
                            value={formData.latitude}
                            onChange={handleInputChange}
                            placeholder="32.5734"
                          />
                        </Form.Group>
                      </Col>
                      <Col md={6}>
                        <Form.Group className="mb-3">
                          <Form.Label>Longitude</Form.Label>
                          <Form.Control
                            type="number"
                            step="any"
                            name="longitude"
                            value={formData.longitude}
                            onChange={handleInputChange}
                            placeholder="74.0788"
                          />
                        </Form.Group>
                      </Col>
                    </Row>
                  </Col>
                </Row>

                <Form.Group className="mb-4">
                  <Form.Label>Description</Form.Label>
                  <Form.Control
                    as="textarea"
                    rows={4}
                    name="description"
                    value={formData.description}
                    onChange={handleInputChange}
                    placeholder="Describe the hotel facilities, amenities, and other details..."
                  />
                </Form.Group>

                <div className="d-flex justify-content-between">
                  <Button 
                    variant="secondary" 
                    onClick={() => navigate('/hotels')}
                    disabled={saving}
                  >
                    <i className="fas fa-arrow-left me-2"></i>
                    Cancel
                  </Button>
                  <Button 
                    type="submit" 
                    variant="primary"
                    disabled={saving}
                  >
                    {saving ? (
                      <>
                        <Spinner animation="border" size="sm" className="me-2" />
                        {isEdit ? 'Updating...' : 'Creating...'}
                      </>
                    ) : (
                      <>
                        <i className="fas fa-save me-2"></i>
                        {isEdit ? 'Update Hotel' : 'Create Hotel'}
                      </>
                    )}
                  </Button>
                </div>
              </Form>
            </Card.Body>
          </Card>
        </Col>
      </Row>
    </Container>
  );
};

export default HotelForm;
