import React, { useEffect } from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate, useParams, useLocation } from 'react-router-dom';
import 'bootstrap/dist/css/bootstrap.min.css';
import './App.css';
import './styles/event-pages.css';

// Context
import { AuthProvider } from './contexts/AuthContext';
import { ToastProvider } from './contexts/ToastContext';

// Components
import ToastContainer from './components/ToastContainer';
import ProtectedRoute from './components/ProtectedRoute';
import Navbar from './components/Navbar';
import ScrollToTop from './components/ScrollToTop';
import NewHome from './pages/NewHome';
import Login from './pages/Login';
import Dashboard from './pages/Dashboard';

import Register from './pages/Register';
import AdminDashboard from './pages/AdminDashboard';
import EventList from './pages/EventList';
import EventDetail from './pages/EventDetail';
import PublicEventDetail from './pages/PublicEventDetail';
import EventForm from './pages/EventForm';
import OrganizationList from './pages/OrganizationList';
import OrganizationForm from './pages/OrganizationForm';
import OrganizationDetail from './pages/OrganizationDetail';
import OrganizationSettingsManagement from './pages/OrganizationSettingsManagement';
import ParticipantRegistration from './pages/ParticipantRegistration';
import ParticipantList from './pages/ParticipantList';
import ParticipantManagement from './pages/ParticipantManagement';
import ParticipantEdit from './pages/ParticipantEdit';
import EventSchedule from './pages/EventSchedule';
import EventScheduleManagement from './pages/EventScheduleManagement';
import EventGallery from './pages/EventGallery';
import QRScanner from './pages/QRScanner';
import QRCodeInfo from './pages/QRCodeInfo';
import MobileQRLanding from './pages/MobileQRLanding';
import ParticipantVerify from './pages/ParticipantVerify';
import HotelList from './pages/HotelList';
import HotelDetail from './pages/HotelDetail';
import HotelForm from './pages/HotelForm';
import DriverList from './pages/DriverList';
import DriverDetail from './pages/DriverDetail';
import DriverForm from './pages/DriverForm';
import ParticipantTypeList from './pages/ParticipantTypeList';
import EmailManagement from './pages/EmailManagement';
import EventGalleryManagement from './pages/EventGalleryManagement';
import GalleryCategoryManagement from './pages/GalleryCategoryManagement';
import GalleryImageManagement from './pages/GalleryImageManagement';
import OrganizationSettings from './pages/OrganizationSettings';
import AttendanceManagement from './pages/AttendanceManagement';
import BulkEmailAnnouncement from './pages/BulkEmailAnnouncement';
import GalleryDemo from './pages/GalleryDemo';
import TestRoute from './pages/TestRoute';
import EventFeedback from './pages/EventFeedback';
import FeedbackManagement from './pages/FeedbackManagement';
import InlineFeedbackPage from './pages/InlineFeedbackPage';

import ContactPersonList from './pages/ContactPersonList';
import ContactPersonDetail from './pages/ContactPersonDetail';

// Redirect components for schedule and gallery pages
const ScheduleRedirect: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  return <Navigate to={`/events/${id}?tab=schedule`} replace />;
};

const GalleryRedirect: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  return <Navigate to={`/events/${id}?tab=gallery`} replace />;
};

// Component to scroll to top on route change
const ScrollToTopOnRouteChange: React.FC = () => {
  const location = useLocation();

  useEffect(() => {
    window.scrollTo({
      top: 0,
      behavior: 'smooth'
    });
  }, [location.pathname]);

  return null;
};

function App() {
  return (
    <AuthProvider>
      <ToastProvider>
        <Router>
          <div className="App bg-white">
            <ToastContainer />
            <ScrollToTop />
            <ScrollToTopOnRouteChange />
          <Routes>
            {/* Public Routes - Home without container */}
            <Route path="/" element={<NewHome />} />
            <Route path="/new-home" element={<NewHome />} />

            {/* Other public routes */}
            <Route path="/login" element={<Login />} />

            <Route path="/events/:id" element={<EventDetail />} />
            {/* Redirect schedule and gallery to main event page with tabs */}
            <Route path="/events/:id/schedule" element={<ScheduleRedirect />} />
            <Route path="/events/:id/gallery" element={<GalleryRedirect />} />
            <Route path="/events/new" element={
              <div className="container-fluid">
                <ProtectedRoute requireAdmin={true}>
                  <EventForm />
                </ProtectedRoute>
              </div>
            } />
            <Route path="/events/:id/edit" element={
              <div className="container-fluid">
                <ProtectedRoute requireAdmin={true}>
                  <EventForm />
                </ProtectedRoute>
              </div>
            } />
            <Route path="/register" element={<Register />} />
            <Route path="/participant-register" element={<ParticipantRegistration />} />
            <Route path="/verify/:uuid" element={<ParticipantVerify />} />

              {/* Protected Routes */}
              <Route path="/dashboard" element={
                <>
                  <Navbar />
                  <div className="container-fluid">
                    <ProtectedRoute requireAdmin={true}>
                      <AdminDashboard />
                    </ProtectedRoute>
                  </div>
                </>
              } />

              <Route path="/admin" element={
                <>
                  <Navbar />
                  <div className="container-fluid">
                    <ProtectedRoute requireAdmin={true}>
                      <AdminDashboard />
                    </ProtectedRoute>
                  </div>
                </>
              } />

              <Route path="/admin/events" element={
                <>
                  <Navbar />
                  <div className="container-fluid">
                    <ProtectedRoute requireAdmin={true}>
                      <EventList />
                    </ProtectedRoute>
                  </div>
                </>
              } />

              <Route path="/admin/events/new" element={
                <>
                  <Navbar />
                  <div className="container-fluid">
                    <ProtectedRoute requireAdmin={true}>
                      <EventForm />
                    </ProtectedRoute>
                  </div>
                </>
              } />

              <Route path="/admin/events/:id" element={
                <>
                  <Navbar />
                  <div className="container-fluid">
                    <ProtectedRoute requireAdmin={true}>
                      <EventDetail />
                    </ProtectedRoute>
                  </div>
                </>
              } />
              <Route path="/admin/events/:id/edit" element={
                <>
                  <Navbar />
                  <div className="container-fluid">
                    <ProtectedRoute requireAdmin={true}>
                      <EventForm />
                    </ProtectedRoute>
                  </div>
                </>
              } />
              <Route path="/schedule-management" element={
                <>
                  <Navbar />
                  <div className="container-fluid">
                    <ProtectedRoute requireAdmin={true}>
                      <EventScheduleManagement />
                    </ProtectedRoute>
                  </div>
                </>
              } />
              <Route path="/gallery-demo" element={<div className="container-fluid"><GalleryDemo /></div>} />

              <Route path="/contact-persons" element={
                <>
                  <Navbar />
                  <div className="container-fluid"><ContactPersonList /></div>
                </>
              } />
              <Route path="/contact-persons/:id" element={
                <>
                  <Navbar />
                  <div className="container-fluid"><ContactPersonDetail /></div>
                </>
              } />

              <Route path="/participants" element={
                <>
                  <Navbar />
                  <div className="container-fluid">
                    <ProtectedRoute requireAdmin={true}>
                      <ParticipantList />
                    </ProtectedRoute>
                  </div>
                </>
              } />

              <Route path="/participants/manage" element={
                <>
                  <Navbar />
                  <div className="container-fluid">
                    <ProtectedRoute requireAdmin={true}>
                      <ParticipantManagement />
                    </ProtectedRoute>
                  </div>
                </>
              } />
              <Route path="/participants/:id/edit" element={
                <>
                  <Navbar />
                  <div className="container-fluid">
                    <ProtectedRoute requireAdmin={true}>
                      <ParticipantEdit />
                    </ProtectedRoute>
                  </div>
                </>
              } />

              <Route path="/scan" element={
                <>
                  <Navbar />
                  <div className="container-fluid">
                    <ProtectedRoute requireAdmin={true}>
                      <QRScanner />
                    </ProtectedRoute>
                  </div>
                </>
              } />

              <Route path="/qr-info" element={
                <>
                  <Navbar />
                  <div className="container-fluid">
                    <ProtectedRoute requireAdmin={true}>
                      <QRCodeInfo />
                    </ProtectedRoute>
                  </div>
                </>
              } />

              {/* Public route for QR code URLs from mobile cameras */}
              <Route path="/events" element={<MobileQRLanding />} />

              {/* Organization Routes */}
              <Route path="/organizations" element={
                <>
                  <Navbar />
                  <div className="container-fluid">
                    <ProtectedRoute requireAdmin={true}>
                      <OrganizationList />
                    </ProtectedRoute>
                  </div>
                </>
              } />
              <Route path="/organizations/new" element={
                <>
                  <Navbar />
                  <div className="container-fluid">
                    <ProtectedRoute requireAdmin={true}>
                      <OrganizationForm />
                    </ProtectedRoute>
                  </div>
                </>
              } />
              <Route path="/organizations/:id" element={
                <>
                  <Navbar />
                  <div className="container-fluid">
                    <ProtectedRoute requireAdmin={true}>
                      <OrganizationDetail />
                    </ProtectedRoute>
                  </div>
                </>
              } />
              <Route path="/organizations/:id/edit" element={
                <>
                  <Navbar />
                  <div className="container-fluid">
                    <ProtectedRoute requireAdmin={true}>
                      <OrganizationForm />
                    </ProtectedRoute>
                  </div>
                </>
              } />

              <Route path="/organization-settings" element={
                <>
                  <Navbar />
                  <div className="container-fluid">
                    <ProtectedRoute requireAdmin={true}>
                      <OrganizationSettingsManagement />
                    </ProtectedRoute>
                  </div>
                </>
              } />

              {/* Hotel Management Routes */}
              <Route path="/hotels" element={
                <>
                  <Navbar />
                  <div className="container-fluid"><HotelList /></div>
                </>
              } />
              <Route path="/hotels/new" element={
                <>
                  <Navbar />
                  <div className="container-fluid"><HotelForm /></div>
                </>
              } />
              <Route path="/hotels/:id" element={
                <>
                  <Navbar />
                  <div className="container-fluid"><HotelDetail /></div>
                </>
              } />
              <Route path="/hotels/:id/edit" element={
                <>
                  <Navbar />
                  <div className="container-fluid"><HotelForm /></div>
                </>
              } />

              {/* Driver Management Routes */}
              <Route path="/drivers" element={
                <>
                  <Navbar />
                  <div className="container-fluid"><DriverList /></div>
                </>
              } />
              <Route path="/drivers/new" element={
                <>
                  <Navbar />
                  <div className="container-fluid"><DriverForm /></div>
                </>
              } />
              <Route path="/drivers/:id" element={
                <>
                  <Navbar />
                  <div className="container-fluid"><DriverDetail /></div>
                </>
              } />
              <Route path="/drivers/:id/edit" element={
                <>
                  <Navbar />
                  <div className="container-fluid"><DriverForm /></div>
                </>
              } />
              <Route path="/drivers/:id/assignments" element={
                <>
                  <Navbar />
                  <div className="container-fluid"><DriverDetail /></div>
                </>
              } />

              {/* Participant Type Management Routes */}
              <Route path="/participant-types" element={
                <>
                  <Navbar />
                  <div className="container-fluid"><ParticipantTypeList /></div>
                </>
              } />



              {/* Email Management Routes */}
              <Route path="/email-management" element={
                <>
                  <Navbar />
                  <div className="container-fluid">
                    <ProtectedRoute requireAdmin={true}>
                      <EmailManagement />
                    </ProtectedRoute>
                  </div>
                </>
              } />

              {/* Gallery Management Routes */}
              <Route path="/gallery-management" element={
                <>
                  <Navbar />
                  <div className="container-fluid">
                    <ProtectedRoute requireAdmin={true}>
                      <EventGalleryManagement />
                    </ProtectedRoute>
                  </div>
                </>
              } />

              <Route path="/test-route" element={<TestRoute />} />

              <Route path="/gallery-categories" element={
                <>
                  <Navbar />
                  <div className="container-fluid">
                    <ProtectedRoute requireAdmin={true}>
                      <GalleryCategoryManagement />
                    </ProtectedRoute>
                  </div>
                </>
              } />

              <Route path="/gallery-images" element={
                <>
                  <Navbar />
                  <div className="container-fluid">
                    <ProtectedRoute requireAdmin={true}>
                      <GalleryImageManagement />
                    </ProtectedRoute>
                  </div>
                </>
              } />

              {/* Feedback Routes */}
              <Route path="/events/:id/feedback" element={<InlineFeedbackPage />} />
              <Route path="/events/:id/feedback-form" element={<EventFeedback />} />
              <Route path="/feedback-management" element={
                <>
                  <Navbar />
                  <div className="container-fluid">
                    <ProtectedRoute requireAdmin={true}>
                      <FeedbackManagement />
                    </ProtectedRoute>
                  </div>
                </>
              } />

              <Route path="/organization-settings" element={
                <>
                  <Navbar />
                  <div className="container-fluid">
                    <ProtectedRoute requireAdmin={true}>
                      <OrganizationSettings />
                    </ProtectedRoute>
                  </div>
                </>
              } />

              {/* Attendance Management Routes */}
              <Route path="/attendance-management" element={
                <>
                  <Navbar />
                  <div className="container-fluid">
                    <ProtectedRoute requireAdmin={true}>
                      <AttendanceManagement />
                    </ProtectedRoute>
                  </div>
                </>
              } />

              {/* Bulk Email Announcement Routes */}
              <Route path="/bulk-email" element={
                <>
                  <Navbar />
                  <div className="container-fluid">
                    <ProtectedRoute requireAdmin={true}>
                      <BulkEmailAnnouncement />
                    </ProtectedRoute>
                  </div>
                </>
              } />
            </Routes>
          </div>
        </Router>
      </ToastProvider>
    </AuthProvider>
  );
}

export default App;
