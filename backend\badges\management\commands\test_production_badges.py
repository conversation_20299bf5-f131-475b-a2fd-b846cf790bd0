from django.core.management.base import BaseCommand
from badges.models import Badge
from participants.models import Participant
from django.db import connection


class Command(BaseCommand):
    help = 'Test the simplified SVG-style badge generation in production'

    def add_arguments(self, parser):
        parser.add_argument(
            '--participant-id',
            type=int,
            help='Specific participant ID to generate badge for',
        )
        parser.add_argument(
            '--count',
            type=int,
            default=1,
            help='Number of participants to test (default: 1)',
        )

    def handle(self, *args, **options):
        self.stdout.write(
            self.style.SUCCESS('🎨 Testing Simplified SVG Badge Generation (Production)')
        )
        self.stdout.write('=' * 60)

        # Check database connection
        try:
            with connection.cursor() as cursor:
                cursor.execute("SELECT 1")
            self.stdout.write('✅ Database connection successful')
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f'❌ Database connection failed: {str(e)}')
            )
            return

        # Get participants
        if options['participant_id']:
            try:
                participants = [Participant.objects.get(id=options['participant_id'])]
                self.stdout.write(f'📋 Testing with specific participant ID: {options["participant_id"]}')
            except Participant.DoesNotExist:
                self.stdout.write(
                    self.style.ERROR(f'❌ Participant with ID {options["participant_id"]} not found')
                )
                return
        else:
            try:
                participants = Participant.objects.all()[:options['count']]
                self.stdout.write(f'📋 Testing with first {len(participants)} participants')
            except Exception as e:
                self.stdout.write(
                    self.style.ERROR(f'❌ Error fetching participants: {str(e)}')
                )
                return

        if not participants:
            self.stdout.write(
                self.style.WARNING('⚠️  No participants found in database.')
            )
            return

        generated_count = 0
        updated_count = 0
        error_count = 0

        for participant in participants:
            try:
                self.stdout.write(f'\n👤 Processing: {participant.first_name} {participant.last_name}')
                self.stdout.write(f'   📧 Email: {getattr(participant, "email", "N/A")}')
                self.stdout.write(f'   🏢 Institution: {getattr(participant, "institution_name", "N/A")}')
                
                # Get or create badge
                badge, created = Badge.objects.get_or_create(
                    participant=participant
                )
                
                if created:
                    self.stdout.write('   ✨ Created new badge record')
                else:
                    self.stdout.write('   🔄 Using existing badge record')
                
                # Generate badge
                self.stdout.write('   🎨 Generating SVG-style badge...')
                badge_img = badge.generate_badge()
                
                if created:
                    generated_count += 1
                    status = '✅ CREATED'
                else:
                    updated_count += 1
                    status = '🔄 UPDATED'
                
                self.stdout.write(f'      {status} Badge generated successfully')
                self.stdout.write(f'      📏 Dimensions: {badge_img.size}')
                if badge.badge_image:
                    self.stdout.write(f'      📁 Saved to: {badge.badge_image.name}')
                if badge.qr_code_image:
                    self.stdout.write(f'      🔗 QR Code: {badge.qr_code_image.name}')
                    
            except Exception as e:
                error_count += 1
                self.stdout.write(
                    self.style.ERROR(f'   ❌ Error generating badge: {str(e)}')
                )
                # Print traceback for debugging
                import traceback
                self.stdout.write(traceback.format_exc())

        # Summary
        self.stdout.write('\n' + '=' * 60)
        self.stdout.write(self.style.SUCCESS('📊 PRODUCTION TEST SUMMARY'))
        self.stdout.write(f'✅ Created: {generated_count}')
        self.stdout.write(f'🔄 Updated: {updated_count}')
        self.stdout.write(f'❌ Errors: {error_count}')
        self.stdout.write(f'📋 Total processed: {len(participants)}')
        
        if generated_count > 0 or updated_count > 0:
            self.stdout.write(
                self.style.SUCCESS('\n🎉 Production badge generation test completed successfully!')
            )
            self.stdout.write('💡 The simplified SVG badge generation is working in production.')
        elif error_count > 0:
            self.stdout.write(
                self.style.ERROR('\n💥 Production badge generation test completed with errors!')
            )
        else:
            self.stdout.write(
                self.style.WARNING('\n⚠️  No badges were generated (all already exist)')
            )
            
        # Additional info
        self.stdout.write('\n📋 Next steps:')
        self.stdout.write('• Access demo at: https://event.uog.edu.et/api/badges/demo/')
        self.stdout.write('• Test badge API: https://event.uog.edu.et/api/badges/test-svg-badge/')
        self.stdout.write('• Use existing badge endpoints with improved generation')
