# Test HTTP-Only Deployment
# University of Gondar Event Management System

Write-Host "Testing HTTP-Only Deployment" -ForegroundColor Blue
Write-Host "============================" -ForegroundColor Blue

$DOMAIN = "event.uog.edu.et"
$INTERNAL_IP = "************"

Write-Host "Configuration:" -ForegroundColor Yellow
Write-Host "  Domain: $DOMAIN" -ForegroundColor White
Write-Host "  Internal IP: $INTERNAL_IP" -ForegroundColor White
Write-Host "  Mode: HTTP-Only (No SSL)" -ForegroundColor White

Write-Host "`nTesting Docker Services..." -ForegroundColor Yellow

# Check Docker services
try {
    $services = docker-compose -f docker-compose.prod.yml ps --format "table {{.Name}}\t{{.Status}}"
    Write-Host "Docker Services Status:" -ForegroundColor Green
    Write-Host $services -ForegroundColor White
} catch {
    Write-Host "ERROR: Cannot check Docker services" -ForegroundColor Red
    exit 1
}

Write-Host "`nTesting HTTP Access..." -ForegroundColor Yellow

# Test localhost access
Write-Host "Testing localhost access..." -ForegroundColor Cyan
try {
    $response = Invoke-WebRequest -Uri "http://localhost" -Method Head -TimeoutSec 10
    Write-Host "SUCCESS: Localhost accessible (Status: $($response.StatusCode))" -ForegroundColor Green
    
    # Show security headers
    Write-Host "Security Headers:" -ForegroundColor Cyan
    if ($response.Headers["X-Frame-Options"]) {
        Write-Host "  X-Frame-Options: $($response.Headers["X-Frame-Options"])" -ForegroundColor White
    }
    if ($response.Headers["X-Content-Type-Options"]) {
        Write-Host "  X-Content-Type-Options: $($response.Headers["X-Content-Type-Options"])" -ForegroundColor White
    }
    if ($response.Headers["X-XSS-Protection"]) {
        Write-Host "  X-XSS-Protection: $($response.Headers["X-XSS-Protection"])" -ForegroundColor White
    }
    
} catch {
    Write-Host "ERROR: Localhost not accessible" -ForegroundColor Red
    Write-Host "Error: $($_.Exception.Message)" -ForegroundColor Red
}

# Test direct IP access
Write-Host "`nTesting direct IP access..." -ForegroundColor Cyan
try {
    $response = Invoke-WebRequest -Uri "http://$INTERNAL_IP" -Method Head -TimeoutSec 10
    Write-Host "SUCCESS: Direct IP accessible (Status: $($response.StatusCode))" -ForegroundColor Green
} catch {
    Write-Host "WARNING: Direct IP not accessible" -ForegroundColor Yellow
    Write-Host "This may be normal depending on nginx configuration" -ForegroundColor Yellow
}

# Test domain access (internal)
Write-Host "`nTesting internal domain access..." -ForegroundColor Cyan
try {
    $response = Invoke-WebRequest -Uri "http://$DOMAIN" -Method Head -TimeoutSec 10
    Write-Host "SUCCESS: Internal domain accessible (Status: $($response.StatusCode))" -ForegroundColor Green
} catch {
    Write-Host "EXPECTED: Internal domain not accessible from server" -ForegroundColor Yellow
    Write-Host "This is normal - test from client machines instead" -ForegroundColor Yellow
}

Write-Host "`nTesting API Endpoints..." -ForegroundColor Yellow

# Test health endpoint
Write-Host "Testing health endpoint..." -ForegroundColor Cyan
try {
    $response = Invoke-WebRequest -Uri "http://localhost/health/" -Method Get -TimeoutSec 10
    Write-Host "SUCCESS: Health endpoint accessible (Status: $($response.StatusCode))" -ForegroundColor Green
} catch {
    Write-Host "WARNING: Health endpoint not accessible" -ForegroundColor Yellow
    Write-Host "Error: $($_.Exception.Message)" -ForegroundColor Yellow
}

# Test API endpoint
Write-Host "Testing API endpoint..." -ForegroundColor Cyan
try {
    $response = Invoke-WebRequest -Uri "http://localhost/api/" -Method Get -TimeoutSec 10
    Write-Host "SUCCESS: API endpoint accessible (Status: $($response.StatusCode))" -ForegroundColor Green
} catch {
    Write-Host "INFO: API endpoint returned error (this may be normal)" -ForegroundColor Cyan
    Write-Host "Status: $($_.Exception.Response.StatusCode)" -ForegroundColor White
}

Write-Host "`nTesting DNS Resolution..." -ForegroundColor Yellow

# Test internal DNS
try {
    $dnsResult = Resolve-DnsName -Name $DOMAIN -ErrorAction Stop
    $resolvedIP = ($dnsResult | Where-Object {$_.Type -eq "A"}).IPAddress
    
    if ($resolvedIP -eq $INTERNAL_IP) {
        Write-Host "SUCCESS: Internal DNS resolves correctly ($DOMAIN -> $resolvedIP)" -ForegroundColor Green
    } else {
        Write-Host "WARNING: DNS resolves to $resolvedIP (expected $INTERNAL_IP)" -ForegroundColor Yellow
    }
} catch {
    Write-Host "ERROR: DNS resolution failed for $DOMAIN" -ForegroundColor Red
}

Write-Host "`nTesting Network Connectivity..." -ForegroundColor Yellow

# Test port 80 locally
Write-Host "Testing local port 80..." -ForegroundColor Cyan
try {
    $portTest = Test-NetConnection -ComputerName "localhost" -Port 80 -WarningAction SilentlyContinue
    if ($portTest.TcpTestSucceeded) {
        Write-Host "SUCCESS: Port 80 is open locally" -ForegroundColor Green
    } else {
        Write-Host "ERROR: Port 80 is not accessible locally" -ForegroundColor Red
    }
} catch {
    Write-Host "ERROR: Cannot test port 80" -ForegroundColor Red
}

Write-Host "`nDeployment Test Summary:" -ForegroundColor Blue
Write-Host "========================" -ForegroundColor Blue

Write-Host "HTTP-Only Configuration: ACTIVE" -ForegroundColor Green
Write-Host "SSL/HTTPS: DISABLED" -ForegroundColor Yellow
Write-Host "Internal Access: READY" -ForegroundColor Green

Write-Host "`nAccess Instructions:" -ForegroundColor Yellow
Write-Host "===================" -ForegroundColor Yellow
Write-Host "From Server: http://localhost" -ForegroundColor White
Write-Host "From Internal Network: http://$DOMAIN" -ForegroundColor White
Write-Host "Direct IP: http://$INTERNAL_IP" -ForegroundColor White

Write-Host "`nClient Testing:" -ForegroundColor Yellow
Write-Host "===============" -ForegroundColor Yellow
Write-Host "1. From domain-joined computers, open browser" -ForegroundColor White
Write-Host "2. Navigate to: http://$DOMAIN" -ForegroundColor Cyan
Write-Host "3. Verify all application features work" -ForegroundColor White

Write-Host "`nExternal Access (Optional):" -ForegroundColor Yellow
Write-Host "===========================" -ForegroundColor Yellow
Write-Host "To enable internet access:" -ForegroundColor White
Write-Host "1. Configure router/firewall to allow port 80" -ForegroundColor White
Write-Host "2. Set up port forwarding: External 80 -> Internal $INTERNAL_IP`:80" -ForegroundColor White
Write-Host "3. Test from outside: http://$DOMAIN" -ForegroundColor White

Write-Host "`nMaintenance Commands:" -ForegroundColor Yellow
Write-Host "=====================" -ForegroundColor Yellow
Write-Host "Check services: docker-compose -f docker-compose.prod.yml ps" -ForegroundColor Cyan
Write-Host "View logs: docker-compose -f docker-compose.prod.yml logs [service]" -ForegroundColor Cyan
Write-Host "Restart: docker-compose -f docker-compose.prod.yml restart" -ForegroundColor Cyan

Write-Host "`nHTTP-Only Deployment Test Complete!" -ForegroundColor Green
