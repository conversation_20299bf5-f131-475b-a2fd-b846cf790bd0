#!/bin/bash
set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}🚀 Starting Celery Worker for UoG Event Management System...${NC}"

# Wait for database to be ready
echo -e "${YELLOW}⏳ Waiting for database to be ready...${NC}"
until nc -z db 5432; do
  echo -e "${YELLOW}⏳ Database is unavailable - sleeping${NC}"
  sleep 1
done
echo -e "${GREEN}✅ Database is ready!${NC}"

# Wait for Redis to be ready
echo -e "${YELLOW}⏳ Waiting for Redis to be ready...${NC}"
until nc -z redis 6379; do
  echo -e "${YELLOW}⏳ Redis is unavailable - sleeping${NC}"
  sleep 1
done
echo -e "${GREEN}✅ Redis is ready!${NC}"

# Wait a bit more for the main backend to finish initialization
echo -e "${YELLOW}⏳ Waiting for backend initialization...${NC}"
sleep 10

echo -e "${GREEN}✅ Celery worker initialization complete!${NC}"
echo -e "${BLUE}🔄 Starting Celery worker...${NC}"

# Execute the main command
exec "$@"
