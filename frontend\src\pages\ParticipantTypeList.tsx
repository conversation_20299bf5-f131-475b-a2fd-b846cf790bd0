import React, { useState, useEffect } from 'react';
import { Con<PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>ton, Badge, Form, InputGroup, <PERSON><PERSON>, Spin<PERSON>, Modal, Table } from 'react-bootstrap';
import { participantTypeService, ParticipantType } from '../services/api';

const ParticipantTypeList: React.FC = () => {
  const [participantTypes, setParticipantTypes] = useState<ParticipantType[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);
  const [selectedType, setSelectedType] = useState<ParticipantType | null>(null);
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    color: '#007bff'
  });

  useEffect(() => {
    fetchParticipantTypes();
  }, []);

  const fetchParticipantTypes = async () => {
    try {
      setLoading(true);
      const response = await participantTypeService.getParticipantTypes();
      const typesData = Array.isArray(response.data) ? response.data : 
                       ((response.data as any)?.results ? (response.data as any).results : []);
      setParticipantTypes(typesData);
      setError(null);
    } catch (err) {
      console.error('Error fetching participant types:', err);
      setError('Failed to load participant types. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const filteredTypes = participantTypes.filter(type =>
    type.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    type.description.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const resetForm = () => {
    setFormData({
      name: '',
      description: '',
      color: '#007bff'
    });
  };

  const openCreateModal = () => {
    resetForm();
    setShowCreateModal(true);
  };

  const openEditModal = (type: ParticipantType) => {
    setSelectedType(type);
    setFormData({
      name: type.name,
      description: type.description,
      color: type.color
    });
    setShowEditModal(true);
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    try {
      if (showCreateModal) {
        await participantTypeService.createParticipantType(formData);
      } else if (selectedType) {
        await participantTypeService.updateParticipantType(selectedType.id, formData);
      }
      
      await fetchParticipantTypes();
      setShowCreateModal(false);
      setShowEditModal(false);
      setSelectedType(null);
      resetForm();
    } catch (err) {
      console.error('Error saving participant type:', err);
      setError('Failed to save participant type. Please try again.');
    }
  };

  const handleDelete = async (type: ParticipantType) => {
    if (window.confirm(`Are you sure you want to delete "${type.name}"? This action cannot be undone.`)) {
      try {
        await participantTypeService.deleteParticipantType(type.id);
        await fetchParticipantTypes();
      } catch (err) {
        console.error('Error deleting participant type:', err);
        setError('Failed to delete participant type. Please try again.');
      }
    }
  };

  if (loading) {
    return (
      <Container className="text-center mt-5">
        <Spinner animation="border" role="status">
          <span className="visually-hidden">Loading...</span>
        </Spinner>
        <p className="mt-3">Loading participant types...</p>
      </Container>
    );
  }

  return (
    <Container className="py-4">
      <Row className="mb-4">
        <Col>
          <div className="d-flex justify-content-between align-items-center mb-4">
            <div>
              <h1 className="display-5 fw-bold text-primary">
                <i className="fas fa-tags me-3"></i>
                Participant Types
              </h1>
              <p className="lead text-muted">Manage different categories of event participants</p>
            </div>
            <Button variant="primary" size="lg" onClick={openCreateModal}>
              <i className="fas fa-plus me-2"></i>
              Add New Type
            </Button>
          </div>

          {/* Search Controls */}
          <Row className="mb-4">
            <Col md={6}>
              <InputGroup>
                <InputGroup.Text>
                  <i className="fas fa-search"></i>
                </InputGroup.Text>
                <Form.Control
                  type="text"
                  placeholder="Search participant types by name or description..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                />
              </InputGroup>
            </Col>
          </Row>

          {error && (
            <Alert variant="danger" className="mb-4">
              <i className="fas fa-exclamation-triangle me-2"></i>
              {error}
            </Alert>
          )}
        </Col>
      </Row>

      {/* Participant Types */}
      {filteredTypes.length === 0 ? (
        <Card className="text-center py-5">
          <Card.Body>
            <i className="fas fa-tags fa-4x text-muted mb-3"></i>
            <h4 className="text-muted">No Participant Types Found</h4>
            <p className="text-muted">
              {searchTerm ? 'No participant types match your search criteria.' : 'No participant types have been created yet.'}
            </p>
            <Button variant="primary" onClick={openCreateModal}>
              <i className="fas fa-plus me-2"></i>
              Create First Type
            </Button>
          </Card.Body>
        </Card>
      ) : (
        <>
          {/* Grid View */}
          <Row className="mb-4">
            {filteredTypes.map((type) => (
              <Col lg={4} md={6} key={type.id} className="mb-4">
                <Card className="h-100 shadow-sm type-card">
                  <Card.Header style={{ backgroundColor: type.color, color: 'white' }}>
                    <div className="d-flex justify-content-between align-items-center">
                      <h5 className="mb-0">{type.name}</h5>
                      <Badge 
                        bg="light" 
                        text="dark"
                        style={{ fontSize: '0.7rem' }}
                      >
                        {type.color}
                      </Badge>
                    </div>
                  </Card.Header>
                  
                  <Card.Body className="d-flex flex-column">
                    <div className="mb-3 flex-grow-1">
                      <p className="text-muted">
                        {type.description || 'No description provided'}
                      </p>
                    </div>

                    <div className="mb-3">
                      <small className="text-muted">
                        <i className="fas fa-calendar me-1"></i>
                        Created: {new Date(type.created_at).toLocaleDateString()}
                      </small>
                    </div>

                    <div className="d-grid gap-2">
                      <Button 
                        variant="outline-primary" 
                        size="sm"
                        onClick={() => openEditModal(type)}
                      >
                        <i className="fas fa-edit me-2"></i>
                        Edit Type
                      </Button>
                      <Button 
                        variant="outline-danger" 
                        size="sm"
                        onClick={() => handleDelete(type)}
                      >
                        <i className="fas fa-trash me-2"></i>
                        Delete
                      </Button>
                    </div>
                  </Card.Body>
                </Card>
              </Col>
            ))}
          </Row>

          {/* Table View */}
          <Card>
            <Card.Header>
              <h5 className="mb-0">
                <i className="fas fa-table me-2"></i>
                All Participant Types
              </h5>
            </Card.Header>
            <Card.Body className="p-0">
              <Table responsive striped hover className="mb-0">
                <thead>
                  <tr>
                    <th>Color</th>
                    <th>Name</th>
                    <th>Description</th>
                    <th>Created</th>
                    <th>Actions</th>
                  </tr>
                </thead>
                <tbody>
                  {filteredTypes.map((type) => (
                    <tr key={type.id}>
                      <td>
                        <div 
                          className="rounded-circle d-inline-block"
                          style={{ 
                            width: '20px', 
                            height: '20px', 
                            backgroundColor: type.color 
                          }}
                        ></div>
                      </td>
                      <td>
                        <strong>{type.name}</strong>
                      </td>
                      <td>
                        <span className="text-muted">
                          {type.description || 'No description'}
                        </span>
                      </td>
                      <td>
                        <small className="text-muted">
                          {new Date(type.created_at).toLocaleDateString()}
                        </small>
                      </td>
                      <td>
                        <div className="btn-group">
                          <Button 
                            variant="outline-primary" 
                            size="sm"
                            onClick={() => openEditModal(type)}
                          >
                            <i className="fas fa-edit"></i>
                          </Button>
                          <Button 
                            variant="outline-danger" 
                            size="sm"
                            onClick={() => handleDelete(type)}
                          >
                            <i className="fas fa-trash"></i>
                          </Button>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </Table>
            </Card.Body>
          </Card>
        </>
      )}

      {/* Statistics */}
      {participantTypes.length > 0 && (
        <Row className="mt-5">
          <Col>
            <Card className="bg-light">
              <Card.Body>
                <Row className="text-center">
                  <Col md={4}>
                    <h4 className="text-primary">{participantTypes.length}</h4>
                    <p className="text-muted mb-0">Total Types</p>
                  </Col>
                  <Col md={4}>
                    <h4 className="text-success">{participantTypes.filter(t => t.description).length}</h4>
                    <p className="text-muted mb-0">With Descriptions</p>
                  </Col>
                  <Col md={4}>
                    <h4 className="text-info">{new Set(participantTypes.map(t => t.color)).size}</h4>
                    <p className="text-muted mb-0">Unique Colors</p>
                  </Col>
                </Row>
              </Card.Body>
            </Card>
          </Col>
        </Row>
      )}

      {/* Create/Edit Modal */}
      <Modal show={showCreateModal || showEditModal} onHide={() => {
        setShowCreateModal(false);
        setShowEditModal(false);
        setSelectedType(null);
        resetForm();
      }}>
        <Modal.Header closeButton>
          <Modal.Title>
            <i className="fas fa-tag me-2"></i>
            {showCreateModal ? 'Create New Participant Type' : 'Edit Participant Type'}
          </Modal.Title>
        </Modal.Header>
        <Form onSubmit={handleSubmit}>
          <Modal.Body>
            <Form.Group className="mb-3">
              <Form.Label>Type Name *</Form.Label>
              <Form.Control
                type="text"
                value={formData.name}
                onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                placeholder="e.g., Speaker, Attendee, Organizer"
                required
              />
            </Form.Group>

            <Form.Group className="mb-3">
              <Form.Label>Description</Form.Label>
              <Form.Control
                as="textarea"
                rows={3}
                value={formData.description}
                onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                placeholder="Describe this participant type..."
              />
            </Form.Group>

            <Form.Group className="mb-3">
              <Form.Label>Badge Color *</Form.Label>
              <div className="d-flex align-items-center gap-3">
                <Form.Control
                  type="color"
                  value={formData.color}
                  onChange={(e) => setFormData({ ...formData, color: e.target.value })}
                  style={{ width: '60px', height: '40px' }}
                />
                <Form.Control
                  type="text"
                  value={formData.color}
                  onChange={(e) => setFormData({ ...formData, color: e.target.value })}
                  placeholder="#007bff"
                />
                <div 
                  className="rounded px-3 py-2 text-white fw-bold"
                  style={{ backgroundColor: formData.color }}
                >
                  Preview
                </div>
              </div>
              <Form.Text className="text-muted">
                This color will be used for participant badges and identification.
              </Form.Text>
            </Form.Group>
          </Modal.Body>
          <Modal.Footer>
            <Button variant="secondary" onClick={() => {
              setShowCreateModal(false);
              setShowEditModal(false);
              setSelectedType(null);
              resetForm();
            }}>
              Cancel
            </Button>
            <Button variant="primary" type="submit">
              <i className="fas fa-save me-2"></i>
              {showCreateModal ? 'Create Type' : 'Save Changes'}
            </Button>
          </Modal.Footer>
        </Form>
      </Modal>

      <style>{`
        .type-card {
          transition: transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
        }
        
        .type-card:hover {
          transform: translateY(-5px);
          box-shadow: 0 8px 25px rgba(0,0,0,0.15) !important;
        }
      `}</style>
    </Container>
  );
};

export default ParticipantTypeList;
