import React, { useState } from 'react';
import { Container, <PERSON>, <PERSON>, <PERSON>, But<PERSON>, Alert, Form, ProgressBar, Badge } from 'react-bootstrap';
import { driverService } from '../services/api';

interface BulkInsertionResult {
  success: number;
  failed: number;
  errors: string[];
}

const BulkDataInsertion: React.FC = () => {
  const [loading, setLoading] = useState(false);
  const [result, setResult] = useState<BulkInsertionResult | null>(null);
  const [error, setError] = useState('');

  // Driver data from your provided information
  const driversData = [
    { name: "Getnet Mehari", phone: "0918036666", car_code: "04-08391", photo: "event_photo_1754059938_ጌትነት-መሀሪ.jpg" },
    { name: "<PERSON><PERSON><PERSON>", phone: "0918791360", car_code: "04-08389", photo: "event_photo_1754060087_ዳዊት-ሠሙ.jpg" },
    { name: "<PERSON><PERSON><PERSON>", phone: "0918704213", car_code: "04-08388", photo: "event_photo_1754060071_አዲሱ-ሹምየ-.jpg" },
    { name: "Mandefro Asefa", phone: "0918774583", car_code: "04-08390", photo: "event_photo_1754060050_Mandefro-Asefa-.jpg" },
    { name: "Endinew Marie", phone: "0918076590", car_code: "25997" },
    { name: "Henok Tadesse", phone: "0925176158", car_code: "19993" },
    { name: "Daniel Bayable", phone: "0918033549", car_code: "22881" },
    { name: "Thomas Asnakew", phone: "0918194134", car_code: "22890" },
    { name: "Yibeltal Mesfin", phone: "0918705505", car_code: "21305" },
    { name: "Kasahun Endayeh", phone: "0967150817", car_code: "20262" },
    { name: "Addisu Yohannes", phone: "0986804684", car_code: "32017" },
    { name: "Endalamaw Abuhay", phone: "0918298380", car_code: "52459" },
    { name: "Shikur Aman", phone: "0918787332", car_code: "29561" },
    { name: "Kefyalew Mihret", phone: "0918044197", car_code: "29561" },
    { name: "Bewket Shiferaw", phone: "0922554052", car_code: "1991" },
    { name: "Wasyehun Sisay", phone: "0918046922", car_code: "10864" },
    { name: "Worku Genet", phone: "0927747378", car_code: "21302" },
    { name: "Eshetu Kemal", phone: "0918805252", car_code: "29565" },
    { name: "Fentahun Yitayew", phone: "0918046904", car_code: "29566" },
    { name: "Takele Alebachew", phone: "0918042015", car_code: "29562" },
    { name: "Maru Shiferaw", phone: "0918810968", car_code: "23742" },
    { name: "Getahun Fentie", phone: "0918033725", car_code: "20495" },
    { name: "Wanna Sajin", phone: "0900000000", car_code: "20428" },
    { name: "Alewond Kenaw", phone: "0905972702", car_code: "18532" },
    { name: "Desalegn Worku", phone: "0918149804", car_code: "09535" },
    { name: "Eliyas Sitotaw", phone: "0900000000", car_code: "14774" }
  ];

  // Focal persons data
  const focalPersonsData = [
    { name: "Belay", email: "<EMAIL>", phone: "0918805970", availability: "full-time" },
    { name: "Demis Mulatu", email: "<EMAIL>", phone: "0924110714", availability: "full-time" },
    { name: "Mebratu Tilahun", email: "<EMAIL>", phone: "0933577374", availability: "full-time" },
    { name: "Dr. Alene", email: "<EMAIL>", phone: "0921252233", availability: "full-time" },
    { name: "Dr. Worku Abebe", email: "<EMAIL>", phone: "0910108943", availability: "full-time" },
    { name: "Yonas Addisu", email: "<EMAIL>", phone: "0918441624", availability: "full-time" },
    { name: "Nega Nigussie", email: "<EMAIL>", phone: "0972227034", availability: "full-time" },
    { name: "Tadesse Woldegebriel", email: "<EMAIL>", phone: "0918778670", availability: "full-time", photo: "event_photo_1754143265_Tadesse1.jpg" },
    { name: "Yenesew Alene", email: "<EMAIL>", phone: "0910040073", availability: "full-time" }
  ];

  // Hotels data
  const hotelsData = [
    { name: "Jantekel Hotel", email: "<EMAIL>", phone: "0965363738", contact_person_name: "Efriem Ejigu", contact_person_phone: "0942858083", contact_person_photo: "event_photo_1754061282_Jantekel.jpg" },
    { name: "Haile Resort", email: "<EMAIL>", phone: "0901010101", contact_person_name: "Aderajew Amha", contact_person_phone: "0903141595" },
    { name: "Goha Hotel", address: "Buluko", email: "<EMAIL>", phone: "0581110694", contact_person_name: "Yordanos Yigzaw", contact_person_phone: "0900162940", contact_person_photo: "event_photo_1754061443_Goha.jpg" },
    { name: "Zobil Resort", address: "Bulko", email: "<EMAIL>", phone: "0582113737", contact_person_name: "Robel Taye", contact_person_phone: "0920636363", contact_person_photo: "event_photo_1754061504_Zobli.jpg" },
    { name: "Florida Hotel", address: "Maraki", email: "<EMAIL>", phone: "0918350860", contact_person_name: "Muluye Fissha", contact_person_phone: "0915186232" },
    { name: "Olympic Hotel", address: "Maraki", phone: "0915300505", contact_person_name: "Worku Mulaw", contact_person_phone: "0977325637", contact_person_photo: "event_photo_1754061616_Olompic.jpg" },
    { name: "Rozeo Hotel", address: "Embassy", email: "<EMAIL>", phone: "0582111222", contact_person_name: "Alazar", contact_person_phone: "0946472455", contact_person_photo: "event_photo_1754061659_Rozeo-hotel-.jpg" },
    { name: "PLAZA Hotel", address: "MarakiI", email: "<EMAIL>", phone: "0582112121", contact_person_name: "Girma Worku", contact_person_phone: "0991013037" },
    { name: "AG Hotel", address: "Piaza", email: "<EMAIL>", phone: "0975154646", contact_person_name: "Atinafu Yirsaw", contact_person_phone: "0975154646", contact_person_photo: "event_photo_1754061763_AG.jpg" },
    { name: "Gondar Hills", phone: "0900000000" }
  ];

  // Participant types data
  const participantTypesData = [
    { name: "VIP", description: "VIP participants", is_active: true },
    { name: "President", description: "President level participants", is_active: true },
    { name: "Driver", description: "Default participant type", is_active: true },
    { name: "Faculty", description: "Default participant type", is_active: true },
    { name: "Student", description: "Default participant type", is_active: true },
    { name: "Guest", description: "Default participant type", is_active: true },
    { name: "Security", description: "Default participant type", is_active: true },
    { name: "Vice President", description: "Vice President level", is_active: true },
    { name: "Media", description: "Media personnel", is_active: true },
    { name: "Coordinator", description: "Event coordinators", is_active: true },
    { name: "Housekeeping", description: "Housekeeping staff", is_active: true },
    { name: "Plan Director", description: "Planning directors", is_active: true },
    { name: "Other", description: "Other participants", is_active: true },
    { name: "Vice Chairperson of the Board", description: "Board vice chairperson", is_active: true },
    { name: "Staff", description: "Default participant type", is_active: true },
    { name: "External Participant", description: "Default participant type", is_active: true },
    { name: "Guest Speaker", description: "Guest speakers", is_active: true }
  ];

  // Places to visit data
  const placesToVisitData = [
    { name: "University Campuses", description: "University campus tours", is_active: true },
    { name: "Castle of Fasil", description: "Historical castle visit", is_active: true },
    { name: "Corridor Development", description: "Development corridor tour", is_active: true },
    { name: "St. Trinity church", description: "Religious site visit", is_active: true },
    { name: "Fasil Bath", description: "Historical bath site", is_active: true }
  ];

  const insertDrivers = async () => {
    setLoading(true);
    setError('');
    setResult(null);

    try {
      let success = 0;
      let failed = 0;
      const errors: string[] = [];

      for (const driverData of driversData) {
        try {
          const formData = new FormData();
          formData.append('name', driverData.name);
          formData.append('phone', driverData.phone);
          formData.append('car_plate', driverData.car_code);
          formData.append('car_code', driverData.car_code);
          formData.append('car_model', 'Not specified');
          formData.append('car_color', 'Not specified');
          formData.append('license_number', 'Not specified');
          formData.append('email', `${driverData.name.toLowerCase().replace(/\s+/g, '.')}@driver.uog.et`);
          formData.append('is_available', 'true');
          formData.append('notes', 'Bulk imported driver');
          formData.append('event', '1'); // Assuming event ID 1

          await driverService.createDriver(formData);
          success++;
        } catch (err: any) {
          failed++;
          errors.push(`${driverData.name}: ${err.response?.data?.detail || err.message}`);
        }
      }

      setResult({ success, failed, errors });
    } catch (err: any) {
      setError('Failed to insert drivers: ' + err.message);
    } finally {
      setLoading(false);
    }
  };

  const insertAllData = async () => {
    setLoading(true);
    setError('');
    setResult(null);

    try {
      // Insert drivers first
      await insertDrivers();
      
      // You can add similar functions for other data types here
      // await insertFocalPersons();
      // await insertHotels();
      // await insertParticipantTypes();
      // await insertPlacesToVisit();

    } catch (err: any) {
      setError('Failed to insert data: ' + err.message);
    } finally {
      setLoading(false);
    }
  };

  return (
    <Container className="py-4">
      <Row>
        <Col>
          <Card>
            <Card.Header>
              <h3 className="mb-0">
                <i className="fas fa-database me-2"></i>
                Bulk Data Insertion
              </h3>
            </Card.Header>
            <Card.Body>
              {error && (
                <Alert variant="danger" onClose={() => setError('')} dismissible>
                  {error}
                </Alert>
              )}

              {result && (
                <Alert variant={result.failed > 0 ? 'warning' : 'success'}>
                  <h6>Insertion Results:</h6>
                  <div className="d-flex gap-3 mb-2">
                    <Badge bg="success">Success: {result.success}</Badge>
                    <Badge bg="danger">Failed: {result.failed}</Badge>
                  </div>
                  {result.errors.length > 0 && (
                    <details>
                      <summary>View Errors ({result.errors.length})</summary>
                      <ul className="mt-2 mb-0">
                        {result.errors.map((error, index) => (
                          <li key={index} className="small text-danger">{error}</li>
                        ))}
                      </ul>
                    </details>
                  )}
                </Alert>
              )}

              <div className="mb-4">
                <h5>Data Summary:</h5>
                <Row>
                  <Col md={6}>
                    <ul className="list-unstyled">
                      <li><i className="fas fa-car me-2 text-primary"></i>Drivers: {driversData.length} records</li>
                      <li><i className="fas fa-user-tie me-2 text-info"></i>Focal Persons: {focalPersonsData.length} records</li>
                      <li><i className="fas fa-hotel me-2 text-success"></i>Hotels: {hotelsData.length} records</li>
                    </ul>
                  </Col>
                  <Col md={6}>
                    <ul className="list-unstyled">
                      <li><i className="fas fa-users me-2 text-warning"></i>Participant Types: {participantTypesData.length} records</li>
                      <li><i className="fas fa-map-marker-alt me-2 text-danger"></i>Places to Visit: {placesToVisitData.length} records</li>
                    </ul>
                  </Col>
                </Row>
              </div>

              {loading && (
                <div className="mb-3">
                  <ProgressBar animated now={100} label="Inserting data..." />
                </div>
              )}

              <div className="d-flex gap-2">
                <Button
                  variant="primary"
                  onClick={insertDrivers}
                  disabled={loading}
                >
                  <i className="fas fa-car me-2"></i>
                  Insert Drivers Only
                </Button>
                <Button
                  variant="success"
                  onClick={insertAllData}
                  disabled={loading}
                >
                  <i className="fas fa-database me-2"></i>
                  Insert All Data
                </Button>
              </div>

              <div className="mt-4">
                <h6>Preview - First 5 Drivers:</h6>
                <div className="table-responsive">
                  <table className="table table-sm">
                    <thead>
                      <tr>
                        <th>Name</th>
                        <th>Phone</th>
                        <th>Car Code</th>
                        <th>Photo</th>
                      </tr>
                    </thead>
                    <tbody>
                      {driversData.slice(0, 5).map((driver, index) => (
                        <tr key={index}>
                          <td>{driver.name}</td>
                          <td>{driver.phone}</td>
                          <td>{driver.car_code}</td>
                          <td>{driver.photo ? <Badge bg="info">Has Photo</Badge> : <Badge bg="secondary">No Photo</Badge>}</td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              </div>
            </Card.Body>
          </Card>
        </Col>
      </Row>
    </Container>
  );
};

export default BulkDataInsertion;
