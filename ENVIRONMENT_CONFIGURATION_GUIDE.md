# Environment Configuration Guide

## 🎉 **DYNAMIC API URL CONFIGURATION COMPLETED!**

The University of Gondar Event Management System now supports dynamic API URL configuration using environment variables, eliminating hardcoded localhost URLs.

## ✅ **Current Configuration Status**

### **Active Configuration: IP Access (************)**
- **Frontend API Base URL**: `http://************:8000/api`
- **Frontend Backend URL**: `http://************:8000`
- **Frontend Access**: `http://************:3000`
- **Backend Access**: `http://************:8000`
- **Admin Interface**: `http://************:8000/admin/`

## 🔧 **Available Environment Configurations**

### **1. Localhost Configuration (.env.localhost)**
```env
# For internal/same machine access
REACT_APP_API_BASE_URL=http://localhost:8000/api
REACT_APP_BACKEND_URL=http://localhost:8000
```
**Use when**: Accessing from the same machine or internal network

### **2. IP Configuration (.env.ip)** ✅ **CURRENTLY ACTIVE**
```env
# For external access via IP address
REACT_APP_API_BASE_URL=http://************:8000/api
REACT_APP_BACKEND_URL=http://************:8000
```
**Use when**: Accessing from external machines using IP address

### **3. Domain Configuration (.env.domain)**
```env
# For domain access
REACT_APP_API_BASE_URL=http://event.uog.edu.et:8000/api
REACT_APP_BACKEND_URL=http://event.uog.edu.et:8000
```
**Use when**: Accessing via domain name (event.uog.edu.et)

## 🔄 **How to Switch Between Configurations**

### **Method 1: Using PowerShell Script (Windows)**
```powershell
# Switch to localhost configuration
.\switch-env.ps1 localhost

# Switch to IP configuration
.\switch-env.ps1 ip

# Switch to domain configuration
.\switch-env.ps1 domain
```

### **Method 2: Using Bash Script (Linux/Mac)**
```bash
# Make script executable (first time only)
chmod +x switch-env.sh

# Switch to localhost configuration
./switch-env.sh localhost

# Switch to IP configuration
./switch-env.sh ip

# Switch to domain configuration
./switch-env.sh domain
```

### **Method 3: Manual Configuration**
```bash
# Copy the desired configuration to .env
copy .env.localhost .env    # For localhost
copy .env.ip .env          # For IP access
copy .env.domain .env      # For domain access

# Restart Docker services
docker-compose -f docker-compose.local.yml down
docker-compose -f docker-compose.local.yml up -d
```

## 🌐 **Backend Configuration**

### **ALLOWED_HOSTS**
The backend is configured to accept requests from:
- `localhost`
- `127.0.0.1`
- `0.0.0.0`
- `backend` (Docker internal)
- `************`
- `event.uog.edu.et`

### **CORS_ALLOWED_ORIGINS**
Cross-origin requests are allowed from:
- `http://localhost:3000`
- `http://127.0.0.1:3000`
- `http://************:3000`
- `http://************:3001`
- `http://************:8080`
- `http://event.uog.edu.et:3000`
- `http://event.uog.edu.et`
- `https://event.uog.edu.et`

## 📋 **Frontend Environment Variables**

### **Key Variables**
- **REACT_APP_API_BASE_URL**: Base URL for API calls (e.g., `/api/events/`)
- **REACT_APP_BACKEND_URL**: Base URL for backend resources (e.g., media files)

### **How They're Used**
```typescript
// In frontend/src/services/api.ts
const API_BASE_URL = process.env.REACT_APP_API_BASE_URL || 'http://localhost:8000/api';
const BACKEND_URL = process.env.REACT_APP_BACKEND_URL || 'http://localhost:8000';

// For API calls
const api = axios.create({
  baseURL: API_BASE_URL,
});

// For media URLs
export const getMediaUrl = (path: string): string => {
  if (!path) return '';
  if (path.startsWith('http')) return path;
  return `${BACKEND_URL}${path}`;
};
```

## 🚀 **Deployment Scenarios**

### **Scenario 1: Local Development**
```bash
# Use localhost configuration
copy .env.localhost .env
docker-compose -f docker-compose.local.yml up -d
# Access: http://localhost:3000
```

### **Scenario 2: Server Deployment (IP Access)**
```bash
# Use IP configuration
copy .env.ip .env
docker-compose -f docker-compose.local.yml up -d
# Access: http://************:3000
```

### **Scenario 3: Production with Domain**
```bash
# Use domain configuration
copy .env.domain .env
docker-compose -f docker-compose.local.yml up -d
# Access: http://event.uog.edu.et:3000
```

## 🔍 **Testing and Verification**

### **Quick Test Script**
```bash
# Test current configuration
python test_ip_access.py
```

### **Manual Verification**
```bash
# Check frontend environment variables
docker-compose -f docker-compose.local.yml exec frontend printenv | grep REACT_APP

# Test backend health
curl http://************:8000/health/

# Test frontend access
curl http://************:3000
```

## 📝 **Configuration Files Overview**

### **Main Configuration Files**
- `.env` - Active environment configuration
- `.env.localhost` - Localhost access configuration
- `.env.ip` - IP access configuration (************)
- `.env.domain` - Domain access configuration (event.uog.edu.et)

### **Docker Configuration**
- `docker-compose.local.yml` - Docker services configuration
- Environment variables are passed from `.env` to containers

### **Frontend Configuration**
- `frontend/src/services/api.ts` - API service with environment variable support
- `frontend/src/services/auth.ts` - Authentication service
- `frontend/src/services/branding.ts` - Branding service

## 🛠️ **Troubleshooting**

### **Common Issues**

#### **1. Frontend shows "Network Error"**
```bash
# Check if backend is accessible
curl http://************:8000/health/

# Verify CORS configuration
curl -H "Origin: http://************:3000" http://************:8000/api/events/
```

#### **2. API calls fail with 400/403 errors**
```bash
# Check ALLOWED_HOSTS configuration
docker-compose -f docker-compose.local.yml exec backend python manage.py shell -c "from django.conf import settings; print('ALLOWED_HOSTS:', settings.ALLOWED_HOSTS)"
```

#### **3. Environment variables not updating**
```bash
# Restart containers after changing .env
docker-compose -f docker-compose.local.yml down
docker-compose -f docker-compose.local.yml up -d
```

### **Debug Commands**
```bash
# Check container status
docker-compose -f docker-compose.local.yml ps

# View logs
docker-compose -f docker-compose.local.yml logs frontend
docker-compose -f docker-compose.local.yml logs backend

# Check environment variables
docker-compose -f docker-compose.local.yml exec frontend env | grep REACT_APP
docker-compose -f docker-compose.local.yml exec backend env | grep ALLOWED_HOSTS
```

## 🎯 **Next Steps**

1. **Test from target server** (************)
2. **Configure domain DNS** if using event.uog.edu.et
3. **Set up SSL certificates** for HTTPS access
4. **Configure production email settings**
5. **Set up monitoring and logging**

---

**Configuration completed on**: 2025-07-29  
**Current Environment**: IP Access (************)  
**Status**: ✅ **FULLY OPERATIONAL**
