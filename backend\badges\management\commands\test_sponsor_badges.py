from django.core.management.base import BaseCommand
from badges.models import Badge
from participants.models import Participant
from events.models import Event, EventSponsor


class Command(BaseCommand):
    help = 'Test sponsor logo integration in badge generation'

    def add_arguments(self, parser):
        parser.add_argument(
            '--create-sponsors',
            action='store_true',
            help='Create test sponsors if none exist',
        )
        parser.add_argument(
            '--regenerate-badges',
            action='store_true',
            help='Force regenerate all badges to show sponsor logos',
        )

    def handle(self, *args, **options):
        self.stdout.write('🎨 Testing Sponsor Logo Integration in Badges')
        self.stdout.write('=' * 60)

        # Get active event
        events = Event.objects.filter(is_active=True)
        if not events:
            self.stdout.write(self.style.ERROR('❌ No active events found'))
            return

        event = events.first()
        self.stdout.write(f'📅 Using event: {event.name}')

        # Create sponsors if requested
        if options['create_sponsors']:
            self.create_test_sponsors(event)

        # Check current sponsors
        self.check_sponsors(event)

        # Test badge generation
        if options['regenerate_badges']:
            self.regenerate_badges(event)
        else:
            self.test_single_badge(event)

        self.stdout.write('\n' + '=' * 60)
        self.stdout.write('💡 NEXT STEPS:')
        self.stdout.write('1. Add actual logo files to sponsors in Django admin')
        self.stdout.write('2. Run: python manage.py test_sponsor_badges --regenerate-badges')
        self.stdout.write('3. Check generated badges for sponsor logos')

    def create_test_sponsors(self, event):
        """Create test sponsors"""
        self.stdout.write('\n🏢 Creating test sponsors...')
        
        sponsors_data = [
            {'name': 'Tech Solutions Inc', 'sponsor_type': 'platinum'},
            {'name': 'Innovation Partners', 'sponsor_type': 'gold'},
            {'name': 'Future Systems', 'sponsor_type': 'silver'},
            {'name': 'Digital Dynamics', 'sponsor_type': 'bronze'},
            {'name': 'Smart Technologies', 'sponsor_type': 'partner'},
        ]

        for i, sponsor_data in enumerate(sponsors_data):
            sponsor, created = EventSponsor.objects.get_or_create(
                event=event,
                name=sponsor_data['name'],
                defaults={
                    'sponsor_type': sponsor_data['sponsor_type'],
                    'description': f'{sponsor_data["name"]} - Supporting innovation in education',
                    'display_order': i + 1,
                    'is_active': True
                }
            )
            
            status = 'created' if created else 'exists'
            self.stdout.write(f'✅ Sponsor: {sponsor.name} ({status})')

    def check_sponsors(self, event):
        """Check existing sponsors"""
        self.stdout.write('\n🏢 Current sponsors:')
        
        sponsors = EventSponsor.objects.filter(event=event, is_active=True).order_by('display_order')
        
        if not sponsors:
            self.stdout.write(self.style.WARNING('❌ No sponsors found'))
            self.stdout.write('   Run with --create-sponsors to add test sponsors')
            return

        for sponsor in sponsors:
            logo_status = "✅ Has logo" if sponsor.logo else "❌ No logo"
            self.stdout.write(f'   • {sponsor.name} ({sponsor.sponsor_type}) - {logo_status}')
            if sponsor.logo:
                try:
                    # Check if logo file exists
                    if sponsor.logo.path:
                        self.stdout.write(f'     📁 Logo: {sponsor.logo.path}')
                except:
                    self.stdout.write(f'     ❌ Logo file missing')

    def test_single_badge(self, event):
        """Test badge generation with one participant"""
        self.stdout.write('\n🎨 Testing badge generation...')
        
        participants = Participant.objects.filter(event=event)[:1]
        if not participants:
            self.stdout.write(self.style.ERROR('❌ No participants found'))
            return

        participant = participants[0]
        self.stdout.write(f'👤 Testing with: {participant.full_name}')

        try:
            badge, created = Badge.objects.get_or_create(participant=participant)
            
            # Force regeneration
            badge.is_generated = False
            badge.badge_image = None
            badge.save()
            
            # Generate badge
            badge_img = badge.generate_badge()
            
            self.stdout.write(self.style.SUCCESS('✅ Badge generated successfully!'))
            if badge.badge_image:
                self.stdout.write(f'📁 Saved to: {badge.badge_image.path}')
            
        except Exception as e:
            self.stdout.write(self.style.ERROR(f'❌ Error: {e}'))

    def regenerate_badges(self, event):
        """Regenerate all badges for the event"""
        self.stdout.write('\n🔄 Regenerating all badges...')
        
        participants = Participant.objects.filter(event=event)
        total = participants.count()
        
        if total == 0:
            self.stdout.write(self.style.ERROR('❌ No participants found'))
            return

        success_count = 0
        error_count = 0

        for i, participant in enumerate(participants, 1):
            try:
                badge, created = Badge.objects.get_or_create(participant=participant)
                
                # Force regeneration
                badge.is_generated = False
                badge.badge_image = None
                badge.save()
                
                # Generate badge
                badge.generate_badge()
                
                success_count += 1
                self.stdout.write(f'✅ [{i}/{total}] {participant.full_name}')
                
            except Exception as e:
                error_count += 1
                self.stdout.write(f'❌ [{i}/{total}] {participant.full_name}: {e}')

        self.stdout.write(f'\n📊 RESULTS:')
        self.stdout.write(f'   ✅ Success: {success_count}')
        self.stdout.write(f'   ❌ Errors: {error_count}')
        self.stdout.write(f'   📈 Success rate: {(success_count/total)*100:.1f}%')
