#!/usr/bin/env python
"""
Script to update Event ID 6 with Google Maps embed
"""
import os
import sys
import django

# Setup Django
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'event_management.settings')
django.setup()

from events.models import Event

def update_event_map():
    """Update Event ID 6 with Google Maps embed"""
    
    print("🗺️ Updating Event ID 6 with Google Maps embed...")
    
    # Google Maps embed iframe for University of Gondar
    map_embed = '''<iframe src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d5921.959733237419!2d37.43740867557601!3d12.58941299257301!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x164328c02d6cc4b1%3A0x57e4ce60a16af1c4!2zVW5pdmVyc2l0eSBvZiBHb25kYXIgLSDhjI7hipXhi7DhiK0g4Yup4YqS4Ymo4Yir4Yiy4Ymy!5e1!3m2!1sen!2set!4v1754096354271!5m2!1sen!2set" width="600" height="450" style="border:0;" allowfullscreen="" loading="lazy" referrerpolicy="no-referrer-when-downgrade"></iframe>'''
    
    try:
        # Get the event
        event = Event.objects.get(id=6)
        
        # Update the map_embed field
        event.map_embed = map_embed
        event.save()
        
        print(f"✅ Successfully updated event: {event.name}")
        print(f"   Event ID: {event.id}")
        print(f"   Map embed added: {len(map_embed)} characters")
        
    except Event.DoesNotExist:
        print("❌ Event with ID 6 not found!")
        return False
    
    except Exception as e:
        print(f"❌ Error updating event: {str(e)}")
        return False
    
    return True

if __name__ == "__main__":
    success = update_event_map()
    if success:
        print("\n🎉 Event map embed updated successfully!")
        print("   The University of Gondar location map is now embedded in the event.")
    else:
        print("\n💥 Failed to update event map embed.")
