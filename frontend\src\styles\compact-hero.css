/* Compact Hero Section Styles */
.hero-section-compact {
  position: relative;
  min-height: 60vh;
  max-height: 80vh;
  width: 100vw;
  margin-left: calc(-50vw + 50%);
  display: flex;
  align-items: center;
  overflow: hidden;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.hero-background-compact {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1;
}

.hero-bg-image {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
}

.hero-overlay-compact {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(102, 126, 234, 0.8) 0%, rgba(118, 75, 162, 0.8) 100%);
  z-index: 2;
}

.hero-content-compact {
  position: relative;
  z-index: 3;
  padding: 2rem 0;
}

.hero-text-compact {
  color: white;
}

.hero-title-compact {
  font-size: 3rem;
  font-weight: 700;
  margin-bottom: 1rem;
  line-height: 1.2;
}

.hero-subtitle-compact {
  display: block;
  font-size: 1.2rem;
  font-weight: 400;
  color: rgba(255, 255, 255, 0.8);
  margin-top: 0.5rem;
}

.featured-event-card {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border-radius: 15px;
  padding: 1.5rem;
  margin: 1.5rem 0;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.event-status-compact {
  margin-bottom: 1rem;
}

.status {
  display: inline-block;
  padding: 0.5rem 1rem;
  border-radius: 25px;
  font-size: 0.9rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.status.upcoming {
  background: linear-gradient(45deg, #28a745, #20c997);
  color: white;
}

.status.live {
  background: linear-gradient(45deg, #dc3545, #fd7e14);
  color: white;
  animation: pulse 2s infinite;
}

.status.past {
  background: linear-gradient(45deg, #6c757d, #495057);
  color: white;
}

@keyframes pulse {
  0% { transform: scale(1); }
  50% { transform: scale(1.05); }
  100% { transform: scale(1); }
}

.event-title-compact {
  font-size: 1.8rem;
  font-weight: 600;
  margin-bottom: 1rem;
  color: white;
}

.event-description-compact {
  font-size: 1.1rem;
  color: rgba(255, 255, 255, 0.9);
  margin-bottom: 1.5rem;
  line-height: 1.6;
}

.event-meta-compact {
  display: flex;
  flex-wrap: wrap;
  gap: 1.5rem;
  margin-bottom: 1.5rem;
}

.event-meta-compact span {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: rgba(255, 255, 255, 0.8);
  font-size: 0.95rem;
}

.event-meta-compact i {
  color: #ffd700;
}

.hero-actions-compact {
  display: flex;
  gap: 1rem;
  flex-wrap: wrap;
}

.btn-compact {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.8rem 2rem;
  border-radius: 25px;
  text-decoration: none;
  font-weight: 600;
  font-size: 1rem;
  transition: all 0.3s ease;
  border: none;
  cursor: pointer;
}

.btn-primary-compact {
  background: linear-gradient(45deg, #007bff, #0056b3);
  color: white;
  box-shadow: 0 4px 15px rgba(0, 123, 255, 0.3);
}

.btn-primary-compact:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 123, 255, 0.4);
  color: white;
  text-decoration: none;
}

.btn-secondary-compact {
  background: rgba(255, 255, 255, 0.2);
  color: white;
  border: 2px solid rgba(255, 255, 255, 0.3);
  backdrop-filter: blur(10px);
}

.btn-secondary-compact:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: translateY(-2px);
  color: white;
  text-decoration: none;
}

.hero-event-selector {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border-radius: 15px;
  padding: 1.5rem;
  border: 1px solid rgba(255, 255, 255, 0.2);
  max-height: 400px;
  overflow-y: auto;
}

.selector-title {
  color: white;
  font-size: 1.5rem;
  font-weight: 600;
  margin-bottom: 1.5rem;
  text-align: center;
}

.event-selector-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.event-selector-item {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1rem;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 15px;
  cursor: pointer;
  transition: all 0.3s ease;
  border: 2px solid transparent;
}

.event-selector-item:hover {
  background: rgba(255, 255, 255, 0.2);
  transform: translateY(-2px);
}

.event-selector-item.active {
  background: rgba(255, 255, 255, 0.3);
  border-color: rgba(255, 255, 255, 0.5);
  box-shadow: 0 4px 15px rgba(255, 255, 255, 0.2);
}

.event-selector-image {
  width: 60px;
  height: 60px;
  border-radius: 10px;
  overflow: hidden;
  flex-shrink: 0;
}

.event-selector-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.placeholder-image {
  width: 100%;
  height: 100%;
  background: linear-gradient(45deg, #667eea, #764ba2);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 1.5rem;
}

.event-selector-content {
  flex: 1;
  color: white;
}

.event-selector-content h4 {
  font-size: 1rem;
  font-weight: 600;
  margin-bottom: 0.25rem;
  color: white;
}

.event-selector-content p {
  font-size: 0.9rem;
  color: rgba(255, 255, 255, 0.8);
  margin-bottom: 0.25rem;
}

.participant-count {
  font-size: 0.8rem;
  color: rgba(255, 255, 255, 0.6);
}

/* Responsive Design */
@media (max-width: 768px) {
  .hero-section-compact {
    min-height: 50vh;
    max-height: 70vh;
  }

  .hero-content-compact {
    padding: 1.5rem 0;
  }

  .hero-title-compact {
    font-size: 1.8rem;
  }

  .hero-subtitle-compact {
    font-size: 0.9rem;
  }

  .featured-event-card {
    padding: 1rem;
    margin: 1rem 0;
  }

  .event-meta-compact {
    flex-direction: column;
    gap: 0.5rem;
  }

  .hero-actions-compact {
    flex-direction: column;
    gap: 0.5rem;
  }

  .btn-compact {
    text-align: center;
    justify-content: center;
    padding: 0.6rem 1.5rem;
    font-size: 0.9rem;
  }

  .hero-event-selector {
    margin-top: 1.5rem;
    padding: 1rem;
    max-height: 300px;
  }

  .event-selector-item {
    padding: 0.8rem;
  }

  .event-selector-image {
    width: 50px;
    height: 50px;
  }
}
