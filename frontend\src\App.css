/* Custom styles for Event Management System */

.App {
  min-height: 100vh;
}

/* Bulk Email Announcement Styles */
.bg-gradient-primary {
  background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%) !important;
}

.bulk-email-card {
  border: none;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  border-radius: 15px;
  overflow: hidden;
}

.bulk-email-header {
  background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
  color: white;
  padding: 2rem;
  text-align: center;
}

.bulk-email-form .form-label {
  font-weight: 600;
  color: #333;
  margin-bottom: 0.75rem;
}

.bulk-email-form .form-control,
.bulk-email-form .form-select {
  border: 2px solid #e9ecef;
  border-radius: 8px;
  padding: 0.75rem;
  font-size: 1rem;
  transition: all 0.3s ease;
}

.bulk-email-form .form-control:focus,
.bulk-email-form .form-select:focus {
  border-color: #2a5298;
  box-shadow: 0 0 0 0.2rem rgba(42, 82, 152, 0.25);
}

.recipient-selection {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
  margin-bottom: 1rem;
}

.recipient-option {
  padding: 1rem;
  border: 2px solid #e9ecef;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  text-align: center;
  background: white;
}

.recipient-option:hover {
  border-color: #2a5298;
  background-color: #f8f9fa;
  transform: translateY(-2px);
}

.recipient-option.selected {
  border-color: #2a5298;
  background-color: #e3f2fd;
  color: #1e3c72;
}

.participant-types-section {
  background-color: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  padding: 1.5rem;
  margin-top: 1rem;
}

.preview-section {
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border: 1px solid #dee2e6;
  border-radius: 12px;
  padding: 1.5rem;
  margin-top: 2rem;
}

.preview-content {
  background: white;
  border: 1px solid #dee2e6;
  border-radius: 8px;
  padding: 1rem;
  min-height: 120px;
  white-space: pre-line;
  font-family: inherit;
}

.send-button {
  background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
  border: none;
  border-radius: 50px;
  padding: 1rem 3rem;
  font-size: 1.1rem;
  font-weight: 600;
  color: white;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(42, 82, 152, 0.3);
}

.send-button:hover:not(:disabled) {
  transform: translateY(-3px);
  box-shadow: 0 6px 20px rgba(42, 82, 152, 0.4);
}

.send-button:disabled {
  opacity: 0.7;
  cursor: not-allowed;
  transform: none;
}

/* Hover effects */
.hover-shadow {
  transition: box-shadow 0.3s ease-in-out;
}

.hover-shadow:hover {
  box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15) !important;
}

/* Custom button styles */
.btn {
  border-radius: 0.5rem;
  font-weight: 500;
  transition: all 0.3s ease;
}

.btn:hover {
  transform: translateY(-1px);
}

/* Card styles */
.card {
  border-radius: 1rem;
  border: none;
  transition: all 0.3s ease;
}

.card-header {
  border-radius: 1rem 1rem 0 0 !important;
  border-bottom: none;
  font-weight: 600;
}

/* Form styles */
.form-control, .form-select {
  border-radius: 0.5rem;
  border: 1px solid #dee2e6;
  transition: all 0.3s ease;
}

.form-control:focus, .form-select:focus {
  border-color: #0d6efd;
  box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
}

/* Badge styles */
.badge {
  font-weight: 500;
  padding: 0.5em 0.75em;
}

/* Navbar styles */
.navbar-brand {
  font-weight: 700;
  font-size: 1.5rem;
}

/* Hero section */
.bg-primary {
  background: linear-gradient(135deg, #0d6efd 0%, #0056b3 100%) !important;
}

/* Loading spinner */
.spinner-border {
  width: 3rem;
  height: 3rem;
}

/* Responsive images */
.card-img-top {
  transition: transform 0.3s ease;
}

.card:hover .card-img-top {
  transform: scale(1.05);
}

/* Date picker styles */
.react-datepicker-wrapper {
  width: 100%;
}

.react-datepicker__input-container input {
  width: 100%;
  padding: 0.375rem 0.75rem;
  border: 1px solid #dee2e6;
  border-radius: 0.5rem;
  font-size: 1rem;
  line-height: 1.5;
  color: #212529;
  background-color: #fff;
  transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

.react-datepicker__input-container input:focus {
  border-color: #0d6efd;
  outline: 0;
  box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
}

/* Alert styles */
.alert {
  border-radius: 0.75rem;
  border: none;
}

/* Layout fixes to prevent footer overlap */
.main-content-wrapper {
  min-height: auto !important;
  padding-bottom: 80px !important;
}

/* Ensure proper spacing for content in hero */
.hero-content-wrapper {
  margin-bottom: 80px !important;
  padding-bottom: 80px !important;
  min-height: auto !important;
}

/* Footer positioning */
.footer-section {
  margin-top: auto;
  position: relative;
  z-index: 10;
}

/* Ensure body has proper min-height */
body {
  min-height: 100vh;
}

/* Fix for pages with content in hero */
.content-card {
  margin-bottom: 80px !important;
  min-height: auto !important;
  overflow: visible !important;
}

/* Fix for dynamic content pages */
.hero-section {
  min-height: auto !important;
  height: auto !important;
  overflow: visible !important;
}

/* QR Scanner Mobile Optimizations */
.qr-scanner-container {
  max-width: 100%;
  margin: 0 auto;
}

/* Mobile QR Scanner Styles */
@media (max-width: 768px) {
  #qr-reader {
    width: 100% !important;
    max-width: 350px !important;
    margin: 0 auto !important;
  }

  #qr-reader > div {
    border-radius: 15px !important;
    overflow: hidden !important;
  }

  #qr-reader video {
    border-radius: 15px !important;
    width: 100% !important;
    height: auto !important;
    object-fit: cover !important;
  }

  /* QR Scanner controls for mobile */
  #qr-reader__dashboard_section {
    padding: 10px !important;
  }

  #qr-reader__dashboard_section_csr {
    margin: 10px 0 !important;
  }

  /* Camera selection dropdown for mobile */
  #qr-reader__camera_selection {
    width: 100% !important;
    margin-bottom: 10px !important;
    padding: 8px !important;
    border-radius: 8px !important;
    border: 2px solid #dee2e6 !important;
    font-size: 14px !important;
  }

  /* Start/Stop button styling for mobile */
  #qr-reader__camera_permission_button,
  #qr-reader__stop_button {
    width: 100% !important;
    padding: 12px !important;
    font-size: 16px !important;
    border-radius: 8px !important;
    margin: 5px 0 !important;
  }

  /* Mobile-friendly button improvements */
  .btn-lg.px-4.py-3 {
    font-size: 18px !important;
    padding: 15px 25px !important;
    width: 100% !important;
    margin: 10px 0 !important;
  }
}

/* QR Scanner success/error states */
.qr-scanner-success {
  background-color: #d4edda !important;
  border-color: #c3e6cb !important;
  color: #155724 !important;
  padding: 15px !important;
  border-radius: 8px !important;
  margin: 10px 0 !important;
}

.qr-scanner-error {
  background-color: #f8d7da !important;
  border-color: #f5c6cb !important;
  color: #721c24 !important;
  padding: 15px !important;
  border-radius: 8px !important;
  margin: 10px 0 !important;
}
