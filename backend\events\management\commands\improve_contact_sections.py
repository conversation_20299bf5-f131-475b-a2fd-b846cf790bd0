from django.core.management.base import BaseCommand
from events.models import EmailTemplate


class Command(BaseCommand):
    help = 'Improve contact person sections in all email templates'

    def handle(self, *args, **options):
        self.stdout.write("Improving contact person sections in all email templates...")
        
        # Enhanced contact section HTML
        enhanced_contact_html = '''
            <!-- Enhanced Contact Information -->
            <div style="background: white; margin: 20px 0; padding: 25px; border-radius: 10px; box-shadow: 0 2px 8px rgba(0,0,0,0.1); border-top: 4px solid #4CAF50;">
                <h3 style="color: #2e7d32; margin-top: 0; font-size: 18px;">👤 Your Dedicated Contact Person</h3>
                
                <!-- Contact Person Card -->
                <div style="background: #e8f5e8; border-radius: 10px; padding: 20px; margin-bottom: 20px;">
                    <div style="display: flex; align-items: center; margin-bottom: 15px;">
                        <div style="background: #4CAF50; color: white; width: 50px; height: 50px; border-radius: 50%; display: flex; align-items: center; justify-content: center; font-size: 24px; margin-right: 15px;">👤</div>
                        <div>
                            <h4 style="margin: 0; color: #2e7d32; font-size: 18px;">{{contact_person_name|default:"Event Coordinator"}}</h4>
                            <p style="margin: 5px 0 0 0; color: #666; font-size: 14px;">{{contact_person_title|default:"University of Gondar Events Team"}}</p>
                        </div>
                    </div>
                    
                    <!-- Contact Methods -->
                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 15px;">
                        <div style="background: white; padding: 15px; border-radius: 8px; border-left: 4px solid #4CAF50;">
                            <div style="display: flex; align-items: center; margin-bottom: 8px;">
                                <span style="font-size: 18px; margin-right: 8px;">📧</span>
                                <strong style="color: #2e7d32;">Email</strong>
                            </div>
                            <a href="mailto:{{contact_person_email|default:'<EMAIL>'}}" style="color: #1976d2; text-decoration: none; font-size: 14px;">
                                {{contact_person_email|default:"<EMAIL>"}}
                            </a>
                        </div>
                        <div style="background: white; padding: 15px; border-radius: 8px; border-left: 4px solid #2196F3;">
                            <div style="display: flex; align-items: center; margin-bottom: 8px;">
                                <span style="font-size: 18px; margin-right: 8px;">📞</span>
                                <strong style="color: #1976d2;">Phone</strong>
                            </div>
                            <a href="tel:{{contact_person_phone|default:'+251-58-114-0481'}}" style="color: #1976d2; text-decoration: none; font-size: 14px;">
                                {{contact_person_phone|default:"+251-58-114-0481"}}
                            </a>
                        </div>
                    </div>
                </div>
                
                <!-- Additional Support -->
                <div style="background: #f8f9fa; border-radius: 8px; padding: 15px;">
                    <h4 style="color: #495057; margin-top: 0; margin-bottom: 15px;">🏢 Additional Support</h4>
                    <div style="display: grid; grid-template-columns: 1fr 1fr 1fr; gap: 15px;">
                        <div style="text-align: center;">
                            <div style="font-size: 24px; margin-bottom: 5px;">🕐</div>
                            <div style="font-weight: bold; color: #495057; font-size: 12px;">Office Hours</div>
                            <div style="color: #666; font-size: 11px;">Mon-Fri<br>8:00 AM - 5:00 PM</div>
                        </div>
                        <div style="text-align: center;">
                            <div style="font-size: 24px; margin-bottom: 5px;">🚨</div>
                            <div style="font-weight: bold; color: #495057; font-size: 12px;">Emergency</div>
                            <div style="color: #666; font-size: 11px;">24/7 Support<br>During Events</div>
                        </div>
                        <div style="text-align: center;">
                            <div style="font-size: 24px; margin-bottom: 5px;">💬</div>
                            <div style="font-weight: bold; color: #495057; font-size: 12px;">Response Time</div>
                            <div style="color: #666; font-size: 11px;">Within 2 Hours<br>Business Days</div>
                        </div>
                    </div>
                </div>
                
                <!-- Quick Actions -->
                <div style="margin-top: 20px;">
                    <h4 style="color: #495057; margin-bottom: 15px;">⚡ Quick Actions</h4>
                    <div style="display: flex; gap: 10px; flex-wrap: wrap;">
                        <a href="mailto:{{contact_person_email|default:'<EMAIL>'}}?subject=Question about {{event_name}}" 
                           style="background: #4CAF50; color: white; padding: 8px 15px; border-radius: 20px; text-decoration: none; font-size: 12px; font-weight: bold;">
                            📧 Send Email
                        </a>
                        <a href="tel:{{contact_person_phone|default:'+251-58-114-0481'}}" 
                           style="background: #2196F3; color: white; padding: 8px 15px; border-radius: 20px; text-decoration: none; font-size: 12px; font-weight: bold;">
                            📞 Call Now
                        </a>
                        <span style="background: #ff9800; color: white; padding: 8px 15px; border-radius: 20px; font-size: 12px; font-weight: bold;">
                            🌐 Visit Website
                        </span>
                    </div>
                </div>
            </div>'''
        
        # Enhanced contact section for text emails
        enhanced_contact_text = '''
Your Dedicated Contact Person:
{contact_person_name} - {contact_person_title}

Contact Information:
- Email: {contact_person_email}
- Phone: {contact_person_phone}

Additional Support:
- Office Hours: Monday - Friday, 8:00 AM - 5:00 PM
- Emergency Support: 24/7 during events
- Response Time: Within 2 hours on business days

Quick Actions:
- Send Email: {contact_person_email}
- Call Now: {contact_person_phone}
- Visit our website for more information'''
        
        # Templates to update
        templates_to_update = [
            'registration_confirmation',
            'event_details', 
            'event_reminder',
            'daily_gallery',
            'schedule_update',
            'badge_notification'
        ]
        
        updated_count = 0
        
        for template_type in templates_to_update:
            try:
                template = EmailTemplate.objects.get(template_type=template_type)
                
                # Update HTML content - replace existing contact section
                html_content = template.html_content
                
                # Find and replace existing contact sections
                contact_patterns = [
                    '<!-- Contact Information -->',
                    '<div style="background: #e8f5e8; border-left: 4px solid #4CAF50;',
                    '📞 Need Help?',
                    '📞 Questions?',
                    '📞 Last-Minute Questions?',
                    '📞 Questions About Schedule Changes?'
                ]
                
                # Find the start of any existing contact section
                contact_start = -1
                for pattern in contact_patterns:
                    pos = html_content.find(pattern)
                    if pos != -1:
                        contact_start = pos
                        break
                
                if contact_start != -1:
                    # Find the end of the contact section (before footer)
                    footer_start = html_content.find('<!-- Footer -->')
                    if footer_start == -1:
                        footer_start = html_content.find('<div style="background: #f8f9fa; padding: 20px; text-align: center;')
                    
                    if footer_start != -1:
                        # Replace the contact section
                        before_contact = html_content[:contact_start]
                        after_contact = html_content[footer_start:]
                        template.html_content = before_contact + enhanced_contact_html + '\n        \n        ' + after_contact
                    else:
                        # Append before closing body tag
                        body_end = html_content.rfind('</div>\n    </div>\n</body>')
                        if body_end != -1:
                            before_body_end = html_content[:body_end]
                            after_body_end = html_content[body_end:]
                            template.html_content = before_body_end + enhanced_contact_html + '\n        \n        ' + after_body_end
                else:
                    # Append before footer if no existing contact section found
                    footer_start = html_content.find('<!-- Footer -->')
                    if footer_start != -1:
                        before_footer = html_content[:footer_start]
                        after_footer = html_content[footer_start:]
                        template.html_content = before_footer + enhanced_contact_html + '\n        \n        ' + after_footer
                
                # Update text content - replace existing contact section
                text_content = template.text_content
                
                # Find and replace existing contact sections in text
                text_patterns = [
                    'Need help?',
                    'Questions?',
                    'Last-minute questions?',
                    'Questions about',
                    'Contact us at'
                ]
                
                text_contact_start = -1
                for pattern in text_patterns:
                    pos = text_content.lower().find(pattern.lower())
                    if pos != -1:
                        text_contact_start = pos
                        break
                
                if text_contact_start != -1:
                    # Find the end of contact section (before signature)
                    signature_start = text_content.find('Best regards,')
                    if signature_start != -1:
                        before_contact = text_content[:text_contact_start]
                        after_contact = text_content[signature_start:]
                        template.text_content = before_contact + enhanced_contact_text + '\n\n' + after_contact
                    else:
                        # Append at the end
                        template.text_content = text_content + '\n\n' + enhanced_contact_text
                else:
                    # Append before signature
                    signature_start = text_content.find('Best regards,')
                    if signature_start != -1:
                        before_signature = text_content[:signature_start]
                        after_signature = text_content[signature_start:]
                        template.text_content = before_signature + enhanced_contact_text + '\n\n' + after_signature
                
                template.save()
                updated_count += 1
                self.stdout.write(f"✓ Updated {template_type} template with enhanced contact section")
                
            except EmailTemplate.DoesNotExist:
                self.stdout.write(f"⚠️ Template {template_type} not found")
            except Exception as e:
                self.stdout.write(f"❌ Error updating {template_type}: {e}")
        
        self.stdout.write(f"\n✅ Enhanced contact sections in {updated_count} email templates!")
        self.stdout.write("Contact person sections now include:")
        self.stdout.write("  • Dedicated contact person card with photo placeholder")
        self.stdout.write("  • Email and phone with clickable links")
        self.stdout.write("  • Office hours and response time information")
        self.stdout.write("  • Quick action buttons for immediate contact")
        self.stdout.write("  • Professional design matching other sections")
