#!/usr/bin/env python
"""
Test the new SVG badge generation with real participants
"""
import os
import sys
import django

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'event_management.settings')
django.setup()

from participants.models import Participant
from badges.models import Badge

def test_real_participant_badge():
    """Test badge generation with a real participant"""
    print("🧪 Testing SVG Badge Generation with Real Participant")
    print("=" * 60)
    
    try:
        # Get first participant
        participant = Participant.objects.first()
        if not participant:
            print("❌ No participants found in database")
            return
        
        print(f"👤 Testing with: {participant.first_name} {participant.last_name}")
        print(f"📧 Email: {getattr(participant, 'email', 'N/A')}")
        print(f"🏢 Institution: {getattr(participant, 'institution_name', 'N/A')}")
        print(f"🎯 Type: {participant.participant_type.name if participant.participant_type else 'N/A'}")
        
        # Get or create badge
        badge, created = Badge.objects.get_or_create(participant=participant)
        print(f"🎫 Badge {'created' if created else 'found'}")
        
        # Test the new SVG generation
        print("🎨 Generating SVG-style badge...")
        badge_img = badge.generate_badge()
        
        print(f"✅ Badge generated successfully!")
        print(f"📏 Dimensions: {badge_img.size}")
        print(f"💾 Badge saved: {badge.badge_image.name if badge.badge_image else 'Not saved'}")
        print(f"🔗 QR Code: {badge.qr_code_image.name if badge.qr_code_image else 'Not saved'}")
        
        # Test badge properties
        print(f"🔄 Generation status: {'Generated' if badge.is_generated else 'Not generated'}")
        print(f"📅 Generated at: {badge.generated_at}")
        
        print("\n🎉 Real participant badge generation test successful!")
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_real_participant_badge()
