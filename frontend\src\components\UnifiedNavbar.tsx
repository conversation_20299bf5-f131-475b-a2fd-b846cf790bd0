import React from 'react';
import { Link, useLocation } from 'react-router-dom';
import { useBranding } from '../hooks/useBranding';
import { getMediaUrl } from '../services/api';

const UnifiedNavbar: React.FC = () => {
  const location = useLocation();
  const { organization } = useBranding();
  const isHomePage = location.pathname === '/';

  const handleSectionClick = (sectionId: string) => {
    if (isHomePage) {
      // If on home page, smooth scroll to section
      const element = document.getElementById(sectionId);
      if (element) {
        element.scrollIntoView({ behavior: 'smooth' });
      }
    } else {
      // If not on home page, navigate to home with section
      window.location.href = `/#${sectionId}`;
    }
  };

  return (
    <nav className="navbar navbar-expand-lg navbar-light bg-white fixed-top shadow-lg modern-navbar">
      <div className="container">
        {/* Brand */}
        <Link to="/" className="navbar-brand d-flex align-items-center brand-logo">
          {organization?.logo ? (
            <img
              src={getMediaUrl(organization.logo)}
              alt={organization.name}
              className="brand-image"
              style={{ height: '45px', width: 'auto', marginRight: '12px' }}
            />
          ) : (
            <div className="brand-icon-wrapper me-3">
              <i className="fas fa-university text-primary" style={{ fontSize: '1.8rem' }}></i>
            </div>
          )}
          <div className="d-flex flex-column brand-text">
            <span className="fw-bold text-primary brand-title" style={{ fontSize: '1.2rem', lineHeight: '1.2' }}>
              {organization?.short_name || 'UoG'} Events
            </span>
            <small className="text-muted brand-subtitle" style={{ fontSize: '0.75rem', lineHeight: '1' }}>
              {organization?.name || 'University of Gondar'}
            </small>
          </div>
        </Link>

        {/* Mobile Toggle */}
        <button
          className="navbar-toggler modern-toggler"
          type="button"
          data-bs-toggle="collapse"
          data-bs-target="#navbarNav"
          aria-controls="navbarNav"
          aria-expanded="false"
          aria-label="Toggle navigation"
        >
          <span></span>
          <span></span>
          <span></span>
        </button>

        {/* Navigation Menu */}
        <div className="collapse navbar-collapse" id="navbarNav">
          <ul className="navbar-nav ms-auto modern-nav">
            {/* Home */}
            <li className="nav-item">
              <Link
                to="/"
                className={`nav-link modern-nav-link ${location.pathname === '/' ? 'active' : ''}`}
              >
                <i className="fas fa-home me-2"></i>
                <span>Home</span>
              </Link>
            </li>

            {/* Events */}
            <li className="nav-item">
              <a
                href="/#events"
                className={`nav-link modern-nav-link ${location.pathname === '/events' ? 'active' : ''}`}
              >
                <i className="fas fa-calendar-alt me-2"></i>
                <span>Events</span>
              </a>
            </li>

            {/* Gondar City */}
            <li className="nav-item">
              <a
                href="#gondar-city"
                className="nav-link modern-nav-link"
                onClick={(e) => {
                  e.preventDefault();
                  handleSectionClick('gondar-city');
                }}
              >
                <i className="fas fa-city me-2"></i>
                <span>Gondar City</span>
              </a>
            </li>

            {/* University */}
            <li className="nav-item">
              <a
                href="#university"
                className="nav-link modern-nav-link"
                onClick={(e) => {
                  e.preventDefault();
                  handleSectionClick('university');
                }}
              >
                <i className="fas fa-university me-2"></i>
                <span>University</span>
              </a>
            </li>

            {/* Home Page Sections (only show if on home page or as navigation to home) */}
            {isHomePage ? (
              <>
                <li className="nav-item">
                  <a
                    href="#gallery"
                    className="nav-link modern-nav-link"
                    onClick={(e) => {
                      e.preventDefault();
                      handleSectionClick('gallery');
                    }}
                  >
                    <i className="fas fa-images me-2"></i>
                    <span>Gallery</span>
                  </a>
                </li>
                <li className="nav-item">
                  <a
                    href="#about"
                    className="nav-link modern-nav-link"
                    onClick={(e) => {
                      e.preventDefault();
                      handleSectionClick('about');
                    }}
                  >
                    <i className="fas fa-info-circle me-2"></i>
                    <span>About</span>
                  </a>
                </li>
                <li className="nav-item">
                  <a
                    href="#contact"
                    className="nav-link modern-nav-link"
                    onClick={(e) => {
                      e.preventDefault();
                      handleSectionClick('contact');
                    }}
                  >
                    <i className="fas fa-envelope me-2"></i>
                    <span>Contact</span>
                  </a>
                </li>
              </>
            ) : (
              <>
                <li className="nav-item">
                  <a
                    href="/#gallery"
                    className="nav-link modern-nav-link"
                    onClick={() => handleSectionClick('gallery')}
                  >
                    <i className="fas fa-images me-2"></i>
                    <span>Gallery</span>
                  </a>
                </li>
                <li className="nav-item">
                  <a
                    href="/#about"
                    className="nav-link modern-nav-link"
                    onClick={() => handleSectionClick('about')}
                  >
                    <i className="fas fa-info-circle me-2"></i>
                    <span>About</span>
                  </a>
                </li>
                <li className="nav-item">
                  <a
                    href="/#contact"
                    className="nav-link modern-nav-link"
                    onClick={() => handleSectionClick('contact')}
                  >
                    <i className="fas fa-envelope me-2"></i>
                    <span>Contact</span>
                  </a>
                </li>
              </>
            )}

            {/* CTA Button */}
            <li className="nav-item ms-3">
              <Link
                to="/register"
                className="btn btn-primary modern-cta-btn"
              >
                <i className="fas fa-user-plus me-2"></i>
                <span>Join Events</span>
              </Link>
            </li>
          </ul>
        </div>
      </div>
    </nav>
  );
};

export default UnifiedNavbar;
