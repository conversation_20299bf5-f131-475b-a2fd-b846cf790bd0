from django.core.management.base import BaseCommand
from events.models import EmailTemplate


class Command(BaseCommand):
    help = 'Enhance gallery and reminder email templates with professional design'

    def handle(self, *args, **options):
        self.stdout.write("Enhancing gallery and reminder email templates...")
        
        # Developer credits HTML
        developer_credits = '''
            <!-- Developer Credits -->
            <div style="margin-top: 20px; padding-top: 15px; border-top: 1px solid #dee2e6;">
                <p style="margin: 0 0 10px 0; font-weight: bold; color: #495057;">Developed by:</p>
                <div style="display: flex; justify-content: center; gap: 20px; flex-wrap: wrap;">
                    <a href="https://www.linkedin.com/in/tewodros-abebaw-chekol/" style="color: #0077b5; text-decoration: none; font-size: 13px;">
                        <strong>Tewodros Abebaw</strong>
                    </a>
                    <a href="https://www.linkedin.com/in/aragaw-mebratu-a3096514a/" style="color: #0077b5; text-decoration: none; font-size: 13px;">
                        <strong>Aragaw Mebratu</strong>
                    </a>
                    <a href="https://www.linkedin.com/in/meseret-teshale-bb24b767/" style="color: #0077b5; text-decoration: none; font-size: 13px;">
                        <strong>Meseret Teshale</strong>
                    </a>
                </div>
            </div>'''
        
        # Enhanced Daily Gallery Template
        try:
            gallery_template, created = EmailTemplate.objects.get_or_create(
                template_type='daily_gallery',
                defaults={
                    'name': 'Daily Gallery',
                    'subject': 'Daily Event Photos - {event_name}',
                    'is_active': True
                }
            )
            
            gallery_template.html_content = f"""<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>Daily Event Photos - University of Gondar</title>
</head>
<body style="font-family: Arial, sans-serif; margin: 0; padding: 20px; background-color: #f5f5f5;">
    <div style="max-width: 700px; margin: 0 auto; background-color: white; border-radius: 10px; overflow: hidden; box-shadow: 0 2px 10px rgba(0,0,0,0.1);">
        
        <!-- Header -->
        <div style="background: linear-gradient(135deg, #e91e63 0%, #ad1457 100%); color: white; padding: 30px; text-align: center;">
            <h1 style="margin: 0; font-size: 28px;">🎓 University of Gondar</h1>
            <h2 style="margin: 10px 0 0 0; font-size: 24px;">Daily Event Photos</h2>
            <div style="background: #4CAF50; color: white; padding: 10px 20px; border-radius: 25px; font-size: 16px; font-weight: bold; display: inline-block; margin-top: 15px;">
                📸 {{image_count}} Photos from {{date}}
            </div>
        </div>
        
        <!-- Content -->
        <div style="padding: 30px;">
            <h2 style="color: #333; margin-top: 0;">Hello {{participant_name}}!</h2>
            <p style="color: #666; line-height: 1.6; font-size: 16px;">
                Here are the beautiful moments captured during <strong style="color: #e91e63;">{{event_name}}</strong> on {{date}}.
            </p>
            
            <!-- Photo Package Info -->
            <div style="background: white; margin: 20px 0; padding: 25px; border-radius: 10px; box-shadow: 0 2px 8px rgba(0,0,0,0.1); border-top: 4px solid #e91e63;">
                <h3 style="color: #e91e63; margin-top: 0; font-size: 18px;">📦 Photo Package Details</h3>
                
                <!-- Stats Grid -->
                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 15px; margin-bottom: 20px;">
                    <div style="background: #f8f9fa; padding: 15px; border-radius: 8px; border-left: 4px solid #e91e63; text-align: center;">
                        <div style="font-size: 32px; color: #e91e63; margin-bottom: 5px;">📸</div>
                        <div style="font-weight: bold; color: #e91e63; margin-bottom: 5px;">Total Photos</div>
                        <div style="color: #333; font-size: 24px; font-weight: bold;">{{image_count}}</div>
                    </div>
                    <div style="background: #f8f9fa; padding: 15px; border-radius: 8px; border-left: 4px solid #4CAF50; text-align: center;">
                        <div style="font-size: 32px; color: #4CAF50; margin-bottom: 5px;">📅</div>
                        <div style="font-weight: bold; color: #4CAF50; margin-bottom: 5px;">Event Date</div>
                        <div style="color: #333; font-size: 18px; font-weight: bold;">{{date}}</div>
                    </div>
                </div>
                
                <!-- Download Instructions -->
                <div style="background: #e8f4fd; border: 1px solid #bee5eb; border-radius: 8px; padding: 20px;">
                    <h4 style="color: #0c5460; margin-top: 0; margin-bottom: 15px;">📥 Download Instructions</h4>
                    <ul style="color: #0c5460; margin: 0; padding-left: 20px; line-height: 1.6;">
                        <li><strong>ZIP File:</strong> All photos are included in the attached ZIP file</li>
                        <li><strong>High Quality:</strong> Photos are in high resolution for printing</li>
                        <li><strong>File Names:</strong> Each photo has a descriptive filename</li>
                        <li><strong>Usage Rights:</strong> Photos are for personal use and sharing</li>
                        <li><strong>Print Ready:</strong> Suitable for photo albums and printing</li>
                    </ul>
                </div>
            </div>
            
            <!-- Sharing Guidelines -->
            <div style="background: white; margin: 20px 0; padding: 25px; border-radius: 10px; box-shadow: 0 2px 8px rgba(0,0,0,0.1); border-top: 4px solid #ff9800;">
                <h3 style="color: #ff9800; margin-top: 0; font-size: 18px;">📱 Sharing Guidelines</h3>
                <ul style="color: #666; line-height: 1.6;">
                    <li><strong>Social Media:</strong> Feel free to share on your social media platforms</li>
                    <li><strong>Tag Us:</strong> Use #UniversityOfGondar #{{event_name}} when sharing</li>
                    <li><strong>Privacy:</strong> Please respect the privacy of other participants</li>
                    <li><strong>Credit:</strong> Photo credit: University of Gondar Events</li>
                </ul>
            </div>
            
            <!-- Contact Information -->
            <div style="background: #e8f5e8; border-left: 4px solid #4CAF50; padding: 20px; margin: 20px 0; border-radius: 5px;">
                <h3 style="color: #2e7d32; margin-top: 0;">📞 Questions?</h3>
                <p style="color: #666; margin-bottom: 15px;">If you have any questions about the photos or need additional copies:</p>
                <ul style="color: #666; line-height: 1.6;">
                    <li><strong>Email:</strong> <EMAIL></li>
                    <li><strong>Phone:</strong> +251-58-114-0481</li>
                    <li><strong>Office Hours:</strong> Monday - Friday, 8:00 AM - 5:00 PM</li>
                </ul>
            </div>
        </div>
        
        <!-- Footer -->
        <div style="background: #f8f9fa; padding: 20px; text-align: center; color: #666; font-size: 14px;">
            <p style="margin: 0;"><strong>University of Gondar Events</strong></p>
            <p style="margin: 5px 0;">Thank you for being part of our memorable event!</p>
            <p style="margin: 5px 0;">&copy; {{current_year}} University of Gondar. All rights reserved.</p>
            {developer_credits}
        </div>
    </div>
</body>
</html>"""
            
            gallery_template.text_content = """Daily Event Photos - {event_name}

Hello {participant_name},

Here are the photos from {event_name} for {date}.

Photo Package Details:
- Total Photos: {image_count}
- Date: {date}
- Format: High-resolution ZIP file

All photos are included in the attached ZIP file and are ready for printing and sharing.

Sharing Guidelines:
- Feel free to share on social media
- Use #UniversityOfGondar #{event_name} when sharing
- Please respect the privacy of other participants
- Photo credit: University of Gondar Events

Questions? Contact <NAME_EMAIL> or +251-58-114-0481

Best regards,
University of Gondar Events Team"""
            
            gallery_template.save()
            action = "Created" if created else "Updated"
            self.stdout.write(f"✓ {action} enhanced daily gallery template")
            
        except Exception as e:
            self.stdout.write(f"❌ Error with gallery template: {e}")
        
        # Enhanced Event Reminder Template
        try:
            reminder_template, created = EmailTemplate.objects.get_or_create(
                template_type='event_reminder',
                defaults={
                    'name': 'Event Reminder',
                    'subject': 'Reminder: {event_name} in {days_before} day(s)',
                    'is_active': True
                }
            )
            
            reminder_template.html_content = f"""<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>Event Reminder - University of Gondar</title>
</head>
<body style="font-family: Arial, sans-serif; margin: 0; padding: 20px; background-color: #f5f5f5;">
    <div style="max-width: 700px; margin: 0 auto; background-color: white; border-radius: 10px; overflow: hidden; box-shadow: 0 2px 10px rgba(0,0,0,0.1);">
        
        <!-- Header -->
        <div style="background: linear-gradient(135deg, #ff9800 0%, #f57c00 100%); color: white; padding: 30px; text-align: center;">
            <h1 style="margin: 0; font-size: 28px;">🎓 University of Gondar</h1>
            <h2 style="margin: 10px 0 0 0; font-size: 24px;">Event Reminder</h2>
            <div style="background: #f44336; color: white; padding: 10px 20px; border-radius: 25px; font-size: 16px; font-weight: bold; display: inline-block; margin-top: 15px;">
                ⏰ {{days_before}} Day(s) Remaining
            </div>
        </div>
        
        <!-- Content -->
        <div style="padding: 30px;">
            <h2 style="color: #333; margin-top: 0;">Hello {{participant_name}}!</h2>
            <p style="color: #666; line-height: 1.6; font-size: 16px;">
                This is a friendly reminder that <strong style="color: #ff9800;">{{event_name}}</strong> is coming up soon!
            </p>
            
            <!-- Event Information -->
            <div style="background: white; margin: 20px 0; padding: 25px; border-radius: 10px; box-shadow: 0 2px 8px rgba(0,0,0,0.1); border-top: 4px solid #2196F3;">
                <h3 style="color: #2196F3; margin-top: 0; font-size: 18px;">📅 Event Information</h3>
                <table style="width: 100%; border-collapse: collapse;">
                    <tr>
                        <td style="padding: 10px 0; font-weight: bold; color: #1e3c72; width: 30%;">Event Name:</td>
                        <td style="padding: 10px 0; color: #333;">{{event_name}}</td>
                    </tr>
                    <tr>
                        <td style="padding: 10px 0; font-weight: bold; color: #1e3c72;">Date & Time:</td>
                        <td style="padding: 10px 0; color: #333;">{{event_date}}</td>
                    </tr>
                    <tr>
                        <td style="padding: 10px 0; font-weight: bold; color: #1e3c72;">Location:</td>
                        <td style="padding: 10px 0; color: #333;">{{event_location}}</td>
                    </tr>
                    <tr>
                        <td style="padding: 10px 0; font-weight: bold; color: #1e3c72;">Days Remaining:</td>
                        <td style="padding: 10px 0; color: #f44336; font-weight: bold; font-size: 18px;">{{days_before}}</td>
                    </tr>
                </table>
            </div>
            
            <!-- Countdown Section -->
            <div style="background: linear-gradient(135deg, #f44336 0%, #d32f2f 100%); color: white; padding: 25px; border-radius: 10px; text-align: center; margin: 20px 0;">
                <h3 style="margin-top: 0; color: white;">⏰ Countdown</h3>
                <div style="font-size: 48px; font-weight: bold; margin: 15px 0;">{{days_before}}</div>
                <div style="font-size: 18px;">Day(s) Until Event</div>
            </div>
            
            <!-- Pre-Event Checklist -->
            <div style="background: white; margin: 20px 0; padding: 25px; border-radius: 10px; box-shadow: 0 2px 8px rgba(0,0,0,0.1); border-top: 4px solid #4CAF50;">
                <h3 style="color: #4CAF50; margin-top: 0; font-size: 18px;">✅ Pre-Event Checklist</h3>
                <ul style="color: #666; line-height: 1.8; font-size: 16px;">
                    <li><strong>📱 Print your event badge</strong> - Bring your personalized badge for check-in</li>
                    <li><strong>⏰ Arrive 15 minutes early</strong> - Allow time for registration and networking</li>
                    <li><strong>📋 Review the agenda</strong> - Check the latest schedule updates</li>
                    <li><strong>🎒 Bring required materials</strong> - Notebooks, pens, and any specified items</li>
                    <li><strong>📞 Save contact numbers</strong> - Keep organizer contacts handy</li>
                    <li><strong>🚗 Plan your transportation</strong> - Check traffic and parking options</li>
                </ul>
            </div>
            
            <!-- Important Reminders -->
            <div style="background: white; margin: 20px 0; padding: 25px; border-radius: 10px; box-shadow: 0 2px 8px rgba(0,0,0,0.1); border-top: 4px solid #ff9800;">
                <h3 style="color: #ff9800; margin-top: 0; font-size: 18px;">⚠️ Important Reminders</h3>
                <ul style="color: #666; line-height: 1.6;">
                    <li><strong>Dress Code:</strong> Business casual or formal attire recommended</li>
                    <li><strong>Meals:</strong> Refreshments will be provided as per schedule</li>
                    <li><strong>Parking:</strong> Free parking available on campus</li>
                    <li><strong>WiFi:</strong> Guest network details will be provided at check-in</li>
                    <li><strong>Photography:</strong> Event photos will be shared after the event</li>
                </ul>
            </div>
            
            <!-- Contact Information -->
            <div style="background: #e8f5e8; border-left: 4px solid #4CAF50; padding: 20px; margin: 20px 0; border-radius: 5px;">
                <h3 style="color: #2e7d32; margin-top: 0;">📞 Last-Minute Questions?</h3>
                <p style="color: #666; margin-bottom: 15px;">If you have any last-minute questions or need assistance:</p>
                <ul style="color: #666; line-height: 1.6;">
                    <li><strong>Email:</strong> <EMAIL></li>
                    <li><strong>Phone:</strong> +251-58-114-0481</li>
                    <li><strong>Emergency Contact:</strong> Available 24/7 during event week</li>
                </ul>
            </div>
        </div>
        
        <!-- Footer -->
        <div style="background: #f8f9fa; padding: 20px; text-align: center; color: #666; font-size: 14px;">
            <p style="margin: 0;"><strong>University of Gondar Events</strong></p>
            <p style="margin: 5px 0;">We're excited to see you at the event!</p>
            <p style="margin: 5px 0;">&copy; {{current_year}} University of Gondar. All rights reserved.</p>
            {developer_credits}
        </div>
    </div>
</body>
</html>"""
            
            reminder_template.text_content = """Event Reminder - {event_name}

Hello {participant_name},

This is a friendly reminder that {event_name} is coming up in {days_before} day(s)!

Event Information:
- Date & Time: {event_date}
- Location: {event_location}
- Days Remaining: {days_before}

Pre-Event Checklist:
- Print and bring your event badge
- Arrive 15 minutes early for check-in
- Review the latest agenda
- Bring required materials (notebooks, pens)
- Save organizer contact numbers
- Plan your transportation

Important Reminders:
- Dress Code: Business casual or formal attire
- Meals: Refreshments provided as per schedule
- Parking: Free parking available on campus
- WiFi: Guest network details at check-in

Last-minute questions? Contact <NAME_EMAIL> or +251-58-114-0481

We're excited to see you at the event!

Best regards,
University of Gondar Events Team"""
            
            reminder_template.save()
            action = "Created" if created else "Updated"
            self.stdout.write(f"✓ {action} enhanced event reminder template")
            
        except Exception as e:
            self.stdout.write(f"❌ Error with reminder template: {e}")
        
        self.stdout.write("Gallery and reminder templates enhancement completed!")
