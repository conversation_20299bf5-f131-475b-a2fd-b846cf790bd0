import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { eventService, Event } from '../services/api';
import ModernLayout from '../components/ModernLayout';
import '../styles/landing.css';
import '../styles/register.css';

const Register: React.FC = () => {
  const [events, setEvents] = useState<Event[]>([]);
  const [selectedEvent, setSelectedEvent] = useState<number | null>(null);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [filterType, setFilterType] = useState('all');

  useEffect(() => {
    const fetchEvents = async () => {
      try {
        const response = await eventService.getEvents();
        // Handle paginated response
        const eventsData = response.data.results || [];
        const upcomingEvents = eventsData.filter((event: Event) => new Date(event.start_date) > new Date());
        setEvents(upcomingEvents);
      } catch (error) {
        console.error('Error fetching events:', error);
        setEvents([]);
      } finally {
        setLoading(false);
      }
    };
    fetchEvents();
  }, []);

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  const formatDateShort = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric'
    });
  };

  const getDaysUntilEvent = (dateString: string) => {
    const eventDate = new Date(dateString);
    const today = new Date();
    const diffTime = eventDate.getTime() - today.getTime();
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    return diffDays;
  };

  const getEventStatus = (startDate: string, endDate: string) => {
    const now = new Date();
    const start = new Date(startDate);
    const end = new Date(endDate);

    if (now < start) {
      const daysUntil = getDaysUntilEvent(startDate);
      if (daysUntil <= 7) return { status: 'soon', label: 'Starting Soon', color: 'warning' };
      if (daysUntil <= 30) return { status: 'upcoming', label: 'Upcoming', color: 'info' };
      return { status: 'future', label: 'Future Event', color: 'secondary' };
    } else if (now >= start && now <= end) {
      return { status: 'ongoing', label: 'Ongoing', color: 'success' };
    } else {
      return { status: 'past', label: 'Past Event', color: 'dark' };
    }
  };

  const filteredEvents = events.filter(event => {
    const matchesSearch = event.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         event.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         event.location.toLowerCase().includes(searchTerm.toLowerCase());

    if (filterType === 'all') return matchesSearch;

    const status = getEventStatus(event.start_date, event.end_date);
    return matchesSearch && status.status === filterType;
  });

  if (loading) {
    return (
      <ModernLayout
        title="Event Registration"
        subtitle="Register for upcoming University of Gondar events"
        contentInHero={true}
        heroBackground="linear-gradient(135deg, #667eea 0%, #764ba2 100%)"
      >
        <div className="row justify-content-center">
          <div className="col-md-6 text-center">
            <div className="spinner-border text-primary" role="status">
              <span className="visually-hidden">Loading...</span>
            </div>
            <p className="mt-3">Loading registration form...</p>
          </div>
        </div>
      </ModernLayout>
    );
  }

  return (
    <ModernLayout
      title="Event Registration"
      subtitle="Discover and register for amazing events"
      contentInHero={true}
      className="register-page"
      heroBackground="linear-gradient(135deg, #667eea 0%, #764ba2 100%)"
    >
      <div className="register-page registration-container" style={{ paddingBottom: '80px', minHeight: 'auto' }}>
        <div className="container">
          {events.length > 0 ? (
            <div className="hero-glass-panel rounded-4 overflow-hidden registration-container" style={{
              background: 'rgba(255, 255, 255, 0.95)',
              backdropFilter: 'blur(20px)',
              border: '1px solid rgba(255, 255, 255, 0.2)',
              boxShadow: '0 25px 50px rgba(0, 0, 0, 0.15)',
              paddingBottom: '80px',
              marginBottom: '80px',
              minHeight: 'auto'
            }}>
              {/* Page Header */}
              <div className="text-white p-4 mb-4" style={{
                background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                borderRadius: '20px 20px 0 0',
                marginTop: '-20px'
              }}>
                <div className="row align-items-center">
                  <div className="col">
                    <h3 className="mb-0 fw-bold" style={{
                      textShadow: '0 2px 4px rgba(0,0,0,0.3)'
                    }}>
                      <i className="fas fa-user-plus me-3"></i>
                      Event Registration
                    </h3>
                    <p className="mb-0 mt-2" style={{
                      opacity: 0.95,
                      textShadow: '0 1px 2px rgba(0,0,0,0.3)'
                    }}>
                      Choose your event and start your registration journey
                    </p>
                  </div>
                  <div className="col-auto">
                    <div className="text-end">
                      <i className="fas fa-rocket fa-2x" style={{
                        opacity: 0.8,
                        textShadow: '0 2px 4px rgba(0,0,0,0.3)'
                      }}></i>
                    </div>
                  </div>
                </div>
              </div>

              {/* Header Section */}
              <div className="registration-header text-center mb-5">
                <div className="header-content">
                  <h1 className="display-4 fw-bold mb-3 gradient-text">
                    Choose Your Event
                  </h1>
                  <p className="lead text-muted mb-4">
                    Discover amazing events and secure your spot today
                  </p>
                  <div className="stats-row d-flex justify-content-center gap-4 mb-4">
                    <div className="stat-item">
                      <span className="stat-number">{events.length}</span>
                      <span className="stat-label">Available Events</span>
                    </div>
                    <div className="stat-item">
                      <span className="stat-number">{events.reduce((sum, event) => sum + event.participant_count, 0)}</span>
                      <span className="stat-label">Total Participants</span>
                    </div>
                  </div>
                </div>
              </div>

              {/* Search and Filter Section */}
              <div className="search-filter-section mb-5">
                <div className="row justify-content-center">
                  <div className="col-lg-8">
                    <div className="search-filter-card">
                      <div className="row g-3">
                        <div className="col-md-8">
                          <div className="search-box">
                            <i className="fas fa-search search-icon"></i>
                            <input
                              type="text"
                              className="form-control search-input"
                              placeholder="Search events by name, description, or location..."
                              value={searchTerm}
                              onChange={(e) => setSearchTerm(e.target.value)}
                            />
                          </div>
                        </div>
                        <div className="col-md-4">
                          <select
                            className="form-select filter-select"
                            value={filterType}
                            onChange={(e) => setFilterType(e.target.value)}
                          >
                            <option value="all">All Events</option>
                            <option value="soon">Starting Soon</option>
                            <option value="upcoming">Upcoming</option>
                            <option value="future">Future Events</option>
                          </select>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              {/* Events Grid */}
              <div className="events-grid-section">
                <div className="row justify-content-center">
                  <div className="col-lg-10">
                    {filteredEvents.length > 0 ? (
                      <div className="events-grid">
                        {filteredEvents.map((event) => {
                          const eventStatus = getEventStatus(event.start_date, event.end_date);
                          const daysUntil = getDaysUntilEvent(event.start_date);

                          return (
                            <div key={event.id} className="event-card-wrapper">
                              <div
                                className={`event-card ${selectedEvent === event.id ? 'selected' : ''}`}
                                onClick={() => setSelectedEvent(event.id)}
                              >
                                {/* Event Header */}
                                <div className="event-header">
                                  <div className="event-date-badge">
                                    <div className="date-month">{formatDateShort(event.start_date)}</div>
                                    <div className="date-year">{new Date(event.start_date).getFullYear()}</div>
                                  </div>
                                  <div className="event-status">
                                    <span className={`status-badge status-${eventStatus.color}`}>
                                      {eventStatus.label}
                                    </span>
                                  </div>
                                  <div className="selection-indicator">
                                    <div className="radio-custom">
                                      <input
                                        type="radio"
                                        name="event"
                                        value={event.id}
                                        checked={selectedEvent === event.id}
                                        onChange={() => setSelectedEvent(event.id)}
                                      />
                                      <span className="radio-checkmark"></span>
                                    </div>
                                  </div>
                                </div>

                                {/* Event Content */}
                                <div className="event-content">
                                  <h3 className="event-title">{event.name}</h3>
                                  <p className="event-description">
                                    {event.description.length > 120
                                      ? event.description.substring(0, 120) + '...'
                                      : event.description
                                    }
                                  </p>

                                  <div className="event-details">
                                    <div className="detail-item">
                                      <i className="fas fa-calendar-alt"></i>
                                      <span>{formatDate(event.start_date)}</span>
                                    </div>
                                    <div className="detail-item">
                                      <i className="fas fa-map-marker-alt"></i>
                                      <span>{event.location}</span>
                                    </div>
                                    <div className="detail-item">
                                      <i className="fas fa-users"></i>
                                      <span>{event.participant_count} registered</span>
                                    </div>
                                  </div>

                                  {daysUntil > 0 && daysUntil <= 30 && (
                                    <div className="countdown-info">
                                      <i className="fas fa-clock"></i>
                                      <span>
                                        {daysUntil === 1 ? 'Starts tomorrow' : `Starts in ${daysUntil} days`}
                                      </span>
                                    </div>
                                  )}
                                </div>

                                {/* Event Footer */}
                                <div className="event-footer">
                                  <div className="event-meta">
                                    <span className="event-city">{event.city}, {event.country}</span>
                                  </div>
                                  <div className="event-action">
                                    <span className="select-text">
                                      {selectedEvent === event.id ? 'Selected' : 'Click to select'}
                                    </span>
                                  </div>
                                </div>
                              </div>
                            </div>
                          );
                        })}
                      </div>
                    ) : (
                      <div className="no-events-found text-center py-5">
                        <i className="fas fa-search fa-3x text-muted mb-3"></i>
                        <h4 className="text-muted">No events found</h4>
                        <p className="text-muted">
                          Try adjusting your search terms or filters to find more events.
                        </p>
                      </div>
                    )}
                  </div>
                </div>
              </div>

              {/* Registration Information Panel */}
              {selectedEvent && (
                <div className="registration-info-section mt-5">
                  <div className="row justify-content-center">
                    <div className="col-lg-8">
                      <div className="registration-info-card">
                        <div className="info-header">
                          <h4 className="info-title">
                            <i className="fas fa-info-circle me-2"></i>
                            Next Steps
                          </h4>
                          <p className="info-subtitle">
                            You're one step away from joining this amazing event!
                          </p>
                        </div>

                        <div className="steps-container">
                          <div className="step-item">
                            <div className="step-icon">
                              <i className="fas fa-user-plus"></i>
                            </div>
                            <div className="step-content">
                              <h6>Complete Registration</h6>
                              <p>Fill out your participant information and preferences</p>
                            </div>
                          </div>

                          <div className="step-item">
                            <div className="step-icon">
                              <i className="fas fa-file-upload"></i>
                            </div>
                            <div className="step-content">
                              <h6>Upload Documents</h6>
                              <p>Submit any required documents or certificates</p>
                            </div>
                          </div>

                          <div className="step-item">
                            <div className="step-icon">
                              <i className="fas fa-check-circle"></i>
                            </div>
                            <div className="step-content">
                              <h6>Get Confirmation</h6>
                              <p>Receive your confirmation email and event details</p>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              )}

              {/* Action Buttons */}
              <div className="action-buttons-section mt-5">
                <div className="row justify-content-center">
                  <div className="col-lg-6">
                    <div className="action-buttons">
                      <Link
                        to="/"
                        className="btn btn-outline-primary btn-lg"
                      >
                        <i className="fas fa-arrow-left me-2"></i>
                        Back to Home
                      </Link>

                      <Link
                        to="/participant-register"
                        className={`btn btn-primary btn-lg ${!selectedEvent ? 'disabled' : ''}`}
                        style={{ pointerEvents: !selectedEvent ? 'none' : 'auto' }}
                      >
                        <i className="fas fa-rocket me-2"></i>
                        Start Registration
                        <span className="btn-shine"></span>
                      </Link>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          ) : (
            <div className="no-events-section">
              <div className="no-events-container">
                <div className="no-events-content">
                  <div className="no-events-icon">
                    <i className="fas fa-calendar-times"></i>
                  </div>
                  <h2 className="no-events-title">No Events Available</h2>
                  <p className="no-events-description">
                    We don't have any upcoming events at the moment.
                    New exciting events are being planned, so check back soon!
                  </p>
                  <div className="no-events-actions">
                    <Link to="/" className="btn btn-primary btn-lg">
                      <i className="fas fa-home me-2"></i>
                      Back to Home
                    </Link>
                    <Link to="/events" className="btn btn-outline-primary btn-lg">
                      <i className="fas fa-calendar me-2"></i>
                      View All Events
                    </Link>
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    </ModernLayout>
  );
};

export default Register;
