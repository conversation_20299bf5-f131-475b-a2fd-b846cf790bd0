/* Event Pages Styles */

/* Event Detail Page */
.event-detail-page {
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
}

.event-hero-section {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border-radius: 0 0 50px 50px;
  margin-bottom: 2rem;
}

.event-hero-section .badge {
  backdrop-filter: blur(10px);
  background: rgba(255, 255, 255, 0.2) !important;
  border: 1px solid rgba(255, 255, 255, 0.3);
}

.event-banner {
  transition: transform 0.3s ease;
  border: 3px solid rgba(255, 255, 255, 0.2);
}

.event-banner:hover {
  transform: scale(1.02);
}

/* Event Info Cards */
.card.border-primary {
  border-width: 2px !important;
  transition: all 0.3s ease;
}

.card.border-primary:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 25px rgba(0, 123, 255, 0.15) !important;
}

.card.border-success:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 25px rgba(40, 167, 69, 0.15) !important;
}

.card.border-warning:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 25px rgba(255, 193, 7, 0.15) !important;
}

.card.border-info:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 25px rgba(23, 162, 184, 0.15) !important;
}

/* Timeline Styles */
.timeline-container {
  position: relative;
}

.timeline-item {
  position: relative;
}

.timeline-connector {
  background: linear-gradient(to bottom, #dee2e6, transparent);
}

.time-badge {
  background: linear-gradient(135deg, #007bff, #0056b3) !important;
  box-shadow: 0 4px 15px rgba(0, 123, 255, 0.3);
  border: none;
}

.schedule-content {
  background: linear-gradient(135deg, #f8f9fa, #ffffff) !important;
  border-left: 4px solid #007bff !important;
  transition: all 0.3s ease;
}

.schedule-content:hover {
  transform: translateX(5px);
  box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
}

/* Event Schedule Page */
.event-schedule-page {
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
}

.schedule-hero-section {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 0 0 50px 50px;
  margin-bottom: 2rem;
}

.bg-gradient-primary {
  background: linear-gradient(135deg, #007bff, #0056b3) !important;
}

.schedule-stat {
  padding: 1.5rem;
  border-radius: 15px;
  background: linear-gradient(135deg, #ffffff, #f8f9fa);
  border: 1px solid #e9ecef;
  transition: all 0.3s ease;
}

.schedule-stat:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
}

/* Daily Schedule Improvements */
.timeline-time .time-block {
  background: linear-gradient(135deg, #f8f9fa, #ffffff) !important;
  border: 2px solid #e9ecef !important;
  transition: all 0.3s ease;
}

.timeline-time .time-block:hover {
  border-color: #007bff !important;
  transform: scale(1.05);
}

/* Card Enhancements */
.card.shadow-lg {
  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1) !important;
  border: none;
  border-radius: 20px;
  overflow: hidden;
}

.card-header.bg-gradient-primary {
  background: linear-gradient(135deg, #007bff, #0056b3) !important;
  border: none;
  padding: 1.5rem 2rem;
}

.card-body {
  padding: 2rem;
}

/* Button Enhancements */
.btn-primary {
  background: linear-gradient(135deg, #007bff, #0056b3);
  border: none;
  border-radius: 25px;
  padding: 0.75rem 2rem;
  font-weight: 600;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(0, 123, 255, 0.3);
}

.btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 123, 255, 0.4);
}

.btn-outline-primary {
  border: 2px solid #007bff;
  border-radius: 25px;
  padding: 0.5rem 1.5rem;
  font-weight: 600;
  transition: all 0.3s ease;
}

.btn-outline-primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(0, 123, 255, 0.2);
}

.btn-success {
  background: linear-gradient(135deg, #28a745, #20c997);
  border: none;
  border-radius: 25px;
  padding: 0.75rem 2rem;
  font-weight: 600;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(40, 167, 69, 0.3);
}

.btn-success:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(40, 167, 69, 0.4);
}

/* Badge Enhancements */
.badge {
  border-radius: 15px;
  padding: 0.5rem 1rem;
  font-weight: 600;
  font-size: 0.85rem;
}

.badge.fs-6 {
  font-size: 0.9rem !important;
  padding: 0.75rem 1.25rem;
}

/* Responsive Improvements */
@media (max-width: 768px) {
  .event-hero-section {
    border-radius: 0 0 30px 30px;
  }
  
  .schedule-hero-section {
    border-radius: 0 0 30px 30px;
  }
  
  .display-4 {
    font-size: 2rem !important;
  }
  
  .timeline-item .row {
    flex-direction: column;
  }
  
  .timeline-time {
    margin-bottom: 1rem;
  }
  
  .schedule-content {
    margin-left: 0 !important;
  }
}

/* Loading and Error States */
.spinner-border {
  border-width: 3px;
}

/* Animation Classes */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.fade-in-up {
  animation: fadeInUp 0.6s ease-out;
}

/* Gallery Preview Enhancements */
.gallery-item img {
  transition: all 0.3s ease;
  border-radius: 10px;
}

.gallery-item img:hover {
  transform: scale(1.05);
  box-shadow: 0 5px 20px rgba(0, 0, 0, 0.2);
}

/* Map Container */
.map-container {
  border-radius: 15px;
  overflow: hidden;
  box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
}

/* Contact Info Styling */
.contact-info p {
  padding: 0.5rem 0;
  border-bottom: 1px solid #f0f0f0;
}

.contact-info p:last-child {
  border-bottom: none;
}

/* Location Info Card */
.location-info {
  background: linear-gradient(135deg, #f8f9fa, #ffffff);
  border-radius: 15px;
  padding: 1.5rem;
  border: 1px solid #e9ecef;
}
