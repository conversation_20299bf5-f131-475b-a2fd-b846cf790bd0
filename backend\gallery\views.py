from rest_framework import viewsets, status
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated, AllowAny
from django.db.models import Q
from .models import GalleryCategory, GalleryImage, EventSlider
from .serializers import (
    GalleryCategorySerializer, GalleryImageSerializer, 
    GalleryImageListSerializer, EventSliderSerializer,
    FeaturedGallerySerializer
)


class GalleryCategoryViewSet(viewsets.ModelViewSet):
    queryset = GalleryCategory.objects.all()
    serializer_class = GalleryCategorySerializer
    
    def get_permissions(self):
        if self.action in ['list', 'retrieve']:
            permission_classes = [AllowAny]
        else:
            permission_classes = [IsAuthenticated]
        return [permission() for permission in permission_classes]
    
    def get_queryset(self):
        queryset = GalleryCategory.objects.all()
        
        # Filter by active status for public views
        if not self.request.user.is_authenticated:
            queryset = queryset.filter(is_active=True)
            
        return queryset.order_by('order', 'name')

    @action(detail=True, methods=['get'])
    def images(self, request, pk=None):
        """Get all images for a specific category"""
        category = self.get_object()
        images = GalleryImage.objects.filter(category=category, is_active=True)
        
        # Apply filters
        is_featured = request.query_params.get('featured')
        if is_featured is not None:
            images = images.filter(is_featured=is_featured.lower() == 'true')
            
        is_slider = request.query_params.get('slider')
        if is_slider is not None:
            images = images.filter(is_slider=is_slider.lower() == 'true')
            
        is_campus = request.query_params.get('campus')
        if is_campus is not None:
            images = images.filter(is_campus=is_campus.lower() == 'true')
        
        serializer = GalleryImageListSerializer(images, many=True, context={'request': request})
        return Response(serializer.data)


class GalleryImageViewSet(viewsets.ModelViewSet):
    queryset = GalleryImage.objects.all()
    
    def get_serializer_class(self):
        if self.action == 'list':
            return GalleryImageListSerializer
        return GalleryImageSerializer
    
    def get_permissions(self):
        if self.action in ['list', 'retrieve', 'featured', 'slider_images', 'campus_images']:
            permission_classes = [AllowAny]
        else:
            permission_classes = [IsAuthenticated]
        return [permission() for permission in permission_classes]
    
    def get_queryset(self):
        queryset = GalleryImage.objects.select_related('category')
        
        # Filter by active status for public views
        if not self.request.user.is_authenticated:
            queryset = queryset.filter(is_active=True)
        
        # Apply filters
        category = self.request.query_params.get('category')
        if category:
            queryset = queryset.filter(category__slug=category)
            
        is_featured = self.request.query_params.get('featured')
        if is_featured is not None:
            queryset = queryset.filter(is_featured=is_featured.lower() == 'true')
            
        is_slider = self.request.query_params.get('slider')
        if is_slider is not None:
            queryset = queryset.filter(is_slider=is_slider.lower() == 'true')
            
        is_campus = self.request.query_params.get('campus')
        if is_campus is not None:
            queryset = queryset.filter(is_campus=is_campus.lower() == 'true')
            
        tags = self.request.query_params.get('tags')
        if tags:
            tag_list = [tag.strip() for tag in tags.split(',')]
            for tag in tag_list:
                queryset = queryset.filter(tags__icontains=tag)
        
        search = self.request.query_params.get('search')
        if search:
            queryset = queryset.filter(
                Q(title__icontains=search) |
                Q(description__icontains=search) |
                Q(tags__icontains=search) |
                Q(photographer__icontains=search) |
                Q(location__icontains=search)
            )
        
        return queryset.order_by('-is_featured', 'order', '-created_at')

    @action(detail=False, methods=['get'])
    def featured(self, request):
        """Get featured gallery images"""
        images = GalleryImage.objects.filter(is_featured=True, is_active=True)
        limit = request.query_params.get('limit')
        if limit:
            try:
                images = images[:int(limit)]
            except ValueError:
                pass
                
        serializer = FeaturedGallerySerializer(images, many=True, context={'request': request})
        return Response(serializer.data)

    @action(detail=False, methods=['get'])
    def slider_images(self, request):
        """Get images marked for slider use"""
        images = GalleryImage.objects.filter(is_slider=True, is_active=True)
        limit = request.query_params.get('limit', 5)
        try:
            images = images[:int(limit)]
        except ValueError:
            images = images[:5]
            
        serializer = GalleryImageListSerializer(images, many=True, context={'request': request})
        return Response(serializer.data)

    @action(detail=False, methods=['get'])
    def campus_images(self, request):
        """Get campus/university related images"""
        images = GalleryImage.objects.filter(is_campus=True, is_active=True)
        limit = request.query_params.get('limit')
        if limit:
            try:
                images = images[:int(limit)]
            except ValueError:
                pass
                
        serializer = GalleryImageListSerializer(images, many=True, context={'request': request})
        return Response(serializer.data)

    @action(detail=False, methods=['post'])
    def bulk_upload(self, request):
        """Bulk upload multiple images to gallery"""
        if not request.user.is_authenticated:
            return Response({'error': 'Authentication required'}, status=status.HTTP_401_UNAUTHORIZED)

        category_id = request.data.get('category')
        if not category_id:
            return Response({'error': 'Category ID is required'}, status=status.HTTP_400_BAD_REQUEST)

        try:
            category = GalleryCategory.objects.get(id=category_id)
        except GalleryCategory.DoesNotExist:
            return Response({'error': 'Category not found'}, status=status.HTTP_404_NOT_FOUND)

        uploaded_images = []
        errors = []

        # Process multiple files
        for key, file in request.FILES.items():
            if key.startswith('image_'):
                try:
                    index = key.split("_")[1]
                    title = request.data.get(f'title_{index}', f'Gallery Image {len(uploaded_images) + 1}')
                    description = request.data.get(f'description_{index}', '')
                    photographer = request.data.get(f'photographer_{index}', '')
                    location = request.data.get(f'location_{index}', '')
                    tags = request.data.get(f'tags_{index}', '')

                    gallery_image = GalleryImage.objects.create(
                        category=category,
                        title=title,
                        description=description,
                        image=file,
                        photographer=photographer,
                        location=location,
                        tags=tags,
                        uploaded_by=request.user
                    )
                    uploaded_images.append(gallery_image)
                except Exception as e:
                    errors.append(f'Error uploading {file.name}: {str(e)}')

        return Response({
            'uploaded': len(uploaded_images),
            'errors': errors,
            'images': GalleryImageListSerializer(uploaded_images, many=True, context={'request': request}).data
        })

    @action(detail=False, methods=['delete'])
    def bulk_delete(self, request):
        """Bulk delete multiple images"""
        if not request.user.is_authenticated:
            return Response({'error': 'Authentication required'}, status=status.HTTP_401_UNAUTHORIZED)

        image_ids = request.data.get('image_ids', [])
        if not image_ids:
            return Response({'error': 'No image IDs provided'}, status=status.HTTP_400_BAD_REQUEST)

        try:
            deleted_count = GalleryImage.objects.filter(id__in=image_ids).delete()[0]
            return Response({'deleted': deleted_count})
        except Exception as e:
            return Response({'error': str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class EventSliderViewSet(viewsets.ModelViewSet):
    queryset = EventSlider.objects.select_related('event')
    serializer_class = EventSliderSerializer
    
    def get_permissions(self):
        if self.action in ['list', 'retrieve', 'active_sliders']:
            permission_classes = [AllowAny]
        else:
            permission_classes = [IsAuthenticated]
        return [permission() for permission in permission_classes]
    
    def get_queryset(self):
        queryset = EventSlider.objects.select_related('event')
        
        # Filter by active status for public views
        if not self.request.user.is_authenticated:
            queryset = queryset.filter(is_active=True, event__is_active=True)
        
        return queryset.order_by('order', '-created_at')

    @action(detail=False, methods=['get'])
    def active_sliders(self, request):
        """Get active event sliders for homepage"""
        sliders = EventSlider.objects.filter(
            is_active=True, 
            event__is_active=True
        ).select_related('event').order_by('order', '-created_at')
        
        limit = request.query_params.get('limit', 5)
        try:
            sliders = sliders[:int(limit)]
        except ValueError:
            sliders = sliders[:5]
            
        serializer = EventSliderSerializer(sliders, many=True, context={'request': request})
        return Response(serializer.data)
