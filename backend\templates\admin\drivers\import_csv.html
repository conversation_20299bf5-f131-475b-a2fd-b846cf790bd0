{% extends "admin/base_site.html" %}
{% load i18n admin_urls static admin_modify %}

{% block title %}Import Drivers from CSV{% endblock %}

{% block breadcrumbs %}
<div class="breadcrumbs">
    <a href="{% url 'admin:index' %}">Home</a>
    &rsaquo; <a href="{% url 'admin:app_list' app_label='drivers' %}">Drivers</a>
    &rsaquo; <a href="{% url 'admin:drivers_driver_changelist' %}">Drivers</a>
    &rsaquo; Import from CSV
</div>
{% endblock %}

{% block content %}
<div class="module aligned">
    <h1>Import Drivers from CSV</h1>
    
    <div class="form-row">
        <div class="help">
            <h3>CSV Format Instructions:</h3>
            <p>Your CSV file should contain the following columns (header row required):</p>
            <ul>
                <li><strong>name</strong> - Driver's full name (required)</li>
                <li><strong>phone</strong> - Phone number (required)</li>
                <li><strong>email</strong> - Email address (optional)</li>
                <li><strong>car_plate</strong> - Vehicle license plate (required)</li>
                <li><strong>car_code</strong> - Internal car identification code (required)</li>
                <li><strong>car_model</strong> - Vehicle model (optional)</li>
                <li><strong>car_color</strong> - Vehicle color (optional)</li>
                <li><strong>license_number</strong> - Driver's license number (optional)</li>
                <li><strong>event</strong> - Event name (required)</li>
                <li><strong>available</strong> - Available status: yes/no (optional, default: yes)</li>
                <li><strong>notes</strong> - Additional notes (optional)</li>
            </ul>
            
            <h4>Sample CSV Content:</h4>
            <pre style="background: #f8f9fa; padding: 10px; border-radius: 5px; font-size: 12px;">
name,phone,email,car_plate,car_code,car_model,car_color,license_number,event,available,notes
John Smith,+92-300-1234567,<EMAIL>,ABC-123,CAR001,Toyota Corolla,White,DL123456,Annual Conference,yes,Experienced driver
Jane Doe,+92-301-7654321,<EMAIL>,XYZ-789,CAR002,Honda Civic,Blue,DL789012,Annual Conference,yes,Available for airport pickup
            </pre>
        </div>
    </div>
    
    <form method="post" enctype="multipart/form-data" class="module aligned">
        {% csrf_token %}
        
        <div class="form-row">
            <div>
                <label for="csv_file" class="required">CSV File:</label>
                <input type="file" name="csv_file" id="csv_file" accept=".csv" required>
                <p class="help">Select a CSV file containing driver information.</p>
            </div>
        </div>
        
        <div class="submit-row">
            <input type="submit" value="Import Drivers" class="default" name="_save">
            <a href="{% url 'admin:drivers_driver_changelist' %}" class="button cancel-link">Cancel</a>
        </div>
    </form>
    
    <div class="form-row">
        <div class="help">
            <h3>Important Notes:</h3>
            <ul>
                <li>Make sure the event names in your CSV match existing events in the system</li>
                <li>Duplicate car plates or car codes will cause import errors</li>
                <li>Phone numbers should include country code for international numbers</li>
                <li>The system will validate email addresses if provided</li>
                <li>Any errors during import will be displayed after processing</li>
            </ul>
        </div>
    </div>
</div>

<style>
.help {
    background: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 5px;
    padding: 15px;
    margin: 15px 0;
}

.help h3, .help h4 {
    color: #495057;
    margin-top: 0;
}

.help ul {
    margin: 10px 0;
    padding-left: 20px;
}

.help li {
    margin: 5px 0;
}

pre {
    overflow-x: auto;
    white-space: pre-wrap;
    word-wrap: break-word;
}

.cancel-link {
    margin-left: 10px;
    text-decoration: none;
    color: #6c757d;
    padding: 8px 15px;
    border: 1px solid #6c757d;
    border-radius: 4px;
}

.cancel-link:hover {
    background-color: #6c757d;
    color: white;
}
</style>
{% endblock %}
