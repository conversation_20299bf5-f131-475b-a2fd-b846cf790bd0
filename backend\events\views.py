from rest_framework import viewsets, status
from rest_framework.decorators import action, api_view, permission_classes
from rest_framework.response import Response
from rest_framework.permissions import AllowAny, IsAuthenticated
from django.shortcuts import get_object_or_404
from django.http import HttpResponse
from django.utils import timezone
from django.contrib.auth import get_user_model
import csv
import io
from .models import Event, EventSchedule, EventGallery, EventSponsor, EventOrganizer, EventDocument
from .pdf_service import SchedulePDFGenerator

User = get_user_model()
from .serializers import (
    EventSerializer, EventListSerializer,
    EventScheduleSerializer, EventGallerySerializer,
    EventSponsorSerializer, EventOrganizerSerializer, EventDocumentSerializer
)


class EventViewSet(viewsets.ModelViewSet):
    queryset = Event.objects.all()

    def get_permissions(self):
        """
        Allow public access for list, retrieve, schedule, gallery, participants, sponsors, and organizers actions,
        require authentication for create, update, delete
        """
        if self.action in ['list', 'retrieve', 'schedule', 'gallery', 'participants', 'sponsors', 'organizers']:
            permission_classes = [AllowAny]
        else:
            permission_classes = [IsAuthenticated]
        return [permission() for permission in permission_classes]

    def get_serializer_class(self):
        if self.action == 'list':
            return EventListSerializer
        return EventSerializer

    @action(detail=True, methods=['get'])
    def schedule(self, request, pk=None):
        """Get event schedule"""
        event = self.get_object()
        schedules = event.schedules.all()
        serializer = EventScheduleSerializer(schedules, many=True)
        return Response(serializer.data)

    @action(detail=True, methods=['get'])
    def gallery(self, request, pk=None):
        """Get event gallery"""
        event = self.get_object()
        gallery = event.gallery.all()
        serializer = EventGallerySerializer(gallery, many=True)
        return Response(serializer.data)

    @action(detail=True, methods=['get'])
    def participants(self, request, pk=None):
        """Get event participants - returns all participants without pagination"""
        event = self.get_object()
        from participants.serializers import ParticipantListSerializer
        participants = event.participants.all()
        serializer = ParticipantListSerializer(participants, many=True)
        return Response(serializer.data)

    @action(detail=True, methods=['get'])
    def sponsors(self, request, pk=None):
        """Get event sponsors"""
        event = self.get_object()
        try:
            sponsors = event.sponsors.filter(is_active=True)
            serializer = EventSponsorSerializer(sponsors, many=True)
            return Response(serializer.data)
        except Exception as e:
            # Return empty list if sponsors don't exist
            return Response([])

    @action(detail=True, methods=['get'])
    def organizers(self, request, pk=None):
        """Get event organizers"""
        event = self.get_object()
        try:
            organizers = event.organizers.filter(is_active=True)
            serializer = EventOrganizerSerializer(organizers, many=True)
            return Response(serializer.data)
        except Exception as e:
            # Return empty list if organizers don't exist
            return Response([])


class EventScheduleViewSet(viewsets.ModelViewSet):
    queryset = EventSchedule.objects.all()
    serializer_class = EventScheduleSerializer

    def get_permissions(self):
        """Allow public access for list and retrieve actions"""
        if self.action in ['list', 'retrieve']:
            permission_classes = [AllowAny]
        else:
            permission_classes = [IsAuthenticated]
        return [permission() for permission in permission_classes]

    def get_queryset(self):
        queryset = super().get_queryset()
        event_id = self.request.query_params.get('event', None)
        if event_id is not None:
            queryset = queryset.filter(event=event_id)
        return queryset


class EventGalleryViewSet(viewsets.ModelViewSet):
    queryset = EventGallery.objects.all()
    serializer_class = EventGallerySerializer

    def get_permissions(self):
        """Allow public access for list, retrieve, featured, and by_date actions"""
        if self.action in ['list', 'retrieve', 'featured', 'by_date']:
            permission_classes = [AllowAny]
        else:
            permission_classes = [IsAuthenticated]
        return [permission() for permission in permission_classes]

    def get_queryset(self):
        queryset = super().get_queryset()
        event_id = self.request.query_params.get('event', None)
        if event_id is not None:
            queryset = queryset.filter(event=event_id)
        return queryset

    def perform_create(self, serializer):
        """Set the uploaded_by field to current user"""
        serializer.save(uploaded_by=self.request.user if self.request.user.is_authenticated else None)

    @action(detail=False, methods=['get'])
    def featured(self, request):
        """Get featured gallery images"""
        featured = self.get_queryset().filter(is_featured=True)
        serializer = self.get_serializer(featured, many=True)
        return Response(serializer.data)

    @action(detail=False, methods=['get'])
    def by_date(self, request):
        """Get gallery images by date"""
        date = request.query_params.get('date')
        if not date:
            return Response({'error': 'Date parameter is required'}, status=status.HTTP_400_BAD_REQUEST)

        try:
            from datetime import datetime
            date_obj = datetime.strptime(date, '%Y-%m-%d').date()
            images = self.get_queryset().filter(uploaded_at__date=date_obj)
            serializer = self.get_serializer(images, many=True)
            return Response(serializer.data)
        except ValueError:
            return Response({'error': 'Invalid date format. Use YYYY-MM-DD'}, status=status.HTTP_400_BAD_REQUEST)

    @action(detail=True, methods=['post'])
    def toggle_featured(self, request, pk=None):
        """Toggle featured status of gallery image"""
        image = self.get_object()
        image.is_featured = not image.is_featured
        image.save()
        serializer = self.get_serializer(image)
        return Response(serializer.data)

    @action(detail=False, methods=['post'])
    def bulk_upload(self, request):
        """Bulk upload multiple images"""
        event_id = request.data.get('event')
        if not event_id:
            return Response({'error': 'Event ID is required'}, status=status.HTTP_400_BAD_REQUEST)

        try:
            event = Event.objects.get(id=event_id)
        except Event.DoesNotExist:
            return Response({'error': 'Event not found'}, status=status.HTTP_404_NOT_FOUND)

        uploaded_images = []
        errors = []

        # Process multiple files
        for key, file in request.FILES.items():
            if key.startswith('image_'):
                try:
                    title = request.data.get(f'title_{key.split("_")[1]}', f'Gallery Image {len(uploaded_images) + 1}')
                    description = request.data.get(f'description_{key.split("_")[1]}', '')

                    gallery_image = EventGallery.objects.create(
                        event=event,
                        title=title,
                        description=description,
                        image=file,
                        uploaded_by=request.user if request.user.is_authenticated else None
                    )
                    uploaded_images.append(gallery_image)
                except Exception as e:
                    errors.append(f'Error uploading {file.name}: {str(e)}')

        if uploaded_images:
            serializer = self.get_serializer(uploaded_images, many=True)
            response_data = {
                'uploaded': len(uploaded_images),
                'errors': len(errors),
                'images': serializer.data
            }
            if errors:
                response_data['error_details'] = errors
            return Response(response_data, status=status.HTTP_201_CREATED)
        else:
            return Response({'error': 'No images were uploaded', 'details': errors},
                          status=status.HTTP_400_BAD_REQUEST)

    @action(detail=False, methods=['delete'])
    def bulk_delete(self, request):
        """Bulk delete gallery images"""
        image_ids = request.data.get('image_ids', [])
        if not image_ids:
            return Response({'error': 'No image IDs provided'}, status=status.HTTP_400_BAD_REQUEST)

        deleted_count = 0
        for image_id in image_ids:
            try:
                image = EventGallery.objects.get(id=image_id)
                image.delete()
                deleted_count += 1
            except EventGallery.DoesNotExist:
                pass

        return Response({'deleted': deleted_count}, status=status.HTTP_200_OK)

    @action(detail=True, methods=['get'], permission_classes=[AllowAny])
    def sponsors(self, request, pk=None):
        """Get sponsors for an event"""
        event = self.get_object()
        sponsors = EventSponsor.objects.filter(event=event, is_active=True).order_by('display_order')
        serializer = EventSponsorSerializer(sponsors, many=True)
        return Response(serializer.data)

    @action(detail=True, methods=['get'], permission_classes=[AllowAny])
    def organizers(self, request, pk=None):
        """Get organizers for an event"""
        event = self.get_object()
        organizers = EventOrganizer.objects.filter(event=event, is_active=True).order_by('display_order')
        serializer = EventOrganizerSerializer(organizers, many=True)
        return Response(serializer.data)


class EventDocumentViewSet(viewsets.ModelViewSet):
    queryset = EventDocument.objects.all()
    serializer_class = EventDocumentSerializer

    def get_permissions(self):
        """Allow public access for list and retrieve, require authentication for CUD operations"""
        if self.action in ['list', 'retrieve', 'download']:
            permission_classes = [AllowAny]
        else:
            permission_classes = [IsAuthenticated]
        return [permission() for permission in permission_classes]

    def get_queryset(self):
        """Filter documents by event and public visibility"""
        queryset = EventDocument.objects.filter(is_active=True)

        # Filter by event if provided
        event_id = self.request.query_params.get('event', None)
        if event_id:
            queryset = queryset.filter(event_id=event_id)

        # For non-authenticated users, only show public documents
        if not self.request.user.is_authenticated:
            queryset = queryset.filter(is_public=True)

        return queryset.select_related('event', 'uploaded_by').order_by('-uploaded_at')

    def perform_create(self, serializer):
        """Set the uploaded_by field to current user"""
        serializer.save(uploaded_by=self.request.user)

    @action(detail=True, methods=['get'], permission_classes=[AllowAny])
    def download(self, request, pk=None):
        """Download a document and increment download count"""
        document = self.get_object()

        # Check if document is public or user is authenticated
        if not document.is_public and not request.user.is_authenticated:
            return Response({'error': 'Document is not publicly available'},
                          status=status.HTTP_403_FORBIDDEN)

        # Increment download count
        document.download_count += 1
        document.save(update_fields=['download_count'])

        # Return file response
        try:
            from django.http import FileResponse
            import os

            # Get the file path
            file_path = document.file.path
            if not os.path.exists(file_path):
                return Response({'error': 'File not found'}, status=status.HTTP_404_NOT_FOUND)

            # Create file response
            response = FileResponse(
                open(file_path, 'rb'),
                content_type='application/octet-stream',
                as_attachment=True,
                filename=os.path.basename(file_path)
            )

            # Add CORS headers
            response['Access-Control-Allow-Origin'] = '*'
            response['Access-Control-Allow-Methods'] = 'GET'
            response['Access-Control-Allow-Headers'] = 'Content-Type'

            return response
        except Exception as e:
            return Response({'error': f'File access error: {str(e)}'}, status=status.HTTP_404_NOT_FOUND)

    @action(detail=False, methods=['post'])
    def bulk_upload(self, request):
        """Bulk upload multiple documents"""
        event_id = request.data.get('event')
        if not event_id:
            return Response({'error': 'Event ID is required'}, status=status.HTTP_400_BAD_REQUEST)

        try:
            event = Event.objects.get(id=event_id)
        except Event.DoesNotExist:
            return Response({'error': 'Event not found'}, status=status.HTTP_404_NOT_FOUND)

        uploaded_documents = []
        errors = []

        # Process multiple files
        for key, file in request.FILES.items():
            if key.startswith('document_'):
                try:
                    title = request.data.get(f'title_{key.split("_")[1]}', f'Document {len(uploaded_documents) + 1}')
                    description = request.data.get(f'description_{key.split("_")[1]}', '')
                    document_type = request.data.get(f'type_{key.split("_")[1]}', 'other')

                    document = EventDocument.objects.create(
                        event=event,
                        title=title,
                        description=description,
                        document_type=document_type,
                        file=file,
                        uploaded_by=request.user if request.user.is_authenticated else None
                    )
                    uploaded_documents.append(document)
                except Exception as e:
                    errors.append(f'Error uploading {file.name}: {str(e)}')

        if uploaded_documents:
            serializer = self.get_serializer(uploaded_documents, many=True)
            response_data = {
                'uploaded': len(uploaded_documents),
                'errors': len(errors),
                'documents': serializer.data
            }
            if errors:
                response_data['error_details'] = errors
            return Response(response_data, status=status.HTTP_201_CREATED)
        else:
            return Response({'error': 'No documents were uploaded', 'details': errors},
                          status=status.HTTP_400_BAD_REQUEST)


@api_view(['GET'])
@permission_classes([AllowAny])
def public_statistics(request):
    """Get public statistics for hero section"""
    from participants.models import Participant
    from django.db.models import Count

    # Get basic statistics
    stats = {}

    # Total events
    total_events = Event.objects.count()
    active_events = Event.objects.filter(
        start_date__lte=timezone.now(),
        end_date__gte=timezone.now()
    ).count()

    # Total participants
    total_participants = Participant.objects.count()
    confirmed_participants = Participant.objects.filter(is_confirmed=True).count()

    # Total photos
    total_photos = EventGallery.objects.count()
    featured_photos = EventGallery.objects.filter(is_featured=True).count()

    # Unique countries (if participant model has country field)
    try:
        unique_countries = Participant.objects.values('country').distinct().count()
    except:
        unique_countries = 0

    # Unique organizations/institutions
    try:
        unique_organizations = Participant.objects.values('organization').distinct().count()
    except:
        unique_organizations = 0

    # Format numbers for display
    def format_number(num):
        if num >= 1000:
            return f"{num//1000}K+"
        elif num >= 100:
            return f"{num//100}00+"
        elif num >= 10:
            return f"{num//10}0+"
        else:
            return str(num)

    stats = {
        'events': {
            'total': format_number(total_events),
            'active': active_events,
            'label': 'Events Hosted'
        },
        'participants': {
            'total': format_number(total_participants),
            'confirmed': confirmed_participants,
            'label': 'Participants'
        },
        'photos': {
            'total': format_number(total_photos),
            'featured': featured_photos,
            'label': 'Gallery Photos'
        },
        'countries': {
            'total': format_number(unique_countries) if unique_countries > 0 else '25+',
            'label': 'Countries'
        },
        'organizations': {
            'total': format_number(unique_organizations) if unique_organizations > 0 else '50+',
            'label': 'Organizations'
        }
    }

    return Response(stats)


@api_view(['GET'])
@permission_classes([AllowAny])
def event_schedule_list(request, event_id):
    """Get schedules for a specific event"""
    event = get_object_or_404(Event, id=event_id)
    schedules = EventSchedule.objects.filter(event=event).order_by('start_time')
    serializer = EventScheduleSerializer(schedules, many=True)
    return Response(serializer.data)


@api_view(['GET'])
@permission_classes([AllowAny])
def event_gallery_list(request, event_id):
    """Get gallery images for a specific event"""
    event = get_object_or_404(Event, id=event_id)
    gallery = EventGallery.objects.filter(event=event).order_by('-uploaded_at')
    serializer = EventGallerySerializer(gallery, many=True)
    return Response(serializer.data)


@api_view(['GET'])
@permission_classes([AllowAny])
def event_schedule_pdf(request, event_id):
    """Download event schedule as PDF"""
    event = get_object_or_404(Event, id=event_id)
    pdf_generator = SchedulePDFGenerator(event)
    return pdf_generator.get_http_response()
