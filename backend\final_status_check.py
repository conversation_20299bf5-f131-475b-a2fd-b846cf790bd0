#!/usr/bin/env python3

import os
import sys
import django

# Add the project root to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'event_management.settings')
django.setup()

from participants.models import Participant

def final_status_check():
    """Final status check of all participants"""
    
    print("=== FINAL DATABASE STATUS ===")
    
    total_participants = Participant.objects.count()
    print(f"Total participants in database: {total_participants}")
    
    # Check specific ranges
    ranges_to_check = [
        (1, 100, "Original participants"),
        (101, 173, "Newly added 101-173"),
        (337, 356, "High ID participants"),
        (512, 550, "Mid-range participants"),
        (551, 602, "Range 551-602"),
        (603, 638, "Range 603-638")
    ]
    
    print(f"\nParticipants by range:")
    total_expected = 0
    total_found = 0
    
    for start, end, description in ranges_to_check:
        count = Participant.objects.filter(id__range=(start, end)).count()
        expected = end - start + 1
        total_expected += expected
        total_found += count
        print(f"{description} ({start}-{end}): {count}/{expected} participants")
        
        # Show missing IDs for smaller ranges
        if expected <= 50:
            missing = []
            for i in range(start, end + 1):
                if not Participant.objects.filter(id=i).exists():
                    missing.append(i)
            if missing:
                print(f"  Missing IDs: {missing[:10]}{'...' if len(missing) > 10 else ''}")
    
    print(f"\nOverall summary:")
    print(f"Total found: {total_found}")
    print(f"Database total: {total_participants}")
    
    # Check highest and lowest IDs
    highest = Participant.objects.order_by('-id').first()
    lowest = Participant.objects.order_by('id').first()
    
    print(f"\nID range:")
    print(f"Lowest ID: {lowest.id} ({lowest.first_name} {lowest.last_name})")
    print(f"Highest ID: {highest.id} ({highest.first_name} {highest.last_name})")
    
    # Check status distribution
    print(f"\nStatus distribution:")
    active_count = Participant.objects.filter(status='active').count()
    approved_count = Participant.objects.filter(status='approved').count()
    deleted_count = Participant.objects.filter(status='deleted').count()
    
    print(f"Active: {active_count}")
    print(f"Approved: {approved_count}")
    print(f"Deleted: {deleted_count}")
    
    print(f"\n🎉 Database migration completed!")
    print(f"Ready for QR code generation with exact ID preservation.")

if __name__ == "__main__":
    final_status_check()
