from django.db import models
from django.contrib.auth import get_user_model
from events.models import Event, EventSchedule
from participants.models import Participant

User = get_user_model()


class EventFeedback(models.Model):
    """Main feedback model for events"""
    
    RATING_CHOICES = [
        (1, '⭐ Poor'),
        (2, '⭐⭐ Below Average'),
        (3, '⭐⭐⭐ Average'),
        (4, '⭐⭐⭐⭐ Good'),
        (5, '⭐⭐⭐⭐⭐ Excellent'),
    ]
    
    EXPECTATION_CHOICES = [
        ('yes', 'Yes'),
        ('somewhat', 'Somewhat'),
        ('no', 'No'),
    ]
    
    NETWORKING_CHOICES = [
        ('yes', 'Yes'),
        ('somewhat', 'Somewhat'),
        ('no', 'No'),
    ]
    
    # Event and participant information
    event = models.ForeignKey(Event, on_delete=models.CASCADE, related_name='feedback')
    participant = models.ForeignKey(Participant, on_delete=models.SET_NULL, null=True, blank=True, related_name='feedback')
    
    # Optional participant information (for anonymous feedback)
    participant_name = models.Char<PERSON>ield(max_length=200, blank=True, help_text="Optional: Participant name")
    participant_email = models.EmailField(blank=True, help_text="Optional: For follow-up or event updates")
    institution_name = models.CharField(max_length=200, blank=True, help_text="Optional: Institution/Organization")
    position_title = models.CharField(max_length=200, blank=True, help_text="Optional: Position/Title")
    
    # Overall Experience
    overall_satisfaction = models.IntegerField(choices=RATING_CHOICES, help_text="Overall satisfaction rating")
    met_expectations = models.CharField(max_length=10, choices=EXPECTATION_CHOICES, help_text="Did the conference meet expectations?")
    most_valuable_aspect = models.TextField(help_text="Most valuable aspect of the event")
    improvement_suggestions = models.TextField(blank=True, help_text="What could be improved for future conferences?")
    
    # Sessions & Content
    session_relevance = models.IntegerField(choices=RATING_CHOICES, help_text="How relevant were the sessions?")
    most_valuable_sessions = models.TextField(blank=True, help_text="Which sessions were most valuable?")
    future_topics_suggestions = models.TextField(blank=True, help_text="Suggestions for future topics or session formats")
    
    # Speakers & Moderators
    speaker_quality = models.IntegerField(choices=RATING_CHOICES, help_text="Quality of speakers/moderators")
    speaker_comments = models.TextField(blank=True, help_text="Comments on speaker expertise, engagement, or delivery")
    
    # Logistics & Organization
    venue_facilities = models.IntegerField(choices=RATING_CHOICES, help_text="Venue & Facilities rating")
    technical_setup = models.IntegerField(choices=RATING_CHOICES, help_text="Technical Setup rating")
    time_management = models.IntegerField(choices=RATING_CHOICES, help_text="Time Management & Scheduling rating")
    transportation_accessibility = models.IntegerField(choices=RATING_CHOICES, help_text="Transportation & Accessibility rating")
    pre_event_communication = models.IntegerField(choices=RATING_CHOICES, help_text="Pre-event Communication rating")
    logistics_comments = models.TextField(blank=True, help_text="Additional comments on logistics")
    
    # Networking Opportunities
    sufficient_networking = models.CharField(max_length=10, choices=NETWORKING_CHOICES, help_text="Sufficient networking opportunities?")
    networking_improvements = models.TextField(blank=True, help_text="How could networking be improved?")
    
    # Future Suggestions
    future_topics = models.TextField(blank=True, help_text="Topics for future conferences")
    additional_feedback = models.TextField(blank=True, help_text="Any other feedback or recommendations")
    
    # File uploads
    uploaded_file = models.FileField(upload_to='feedback_uploads/', null=True, blank=True, help_text="Optional: Upload relevant materials")
    
    # Consent and metadata
    consent_given = models.BooleanField(default=False, help_text="Consent to use feedback for improvement and reporting")
    is_anonymous = models.BooleanField(default=False, help_text="Whether this is anonymous feedback")
    ip_address = models.GenericIPAddressField(null=True, blank=True, help_text="IP address for tracking")
    user_agent = models.TextField(blank=True, help_text="Browser user agent")
    
    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        ordering = ['-created_at']
        verbose_name = 'Event Feedback'
        verbose_name_plural = 'Event Feedback'
        # Ensure one feedback per participant per event (if participant is provided)
        unique_together = [['event', 'participant']]
    
    def __str__(self):
        if self.participant:
            return f"Feedback for {self.event.name} by {self.participant.full_name}"
        elif self.participant_name:
            return f"Feedback for {self.event.name} by {self.participant_name}"
        else:
            return f"Anonymous feedback for {self.event.name}"
    
    @property
    def average_rating(self):
        """Calculate average rating across all rating fields"""
        ratings = [
            self.overall_satisfaction,
            self.session_relevance,
            self.speaker_quality,
            self.venue_facilities,
            self.technical_setup,
            self.time_management,
            self.transportation_accessibility,
            self.pre_event_communication,
        ]
        return sum(ratings) / len(ratings)
    
    @property
    def participant_display_name(self):
        """Get display name for participant"""
        if self.participant:
            return self.participant.full_name
        elif self.participant_name:
            return self.participant_name
        else:
            return "Anonymous"


class SessionFeedback(models.Model):
    """Feedback for specific event sessions"""
    
    RATING_CHOICES = [
        (1, '⭐ Poor'),
        (2, '⭐⭐ Below Average'),
        (3, '⭐⭐⭐ Average'),
        (4, '⭐⭐⭐⭐ Good'),
        (5, '⭐⭐⭐⭐⭐ Excellent'),
    ]
    
    event_feedback = models.ForeignKey(EventFeedback, on_delete=models.CASCADE, related_name='session_feedback')
    session = models.ForeignKey(EventSchedule, on_delete=models.CASCADE, related_name='feedback')
    
    # Session-specific ratings
    content_quality = models.IntegerField(choices=RATING_CHOICES, help_text="Content quality rating")
    speaker_effectiveness = models.IntegerField(choices=RATING_CHOICES, help_text="Speaker effectiveness rating")
    session_organization = models.IntegerField(choices=RATING_CHOICES, help_text="Session organization rating")
    audience_engagement = models.IntegerField(choices=RATING_CHOICES, help_text="Audience engagement rating")
    
    # Comments
    what_worked_well = models.TextField(blank=True, help_text="What worked well in this session?")
    improvement_suggestions = models.TextField(blank=True, help_text="What could be improved?")
    additional_comments = models.TextField(blank=True, help_text="Additional comments")
    
    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        ordering = ['-created_at']
        verbose_name = 'Session Feedback'
        verbose_name_plural = 'Session Feedback'
        # Ensure one feedback per session per event feedback
        unique_together = [['event_feedback', 'session']]
    
    def __str__(self):
        return f"Session feedback for {self.session.title} by {self.event_feedback.participant_display_name}"
    
    @property
    def average_rating(self):
        """Calculate average rating for this session"""
        ratings = [
            self.content_quality,
            self.speaker_effectiveness,
            self.session_organization,
            self.audience_engagement,
        ]
        return sum(ratings) / len(ratings)


class FeedbackTemplate(models.Model):
    """Template for feedback forms"""
    
    TEMPLATE_TYPES = [
        ('conference', 'Conference'),
        ('workshop', 'Workshop'),
        ('seminar', 'Seminar'),
        ('symposium', 'Symposium'),
        ('meeting', 'Meeting'),
        ('custom', 'Custom'),
    ]
    
    name = models.CharField(max_length=200, help_text="Template name")
    template_type = models.CharField(max_length=20, choices=TEMPLATE_TYPES, default='conference')
    description = models.TextField(blank=True, help_text="Template description")
    
    # Template configuration (JSON field for flexibility)
    form_config = models.JSONField(default=dict, help_text="Form configuration in JSON format")
    
    # Settings
    is_active = models.BooleanField(default=True)
    is_default = models.BooleanField(default=False, help_text="Default template for this type")
    
    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        ordering = ['template_type', 'name']
        verbose_name = 'Feedback Template'
        verbose_name_plural = 'Feedback Templates'
    
    def __str__(self):
        return f"{self.get_template_type_display()} - {self.name}"
