import axios from 'axios';

const API_BASE_URL = process.env.REACT_APP_API_BASE_URL || '/api';
const BACKEND_URL = process.env.REACT_APP_BACKEND_URL || '';

// Create axios instance
const api = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Add token to requests
api.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('access_token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }

    // For FormData, let the browser set the Content-Type header
    if (config.data instanceof FormData) {
      delete config.headers['Content-Type'];
    }

    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Types
export interface Event {
  id: number;
  name: string;
  description: string;
  start_date: string;
  end_date: string;
  location: string;
  city: string;
  country: string;
  latitude?: number;
  longitude?: number;
  organizer_name: string;
  organizer_email: string;
  organizer_phone: string;
  logo?: string;
  banner?: string;
  map_embed?: string;
  is_active: boolean;
  participant_count: number;
  created_at: string;
  updated_at: string;
}

export interface EventSchedule {
  id: number;
  event: number;
  title: string;
  description: string;
  start_time: string;
  end_time: string;
  location: string;
  speaker: string;
  is_break: boolean;
  created_at: string;
}

export interface EventGallery {
  id: number;
  event: number;
  title: string;
  description: string;
  image: string;
  uploaded_at: string;
  is_featured: boolean;
  uploaded_by?: string;
  is_active?: boolean;
}

export interface EventDocument {
  id: number;
  event: number;
  title: string;
  description: string;
  document_type: string;
  document_type_display: string;
  file: string;
  file_size: number;
  file_extension: string;
  formatted_file_size: string;
  uploaded_by?: number;
  uploaded_by_name?: string;
  uploaded_at: string;
  is_public: boolean;
  download_count: number;
  is_active: boolean;
}

export interface PaginatedResponse<T> {
  count: number;
  next: string | null;
  previous: string | null;
  results: T[];
}

export interface ParticipantType {
  id: number;
  name: string;
  description: string;
  color: string;
  created_at: string;
}

export interface Participant {
  id: number;
  uuid: string;
  first_name: string;
  last_name: string;
  middle_name: string;
  email: string;
  phone: string;
  institution_name: string;
  position: string;
  event: number;
  participant_type: number;
  arrival_date: string;
  departure_date: string;
  profile_photo: string;
  badge_generated: boolean;
  badge_file?: string;
  remarks: string;
  registration_date: string;
  is_confirmed: boolean;
  status: 'pending' | 'approved' | 'rejected';
  full_name: string;
  participant_type_name: string;
  participant_type_color: string;
  event_name: string;
  badge_url?: string;
  // Assignment fields
  assigned_hotel?: number;
  assigned_driver?: number;
  assigned_contact_person?: number;
  assigned_hotel_name?: string;
  assigned_driver_name?: string;
  assigned_contact_person_name?: string;
  // Hotel details
  assigned_hotel_address?: string;
  assigned_hotel_phone?: string;
  assigned_hotel_email?: string;
  // Driver details
  assigned_driver_phone?: string;
  assigned_driver_car_model?: string;
  assigned_driver_car_plate?: string;
  // Contact Person details
  assigned_contact_person_phone?: string;
  assigned_contact_person_email?: string;
  assigned_contact_person_position?: string;
}

export interface VisitingInterest {
  id: number;
  name: string;
  description: string;
  location: string;
  event: number;
  is_active: boolean;
  max_participants?: number;
  current_participants_count: number;
  is_full: boolean;
  created_at: string;
  updated_at: string;
}

export interface ParticipantVisitingInterest {
  id: number;
  visiting_interest: number;
  visiting_interest_name: string;
  visiting_interest_description: string;
  visiting_interest_location: string;
  priority: number;
  selected_at: string;
}

export interface ParticipantRegistration {
  first_name: string;
  last_name: string;
  middle_name?: string;
  email: string;
  phone: string;
  institution_name: string;
  position: string;
  event: number;
  participant_type: number;
  arrival_date: string;
  departure_date: string;
  profile_photo: File;
  remarks?: string;
  visiting_interests?: number[];
}

// Hotel interfaces
export interface Hotel {
  id: number;
  name: string;
  address: string;
  phone: string;
  email: string;
  contact_person: string;
  latitude?: number;
  longitude?: number;
  star_rating?: number;
  website?: string;
  description: string;
  event: number;
  event_name: string;
  is_active: boolean;
  created_at: string;
  updated_at: string;
  available_rooms_count: number;
}



// Driver interfaces
export interface Driver {
  id: number;
  name: string;
  phone: string;
  email: string;
  photo?: string;
  car_plate: string;
  car_code: string;
  car_model: string;
  car_color: string;
  license_number: string;
  is_available: boolean;
  notes: string;
  event: number;
  event_name: string;
  created_at: string;
  updated_at: string;
}

export interface DriverAssignment {
  id: number;
  driver: number;
  participant: number;
  pickup_location: string;
  destination: string;
  pickup_time: string;
  status: 'scheduled' | 'in_progress' | 'completed' | 'cancelled';
  notes: string;
  created_at: string;
  updated_at: string;
  driver_name?: string;
  driver_phone?: string;
  driver_car_plate?: string;
  participant_name?: string;
  participant_phone?: string;
}

// Contact Person interfaces
export interface ContactPerson {
  id: number;
  first_name: string;
  middle_name: string;
  last_name: string;
  full_name: string;
  phone: string;
  email: string;
  photo?: string;
  position: string;
  organization: string;
  is_available: boolean;
  notes: string;
  event: number;
  event_name: string;
  created_at: string;
  updated_at: string;
}

// Badge interfaces
export interface Badge {
  id: number;
  participant: number;
  template?: number;
  qr_code_data: string;
  qr_code_image?: string;
  badge_image?: string;
  is_generated: boolean;
  generated_at?: string;
  created_at: string;
  updated_at: string;
  participant_name?: string;
  participant_email?: string;
  participant_type?: string;
  participant_type_color?: string;
  event_name?: string;
  template_name?: string;
}

export interface DriverBadge {
  id: number;
  driver: number;
  template?: number;
  qr_code_data: string;
  qr_code_image?: string;
  badge_image?: string;
  is_generated: boolean;
  generated_at?: string;
  created_at: string;
  updated_at: string;
  driver_name?: string;
  driver_email?: string;
  driver_phone?: string;
  car_plate?: string;
  car_model?: string;
  event_name?: string;
  template_name?: string;
}

export interface ContactPersonBadge {
  id: number;
  contact_person: number;
  template?: number;
  qr_code_data: string;
  qr_code_image?: string;
  badge_image?: string;
  is_generated: boolean;
  generated_at?: string;
  created_at: string;
  updated_at: string;
  contact_person_name?: string;
  contact_person_email?: string;
  contact_person_phone?: string;
  position?: string;
  organization?: string;
  event_name?: string;
  template_name?: string;
}

export interface BadgeTemplate {
  id: number;
  name: string;
  description: string;
  width: number;
  height: number;
  background_color: string;
  template_file?: string;
  is_default: boolean;
  created_at: string;
  updated_at: string;
}

export interface Developer {
  id: number;
  full_name: string;
  profession: string;
  linkedin_link?: string;
  linkedin_url?: string;
  github_url?: string;
  email?: string;
  photo?: string;
  photo_url?: string;
  bio?: string;
  order: number;
}

export interface PaginatedResponse<T> {
  count: number;
  next: string | null;
  previous: string | null;
  results: T[];
}

// API Services
export const eventService = {
  getEvents: () => api.get<PaginatedResponse<Event>>('/events/'),
  getEvent: (id: number) => api.get<Event>(`/events/${id}/`),
  createEvent: (data: FormData | Partial<Event>) => api.post<Event>('/events/', data),
  updateEvent: (id: number, data: FormData | Partial<Event>) => api.put<Event>(`/events/${id}/`, data),
  deleteEvent: (id: number) => api.delete(`/events/${id}/`),
  getEventSchedule: (id: number) => api.get<EventSchedule[]>(`/events/${id}/schedule/`),
  getEventGallery: (id: number) => api.get<EventGallery[]>(`/events/${id}/gallery/`),
  getEventParticipants: (id: number) => api.get<Participant[]>(`/events/${id}/participants/`),
  // Public endpoints
  getPublicEvents: () => api.get<PaginatedResponse<Event>>('/public/events/'),
  getPublicEvent: (id: number) => api.get<Event>(`/public/events/${id}/`),
};

// Old gallery service - replaced by new comprehensive gallery service below

export const participantService = {
  getParticipants: (eventId?: number, noPagination: boolean = false) => {
    const params: any = {};
    if (eventId) params.event = eventId;
    if (noPagination) params.no_pagination = 'true';
    return api.get<Participant[]>('/participants/', { params });
  },
  getAllParticipants: (eventId?: number) => {
    // Always fetch all participants without pagination
    const params: any = { no_pagination: 'true' };
    if (eventId) params.event = eventId;
    return api.get<Participant[]>('/participants/', { params });
  },
  getParticipant: (id: number) => api.get<Participant>(`/participants/${id}/`),
  registerParticipant: (data: FormData) => api.post<Participant>('/participants/', data, {
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  }),
  updateParticipant: (id: number, data: FormData) => api.put<Participant>(`/participants/${id}/`, data, {
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  }),
  deleteParticipant: (id: number) => api.delete(`/participants/${id}/`),
  confirmParticipant: (id: number) => api.post(`/participants/${id}/confirm/`),
  approveParticipant: (id: number) => api.post(`/participants/${id}/approve/`),
  rejectParticipant: (id: number) => api.post(`/participants/${id}/reject/`),
  assignHotel: (id: number, hotelId: number) => api.post(`/participants/${id}/assign_hotel/`, { hotel_id: hotelId }),
  assignDriver: (id: number, driverId: number) => api.post(`/participants/${id}/assign_driver/`, { driver_id: driverId }),
  assignContactPerson: (id: number, contactPersonId: number) => api.post(`/participants/${id}/assign_contact_person/`, { contact_person_id: contactPersonId }),
  getParticipantBadge: (id: number) => api.get(`/participants/${id}/badge/`),
  regenerateBadge: (id: number) => api.post(`/participants/${id}/regenerate_badge/`),
  verifyParticipant: (identifier: string) => {
    // Use appropriate parameter based on identifier type
    const param = identifier.includes('@') ? 'id' : 'uuid';
    return api.get(`/participants/verify/?${param}=${encodeURIComponent(identifier)}`);
  },
  exportCSV: (eventId?: number) => {
    const params = eventId ? { event: eventId } : {};
    return api.get('/participants/export_csv/', { params, responseType: 'blob' });
  },
  downloadSample: () => api.get('/participants/download_sample/', { responseType: 'blob' }),
  downloadSampleWithAssignments: () => api.get('/participants/download_sample_with_assignments/', { responseType: 'blob' }),
  downloadExcelTemplate: () => api.get('/participants/download_excel_template/', { responseType: 'blob' }),
  importCSV: (file: File) => {
    const formData = new FormData();
    formData.append('file', file);
    return api.post('/participants/import_csv/', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
  },
};

export const participantTypeService = {
  getParticipantTypes: () => api.get<ParticipantType[]>('/participant-types/'),
  getParticipantType: (id: number) => api.get<ParticipantType>(`/participant-types/${id}/`),
  createParticipantType: (data: Partial<ParticipantType>) => api.post<ParticipantType>('/participant-types/', data),
  updateParticipantType: (id: number, data: Partial<ParticipantType>) => api.put<ParticipantType>(`/participant-types/${id}/`, data),
  deleteParticipantType: (id: number) => api.delete(`/participant-types/${id}/`),
};

export const visitingInterestService = {
  getVisitingInterests: (eventId?: number) => {
    const params = eventId ? { event: eventId } : {};
    return api.get<VisitingInterest[]>('/visiting-interests/', { params });
  },
  getVisitingInterest: (id: number) => api.get<VisitingInterest>(`/visiting-interests/${id}/`),
  createVisitingInterest: (data: Partial<VisitingInterest>) => api.post<VisitingInterest>('/visiting-interests/', data),
  updateVisitingInterest: (id: number, data: Partial<VisitingInterest>) => api.put<VisitingInterest>(`/visiting-interests/${id}/`, data),
  deleteVisitingInterest: (id: number) => api.delete(`/visiting-interests/${id}/`),
};

export const attendanceService = {
  checkIn: (data: {
    uuid: string;
    event_schedule_id: number;
    checked_in_by?: string;
    notes?: string;
  }) => api.post('/attendance/check_in/', data),
  getAttendance: (participantId?: number, eventScheduleId?: number) => {
    const params: any = {};
    if (participantId) params.participant = participantId;
    if (eventScheduleId) params.event_schedule = eventScheduleId;
    return api.get('/attendance/', { params });
  },
};

// Hotel services
export const hotelService = {
  getHotels: (eventId?: number, isActive?: boolean) => {
    const params: any = {};
    if (eventId) params.event = eventId;
    if (isActive !== undefined) params.active = isActive;
    return api.get<Hotel[]>('/hotels/', { params });
  },
  getHotel: (id: number) => api.get<Hotel>(`/hotels/${id}/`),
  createHotel: (data: Partial<Hotel>) => api.post<Hotel>('/hotels/', data),
  updateHotel: (id: number, data: Partial<Hotel>) => api.put<Hotel>(`/hotels/${id}/`, data),
  deleteHotel: (id: number) => api.delete(`/hotels/${id}/`),
  exportCSV: () => api.get('/hotels/export_csv/', { responseType: 'blob' }),
  downloadSample: () => api.get('/hotels/download_sample/', { responseType: 'blob' }),
  importCSV: (file: File) => {
    const formData = new FormData();
    formData.append('file', file);
    return api.post('/hotels/import_csv/', formData);
  },
};

// Contact Person interface
export interface ContactPerson {
  id: number;
  first_name: string;
  middle_name: string;
  last_name: string;
  full_name: string;
  phone: string;
  email: string;
  photo?: string;
  position: string;
  organization: string;
  is_available: boolean;
  notes: string;
  event: number;
  event_name: string;
  created_at: string;
  updated_at: string;
}

// Contact Person services
export const contactPersonService = {
  getContactPersons: (eventId?: number, isAvailable?: boolean) => {
    const params: any = {};
    if (eventId) params.event = eventId;
    if (isAvailable !== undefined) params.is_available = isAvailable;
    return api.get<ContactPerson[]>('/contact-persons/', { params });
  },
  getContactPerson: (id: number) => api.get<ContactPerson>(`/contact-persons/${id}/`),
  createContactPerson: (data: Partial<ContactPerson>) => api.post<ContactPerson>('/contact-persons/', data),
  updateContactPerson: (id: number, data: Partial<ContactPerson>) => api.put<ContactPerson>(`/contact-persons/${id}/`, data),
  deleteContactPerson: (id: number) => api.delete(`/contact-persons/${id}/`),
  exportCSV: () => api.get('/contact-persons/export_csv/', { responseType: 'blob' }),
  downloadSample: () => api.get('/contact-persons/download_sample/', { responseType: 'blob' }),
  importCSV: (file: File) => {
    const formData = new FormData();
    formData.append('file', file);
    return api.post('/contact-persons/import_csv/', formData);
  },
};



// Driver services
export const driverService = {
  getDrivers: (eventId?: number, isAvailable?: boolean) => {
    const params: any = {};
    if (eventId) params.event = eventId;
    if (isAvailable !== undefined) params.available = isAvailable;
    return api.get<Driver[]>('/drivers/', { params });
  },
  getDriver: (id: number) => api.get<Driver>(`/drivers/${id}/`),
  createDriver: (data: FormData) => api.post<Driver>('/drivers/', data, {
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  }),
  updateDriver: (id: number, data: FormData) => api.put<Driver>(`/drivers/${id}/`, data, {
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  }),
  deleteDriver: (id: number) => api.delete(`/drivers/${id}/`),
  toggleAvailability: (id: number) => api.post(`/drivers/${id}/toggle_availability/`),
};

export const driverAssignmentService = {
  getAssignments: (driverId?: number, participantId?: number, status?: string) => {
    const params: any = {};
    if (driverId) params.driver = driverId;
    if (participantId) params.participant = participantId;
    if (status) params.status = status;
    return api.get<DriverAssignment[]>('/driver-assignments/', { params });
  },
  getAssignment: (id: number) => api.get<DriverAssignment>(`/driver-assignments/${id}/`),
  createAssignment: (data: Partial<DriverAssignment>) => api.post<DriverAssignment>('/driver-assignments/', data),
  updateAssignment: (id: number, data: Partial<DriverAssignment>) => api.put<DriverAssignment>(`/driver-assignments/${id}/`, data),
  deleteAssignment: (id: number) => api.delete(`/driver-assignments/${id}/`),
  startTrip: (id: number) => api.post(`/driver-assignments/${id}/start/`),
  completeTrip: (id: number) => api.post(`/driver-assignments/${id}/complete/`),
  cancelAssignment: (id: number) => api.post(`/driver-assignments/${id}/cancel/`),
};

// Badge services
export const badgeService = {
  getBadges: () => api.get<Badge[]>('/badges/'),
  getBadge: (id: number) => api.get<Badge>(`/badges/${id}/`),
  generateBadge: (id: number) => api.post(`/badges/${id}/generate/`),
  regenerateBadge: (id: number) => api.post(`/badges/${id}/regenerate/`),
  downloadBadge: (id: number) => api.get(`/badges/${id}/download/`, { responseType: 'blob' }),
  bulkGenerate: (participantIds: number[]) => api.post('/badges/bulk_generate/', { participant_ids: participantIds }),
};

export const driverBadgeService = {
  getDriverBadges: () => api.get<DriverBadge[]>('/driver-badges/'),
  getDriverBadge: (id: number) => api.get<DriverBadge>(`/driver-badges/${id}/`),
  generateDriverBadge: (id: number) => api.post(`/driver-badges/${id}/generate/`),
  regenerateDriverBadge: (id: number) => api.post(`/driver-badges/${id}/regenerate/`),
  downloadDriverBadge: (id: number) => api.get(`/driver-badges/${id}/download/`, { responseType: 'blob' }),
  bulkGenerateDriverBadges: (driverIds: number[]) => api.post('/driver-badges/bulk_generate/', { driver_ids: driverIds }),
};

export const contactPersonBadgeService = {
  getContactPersonBadges: () => api.get<ContactPersonBadge[]>('/contact-person-badges/'),
  getContactPersonBadge: (id: number) => api.get<ContactPersonBadge>(`/contact-person-badges/${id}/`),
  generateContactPersonBadge: (id: number) => api.post(`/contact-person-badges/${id}/generate/`),
  regenerateContactPersonBadge: (id: number) => api.post(`/contact-person-badges/${id}/regenerate/`),
  downloadContactPersonBadge: (id: number) => api.get(`/contact-person-badges/${id}/download/`, { responseType: 'blob' }),
  bulkGenerateContactPersonBadges: (contactPersonIds: number[]) => api.post('/contact-person-badges/bulk_generate/', { contact_person_ids: contactPersonIds }),
};

export const badgeTemplateService = {
  getBadgeTemplates: () => api.get<BadgeTemplate[]>('/badge-templates/'),
  getBadgeTemplate: (id: number) => api.get<BadgeTemplate>(`/badge-templates/${id}/`),
  createBadgeTemplate: (data: Omit<BadgeTemplate, 'id' | 'created_at' | 'updated_at'>) =>
    api.post<BadgeTemplate>('/badge-templates/', data),
  updateBadgeTemplate: (id: number, data: Partial<BadgeTemplate>) => api.put<BadgeTemplate>(`/badge-templates/${id}/`, data),
  deleteBadgeTemplate: (id: number) => api.delete(`/badge-templates/${id}/`),
  setDefault: (id: number) => api.post(`/badge-templates/${id}/set_default/`),
};

// Organization interfaces
export interface Organization {
  id: number | null;
  name: string;
  short_name: string;
  logo?: string | null;
  motto: string;
  description: string;
  email: string;
  phone: string;
  website: string;
  address_line_1?: string;
  address_line_2?: string;
  city?: string;
  state_province?: string;
  postal_code?: string;
  country?: string;
  full_address?: string;
  facebook_url?: string;
  twitter_url?: string;
  linkedin_url?: string;
  instagram_url?: string;
  is_active: boolean;
  is_primary: boolean;
  display_name?: string;
  created_at?: string;
  updated_at?: string;
  settings?: OrganizationSettings;
}

export interface OrganizationSettings {
  id: number;
  default_event_duration_hours: number;
  default_registration_fee: number;
  email_signature: string;
  primary_color: string;
  secondary_color: string;
  send_welcome_emails: boolean;
  send_confirmation_emails: boolean;
  send_reminder_emails: boolean;
  created_at: string;
  updated_at: string;
}

// Organization services
export const organizationService = {
  getOrganizations: (isActive?: boolean) => {
    const params: any = {};
    if (isActive !== undefined) params.is_active = isActive;
    return api.get<Organization[]>('/organizations/', { params });
  },
  getOrganization: (id: number) => api.get<Organization>(`/organizations/${id}/`),
  createOrganization: (data: FormData | Partial<Organization>) => api.post<Organization>('/organizations/', data),
  updateOrganization: (id: number, data: FormData | Partial<Organization>) => api.put<Organization>(`/organizations/${id}/`, data),
  deleteOrganization: (id: number) => api.delete(`/organizations/${id}/`),
  getPrimaryOrganization: () => api.get<Organization>('/organizations/primary/'),
  setPrimaryOrganization: (id: number) => api.post(`/organizations/${id}/set_primary/`),
  toggleActive: (id: number) => api.post(`/organizations/${id}/toggle_active/`),
  getSettings: (id: number) => api.get<OrganizationSettings>(`/organizations/${id}/organization_settings/`),
  createSettings: (id: number, data: Partial<OrganizationSettings>) => api.post<OrganizationSettings>(`/organizations/${id}/organization_settings/`, data),
  updateSettings: (id: number, data: Partial<OrganizationSettings>) => api.put<OrganizationSettings>(`/organizations/${id}/organization_settings/`, data),
  // Public endpoints
  getPublicOrganizations: () => api.get<PaginatedResponse<Organization>>('/public/organizations/'),
  getPublicOrganization: (id: number) => api.get<Organization>(`/public/organizations/${id}/`),
  // Bulk email
  sendBulkEmail: (data: any) => api.post('/organizations/bulk-email/send/', data),
};

export const developerService = {
  getDevelopers: () => api.get<PaginatedResponse<Developer>>('/auth/developers/'),
};

// Feedback service
export const feedbackService = {
  // Event feedback
  getEventFeedback: (eventId?: number) => {
    const params = eventId ? { event: eventId } : {};
    return api.get<PaginatedResponse<EventFeedback>>('/feedback/event-feedback/', { params });
  },
  getFeedback: (id: number) => api.get<EventFeedback>(`/feedback/event-feedback/${id}/`),
  createFeedback: (data: FormData | Partial<EventFeedback>) => api.post<EventFeedback>('/feedback/event-feedback/', data),
  updateFeedback: (id: number, data: FormData | Partial<EventFeedback>) => api.put<EventFeedback>(`/feedback/event-feedback/${id}/`, data),
  deleteFeedback: (id: number) => api.delete(`/feedback/event-feedback/${id}/`),

  // Check if feedback exists
  checkExistingFeedback: (eventId: number, participantId?: number, email?: string) => {
    const params: any = { event: eventId };
    if (participantId) params.participant = participantId;
    if (email) params.email = email;
    return api.get('/feedback/event-feedback/check_existing/', { params });
  },

  // Feedback statistics
  getFeedbackStats: (eventId: number) => api.get<FeedbackStats>(`/feedback/event-feedback/statistics/?event=${eventId}`),

  // Public feedback (with consent)
  getPublicFeedback: (eventId: number) => api.get<EventFeedback[]>(`/feedback/event-feedback/public_feedback/?event=${eventId}`),

  // Session feedback
  getSessionFeedback: (eventId?: number) => {
    const params = eventId ? { event: eventId } : {};
    return api.get<PaginatedResponse<SessionFeedback>>('/feedback/session-feedback/', { params });
  },
  createSessionFeedback: (data: Partial<SessionFeedback>) => api.post<SessionFeedback>('/feedback/session-feedback/', data),

  // Feedback templates
  getFeedbackTemplates: () => api.get<PaginatedResponse<FeedbackTemplate>>('/feedback/feedback-templates/'),
  getDefaultTemplate: (templateType: string = 'conference') => api.get<FeedbackTemplate>(`/feedback/feedback-templates/default_template/?type=${templateType}`),
  createTemplate: (data: Partial<FeedbackTemplate>) => api.post<FeedbackTemplate>('/feedback/feedback-templates/', data),
  updateTemplate: (id: number, data: Partial<FeedbackTemplate>) => api.put<FeedbackTemplate>(`/feedback/feedback-templates/${id}/`, data),
  deleteTemplate: (id: number) => api.delete(`/feedback/feedback-templates/${id}/`),
};

// Public Statistics interface
export interface PublicStatistics {
  events: {
    total: string;
    active: number;
    label: string;
  };
  participants: {
    total: string;
    confirmed: number;
    label: string;
  };
  photos: {
    total: string;
    featured: number;
    label: string;
  };
  countries: {
    total: string;
    label: string;
  };
  organizations: {
    total: string;
    label: string;
  };
}

// Public statistics service
export const publicService = {
  getStatistics: () => api.get<PublicStatistics>('/public/statistics/'),
};

// Feedback types
export interface EventFeedback {
  id: number;
  event: number;
  event_name: string;
  participant?: number;
  participant_name_display: string;
  participant_name?: string;
  participant_email?: string;
  institution_name?: string;
  position_title?: string;
  overall_satisfaction: number;
  met_expectations: 'yes' | 'somewhat' | 'no';
  most_valuable_aspect: string;
  improvement_suggestions?: string;
  session_relevance: number;
  most_valuable_sessions?: string;
  future_topics_suggestions?: string;
  speaker_quality: number;
  speaker_comments?: string;
  venue_facilities: number;
  technical_setup: number;
  time_management: number;
  transportation_accessibility: number;
  pre_event_communication: number;
  logistics_comments?: string;
  sufficient_networking: 'yes' | 'somewhat' | 'no';
  networking_improvements?: string;
  future_topics?: string;
  additional_feedback?: string;
  uploaded_file?: File | null;
  consent_given: boolean;
  is_anonymous: boolean;
  session_feedback?: SessionFeedback[];
  session_feedback_data?: any[];
  average_rating: number;
  created_at: string;
  updated_at: string;
}

export interface SessionFeedback {
  id: number;
  session: number;
  session_title: string;
  session_speaker: string;
  content_quality: number;
  speaker_effectiveness: number;
  session_organization: number;
  audience_engagement: number;
  what_worked_well?: string;
  improvement_suggestions?: string;
  additional_comments?: string;
  average_rating: number;
  created_at: string;
  updated_at: string;
}

export interface FeedbackTemplate {
  id: number;
  name: string;
  template_type: 'conference' | 'workshop' | 'seminar' | 'symposium' | 'meeting' | 'custom';
  description?: string;
  form_config: any;
  is_active: boolean;
  is_default: boolean;
  created_at: string;
  updated_at: string;
}

export interface FeedbackStats {
  total_feedback: number;
  average_overall_satisfaction: number;
  average_session_relevance: number;
  average_speaker_quality: number;
  average_logistics_rating: number;
  satisfaction_distribution: { [key: string]: number };
  expectations_met_distribution: { [key: string]: number };
  networking_satisfaction_distribution: { [key: string]: number };
  top_valuable_aspects: string[];
  top_improvement_suggestions: string[];
  top_future_topics: string[];
}

// Gallery types
export interface GalleryCategory {
  id: number;
  name: string;
  description: string;
  slug: string;
  icon: string;
  color: string;
  is_active: boolean;
  order: number;
  image_count: number;
  created_at: string;
  updated_at: string;
}

export interface GalleryImage {
  id: number;
  title: string;
  description: string;
  image: string;
  thumbnail: string;
  category: number;
  category_name: string;
  category_slug: string;
  category_color: string;
  tags: string;
  tag_list: string[];
  is_featured: boolean;
  is_slider: boolean;
  is_campus: boolean;
  is_active: boolean;
  photographer: string;
  date_taken: string;
  location: string;
  alt_text: string;
  order: number;
  created_at: string;
  updated_at: string;
}

export interface EventSlider {
  id: number;
  event: number;
  event_name: string;
  event_start_date: string;
  event_end_date: string;
  event_location: string;
  event_city: string;
  event_country: string;
  event_description: string;
  event_participant_count: number;
  slider_title: string;
  slider_subtitle: string;
  slider_description: string;
  display_title: string;
  display_subtitle: string;
  display_description: string;
  background_image: string;
  background_color: string;
  background_color_end: string;
  background_gradient: string;
  is_active: boolean;
  order: number;
  cta_text: string;
  cta_url: string;
  created_at: string;
  updated_at: string;
}

// Document service
export const documentService = {
  getDocuments: (eventId?: number) => {
    const params: any = {};
    if (eventId) params.event = eventId;
    return api.get<PaginatedResponse<EventDocument>>('/documents/', { params });
  },
  getDocument: (id: number) => api.get<EventDocument>(`/documents/${id}/`),
  createDocument: (data: FormData) => api.post<EventDocument>('/documents/', data),
  updateDocument: (id: number, data: FormData | Partial<EventDocument>) => api.put<EventDocument>(`/documents/${id}/`, data),
  deleteDocument: (id: number) => api.delete(`/documents/${id}/`),
  downloadDocument: (id: number) => api.get(`/documents/${id}/download/`, { responseType: 'blob' }),
  bulkUpload: (data: FormData) => api.post('/documents/bulk_upload/', data),
};

// Gallery service
export const galleryService = {
  // Categories
  getCategories: () => api.get<PaginatedResponse<GalleryCategory>>('/site-gallery/categories/'),
  getCategory: (id: number) => api.get<GalleryCategory>(`/site-gallery/categories/${id}/`),
  createCategory: (data: Partial<GalleryCategory>) => api.post<GalleryCategory>('/site-gallery/categories/', data),
  updateCategory: (id: number, data: Partial<GalleryCategory>) => api.put<GalleryCategory>(`/site-gallery/categories/${id}/`, data),
  deleteCategory: (id: number) => api.delete(`/site-gallery/categories/${id}/`),
  getCategoryImages: (id: number, params?: any) => api.get<GalleryImage[]>(`/site-gallery/categories/${id}/images/`, { params }),

  // Images
  getImages: (params?: any) => api.get<PaginatedResponse<GalleryImage>>('/site-gallery/images/', { params }),
  getImage: (id: number) => api.get<GalleryImage>(`/site-gallery/images/${id}/`),
  createImage: (data: FormData) => api.post<GalleryImage>('/site-gallery/images/', data),
  updateImage: (id: number, data: FormData | Partial<GalleryImage>) => api.put<GalleryImage>(`/site-gallery/images/${id}/`, data),
  deleteImage: (id: number) => api.delete(`/site-gallery/images/${id}/`),
  getFeaturedImages: (params?: any) => api.get<GalleryImage[]>('/site-gallery/images/featured/', { params }),
  getSliderImages: (params?: any) => api.get<GalleryImage[]>('/site-gallery/images/slider_images/', { params }),
  getCampusImages: (params?: any) => api.get<GalleryImage[]>('/site-gallery/images/campus_images/', { params }),

  // Event Sliders
  getEventSliders: (params?: any) => api.get<PaginatedResponse<EventSlider>>('/site-gallery/sliders/', { params }),
  getActiveSliders: (params?: any) => api.get<EventSlider[]>('/site-gallery/sliders/active_sliders/', { params }),
  getEventSlider: (id: number) => api.get<EventSlider>(`/site-gallery/sliders/${id}/`),
  createEventSlider: (data: FormData | Partial<EventSlider>) => api.post<EventSlider>('/site-gallery/sliders/', data),
  updateEventSlider: (id: number, data: FormData | Partial<EventSlider>) => api.put<EventSlider>(`/site-gallery/sliders/${id}/`, data),
  deleteEventSlider: (id: number) => api.delete(`/site-gallery/sliders/${id}/`),
};

// Helper function to get full URL for media files
export const getMediaUrl = (path: string): string => {
  if (!path) return '';
  if (path.startsWith('http')) {
    // If it's a full URL, check if it's localhost and convert to relative
    if (path.includes('localhost')) {
      // Extract the path part after /media/
      const mediaIndex = path.indexOf('/media/');
      if (mediaIndex !== -1) {
        return path.substring(mediaIndex);
      }
    }
    return path;
  }
  // If BACKEND_URL is set, use it, otherwise use relative path
  return BACKEND_URL ? `${BACKEND_URL}${path}` : path;
};

export default api;
