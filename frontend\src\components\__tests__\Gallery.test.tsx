import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import '@testing-library/jest-dom';
import Gallery from '../Gallery';
import { EventGallery } from '../../services/api';

// Mock the API module
jest.mock('../../services/api', () => ({
  getMediaUrl: (path: string) => path,
}));

const mockImages: EventGallery[] = [
  {
    id: 1,
    title: "Test Image 1",
    description: "This is a test image description",
    image: "/media/test1.jpg",
    uploaded_at: "2024-01-15T10:30:00Z",
    is_featured: true,
    event: 1
  },
  {
    id: 2,
    title: "Test Image 2",
    description: "Another test image",
    image: "/media/test2.jpg",
    uploaded_at: "2024-01-16T14:20:00Z",
    is_featured: false,
    event: 1
  }
];

describe('Gallery Component', () => {
  test('renders empty state when no images provided', () => {
    render(<Gallery images={[]} />);
    expect(screen.getByText('No Images Available')).toBeInTheDocument();
  });

  test('renders custom empty state message', () => {
    render(
      <Gallery 
        images={[]} 
        emptyStateMessage="No photos found"
      />
    );
    expect(screen.getByText('No photos found')).toBeInTheDocument();
  });

  test('renders images in grid layout', () => {
    render(<Gallery images={mockImages} />);
    
    expect(screen.getByText('Test Image 1')).toBeInTheDocument();
    expect(screen.getByText('Test Image 2')).toBeInTheDocument();
  });

  test('shows title when provided', () => {
    render(
      <Gallery 
        images={mockImages} 
        title="Test Gallery"
      />
    );
    expect(screen.getByText('Test Gallery')).toBeInTheDocument();
  });

  test('shows featured badge when enabled', () => {
    render(
      <Gallery 
        images={mockImages} 
        showFeaturedBadge={true}
      />
    );
    expect(screen.getByText('Featured')).toBeInTheDocument();
  });

  test('hides featured badge when disabled', () => {
    render(
      <Gallery 
        images={mockImages} 
        showFeaturedBadge={false}
      />
    );
    expect(screen.queryByText('Featured')).not.toBeInTheDocument();
  });

  test('shows download buttons when enabled', () => {
    render(
      <Gallery 
        images={mockImages} 
        showDownloadButton={true}
      />
    );
    const downloadButtons = screen.getAllByTitle('Download image');
    expect(downloadButtons).toHaveLength(2);
  });

  test('hides download buttons when disabled', () => {
    render(
      <Gallery 
        images={mockImages} 
        showDownloadButton={false}
      />
    );
    const downloadButtons = screen.queryAllByTitle('Download image');
    expect(downloadButtons).toHaveLength(0);
  });

  test('opens modal when image is clicked', () => {
    render(<Gallery images={mockImages} />);
    
    const firstImage = screen.getByAltText('Test Image 1');
    fireEvent.click(firstImage);
    
    // Modal should be open with the image title
    expect(screen.getByRole('dialog')).toBeInTheDocument();
  });

  test('shows date grouping when enabled', () => {
    render(
      <Gallery 
        images={mockImages} 
        showDateGrouping={true}
      />
    );
    
    // Should show date headers
    expect(screen.getByText(/Monday, January 15, 2024/)).toBeInTheDocument();
    expect(screen.getByText(/Tuesday, January 16, 2024/)).toBeInTheDocument();
  });

  test('applies custom grid columns', () => {
    const { container } = render(
      <Gallery 
        images={mockImages} 
        gridColumns={{ lg: 6, md: 12, sm: 12 }}
      />
    );
    
    // Check if the correct Bootstrap classes are applied
    const columns = container.querySelectorAll('.col-lg-6');
    expect(columns).toHaveLength(2);
  });

  test('applies custom image height', () => {
    render(
      <Gallery 
        images={mockImages} 
        imageHeight="300px"
      />
    );
    
    const images = screen.getAllByRole('img');
    images.forEach(img => {
      expect(img).toHaveStyle('height: 300px');
    });
  });

  test('applies custom className', () => {
    const { container } = render(
      <Gallery 
        images={mockImages} 
        className="custom-gallery"
      />
    );
    
    expect(container.firstChild).toHaveClass('custom-gallery');
  });
});
