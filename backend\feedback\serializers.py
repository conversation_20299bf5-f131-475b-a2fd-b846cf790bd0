from rest_framework import serializers
from .models import EventFeedback, SessionFeedback, FeedbackTemplate
from events.models import Event, EventSchedule
from participants.models import Participant


class SessionFeedbackSerializer(serializers.ModelSerializer):
    session_title = serializers.CharField(source='session.title', read_only=True)
    session_speaker = serializers.CharField(source='session.speaker', read_only=True)
    average_rating = serializers.ReadOnlyField()
    
    class Meta:
        model = SessionFeedback
        fields = [
            'id', 'session', 'session_title', 'session_speaker',
            'content_quality', 'speaker_effectiveness', 'session_organization', 
            'audience_engagement', 'what_worked_well', 'improvement_suggestions',
            'additional_comments', 'average_rating', 'created_at', 'updated_at'
        ]


class EventFeedbackSerializer(serializers.ModelSerializer):
    session_feedback = SessionFeedbackSerializer(many=True, read_only=True)
    event_name = serializers.Char<PERSON>ield(source='event.name', read_only=True)
    participant_name_display = serializers.Char<PERSON>ield(source='participant_display_name', read_only=True)
    average_rating = serializers.ReadOnlyField()



    # Optional fields for creating feedback
    session_feedback_data = serializers.ListField(
        child=serializers.DictField(), write_only=True, required=False
    )
    
    class Meta:
        model = EventFeedback
        fields = [
            'id', 'event', 'event_name', 'participant', 'participant_name_display',
            'participant_name', 'participant_email', 'institution_name', 'position_title',
            'overall_satisfaction', 'met_expectations', 'most_valuable_aspect',
            'improvement_suggestions', 'session_relevance', 'most_valuable_sessions',
            'future_topics_suggestions', 'speaker_quality', 'speaker_comments',
            'venue_facilities', 'technical_setup', 'time_management',
            'transportation_accessibility', 'pre_event_communication', 'logistics_comments',
            'sufficient_networking', 'networking_improvements', 'future_topics',
            'additional_feedback', 'uploaded_file', 'consent_given', 'is_anonymous',
            'session_feedback', 'session_feedback_data', 'average_rating',
            'created_at', 'updated_at'
        ]
        extra_kwargs = {
            'participant': {'required': False, 'allow_null': True},
            'ip_address': {'write_only': True},
            'user_agent': {'write_only': True},
        }

    def to_internal_value(self, data):
        """Override to handle participant field for unregistered participants"""
        # Always set participant to None for unregistered participants
        if 'participant' in data:
            data = data.copy()  # Make a mutable copy
            data['participant'] = None

        return super().to_internal_value(data)
    
    def create(self, validated_data):
        session_feedback_data = validated_data.pop('session_feedback_data', [])
        
        # Get IP address and user agent from request context
        request = self.context.get('request')
        if request:
            validated_data['ip_address'] = self.get_client_ip(request)
            validated_data['user_agent'] = request.META.get('HTTP_USER_AGENT', '')
        
        # Create the main feedback
        feedback = EventFeedback.objects.create(**validated_data)
        
        # Create session feedback if provided
        for session_data in session_feedback_data:
            session_id = session_data.pop('session_id', None)
            if session_id:
                try:
                    session = EventSchedule.objects.get(id=session_id, event=feedback.event)
                    SessionFeedback.objects.create(
                        event_feedback=feedback,
                        session=session,
                        **session_data
                    )
                except EventSchedule.DoesNotExist:
                    pass  # Skip invalid sessions
        
        return feedback
    
    def get_client_ip(self, request):
        """Get client IP address from request"""
        x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
        if x_forwarded_for:
            ip = x_forwarded_for.split(',')[0]
        else:
            ip = request.META.get('REMOTE_ADDR')
        return ip
    
    def validate(self, data):
        """Custom validation for unregistered participants"""
        # Set default values for required fields if not provided
        defaults = {
            'overall_satisfaction': 3,
            'met_expectations': 'somewhat',
            'most_valuable_aspect': 'No specific comment provided',
            'session_relevance': 3,
            'speaker_quality': 3,
            'venue_facilities': 3,
            'technical_setup': 3,
            'time_management': 3,
            'transportation_accessibility': 3,
            'pre_event_communication': 3,
            'sufficient_networking': 'somewhat',
        }

        # Apply defaults for missing required fields
        for field, default_value in defaults.items():
            if field not in data or data[field] is None or data[field] == '':
                data[field] = default_value

        # Always set participant to None since we don't associate with registered participants
        # This allows unregistered participants to submit feedback
        data['participant'] = None

        # If no participant name is provided, make it anonymous
        if not data.get('participant_name'):
            data['is_anonymous'] = True

        return data


class EventFeedbackListSerializer(serializers.ModelSerializer):
    """Simplified serializer for listing feedback"""
    event_name = serializers.CharField(source='event.name', read_only=True)
    participant_name_display = serializers.CharField(source='participant_display_name', read_only=True)
    average_rating = serializers.ReadOnlyField()
    session_count = serializers.SerializerMethodField()
    
    class Meta:
        model = EventFeedback
        fields = [
            'id', 'event', 'event_name', 'participant_name_display',
            'overall_satisfaction', 'average_rating', 'session_count',
            'consent_given', 'is_anonymous', 'created_at'
        ]
    
    def get_session_count(self, obj):
        """Get count of session feedback"""
        return obj.session_feedback.count()


class FeedbackTemplateSerializer(serializers.ModelSerializer):
    class Meta:
        model = FeedbackTemplate
        fields = [
            'id', 'name', 'template_type', 'description', 'form_config',
            'is_active', 'is_default', 'created_at', 'updated_at'
        ]


class EventFeedbackStatsSerializer(serializers.Serializer):
    """Serializer for feedback statistics"""
    total_feedback = serializers.IntegerField()
    average_overall_satisfaction = serializers.FloatField()
    average_session_relevance = serializers.FloatField()
    average_speaker_quality = serializers.FloatField()
    average_logistics_rating = serializers.FloatField()
    
    # Distribution of ratings
    satisfaction_distribution = serializers.DictField()
    expectations_met_distribution = serializers.DictField()
    networking_satisfaction_distribution = serializers.DictField()
    
    # Top feedback themes
    top_valuable_aspects = serializers.ListField()
    top_improvement_suggestions = serializers.ListField()
    top_future_topics = serializers.ListField()


class PublicEventFeedbackSerializer(serializers.ModelSerializer):
    """Public serializer for displaying feedback (without sensitive data)"""
    event_name = serializers.CharField(source='event.name', read_only=True)
    participant_name_display = serializers.SerializerMethodField()
    average_rating = serializers.ReadOnlyField()
    
    class Meta:
        model = EventFeedback
        fields = [
            'id', 'event_name', 'participant_name_display', 'institution_name',
            'overall_satisfaction', 'most_valuable_aspect', 'average_rating',
            'created_at'
        ]
    
    def get_participant_name_display(self, obj):
        """Return participant name or anonymous based on consent"""
        if obj.consent_given and not obj.is_anonymous:
            return obj.participant_display_name
        else:
            return "Anonymous Participant"
