/* Modern Event Registration Page Styles */

.register-page {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  position: relative;
  overflow-x: hidden;
}

.register-page::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: 
    radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 40% 40%, rgba(120, 119, 198, 0.2) 0%, transparent 50%);
  pointer-events: none;
}

.registration-container {
  position: relative;
  z-index: 2;
  padding: 2rem 1rem;
}

/* Header Section */
.registration-header {
  margin-bottom: 3rem;
}

.header-content {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-radius: 24px;
  padding: 3rem 2rem;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  max-width: 800px;
  margin: 0 auto;
}

.gradient-text {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.stats-row {
  margin-top: 2rem;
}

.stat-item {
  text-align: center;
  padding: 1rem;
  background: rgba(255, 255, 255, 0.8);
  border-radius: 16px;
  min-width: 120px;
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);
}

.stat-number {
  display: block;
  font-size: 2rem;
  font-weight: 700;
  color: #667eea;
  line-height: 1;
}

.stat-label {
  display: block;
  font-size: 0.875rem;
  color: #6c757d;
  margin-top: 0.25rem;
}

/* Search and Filter Section */
.search-filter-section {
  margin-bottom: 3rem;
}

.search-filter-card {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-radius: 20px;
  padding: 2rem;
  box-shadow: 0 15px 30px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.search-box {
  position: relative;
}

.search-icon {
  position: absolute;
  left: 1rem;
  top: 50%;
  transform: translateY(-50%);
  color: #6c757d;
  z-index: 3;
}

.search-input {
  padding: 1rem 1rem 1rem 3rem;
  border: 2px solid rgba(102, 126, 234, 0.2);
  border-radius: 16px;
  font-size: 1rem;
  transition: all 0.3s ease;
  background: rgba(255, 255, 255, 0.9);
}

.search-input:focus {
  border-color: #667eea;
  box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
  background: white;
}

.filter-select {
  padding: 1rem;
  border: 2px solid rgba(102, 126, 234, 0.2);
  border-radius: 16px;
  font-size: 1rem;
  transition: all 0.3s ease;
  background: rgba(255, 255, 255, 0.9);
}

.filter-select:focus {
  border-color: #667eea;
  box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
}

/* Events Grid */
.events-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 2rem;
  margin-bottom: 3rem;
}

.event-card-wrapper {
  perspective: 1000px;
}

.event-card {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-radius: 24px;
  padding: 0;
  border: 2px solid rgba(255, 255, 255, 0.3);
  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
  transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  cursor: pointer;
  overflow: hidden;
  position: relative;
}

.event-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
  transform: scaleX(0);
  transition: transform 0.3s ease;
}

.event-card:hover::before,
.event-card.selected::before {
  transform: scaleX(1);
}

.event-card:hover {
  transform: translateY(-8px) scale(1.02);
  box-shadow: 0 25px 50px rgba(0, 0, 0, 0.15);
  border-color: #667eea;
}

.event-card.selected {
  border-color: #667eea;
  background: rgba(102, 126, 234, 0.05);
  transform: translateY(-4px);
  box-shadow: 0 20px 40px rgba(102, 126, 234, 0.2);
}

/* Event Header */
.event-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: 1.5rem 1.5rem 0;
  margin-bottom: 1rem;
}

.event-date-badge {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 0.75rem;
  border-radius: 16px;
  text-align: center;
  min-width: 80px;
  box-shadow: 0 8px 16px rgba(102, 126, 234, 0.3);
}

.date-month {
  font-size: 0.875rem;
  font-weight: 600;
  line-height: 1;
}

.date-year {
  font-size: 1.25rem;
  font-weight: 700;
  line-height: 1;
  margin-top: 0.25rem;
}

.status-badge {
  padding: 0.5rem 1rem;
  border-radius: 20px;
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.status-warning { background: #fff3cd; color: #856404; }
.status-info { background: #d1ecf1; color: #0c5460; }
.status-success { background: #d4edda; color: #155724; }
.status-secondary { background: #e2e3e5; color: #383d41; }

/* Custom Radio Button */
.radio-custom {
  position: relative;
  width: 24px;
  height: 24px;
}

.radio-custom input {
  opacity: 0;
  position: absolute;
  width: 100%;
  height: 100%;
  margin: 0;
  cursor: pointer;
}

.radio-checkmark {
  position: absolute;
  top: 0;
  left: 0;
  width: 24px;
  height: 24px;
  background: white;
  border: 2px solid #dee2e6;
  border-radius: 50%;
  transition: all 0.3s ease;
}

.radio-custom input:checked + .radio-checkmark {
  background: #667eea;
  border-color: #667eea;
}

.radio-checkmark::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 8px;
  height: 8px;
  background: white;
  border-radius: 50%;
  transform: translate(-50%, -50%) scale(0);
  transition: transform 0.2s ease;
}

.radio-custom input:checked + .radio-checkmark::after {
  transform: translate(-50%, -50%) scale(1);
}

/* Event Content */
.event-content {
  padding: 0 1.5rem 1rem;
}

.event-title {
  font-size: 1.25rem;
  font-weight: 700;
  color: #2d3748;
  margin-bottom: 0.75rem;
  line-height: 1.3;
}

.event-description {
  color: #6c757d;
  font-size: 0.95rem;
  line-height: 1.5;
  margin-bottom: 1rem;
}

.event-details {
  margin-bottom: 1rem;
}

.detail-item {
  display: flex;
  align-items: center;
  margin-bottom: 0.5rem;
  font-size: 0.875rem;
  color: #6c757d;
}

.detail-item i {
  width: 16px;
  margin-right: 0.75rem;
  color: #667eea;
}

.countdown-info {
  display: flex;
  align-items: center;
  background: rgba(102, 126, 234, 0.1);
  padding: 0.5rem 1rem;
  border-radius: 12px;
  font-size: 0.875rem;
  color: #667eea;
  font-weight: 600;
}

.countdown-info i {
  margin-right: 0.5rem;
}

/* Event Footer */
.event-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem 1.5rem;
  background: rgba(248, 249, 250, 0.8);
  border-top: 1px solid rgba(0, 0, 0, 0.05);
}

.event-city {
  font-size: 0.875rem;
  color: #6c757d;
}

.select-text {
  font-size: 0.875rem;
  color: #667eea;
  font-weight: 600;
}

.event-card.selected .select-text {
  color: #667eea;
}

/* Registration Info Section */
.registration-info-section {
  margin: 3rem 0;
}

.registration-info-card {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-radius: 24px;
  padding: 2.5rem;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.info-header {
  text-align: center;
  margin-bottom: 2rem;
}

.info-title {
  font-size: 1.5rem;
  font-weight: 700;
  color: #2d3748;
  margin-bottom: 0.5rem;
}

.info-subtitle {
  color: #6c757d;
  font-size: 1rem;
}

.steps-container {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 2rem;
}

.step-item {
  display: flex;
  align-items: flex-start;
  gap: 1rem;
}

.step-icon {
  width: 48px;
  height: 48px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 1.25rem;
  flex-shrink: 0;
}

.step-content h6 {
  font-weight: 600;
  color: #2d3748;
  margin-bottom: 0.5rem;
}

.step-content p {
  color: #6c757d;
  font-size: 0.875rem;
  margin: 0;
}

/* Action Buttons */
.action-buttons-section {
  margin: 3rem 0;
}

.action-buttons {
  display: flex;
  gap: 1rem;
  justify-content: center;
  align-items: center;
}

.action-buttons .btn {
  padding: 1rem 2rem;
  border-radius: 16px;
  font-weight: 600;
  font-size: 1rem;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.action-buttons .btn-primary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border: none;
  box-shadow: 0 8px 16px rgba(102, 126, 234, 0.3);
}

.action-buttons .btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 12px 24px rgba(102, 126, 234, 0.4);
}

.btn-shine {
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  transition: left 0.6s ease;
}

.btn-primary:hover .btn-shine {
  left: 100%;
}

/* No Events Section */
.no-events-section {
  min-height: 60vh;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 3rem 1rem;
}

.no-events-container {
  text-align: center;
  max-width: 600px;
}

.no-events-content {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-radius: 24px;
  padding: 3rem 2rem;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.no-events-icon {
  font-size: 4rem;
  color: #6c757d;
  margin-bottom: 1.5rem;
}

.no-events-title {
  font-size: 2rem;
  font-weight: 700;
  color: #2d3748;
  margin-bottom: 1rem;
}

.no-events-description {
  color: #6c757d;
  font-size: 1.1rem;
  margin-bottom: 2rem;
  line-height: 1.6;
}

.no-events-actions {
  display: flex;
  gap: 1rem;
  justify-content: center;
  flex-wrap: wrap;
}

.no-events-found {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-radius: 20px;
  margin: 2rem 0;
  box-shadow: 0 15px 30px rgba(0, 0, 0, 0.1);
}

/* Responsive Design */
@media (max-width: 768px) {
  .events-grid {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }
  
  .action-buttons {
    flex-direction: column;
    width: 100%;
  }
  
  .action-buttons .btn {
    width: 100%;
  }
  
  .stats-row {
    flex-direction: column;
    gap: 1rem;
  }
  
  .steps-container {
    grid-template-columns: 1fr;
  }
  
  .search-filter-card .row {
    flex-direction: column;
  }
  
  .header-content {
    padding: 2rem 1rem;
  }
}

@media (max-width: 480px) {
  .event-header {
    flex-direction: column;
    gap: 1rem;
    align-items: center;
  }
  
  .event-footer {
    flex-direction: column;
    gap: 0.5rem;
    text-align: center;
  }
  
  .registration-container {
    padding: 1rem 0.5rem;
  }
}
