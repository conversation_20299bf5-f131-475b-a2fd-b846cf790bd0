from rest_framework import viewsets, status, permissions
from rest_framework.decorators import action, api_view, permission_classes
from rest_framework.response import Response
from django.shortcuts import get_object_or_404

from .models import Organization, OrganizationSettings
from .serializers import (
    OrganizationListSerializer,
    OrganizationDetailSerializer,
    OrganizationCreateUpdateSerializer,
    OrganizationSettingsSerializer
)


class OrganizationViewSet(viewsets.ModelViewSet):
    """ViewSet for managing organizations"""
    
    queryset = Organization.objects.all()
    permission_classes = [permissions.IsAuthenticated]
    
    def get_serializer_class(self):
        if self.action == 'list':
            return OrganizationListSerializer
        elif self.action in ['create', 'update', 'partial_update']:
            return OrganizationCreateUpdateSerializer
        return OrganizationDetailSerializer
    
    def get_queryset(self):
        """Filter queryset based on user permissions"""
        queryset = Organization.objects.all()
        
        # Filter by active status if requested
        is_active = self.request.query_params.get('is_active')
        if is_active is not None:
            queryset = queryset.filter(is_active=is_active.lower() == 'true')
        
        return queryset.order_by('-is_primary', 'name')
    
    def perform_create(self, serializer):
        """Create organization with default settings"""
        organization = serializer.save()
        
        # Create default settings for the organization
        OrganizationSettings.objects.create(organization=organization)
    
    @action(detail=True, methods=['get', 'put', 'patch'])
    def organization_settings(self, request, pk=None):
        """Manage organization settings"""
        organization = self.get_object()
        settings, created = OrganizationSettings.objects.get_or_create(
            organization=organization
        )
        
        if request.method == 'GET':
            serializer = OrganizationSettingsSerializer(settings)
            return Response(serializer.data)
        
        elif request.method in ['PUT', 'PATCH']:
            partial = request.method == 'PATCH'
            serializer = OrganizationSettingsSerializer(
                settings, 
                data=request.data, 
                partial=partial
            )
            
            if serializer.is_valid():
                serializer.save()
                return Response(serializer.data)
            return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)
    
    @action(detail=True, methods=['post'])
    def set_primary(self, request, pk=None):
        """Set this organization as the primary organization"""
        organization = self.get_object()
        
        # Remove primary status from all other organizations
        Organization.objects.filter(is_primary=True).update(is_primary=False)
        
        # Set this organization as primary
        organization.is_primary = True
        organization.save()
        
        serializer = self.get_serializer(organization)
        return Response(serializer.data)
    
    @action(detail=False, methods=['get'], permission_classes=[permissions.AllowAny])
    def primary(self, request):
        """Get the primary organization"""
        try:
            primary_org = Organization.objects.get(is_primary=True)
            serializer = self.get_serializer(primary_org)
            return Response(serializer.data)
        except Organization.DoesNotExist:
            return Response(
                {'detail': 'No primary organization found'},
                status=status.HTTP_404_NOT_FOUND
            )
    
    @action(detail=True, methods=['post'])
    def toggle_active(self, request, pk=None):
        """Toggle organization active status"""
        organization = self.get_object()
        organization.is_active = not organization.is_active
        organization.save()
        
        serializer = self.get_serializer(organization)
        return Response(serializer.data)
    
    def destroy(self, request, *args, **kwargs):
        """Custom delete with validation"""
        organization = self.get_object()
        
        # Prevent deletion of primary organization
        if organization.is_primary:
            return Response(
                {'detail': 'Cannot delete the primary organization'}, 
                status=status.HTTP_400_BAD_REQUEST
            )
        
        # Check if organization has associated events
        if hasattr(organization, 'events') and organization.events.exists():
            return Response(
                {'detail': 'Cannot delete organization with associated events'}, 
                status=status.HTTP_400_BAD_REQUEST
            )
        
        return super().destroy(request, *args, **kwargs)


@api_view(['GET'])
@permission_classes([permissions.AllowAny])
def get_primary_organization(request):
    """Get the primary organization for branding purposes"""
    try:
        organization = Organization.objects.filter(is_primary=True).first()
        if organization:
            serializer = OrganizationDetailSerializer(organization)
            return Response(serializer.data)
        else:
            # Return default University of Gondar data if no primary organization exists
            return Response({
                'id': None,
                'name': 'University of Gondar',
                'short_name': 'UoG',
                'motto': 'Excellence in Education, Research and Community Service',
                'logo': None,
                'website': 'https://www.uog.edu.et',
                'email': '<EMAIL>',
                'phone': '+251-58-114-1240',
                'full_address': 'Gondar, Amhara Region, Ethiopia',
                'is_primary': True
            })
    except Exception as e:
        return Response(
            {'error': 'Failed to fetch organization data'},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )


@api_view(['POST'])
@permission_classes([permissions.IsAuthenticated])
def bulk_email_send(request):
    """API endpoint for sending bulk emails from frontend"""
    try:
        data = request.data
        event_id = data.get('event_id')
        subject = data.get('subject', '').strip()
        content = data.get('content', '').strip()
        recipient_type = data.get('recipient_type', 'all')
        participant_type_ids = data.get('participant_type_ids', [])

        if not subject or not content:
            return Response({'error': 'Subject and content are required'}, status=status.HTTP_400_BAD_REQUEST)

        if not event_id:
            return Response({'error': 'Event selection is required'}, status=status.HTTP_400_BAD_REQUEST)

        # Import here to avoid circular imports
        from events.models import Event
        from participants.models import Participant, ParticipantType
        from events.email_service import get_email_service

        try:
            event = Event.objects.get(id=event_id)
        except Event.DoesNotExist:
            return Response({'error': 'Event not found'}, status=status.HTTP_404_NOT_FOUND)

        # Build participant queryset based on filters
        # Use approved status instead of is_confirmed for better filtering
        participants_qs = Participant.objects.filter(event=event, status='approved')

        if recipient_type == 'participant_type' and participant_type_ids:
            participants_qs = participants_qs.filter(participant_type_id__in=participant_type_ids)

        participants = list(participants_qs)

        if not participants:
            return Response({'error': 'No participants found matching the criteria'}, status=status.HTTP_400_BAD_REQUEST)

        # Send emails
        email_service = get_email_service()
        if not email_service.config:
            return Response({'error': 'Email configuration not found. Please configure email settings first.'}, status=status.HTTP_400_BAD_REQUEST)

        success_count = 0
        failed_count = 0

        for participant in participants:
            try:
                # Create context for email
                context = {
                    'participant_name': participant.full_name,
                    'event_name': event.name,
                    'event_date': event.start_date.strftime('%B %d, %Y') if event.start_date else 'TBD',
                    'event_location': event.location or 'TBD',
                    'organization_name': 'University of Gondar',
                    'content': content,
                }

                # Render content with context
                rendered_content = content.format(**context)
                rendered_subject = subject.format(**context)

                # Create HTML email content
                html_content = f"""
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{rendered_subject}</title>
    <style>
        body {{ font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; margin: 0; padding: 0; background-color: #f8f9fa; }}
        .container {{ max-width: 600px; margin: 0 auto; background-color: #ffffff; }}
        .header {{ background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%); color: white; padding: 30px; text-align: center; }}
        .header h1 {{ margin: 0; font-size: 28px; font-weight: 300; }}
        .content {{ padding: 40px 30px; }}
        .content h2 {{ color: #1e3c72; margin-top: 0; }}
        .content p {{ line-height: 1.6; color: #333; margin-bottom: 15px; }}
        .event-info {{ background-color: #f8f9fa; padding: 20px; border-radius: 8px; margin: 20px 0; }}
        .event-info h3 {{ color: #1e3c72; margin-top: 0; }}
        .footer {{ background-color: #1e3c72; color: white; padding: 20px; text-align: center; font-size: 14px; }}
        .footer a {{ color: #ffffff; text-decoration: none; }}
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>📢 Event Announcement</h1>
        </div>
        <div class="content">
            <h2>Dear {participant.full_name},</h2>
            <div style="white-space: pre-line;">{rendered_content}</div>

            <div class="event-info">
                <h3>📅 Event Information</h3>
                <p><strong>Event:</strong> {event.name}</p>
                <p><strong>Date:</strong> {context['event_date']}</p>
                <p><strong>Location:</strong> {context['event_location']}</p>
            </div>

            <p>If you have any questions, please don't hesitate to contact our events team.</p>

            <p>Best regards,<br>
            <strong>University of Gondar Events Team</strong></p>
        </div>
        <div class="footer">
            <p>📧 <EMAIL> | 📞 +251-58-114-1240<br>
            🌐 <a href="https://www.uog.edu.et">www.uog.edu.et</a></p>
        </div>
    </div>
</body>
</html>
                """

                # Send email using the email service
                if email_service._send_raw_email(
                    recipient_email=participant.email,
                    recipient_name=participant.full_name,
                    subject=rendered_subject,
                    html_content=html_content,
                    template_type='custom'
                ):
                    success_count += 1
                else:
                    failed_count += 1

            except Exception as e:
                print(f"Failed to send email to {participant.email}: {e}")
                failed_count += 1

        return Response({
            'success': True,
            'message': f'Bulk email sent successfully! {success_count} emails sent, {failed_count} failed.',
            'success_count': success_count,
            'failed_count': failed_count,
            'total_count': len(participants)
        })

    except Exception as e:
        return Response({'error': f'An error occurred: {str(e)}'}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
