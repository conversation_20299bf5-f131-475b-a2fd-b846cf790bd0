#!/usr/bin/env python3

import os
import sys
import django
from datetime import datetime

# Add the project root to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'event_management.settings')
django.setup()

from participants.models import Participant, ParticipantType
from events.models import Event

def create_final_participants():
    """Create final batch of participants with exact IDs"""
    
    # Get the event (assuming Event ID 1)
    try:
        event = Event.objects.get(id=1)
    except Event.DoesNotExist:
        print("Error: Event with ID 1 not found")
        return
    
    # Get all participant types
    participant_types = {pt.id: pt for pt in ParticipantType.objects.all()}
    
    # Final participants data
    final_participants = [
        # (id, first_name, last_name, middle_name, email, phone, institution_name, participant_type_id, arrival_date, departure_date, profile_photo, remarks, status)
        (2, "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "kux<PERSON>@mailinator.com", "097 9883653", "<PERSON> and Sampson Traders", 4, "2025-07-29 20:41:00", "2025-07-31 17:09:00", None, "Nulla ut deserunt si", "deleted"),
        (3, "Tewodros", "Chekol", "Abebaw", "<EMAIL>", "091 0153087", "University of Gondar", 4, "2025-07-31 05:54:00", "2025-08-04 05:54:00", "event-registration/profile_6886e69e84ab6.jpg", "", "deleted"),
        (4, "Asrat", "Andargie", "Atsedeweyn", "<EMAIL>", "+251 930001197", "University of Gondar", 2, "2025-09-03 06:58:00", "2025-09-07 06:59:00", None, "", "active"),
        (5, "Chirotaw", "Gizaw", "Ayele", "<EMAIL>", "091 6581479", "Hawassa University", 2, "2025-08-03 13:35:00", "2025-08-07 08:30:00", "event-registration/profile_6886fb1bddfec.jpg", "", "active"),
        (6, "Yonas", "Workneh", "Kefialew", "<EMAIL>", "095 6529252", "Gambella University", 23, "2025-08-03 08:20:00", "2025-08-06 02:00:00", "event-registration/profile_6886fb8e154b2.jpg", "", "active"),
        (7, "Fisiha", "Argaw", "Getachew", "<EMAIL>", "091 9682266", "Hawassa University", 23, "2025-07-28 07:22:00", "2025-07-31 07:22:00", "event-registration/profile_6886fbc405514.jpg", "", "active"),
        (8, "Awol", "Ebrahim", "Seid", "<EMAIL>", "097 9018485", "Wollo university", 2, "2025-08-03 07:25:00", "2025-08-07 07:25:00", None, "", "active"),
        (9, "Teshome", "Ababu", "Mulugeta", "<EMAIL>", "091 2381518", "Salale University", 4, "2025-08-04 11:26:00", "2025-08-07 21:27:00", None, "thanks for hosting", "active"),
        (10, "Dr.Mustefa", "Geda", "Bati", "<EMAIL>", "092 0397305", "Arsi University", 23, "2025-08-03 01:35:00", "2025-08-07 06:35:00", None, "", "active"),
        (11, "Getachew", "Dagnew", "Gashaw", "<EMAIL>", "091 1182683", "Oda Bultum University", 8, "2025-08-05 00:00:00", "2025-09-07 10:00:00", "event-registration/profile_688700baa12f1.jpg", "", "active"),
        (12, "Muktar", "Yusuf", "Mohammed", "<EMAIL>", "093 0313003", "Oda Bultun University", 2, "2025-07-28 19:42:00", "2025-07-29 19:35:00", "event-registration/profile_688700fb0a7c9.jpg", "", "active"),
        (13, "Tafesse", "Karo", "Matewos", "<EMAIL>", "+251 916580266", "Hawassa University", 23, "2025-08-03 13:30:00", "2025-08-07 08:00:00", "event-registration/profile_6887019e9192a.jpg", "", "active"),
        (14, "Essey", "Muluneh", "Kebede", "<EMAIL>", "093 2808002", "Bahir Dar university", 23, "2025-08-03 14:50:00", "2025-08-06 11:00:00", "event-registration/profile_688702e7c3962.jpg", "", "active"),
        (15, "Jemal", "A", "Abafita", "<EMAIL>", "096 7876050", "Jimma University", 2, "2025-08-03 14:01:00", "2025-09-05 12:02:00", "event-registration/profile_68870504143f5.jpg", "", "active"),
        (16, "Dr. Feyera", "Hundessa", "Dinsa", "<EMAIL>", "091 1235189", "Salale University", 2, "2025-08-03 14:00:00", "2025-08-07 09:00:00", "event-registration/profile_688706a543075.png", "Dr. Feyera Dinsa Hundessa President of Salale Un...", "active"),
        (17, "Alemu", "Mulleta", "Disassa", "<EMAIL>", "091 1159465", "Mattu University", 2, "2025-07-28 08:10:00", "2025-08-07 19:10:00", "event-registration/profile_688707c50d0f7.jpg", "", "active"),
        (18, "Fisiha", "Argaw", "Getachew", "<EMAIL>", "091 6580261", "Hawassa University", 23, "2025-08-03 08:31:00", "2025-08-07 08:31:00", "event-registration/profile_68870bc9791db.jpg", "", "active"),
        (19, "Berhanemeskel", "Zemenfes", "Tena", "<EMAIL>", "+251 911487816", "Kotebe University of Education", 2, "2025-07-28 08:36:00", "2025-07-30 10:05:00", None, "", "active"),
        (20, "Hawa", "Yimer", "Wolie", "<EMAIL>", "094 5023757", "Wollo University", 23, "2025-08-03 16:00:00", "2025-08-07 08:30:00", None, "", "active"),
        (21, "Tsegaye", "W.medihin", "Deyou", "<EMAIL>", "093 0364625", "Salale University", 23, "2025-08-03 12:30:00", "2025-08-06 03:30:00", None, "", "active"),
        (22, "Dr Lemi", "Enyadene", "Guta", "<EMAIL>", "091 1491772", "ASTU", 2, "2025-08-03 14:07:00", "2025-08-06 14:09:00", "event-registration/profile_688713d090249.jpg", "", "active"),
        (23, "Dr Mohammed", "Darasa", "Udman", "<EMAIL>", "099 1277901", "Samara University", 2, "2025-08-03 15:30:00", "2025-08-07 09:30:00", None, "", "active"),
        (24, "Dr lemma", "Gudata", "Beressa", "<EMAIL>", "091 1263952", "ASTU", 23, "2025-08-03 14:15:00", "2025-08-06 14:16:00", "event-registration/profile_688718e38e0fc.jpg", "", "active"),
        (25, "Dr Ibsa", "Hassen", "Ahmed", "<EMAIL>", "093 5085977", "Oda Bultum Unvirsity", 23, "2025-08-03 14:30:00", "2025-08-06 12:31:00", None, "", "active"),
        (26, "Alemayehu", "Abdi", "Beyene", "<EMAIL>", "093 3775511", "Oda Bultum University", 23, "2025-08-03 14:30:00", "2025-08-06 14:30:00", None, "", "active"),
        (27, "Teramaj", "Kabtimer", "Abebe", "<EMAIL>", "091 3554201", "Mekdela Amba University Educational quality improv...", 8, "2026-09-05 15:31:00", "2026-09-09 14:31:00", None, "", "active"),
        (28, "Tadesse", "Mamo", "Regassa", "<EMAIL>", "091 7804372", "Dembi Dollo University", 2, "2025-08-04 06:56:00", "2025-08-09 09:57:00", "event-registration/profile_68872041d36eb.jpg", "", "active"),
        (29, "Dr Teshome", "Segne", "Abdo", "<EMAIL>", "093 0107537", "ASTU", 23, "2025-08-03 19:51:00", "2025-08-06 13:46:00", "event-registration/profile_68872096ae4c1.jpg", "", "active"),
        (30, "Dr Solomon", "Gebrekirstos", "Abrha", "<EMAIL>", "091 1766278", "CEO for governance and infrastructure, Ministry of...", 8, "2025-08-03 10:06:00", "2025-08-07 10:07:00", "event-registration/profile_68872217300a8.jpeg", "", "active"),
        (31, "Dr Solomon", "Dibeba", "Tiruneh", "<EMAIL>", "097 0113003", "ASTU", 23, "2025-08-03 14:23:00", "2025-08-06 14:23:00", "event-registration/profile_6887265de27db.jpg", "", "active"),
        (32, "BESHIR", "MAHAMMOUD", "ABDULLAHI", "<EMAIL>", "093 3004455", "Jigjiga University", 2, "2025-08-03 12:15:00", "2025-08-30 06:00:00", None, "", "active"),
        (33, "Wohabie", "Bitew", "Birhan", "<EMAIL>", "091 1305606", "Injibara University", 23, "2025-08-03 13:15:00", "2025-08-08 13:15:00", "event-registration/profile_68872ca040915.jpg", "", "active"),
        (34, "Kindie", "Fenta", "Birhan", "<EMAIL>", "091 8779183", "Injibara University", 23, "2025-08-03 13:15:00", "2025-08-08 13:15:00", "event-registration/profile_68872ceaa7fbc.jpg", "", "active"),
        (36, "Prof Daniel", "Azene", "Kitaw", "<EMAIL>", "094 3294343", "ASTU", 1, "2025-08-03 14:35:00", "2025-08-06 14:35:00", "event-registration/profile_68872dd945c52.jpg", "", "active"),
        (37, "Aemero", "Tiku", "Tadesse", "<EMAIL>", "093 0416920", "Injibara university", 23, "2025-08-03 19:15:00", "2025-08-08 19:15:00", "event-registration/profile_68872eb37faaa.jpg", "", "active"),
        (38, "Fikadu", "Abdissa", "Mitiku", "<EMAIL>", "092 3612548", "Arsi University", 2, "2025-08-03 11:08:00", "2025-08-07 11:08:00", None, "", "active"),
        (39, "Esayas", "Mengistu", "Lakew", "<EMAIL>", "091 3103634", "Mettu University", 25, "2025-07-28 10:39:00", "2025-07-31 10:39:00", "event-registration/profile_68873185245a4.jpg", "I am Strategic affair executive but the is no opti...", "active"),
        (40, "Dr. Hailu", "Demessie", "Fekadu", "<EMAIL>", "091 1717608", "Arsi University", 23, "2025-08-03 01:35:00", "2025-08-07 12:45:00", None, "", "active"),
        (41, "Lingerew", "Zegeye", "Atinkut", "<EMAIL>", "091 8768242", "Injibara University", 8, "2025-08-03 03:40:00", "2025-08-07 08:30:00", "event-registration/profile_6887351e7d26d.jpg", "I am Strategic Affairs Executive In Injibara Univ...", "active"),
        (42, "Galma", "Huka", "Bonaya", "<EMAIL>", "092 4690022", "Borana University", 23, "2025-08-03 10:50:00", "2025-08-07 10:20:00", None, "", "active"),
        (43, "Gebrekidan", "Weldeslasse", "Tesfay", "<EMAIL>", "+251 914314743", "Adigrat University", 23, "2025-08-03 11:52:00", "2025-08-07 11:52:00", None, "", "active"),
        (44, "Kula", "Tache", "Jilo", "<EMAIL>", "091 6323883", "Borana University", 23, "2025-08-03 22:30:00", "2025-09-06 09:30:00", None, "", "active"),
        (45, "Alemayehu", "Haye", "Teklemariam", "<EMAIL>", "091 1436898", "Denbidolo University", 29, "2025-08-03 12:29:00", "2025-08-07 12:29:00", "event-registration/profile_68874401bae0e.jpg", "", "active"),
        (46, "Samuel", "Dira", "Jilo", "<EMAIL>", "091 6580260", "Hawassa University", 23, "2025-08-03 13:34:00", "2025-08-07 20:35:00", None, "", "active"),
        (47, "Befekadu", "Beyenssa", "Chemere", "<EMAIL>", "091 1840705", "Borana University", 2, "2025-08-03 10:17:00", "2025-08-07 10:17:00", None, "", "active"),
        (48, "Solomon", "Mamo", "Yifru", "<EMAIL>", "091 3272172", "Arba Minch University", 8, "2025-08-03 07:09:00", "2025-08-07 07:09:00", None, "", "active"),
        (49, "Dr. Abdlmuhsin", "Alajib", "Hassen", "<EMAIL>", "091 1284201", "Assosa University", 23, "2025-08-03 14:00:00", "2025-08-07 10:30:00", None, "", "active"),
        (50, "Mengesha", "Ejigu", "Ayene", "<EMAIL>", "093 0377642", "Bahir Dar University", 2, "2025-08-03 16:01:00", "2025-08-05 17:01:00", "event-registration/profile_68874ddc84180.jpg", "", "active"),
        (51, "YARED", "Gelaw", "Mulu", "<EMAIL>", "093 0317480", "DEBARK UNIVERSITY", 23, "2025-09-03 16:30:00", "2025-09-07 14:30:00", None, "", "active"),
        (52, "Shimeles", "Tessema", "Bekele", "<EMAIL>", "091 8708091", "BDU", 8, "2025-08-02 09:49:00", "2025-08-04 10:30:00", None, "", "active"),
        (53, "Dr Kemal Abdurahim", "Ahmed", "Abdurahim", "<EMAIL>", "097 9600008", "Assosa University", 2, "2025-08-03 12:15:00", "2025-08-07 10:20:00", "event-registration/profile_688755e902704.jpg", "", "active"),
        (54, "Mrs Mesay", "Assefa", "Mesfin", "<EMAIL>", "091 1741789", "ASTU", 4, "2025-08-03 14:09:00", "2025-08-06 14:09:00", "event-registration/profile_68875756a5455.jpg", "", "active"),
        (55, "Dr. Kassa", "Retta", "Shawle", "<EMAIL>", "090 0975369", "Mekdela Amba University", 2, "2025-08-03 11:23:00", "2025-08-07 11:23:00", "event-registration/profile_688757dde3d77.jpg", "", "active"),
        (56, "Dr. Diriba", "Gemeda", "Diba", "<EMAIL>", "091 1895155", "Wollega University", 23, "2025-08-03 10:01:00", "2025-08-07 08:04:00", "event-registration/profile_688759b40ce6d.jpg", "See you in Gondor, a historical city!", "active"),
        (57, "Duresa", "Geleto", "Deksiso", "<EMAIL>", "921477136", "Arsi University", 1, "2025-08-05 08:00:00", "2025-09-07 08:00:00", None, "", "active"),
        (58, "Degela", "Done", "Ergano", "<EMAIL>", "091 1030822", "Bonga University", 2, "2025-07-28 14:13:00", "2025-08-27 11:12:00", None, "", "active"),
        (59, "Towfik", "Ali", "Jemal", "<EMAIL>", "096 0344784", "Werabe University", 2, "2025-08-03 16:15:00", "2025-08-06 17:20:00", None, "", "active"),
        (60, "Dr. Reda", "Hora", "Nemo", "<EMAIL>", "+251 917842053", "Dambi Dollo University", 23, "2025-08-03 12:00:00", "2025-08-07 08:30:00", "event-registration/profile_68875f8d72cbe.jpg", "", "active"),
        (61, "Dr Gemechu", "Kerorsa", "Berhanu", "<EMAIL>", "091 7491514", "Dambi Dollo University", 23, "2025-08-03 12:20:00", "2025-08-07 08:30:00", "event-registration/profile_68875fca471b8.jpg", "", "active"),
        (62, "Harbora", "Racha", "Bule", "<EMAIL>", "091 6178794", "Borana University", 4, "2025-08-03 12:15:00", "2025-08-03 22:50:00", "event-registration/profile_688760c9538c3.jpg", "", "active"),
    ]
    
    created_count = 0
    
    for p_data in final_participants:
        # Check if participant type exists
        if p_data[7] not in participant_types:
            print(f"Warning: Participant type {p_data[7]} not found, skipping participant {p_data[1]} {p_data[2]}")
            continue
            
        # Check if participant already exists
        if Participant.objects.filter(id=p_data[0]).exists():
            print(f"Participant with ID {p_data[0]} already exists, skipping {p_data[1]} {p_data[2]}")
            continue
            
        # Check if email already exists
        if Participant.objects.filter(email=p_data[4]).exists():
            print(f"Email {p_data[4]} already exists, skipping participant {p_data[1]} {p_data[2]}")
            continue
            
        try:
            participant = Participant(
                first_name=p_data[1],
                last_name=p_data[2],
                middle_name=p_data[3],
                email=p_data[4],
                phone=p_data[5],
                institution_name=p_data[6],
                position="Participant",  # Default position
                participant_type=participant_types[p_data[7]],
                arrival_date=p_data[8],
                departure_date=p_data[9],
                profile_photo=p_data[10],
                remarks=p_data[11] or "",
                status=p_data[12],
                event=event
            )
            participant.save()
            
            # Set the ID after saving to preserve exact IDs
            participant.id = p_data[0]
            participant.save()
            
            created_count += 1
            print(f"Created participant: {p_data[1]} {p_data[2]} (ID: {p_data[0]})")
            
        except Exception as e:
            print(f"Error creating participant {p_data[1]} {p_data[2]}: {str(e)}")
    
    print(f"\nCreated {created_count} final participants.")

if __name__ == "__main__":
    create_final_participants()
