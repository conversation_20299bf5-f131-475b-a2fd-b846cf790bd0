#!/usr/bin/env python3
"""
Test script to demonstrate QR verification criteria
This script creates test participants with different statuses to test QR verification
"""

import os
import sys
import django

# Setup Django environment
sys.path.append('/path/to/your/backend')
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'event_management.settings')
django.setup()

from participants.models import Participant
from events.models import Event
import requests

def create_test_participants():
    """Create test participants with different statuses"""
    
    # Get the first event
    event = Event.objects.first()
    if not event:
        print("❌ No event found. Please create an event first.")
        return
    
    test_participants = [
        {
            'email': '<EMAIL>',
            'first_name': 'Approved',
            'last_name': 'Participant',
            'status': 'approved',
            'is_confirmed': True,
            'description': '✅ Should work - approved and confirmed'
        },
        {
            'email': '<EMAIL>',
            'first_name': 'Pending',
            'last_name': 'Participant',
            'status': 'pending',
            'is_confirmed': True,
            'description': '⏳ Should show pending - not yet approved'
        },
        {
            'email': '<EMAIL>',
            'first_name': 'Rejected',
            'last_name': 'Participant',
            'status': 'rejected',
            'is_confirmed': True,
            'description': '❌ Should show rejected - cannot check in'
        },
        {
            'email': '<EMAIL>',
            'first_name': 'Unconfirmed',
            'last_name': 'Participant',
            'status': 'approved',
            'is_confirmed': False,
            'description': '⚠️ Should show not confirmed - approved but not confirmed'
        },
        {
            'email': '<EMAIL>',
            'first_name': 'Assigned',
            'last_name': 'Participant',
            'status': 'assigned',
            'is_confirmed': True,
            'description': '✅ Should work - assigned status (hotel/driver assigned)'
        }
    ]
    
    print("🔧 Creating test participants with different statuses...")
    
    for participant_data in test_participants:
        # Check if participant already exists
        existing = Participant.objects.filter(email=participant_data['email']).first()
        if existing:
            print(f"📝 Updating existing participant: {participant_data['email']}")
            existing.status = participant_data['status']
            existing.is_confirmed = participant_data['is_confirmed']
            existing.save()
        else:
            print(f"➕ Creating new participant: {participant_data['email']}")
            Participant.objects.create(
                event=event,
                first_name=participant_data['first_name'],
                last_name=participant_data['last_name'],
                email=participant_data['email'],
                phone='000000000',
                institution_name='Test University',
                position='Test Participant',
                status=participant_data['status'],
                is_confirmed=participant_data['is_confirmed']
            )

def test_qr_verification():
    """Test QR verification for different participant statuses"""
    
    test_emails = [
        '<EMAIL>',
        '<EMAIL>', 
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>'  # This should fail
    ]
    
    print("\n🧪 Testing QR verification for different participant statuses...")
    print("=" * 80)
    
    for email in test_emails:
        print(f"\n📧 Testing: {email}")
        try:
            response = requests.get(f'http://localhost:8000/api/participants/verify/?id={email}')
            
            if response.status_code == 200:
                data = response.json()
                print(f"✅ Status: {response.status_code}")
                print(f"   Name: {data.get('full_name', 'N/A')}")
                print(f"   Registration Status: {data.get('participant_status', 'N/A')}")
                print(f"   Is Confirmed: {data.get('is_confirmed', 'N/A')}")
                print(f"   Can Check In: {data.get('can_check_in', 'N/A')}")
                print(f"   Verification Status: {data.get('verification_status', 'N/A')}")
                print(f"   Message: {data.get('verification_message', 'N/A')}")
            else:
                print(f"❌ Status: {response.status_code}")
                print(f"   Error: {response.text}")
                
        except Exception as e:
            print(f"💥 Exception: {str(e)}")
        
        print("-" * 40)

def show_verification_criteria():
    """Show the current QR verification criteria"""
    
    print("\n📋 QR VERIFICATION CRITERIA")
    print("=" * 50)
    print("For a participant to successfully check in via QR code:")
    print()
    print("✅ REQUIRED CONDITIONS:")
    print("   1. Participant must exist in database")
    print("   2. is_confirmed = True")
    print("   3. status = 'approved' OR status = 'assigned'")
    print("   4. status ≠ 'rejected'")
    print("   5. Must have valid email address")
    print()
    print("📊 STATUS MEANINGS:")
    print("   • pending: Registration submitted, awaiting admin approval")
    print("   • approved: Registration approved, can check in")
    print("   • rejected: Registration rejected, cannot check in")
    print("   • assigned: Hotel/driver assigned, can check in")
    print("   • email_sent: Details email sent, can check in")
    print()
    print("🔍 VERIFICATION RESPONSES:")
    print("   • approved: ✅ Ready for check-in")
    print("   • pending: ⏳ Pending approval")
    print("   • rejected: ❌ Registration rejected")
    print("   • not_confirmed: ⚠️ Registration not confirmed")
    print("   • not_found: 🚫 Participant not found")

if __name__ == "__main__":
    print("🎯 QR VERIFICATION CRITERIA TEST")
    print("=" * 50)
    
    show_verification_criteria()
    
    # Uncomment the lines below to create test participants and run tests
    # create_test_participants()
    # test_qr_verification()
    
    print("\n💡 To run the full test:")
    print("   1. Uncomment the last two function calls in this script")
    print("   2. Run: python manage.py shell < test_qr_verification_criteria.py")
    print("   3. Test QR codes with the different email addresses")
