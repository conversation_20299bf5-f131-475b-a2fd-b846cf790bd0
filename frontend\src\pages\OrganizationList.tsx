import React, { useState, useEffect } from 'react';
import { Con<PERSON>er, <PERSON>, Col, Card, Badge, Form, InputGroup, Alert, Spin<PERSON>, Button, Modal, Table } from 'react-bootstrap';
import { Link } from 'react-router-dom';
import { organizationService, Organization, getMediaUrl } from '../services/api';

const OrganizationList: React.FC = () => {
  const [organizations, setOrganizations] = useState<Organization[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [filterStatus, setFilterStatus] = useState<string>('');
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [selectedOrganization, setSelectedOrganization] = useState<Organization | null>(null);
  const [deleteLoading, setDeleteLoading] = useState(false);

  useEffect(() => {
    fetchOrganizations();
  }, []);

  const fetchOrganizations = async () => {
    try {
      setLoading(true);
      const response = await organizationService.getOrganizations();
      
      // Handle paginated responses
      const organizationsData = Array.isArray(response.data) 
        ? response.data 
        : (response.data as any).results || [];
      
      setOrganizations(organizationsData);
      setError(null);
    } catch (err) {
      console.error('Error fetching organizations:', err);
      setError('Failed to load organizations. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const filteredOrganizations = organizations.filter(org => {
    const matchesSearch = 
      org.name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      org.short_name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      org.email?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      org.city?.toLowerCase().includes(searchTerm.toLowerCase());
    
    const matchesStatus = filterStatus === '' || 
      (filterStatus === 'active' && org.is_active) ||
      (filterStatus === 'inactive' && !org.is_active) ||
      (filterStatus === 'primary' && org.is_primary);

    return matchesSearch && matchesStatus;
  });

  const handleDeleteOrganization = async () => {
    if (!selectedOrganization || !selectedOrganization.id) return;

    try {
      setDeleteLoading(true);
      await organizationService.deleteOrganization(selectedOrganization.id);
      await fetchOrganizations(); // Refresh the list
      setShowDeleteModal(false);
      setSelectedOrganization(null);
      setError(null);
    } catch (err) {
      console.error('Error deleting organization:', err);
      setError('Failed to delete organization. Please try again.');
    } finally {
      setDeleteLoading(false);
    }
  };

  const handleSetPrimary = async (org: Organization) => {
    if (!org.id) return;

    try {
      await organizationService.setPrimaryOrganization(org.id);
      await fetchOrganizations(); // Refresh the list
      setError(null);
    } catch (err) {
      console.error('Error setting primary organization:', err);
      setError('Failed to set primary organization. Please try again.');
    }
  };

  const handleToggleActive = async (org: Organization) => {
    if (!org.id) return;

    try {
      await organizationService.toggleActive(org.id);
      await fetchOrganizations(); // Refresh the list
      setError(null);
    } catch (err) {
      console.error('Error toggling organization status:', err);
      setError('Failed to update organization status. Please try again.');
    }
  };

  const openDeleteModal = (org: Organization) => {
    setSelectedOrganization(org);
    setShowDeleteModal(true);
  };

  if (loading) {
    return (
      <Container className="text-center mt-5">
        <Spinner animation="border" role="status">
          <span className="visually-hidden">Loading...</span>
        </Spinner>
        <p className="mt-3">Loading organizations...</p>
      </Container>
    );
  }

  return (
    <Container className="py-4">
      <Row className="mb-4">
        <Col>
          <div className="d-flex justify-content-between align-items-center mb-4">
            <div>
              <h1 className="display-5 fw-bold text-primary">
                <i className="fas fa-building me-3"></i>
                Organization Management
              </h1>
              <p className="lead text-muted">Manage organization details, settings, and branding</p>
            </div>
            <Link to="/organizations/new" className="btn btn-primary btn-lg">
              <i className="fas fa-plus me-2"></i>
              Add Organization
            </Link>
          </div>

          {/* Search and Filter Controls */}
          <Row className="mb-4">
            <Col md={6}>
              <InputGroup>
                <InputGroup.Text>
                  <i className="fas fa-search"></i>
                </InputGroup.Text>
                <Form.Control
                  type="text"
                  placeholder="Search organizations by name, email, or city..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                />
              </InputGroup>
            </Col>
            <Col md={6}>
              <Form.Select
                value={filterStatus}
                onChange={(e) => setFilterStatus(e.target.value)}
              >
                <option value="">All Organizations</option>
                <option value="active">Active Organizations</option>
                <option value="inactive">Inactive Organizations</option>
                <option value="primary">Primary Organization</option>
              </Form.Select>
            </Col>
          </Row>

          {error && (
            <Alert variant="danger" className="mb-4">
              <i className="fas fa-exclamation-triangle me-2"></i>
              {error}
            </Alert>
          )}

          {/* Organizations Table */}
          <Card className="shadow-sm">
            <Card.Header className="bg-primary text-white">
              <h5 className="mb-0">
                <i className="fas fa-building me-2"></i>
                Organizations ({filteredOrganizations.length})
              </h5>
            </Card.Header>
            <Card.Body className="p-0">
              {filteredOrganizations.length === 0 ? (
                <div className="text-center py-5">
                  <i className="fas fa-building fa-3x text-muted mb-3"></i>
                  <h5 className="text-muted">No organizations found</h5>
                  <p className="text-muted">Try adjusting your search or filter criteria.</p>
                  <Link to="/organizations/new" className="btn btn-primary">
                    <i className="fas fa-plus me-2"></i>
                    Add Your First Organization
                  </Link>
                </div>
              ) : (
                <Table responsive hover className="mb-0">
                  <thead className="bg-light">
                    <tr>
                      <th>Organization</th>
                      <th>Contact</th>
                      <th>Location</th>
                      <th>Status</th>
                      <th>Actions</th>
                    </tr>
                  </thead>
                  <tbody>
                    {filteredOrganizations.map((org) => (
                      <tr key={org.id}>
                        <td>
                          <div className="d-flex align-items-center">
                            {org.logo && (
                              <img 
                                src={getMediaUrl(org.logo)} 
                                alt={org.name}
                                className="rounded me-3"
                                style={{ width: '40px', height: '40px', objectFit: 'cover' }}
                              />
                            )}
                            <div>
                              <h6 className="mb-0">{org.name}</h6>
                              {org.short_name && (
                                <small className="text-muted">({org.short_name})</small>
                              )}
                              {org.motto && (
                                <div>
                                  <small className="text-muted fst-italic">"{org.motto}"</small>
                                </div>
                              )}
                            </div>
                          </div>
                        </td>
                        <td>
                          <div>
                            <div>
                              <i className="fas fa-envelope me-1 text-muted"></i>
                              <a href={`mailto:${org.email}`} className="text-decoration-none">
                                {org.email}
                              </a>
                            </div>
                            {org.phone && (
                              <div>
                                <i className="fas fa-phone me-1 text-muted"></i>
                                <a href={`tel:${org.phone}`} className="text-decoration-none">
                                  {org.phone}
                                </a>
                              </div>
                            )}
                            {org.website && (
                              <div>
                                <i className="fas fa-globe me-1 text-muted"></i>
                                <a href={org.website} target="_blank" rel="noopener noreferrer" className="text-decoration-none">
                                  Website
                                </a>
                              </div>
                            )}
                          </div>
                        </td>
                        <td>
                          <div>
                            {org.city && org.country ? (
                              <span>{org.city}, {org.country}</span>
                            ) : (
                              <span className="text-muted">Not specified</span>
                            )}
                          </div>
                        </td>
                        <td>
                          <div className="d-flex flex-column gap-1">
                            <Badge bg={org.is_active ? 'success' : 'secondary'}>
                              {org.is_active ? 'Active' : 'Inactive'}
                            </Badge>
                            {org.is_primary && (
                              <Badge bg="warning" text="dark">
                                <i className="fas fa-star me-1"></i>
                                Primary
                              </Badge>
                            )}
                          </div>
                        </td>
                        <td>
                          <div className="d-flex gap-1 flex-wrap">
                            <Link to={`/organizations/${org.id}`} className="btn btn-sm btn-outline-info">
                              <i className="fas fa-eye"></i>
                            </Link>
                            <Link to={`/organizations/${org.id}/edit`} className="btn btn-sm btn-outline-primary">
                              <i className="fas fa-edit"></i>
                            </Link>
                            {!org.is_primary && (
                              <Button
                                size="sm"
                                variant="outline-warning"
                                onClick={() => handleSetPrimary(org)}
                                title="Set as Primary"
                              >
                                <i className="fas fa-star"></i>
                              </Button>
                            )}
                            <Button
                              size="sm"
                              variant={org.is_active ? 'outline-secondary' : 'outline-success'}
                              onClick={() => handleToggleActive(org)}
                              title={org.is_active ? 'Deactivate' : 'Activate'}
                            >
                              <i className={`fas fa-${org.is_active ? 'pause' : 'play'}`}></i>
                            </Button>
                            {!org.is_primary && (
                              <Button
                                size="sm"
                                variant="outline-danger"
                                onClick={() => openDeleteModal(org)}
                                title="Delete Organization"
                              >
                                <i className="fas fa-trash"></i>
                              </Button>
                            )}
                          </div>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </Table>
              )}
            </Card.Body>
          </Card>
        </Col>
      </Row>

      {/* Delete Confirmation Modal */}
      <Modal show={showDeleteModal} onHide={() => setShowDeleteModal(false)}>
        <Modal.Header closeButton>
          <Modal.Title className="text-danger">
            <i className="fas fa-exclamation-triangle me-2"></i>
            Confirm Deletion
          </Modal.Title>
        </Modal.Header>
        <Modal.Body>
          <p>Are you sure you want to delete the organization <strong>{selectedOrganization?.name}</strong>?</p>
          <Alert variant="warning">
            <i className="fas fa-warning me-2"></i>
            This action cannot be undone. All organization data will be permanently deleted.
          </Alert>
        </Modal.Body>
        <Modal.Footer>
          <Button variant="secondary" onClick={() => setShowDeleteModal(false)}>
            Cancel
          </Button>
          <Button 
            variant="danger" 
            onClick={handleDeleteOrganization}
            disabled={deleteLoading}
          >
            {deleteLoading ? (
              <>
                <Spinner animation="border" size="sm" className="me-2" />
                Deleting...
              </>
            ) : (
              <>
                <i className="fas fa-trash me-2"></i>
                Delete Organization
              </>
            )}
          </Button>
        </Modal.Footer>
      </Modal>
    </Container>
  );
};

export default OrganizationList;
