#!/usr/bin/env python3

import os
import sys
import django

# Add the project root to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'event_management.settings')
django.setup()

from participants.models import Participant

def final_summary():
    """Final summary of participant database status"""
    
    print("=== FINAL PARTICIPANT DATABASE SUMMARY ===")
    
    total_participants = Participant.objects.count()
    print(f"Total participants in database: {total_participants}")
    
    # Get all existing IDs
    existing_ids = set(Participant.objects.values_list('id', flat=True))
    
    # Check key ranges that were requested
    key_ranges = [
        (93, 100, "Range 93-100"),
        (101, 173, "Range 101-173"), 
        (337, 356, "Range 337-356"),
        (511, 523, "Range 511-523"),
        (551, 602, "Range 551-602"),
        (603, 638, "Range 603-638")
    ]
    
    print(f"\nKey ranges status:")
    total_expected = 0
    total_found = 0
    
    for start, end, description in key_ranges:
        found_in_range = [i for i in range(start, end + 1) if i in existing_ids]
        missing_in_range = [i for i in range(start, end + 1) if i not in existing_ids]
        expected = end - start + 1
        found = len(found_in_range)
        
        total_expected += expected
        total_found += found
        
        print(f"{description}: {found}/{expected} participants ({(found/expected)*100:.1f}%)")
        if missing_in_range:
            print(f"  Missing: {missing_in_range}")
    
    print(f"\nOverall key ranges: {total_found}/{total_expected} participants ({(total_found/total_expected)*100:.1f}%)")
    
    # Show ID distribution
    print(f"\nID distribution:")
    ranges = [(1, 100), (101, 200), (201, 300), (301, 400), (401, 500), (501, 600), (601, 638)]
    
    for start, end in ranges:
        count = len([i for i in range(start, end + 1) if i in existing_ids])
        total = end - start + 1
        print(f"IDs {start}-{end}: {count}/{total} ({(count/total)*100:.1f}%)")
    
    # Show highest and lowest IDs
    highest = max(existing_ids)
    lowest = min(existing_ids)
    
    print(f"\nID range: {lowest} to {highest}")
    
    # Show status distribution
    print(f"\nStatus distribution:")
    active_count = Participant.objects.filter(status='active').count()
    approved_count = Participant.objects.filter(status='approved').count()
    deleted_count = Participant.objects.filter(status='deleted').count()
    
    print(f"Active: {active_count}")
    print(f"Approved: {approved_count}")
    print(f"Deleted: {deleted_count}")
    
    print(f"\n🎉 Database migration completed!")
    print(f"The database now contains {total_participants} participants with exact IDs preserved for QR code compatibility.")
    
    # Check if we have the most important ranges
    critical_ranges_complete = True
    critical_missing = []
    
    for start, end, description in key_ranges:
        missing_in_range = [i for i in range(start, end + 1) if i not in existing_ids]
        if missing_in_range:
            critical_ranges_complete = False
            critical_missing.extend(missing_in_range)
    
    if critical_ranges_complete:
        print("✅ All critical participant ranges are complete!")
    else:
        print(f"⚠️  Some participants are still missing from critical ranges: {len(critical_missing)} total")

if __name__ == "__main__":
    final_summary()
