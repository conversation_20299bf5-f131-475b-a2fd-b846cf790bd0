from rest_framework import viewsets, status
from rest_framework.permissions import IsAuthenticated, AllowAny
from rest_framework.decorators import action
from rest_framework.response import Response
from django.http import HttpResponse
import csv
import io
from .models import Contact<PERSON>erson
from .serializers import ContactPersonSerializer
from events.models import Event


class ContactPersonViewSet(viewsets.ModelViewSet):
    queryset = ContactPerson.objects.all()
    serializer_class = ContactPersonSerializer

    def get_permissions(self):
        """Allow public access for list, retrieve, create, update, and export actions"""
        if self.action in ['list', 'retrieve', 'create', 'update', 'partial_update', 'export_csv', 'download_sample', 'import_csv']:
            permission_classes = [AllowAny]
        else:
            permission_classes = [IsAuthenticated]
        return [permission() for permission in permission_classes]

    @action(detail=False, methods=['get'])
    def export_csv(self, request):
        """Export contact persons to CSV"""
        response = HttpResponse(content_type='text/csv')
        response['Content-Disposition'] = 'attachment; filename="contact_persons.csv"'

        writer = csv.writer(response)
        writer.writerow([
            'First Name', 'Middle Name', 'Last Name', 'Phone', 'Email', 'Position',
            'Organization', 'Event', 'Available', 'Notes'
        ])

        for contact_person in self.get_queryset():
            writer.writerow([
                contact_person.first_name,
                contact_person.middle_name,
                contact_person.last_name,
                contact_person.phone,
                contact_person.email,
                contact_person.position,
                contact_person.organization,
                contact_person.event.name,
                'Yes' if contact_person.is_available else 'No',
                contact_person.notes
            ])

        return response

    @action(detail=False, methods=['get'])
    def download_sample(self, request):
        """Download sample CSV template for contact persons"""
        response = HttpResponse(content_type='text/csv')
        response['Content-Disposition'] = 'attachment; filename="contact_persons_sample.csv"'

        writer = csv.writer(response)
        writer.writerow([
            'First Name', 'Middle Name', 'Last Name', 'Phone', 'Email', 'Position',
            'Organization', 'Event Name', 'Available', 'Notes'
        ])

        # Add sample data
        writer.writerow([
            'John', 'Michael', 'Doe', '+251911123456', '<EMAIL>', 'Event Coordinator',
            'University of Gondar', 'Annual Conference 2025', 'Yes', 'Primary contact for logistics'
        ])
        writer.writerow([
            'Jane', '', 'Smith', '+251922654321', '<EMAIL>', 'Assistant Director',
            'UoG Events Department', 'Annual Conference 2025', 'Yes', 'Handles participant registration'
        ])
        writer.writerow([
            'Ahmed', 'Hassan', 'Ali', '+251933789012', '<EMAIL>', 'Protocol Officer',
            'Ministry of Education', 'Annual Conference 2025', 'No', 'Government liaison'
        ])

        return response

    @action(detail=False, methods=['post'])
    def import_csv(self, request):
        """Import contact persons from CSV file"""
        if 'file' not in request.FILES:
            return Response(
                {'error': 'No file provided'},
                status=status.HTTP_400_BAD_REQUEST
            )

        csv_file = request.FILES['file']

        if not csv_file.name.endswith('.csv'):
            return Response(
                {'error': 'File must be a CSV'},
                status=status.HTTP_400_BAD_REQUEST
            )

        try:
            # Read CSV file
            decoded_file = csv_file.read().decode('utf-8')
            io_string = io.StringIO(decoded_file)
            reader = csv.DictReader(io_string)

            created_count = 0
            updated_count = 0
            errors = []

            for row_num, row in enumerate(reader, start=2):  # Start from 2 because row 1 is header
                try:
                    # Clean and validate data
                    first_name = row.get('First Name', '').strip()
                    middle_name = row.get('Middle Name', '').strip()
                    last_name = row.get('Last Name', '').strip()
                    phone = row.get('Phone', '').strip()
                    email = row.get('Email', '').strip()
                    position = row.get('Position', '').strip()
                    organization = row.get('Organization', '').strip()
                    event_name = row.get('Event Name', '').strip()
                    available_str = row.get('Available', 'Yes').strip().lower()
                    notes = row.get('Notes', '').strip()

                    # Validate required fields
                    if not first_name:
                        errors.append(f"Row {row_num}: First Name is required")
                        continue
                    if not last_name:
                        errors.append(f"Row {row_num}: Last Name is required")
                        continue
                    if not phone:
                        errors.append(f"Row {row_num}: Phone is required")
                        continue
                    if not email:
                        errors.append(f"Row {row_num}: Email is required")
                        continue
                    if not event_name:
                        errors.append(f"Row {row_num}: Event Name is required")
                        continue

                    # Find event
                    try:
                        event = Event.objects.get(name__iexact=event_name)
                    except Event.DoesNotExist:
                        errors.append(f"Row {row_num}: Event '{event_name}' not found")
                        continue
                    except Event.MultipleObjectsReturned:
                        errors.append(f"Row {row_num}: Multiple events found with name '{event_name}'")
                        continue

                    # Parse availability
                    is_available = available_str in ['yes', 'true', '1', 'available']

                    # Check if contact person already exists (by email and event)
                    existing_contact = ContactPerson.objects.filter(
                        email__iexact=email,
                        event=event
                    ).first()

                    contact_data = {
                        'first_name': first_name,
                        'middle_name': middle_name,
                        'last_name': last_name,
                        'phone': phone,
                        'email': email,
                        'position': position,
                        'organization': organization,
                        'event': event,
                        'is_available': is_available,
                        'notes': notes,
                    }

                    if existing_contact:
                        # Update existing contact person
                        for key, value in contact_data.items():
                            setattr(existing_contact, key, value)
                        existing_contact.save()
                        updated_count += 1
                    else:
                        # Create new contact person
                        ContactPerson.objects.create(**contact_data)
                        created_count += 1

                except Exception as e:
                    errors.append(f"Row {row_num}: {str(e)}")
                    continue

            response_data = {
                'message': f'Import completed. Created: {created_count}, Updated: {updated_count}',
                'created': created_count,
                'updated': updated_count,
                'errors': errors
            }

            if errors:
                response_data['warning'] = f'{len(errors)} rows had errors'

            return Response(response_data, status=status.HTTP_200_OK)

        except Exception as e:
            return Response(
                {'error': f'Error processing file: {str(e)}'},
                status=status.HTTP_400_BAD_REQUEST
            )
