# Rate Limiting Decorators and Utilities
from functools import wraps
from django.http import JsonResponse
from django.core.cache import cache
from django.conf import settings
from decouple import config
import time
import hashlib

class RateLimiter:
    """Simple rate limiter using Django cache"""
    
    def __init__(self, key_prefix='rate_limit'):
        self.key_prefix = key_prefix
        self.enabled = config('RATELIMIT_ENABLE', default=True, cast=bool)
    
    def get_client_ip(self, request):
        """Get client IP address"""
        x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
        if x_forwarded_for:
            ip = x_forwarded_for.split(',')[0]
        else:
            ip = request.META.get('REMOTE_ADDR')
        return ip
    
    def get_cache_key(self, identifier, action):
        """Generate cache key for rate limiting"""
        return f"{self.key_prefix}:{action}:{identifier}"
    
    def is_rate_limited(self, identifier, action, limit_per_minute=60, window_minutes=1):
        """Check if identifier is rate limited for action"""
        if not self.enabled:
            return False
            
        cache_key = self.get_cache_key(identifier, action)
        current_time = int(time.time())
        window_start = current_time - (window_minutes * 60)
        
        # Get existing requests in the time window
        requests = cache.get(cache_key, [])
        
        # Filter requests within the time window
        requests = [req_time for req_time in requests if req_time > window_start]
        
        # Check if limit exceeded
        if len(requests) >= limit_per_minute:
            return True
        
        # Add current request
        requests.append(current_time)
        
        # Store updated requests list
        cache.set(cache_key, requests, window_minutes * 60)
        
        return False
    
    def get_rate_limit_info(self, identifier, action, limit_per_minute=60, window_minutes=1):
        """Get rate limit information"""
        cache_key = self.get_cache_key(identifier, action)
        current_time = int(time.time())
        window_start = current_time - (window_minutes * 60)
        
        requests = cache.get(cache_key, [])
        requests = [req_time for req_time in requests if req_time > window_start]
        
        remaining = max(0, limit_per_minute - len(requests))
        reset_time = window_start + (window_minutes * 60)
        
        return {
            'limit': limit_per_minute,
            'remaining': remaining,
            'reset': reset_time,
            'window_minutes': window_minutes
        }

# Global rate limiter instance
rate_limiter = RateLimiter()

def rate_limit(action, limit_per_minute=60, window_minutes=1, key_func=None):
    """
    Rate limiting decorator for views
    
    Args:
        action: Action name for rate limiting
        limit_per_minute: Number of requests allowed per minute
        window_minutes: Time window in minutes
        key_func: Function to generate custom key (default: uses IP)
    """
    def decorator(view_func):
        @wraps(view_func)
        def wrapper(request, *args, **kwargs):
            if not rate_limiter.enabled:
                return view_func(request, *args, **kwargs)
            
            # Generate identifier
            if key_func:
                identifier = key_func(request)
            else:
                identifier = rate_limiter.get_client_ip(request)
            
            # Check rate limit
            if rate_limiter.is_rate_limited(identifier, action, limit_per_minute, window_minutes):
                rate_info = rate_limiter.get_rate_limit_info(identifier, action, limit_per_minute, window_minutes)
                
                response = JsonResponse({
                    'error': 'Rate limit exceeded',
                    'detail': f'Too many {action} requests. Try again later.',
                    'rate_limit': rate_info
                }, status=429)
                
                # Add rate limit headers
                response['X-RateLimit-Limit'] = str(limit_per_minute)
                response['X-RateLimit-Remaining'] = str(rate_info['remaining'])
                response['X-RateLimit-Reset'] = str(rate_info['reset'])
                response['Retry-After'] = str(window_minutes * 60)
                
                return response
            
            # Add rate limit info to response
            response = view_func(request, *args, **kwargs)
            
            if hasattr(response, 'status_code'):
                rate_info = rate_limiter.get_rate_limit_info(identifier, action, limit_per_minute, window_minutes)
                response['X-RateLimit-Limit'] = str(limit_per_minute)
                response['X-RateLimit-Remaining'] = str(rate_info['remaining'])
                response['X-RateLimit-Reset'] = str(rate_info['reset'])
            
            return response
        
        return wrapper
    return decorator

# Common rate limiting decorators
def api_rate_limit(view_func):
    """Standard API rate limit: 100 requests per hour"""
    return rate_limit('api', limit_per_minute=100, window_minutes=60)(view_func)

def login_rate_limit(view_func):
    """Login rate limit: 5 attempts per minute"""
    return rate_limit('login', limit_per_minute=5, window_minutes=1)(view_func)

def registration_rate_limit(view_func):
    """Registration rate limit: 3 registrations per hour"""
    return rate_limit('registration', limit_per_minute=3, window_minutes=60)(view_func)

def email_rate_limit(view_func):
    """Email sending rate limit: 10 emails per hour"""
    return rate_limit('email', limit_per_minute=10, window_minutes=60)(view_func)

def upload_rate_limit(view_func):
    """File upload rate limit: 20 uploads per hour"""
    return rate_limit('upload', limit_per_minute=20, window_minutes=60)(view_func)

# Custom key functions
def user_key_func(request):
    """Generate key based on authenticated user"""
    if request.user.is_authenticated:
        return f"user_{request.user.id}"
    return rate_limiter.get_client_ip(request)

def email_key_func(request):
    """Generate key based on email address"""
    email = request.data.get('email') or request.POST.get('email')
    if email:
        return f"email_{hashlib.md5(email.encode()).hexdigest()}"
    return rate_limiter.get_client_ip(request)

# Rate limit middleware
class RateLimitMiddleware:
    """Middleware to add rate limiting headers to all responses"""
    
    def __init__(self, get_response):
        self.get_response = get_response
    
    def __call__(self, request):
        response = self.get_response(request)
        
        # Add general rate limit headers if not already present
        if not response.get('X-RateLimit-Limit'):
            ip = rate_limiter.get_client_ip(request)
            rate_info = rate_limiter.get_rate_limit_info(ip, 'general', 1000, 60)  # 1000 per hour general limit
            
            response['X-RateLimit-Limit'] = '1000'
            response['X-RateLimit-Remaining'] = str(rate_info['remaining'])
            response['X-RateLimit-Reset'] = str(rate_info['reset'])
        
        return response
