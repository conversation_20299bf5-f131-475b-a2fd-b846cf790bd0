import React, { useState, useEffect } from 'react';
import { Container, Row, Col, Card, Form, But<PERSON>, Al<PERSON>, Spinner } from 'react-bootstrap';
import { useParams, useNavigate } from 'react-router-dom';
import DatePicker from 'react-datepicker';
import 'react-datepicker/dist/react-datepicker.css';
import { eventService, participantService, participantTypeService, Event, ParticipantType, Participant } from '../services/api';

const ParticipantEdit: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  
  const [participant, setParticipant] = useState<Participant | null>(null);
  const [events, setEvents] = useState<Event[]>([]);
  const [participantTypes, setParticipantTypes] = useState<ParticipantType[]>([]);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState(false);

  const [formData, setFormData] = useState({
    first_name: '',
    last_name: '',
    middle_name: '',
    email: '',
    phone: '',
    institution_name: '',
    position: '',
    event: '',
    participant_type: '',
    arrival_date: new Date(),
    departure_date: new Date(),
    profile_photo: null as File | null,
    remarks: '',
  });

  useEffect(() => {
    fetchData();
  }, [id]);

  const fetchData = async () => {
    try {
      setLoading(true);
      const [participantResponse, eventsResponse, typesResponse] = await Promise.all([
        participantService.getParticipant(parseInt(id!)),
        eventService.getEvents(),
        participantTypeService.getParticipantTypes(),
      ]);

      const participantData = participantResponse.data;
      const eventsData = Array.isArray(eventsResponse.data) 
        ? eventsResponse.data 
        : (eventsResponse.data as any).results || [];
      const typesData = Array.isArray(typesResponse.data) 
        ? typesResponse.data 
        : (typesResponse.data as any).results || [];

      setParticipant(participantData);
      setEvents(eventsData.filter((event: Event) => event.is_active));
      setParticipantTypes(typesData);

      // Populate form with existing data
      setFormData({
        first_name: participantData.first_name || '',
        last_name: participantData.last_name || '',
        middle_name: participantData.middle_name || '',
        email: participantData.email || '',
        phone: participantData.phone || '',
        institution_name: participantData.institution_name || '',
        position: participantData.position || '',
        event: participantData.event?.toString() || '',
        participant_type: participantData.participant_type?.toString() || '',
        arrival_date: new Date(participantData.arrival_date),
        departure_date: new Date(participantData.departure_date),
        profile_photo: null,
        remarks: participantData.remarks || '',
      });

      setError('');
    } catch (err) {
      console.error('Error fetching data:', err);
      setError('Failed to load participant data');
    } finally {
      setLoading(false);
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value,
    }));
  };

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      setFormData(prev => ({
        ...prev,
        profile_photo: e.target.files![0],
      }));
    }
  };

  const handleDateChange = (date: Date | null, field: 'arrival_date' | 'departure_date') => {
    if (date) {
      setFormData(prev => ({
        ...prev,
        [field]: date,
      }));
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!formData.first_name || !formData.last_name || !formData.email || !formData.phone || 
        !formData.institution_name || !formData.position || !formData.event || !formData.participant_type) {
      setError('Please fill in all required fields');
      return;
    }

    try {
      setSaving(true);
      setError('');

      // Create FormData for file upload
      const submitData = new FormData();
      submitData.append('first_name', formData.first_name);
      submitData.append('last_name', formData.last_name);
      submitData.append('middle_name', formData.middle_name);
      submitData.append('email', formData.email);
      submitData.append('phone', formData.phone);
      submitData.append('institution_name', formData.institution_name);
      submitData.append('position', formData.position);
      submitData.append('event', formData.event);
      submitData.append('participant_type', formData.participant_type);
      submitData.append('arrival_date', formData.arrival_date.toISOString());
      submitData.append('departure_date', formData.departure_date.toISOString());
      submitData.append('remarks', formData.remarks);
      
      // Only append photo if a new one was selected
      if (formData.profile_photo) {
        submitData.append('profile_photo', formData.profile_photo);
      }

      await participantService.updateParticipant(parseInt(id!), submitData);
      
      setSuccess(true);
      setTimeout(() => {
        navigate('/participants/manage');
      }, 2000);

    } catch (error: any) {
      setError(error.response?.data?.detail || error.message || 'Update failed');
    } finally {
      setSaving(false);
    }
  };

  if (loading) {
    return (
      <Container className="text-center mt-5">
        <Spinner animation="border" role="status">
          <span className="visually-hidden">Loading...</span>
        </Spinner>
        <p className="mt-3">Loading participant data...</p>
      </Container>
    );
  }

  if (!participant) {
    return (
      <Container className="mt-5">
        <Alert variant="danger">
          <h4>Participant Not Found</h4>
          <p>The participant you're looking for doesn't exist or has been deleted.</p>
          <Button variant="primary" onClick={() => navigate('/participants/manage')}>
            Back to Participant Management
          </Button>
        </Alert>
      </Container>
    );
  }

  return (
    <div className="min-vh-100" style={{ 
      background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
      paddingTop: '2rem',
      paddingBottom: '2rem'
    }}>
      <Container>
        <Row className="justify-content-center">
          <Col lg={10}>
            <Card className="shadow-lg border-0" style={{ borderRadius: '20px' }}>
              <Card.Header className="bg-primary text-white text-center py-4" style={{ borderRadius: '20px 20px 0 0' }}>
                <h2 className="mb-0 fw-bold">
                  <i className="fas fa-user-edit me-3"></i>
                  Edit Participant
                </h2>
                <p className="mb-0 mt-2">Update participant information</p>
              </Card.Header>
              
              <Card.Body className="p-5">
                {error && (
                  <Alert variant="danger" className="mb-4">
                    <i className="fas fa-exclamation-triangle me-2"></i>
                    {error}
                  </Alert>
                )}

                {success && (
                  <Alert variant="success" className="mb-4">
                    <i className="fas fa-check-circle me-2"></i>
                    Participant updated successfully! Redirecting...
                  </Alert>
                )}

                <Form onSubmit={handleSubmit}>
                  {/* Personal Information Section */}
                  <div className="mb-5">
                    <div className="d-flex align-items-center mb-4">
                      <div 
                        className="rounded-circle d-flex align-items-center justify-content-center me-3"
                        style={{ 
                          width: '50px', 
                          height: '50px', 
                          background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                          color: 'white'
                        }}
                      >
                        <i className="fas fa-user fa-lg"></i>
                      </div>
                      <div>
                        <h4 className="mb-0 fw-bold text-dark">Personal Information</h4>
                        <p className="mb-0 text-muted">Basic participant details</p>
                      </div>
                    </div>

                    <Row>
                      <Col md={4}>
                        <Form.Group className="mb-4">
                          <Form.Label className="fw-semibold text-dark">
                            <i className="fas fa-user me-2 text-primary"></i>
                            First Name *
                          </Form.Label>
                          <Form.Control
                            type="text"
                            name="first_name"
                            value={formData.first_name}
                            onChange={handleInputChange}
                            required
                            className="form-control-lg border-0 shadow-sm"
                            style={{ borderRadius: '12px' }}
                          />
                        </Form.Group>
                      </Col>
                      <Col md={4}>
                        <Form.Group className="mb-4">
                          <Form.Label className="fw-semibold text-dark">
                            <i className="fas fa-user me-2 text-primary"></i>
                            Last Name *
                          </Form.Label>
                          <Form.Control
                            type="text"
                            name="last_name"
                            value={formData.last_name}
                            onChange={handleInputChange}
                            required
                            className="form-control-lg border-0 shadow-sm"
                            style={{ borderRadius: '12px' }}
                          />
                        </Form.Group>
                      </Col>
                      <Col md={4}>
                        <Form.Group className="mb-4">
                          <Form.Label className="fw-semibold text-dark">
                            <i className="fas fa-user me-2 text-primary"></i>
                            Middle Name
                          </Form.Label>
                          <Form.Control
                            type="text"
                            name="middle_name"
                            value={formData.middle_name}
                            onChange={handleInputChange}
                            className="form-control-lg border-0 shadow-sm"
                            style={{ borderRadius: '12px' }}
                          />
                        </Form.Group>
                      </Col>
                    </Row>

                    <Row>
                      <Col md={6}>
                        <Form.Group className="mb-4">
                          <Form.Label className="fw-semibold text-dark">
                            <i className="fas fa-envelope me-2 text-primary"></i>
                            Email Address *
                          </Form.Label>
                          <Form.Control
                            type="email"
                            name="email"
                            value={formData.email}
                            onChange={handleInputChange}
                            required
                            className="form-control-lg border-0 shadow-sm"
                            style={{ borderRadius: '12px' }}
                          />
                        </Form.Group>
                      </Col>
                      <Col md={6}>
                        <Form.Group className="mb-4">
                          <Form.Label className="fw-semibold text-dark">
                            <i className="fas fa-phone me-2 text-primary"></i>
                            Phone Number *
                          </Form.Label>
                          <Form.Control
                            type="tel"
                            name="phone"
                            value={formData.phone}
                            onChange={handleInputChange}
                            required
                            className="form-control-lg border-0 shadow-sm"
                            style={{ borderRadius: '12px' }}
                          />
                        </Form.Group>
                      </Col>
                    </Row>
                  </div>

                  {/* Professional Information Section */}
                  <div className="mb-5">
                    <div className="d-flex align-items-center mb-4">
                      <div 
                        className="rounded-circle d-flex align-items-center justify-content-center me-3"
                        style={{ 
                          width: '50px', 
                          height: '50px', 
                          background: 'linear-gradient(135deg, #11998e 0%, #38ef7d 100%)',
                          color: 'white'
                        }}
                      >
                        <i className="fas fa-briefcase fa-lg"></i>
                      </div>
                      <div>
                        <h4 className="mb-0 fw-bold text-dark">Professional Information</h4>
                        <p className="mb-0 text-muted">Work and institutional details</p>
                      </div>
                    </div>

                    <Row>
                      <Col md={6}>
                        <Form.Group className="mb-4">
                          <Form.Label className="fw-semibold text-dark">
                            <i className="fas fa-building me-2 text-primary"></i>
                            Institution Name *
                          </Form.Label>
                          <Form.Control
                            type="text"
                            name="institution_name"
                            value={formData.institution_name}
                            onChange={handleInputChange}
                            required
                            className="form-control-lg border-0 shadow-sm"
                            style={{ borderRadius: '12px' }}
                          />
                        </Form.Group>
                      </Col>
                      <Col md={6}>
                        <Form.Group className="mb-4">
                          <Form.Label className="fw-semibold text-dark">
                            <i className="fas fa-user-tie me-2 text-primary"></i>
                            Position/Title *
                          </Form.Label>
                          <Form.Control
                            type="text"
                            name="position"
                            value={formData.position}
                            onChange={handleInputChange}
                            required
                            className="form-control-lg border-0 shadow-sm"
                            style={{ borderRadius: '12px' }}
                          />
                        </Form.Group>
                      </Col>
                    </Row>
                  </div>

                  {/* Event Information Section */}
                  <div className="mb-5">
                    <div className="d-flex align-items-center mb-4">
                      <div 
                        className="rounded-circle d-flex align-items-center justify-content-center me-3"
                        style={{ 
                          width: '50px', 
                          height: '50px', 
                          background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                          color: 'white'
                        }}
                      >
                        <i className="fas fa-calendar fa-lg"></i>
                      </div>
                      <div>
                        <h4 className="mb-0 fw-bold text-dark">Event Information</h4>
                        <p className="mb-0 text-muted">Event and participation details</p>
                      </div>
                    </div>

                    <Row>
                      <Col md={6}>
                        <Form.Group className="mb-4">
                          <Form.Label className="fw-semibold text-dark">
                            <i className="fas fa-calendar-alt me-2 text-primary"></i>
                            Event *
                          </Form.Label>
                          <Form.Select
                            name="event"
                            value={formData.event}
                            onChange={handleInputChange}
                            required
                            className="form-control-lg border-0 shadow-sm"
                            style={{ borderRadius: '12px' }}
                          >
                            <option value="">
                              {events.length === 0 ? 'Loading events...' : 'Select an event'}
                            </option>
                            {events.map(event => (
                              <option key={event.id} value={event.id}>
                                {event.name} - {new Date(event.start_date).toLocaleDateString()}
                              </option>
                            ))}
                          </Form.Select>
                        </Form.Group>
                      </Col>
                      <Col md={6}>
                        <Form.Group className="mb-4">
                          <Form.Label className="fw-semibold text-dark">
                            <i className="fas fa-user-tag me-2 text-primary"></i>
                            Participant Type *
                          </Form.Label>
                          <Form.Select
                            name="participant_type"
                            value={formData.participant_type}
                            onChange={handleInputChange}
                            required
                            className="form-control-lg border-0 shadow-sm"
                            style={{ borderRadius: '12px' }}
                          >
                            <option value="">
                              {participantTypes.length === 0 ? 'Loading participant types...' : 'Select participant type'}
                            </option>
                            {participantTypes.map(type => (
                              <option key={type.id} value={type.id}>
                                {type.name}
                              </option>
                            ))}
                          </Form.Select>
                        </Form.Group>
                      </Col>
                    </Row>
                  </div>

                  {/* Travel Information Section */}
                  <div className="mb-5">
                    <div className="d-flex align-items-center mb-4">
                      <div 
                        className="rounded-circle d-flex align-items-center justify-content-center me-3"
                        style={{ 
                          width: '50px', 
                          height: '50px', 
                          background: 'linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%)',
                          color: 'white'
                        }}
                      >
                        <i className="fas fa-plane fa-lg"></i>
                      </div>
                      <div>
                        <h4 className="mb-0 fw-bold text-dark">Travel Information</h4>
                        <p className="mb-0 text-muted">Arrival and departure details</p>
                      </div>
                    </div>

                    <Row>
                      <Col md={6}>
                        <Form.Group className="mb-4">
                          <Form.Label className="fw-semibold text-dark">
                            <i className="fas fa-plane-arrival me-2 text-primary"></i>
                            Arrival Date/Time *
                          </Form.Label>
                          <div>
                            <DatePicker
                              selected={formData.arrival_date}
                              onChange={(date) => handleDateChange(date, 'arrival_date')}
                              showTimeSelect
                              dateFormat="MMMM d, yyyy h:mm aa"
                              className="form-control form-control-lg border-0 shadow-sm"
                              required
                              placeholderText="Select arrival date and time"
                            />
                          </div>
                        </Form.Group>
                      </Col>
                      <Col md={6}>
                        <Form.Group className="mb-4">
                          <Form.Label className="fw-semibold text-dark">
                            <i className="fas fa-plane-departure me-2 text-primary"></i>
                            Departure Date/Time *
                          </Form.Label>
                          <div>
                            <DatePicker
                              selected={formData.departure_date}
                              onChange={(date) => handleDateChange(date, 'departure_date')}
                              showTimeSelect
                              dateFormat="MMMM d, yyyy h:mm aa"
                              className="form-control form-control-lg border-0 shadow-sm"
                              required
                              placeholderText="Select departure date and time"
                            />
                          </div>
                        </Form.Group>
                      </Col>
                    </Row>
                  </div>

                  {/* Additional Information Section */}
                  <div className="mb-5">
                    <div className="d-flex align-items-center mb-4">
                      <div 
                        className="rounded-circle d-flex align-items-center justify-content-center me-3"
                        style={{ 
                          width: '50px', 
                          height: '50px', 
                          background: 'linear-gradient(135deg, #a8edea 0%, #fed6e3 100%)',
                          color: 'white'
                        }}
                      >
                        <i className="fas fa-camera fa-lg"></i>
                      </div>
                      <div>
                        <h4 className="mb-0 fw-bold text-dark">Additional Information</h4>
                        <p className="mb-0 text-muted">Photo and special requirements</p>
                      </div>
                    </div>

                    <Row>
                      <Col md={6}>
                        <Form.Group className="mb-4">
                          <Form.Label className="fw-semibold text-dark">
                            <i className="fas fa-camera me-2 text-primary"></i>
                            Profile Photo {!participant.profile_photo && '*'}
                          </Form.Label>
                          <Form.Control
                            type="file"
                            name="profile_photo"
                            onChange={handleFileChange}
                            accept="image/*"
                            className="form-control-lg border-0 shadow-sm"
                            style={{ borderRadius: '12px' }}
                          />
                          <Form.Text className="text-muted">
                            {participant.profile_photo 
                              ? 'Current photo will be kept if no new photo is selected' 
                              : 'Upload a clear photo for badge generation (JPG, PNG, max 5MB)'
                            }
                          </Form.Text>
                        </Form.Group>
                      </Col>
                      <Col md={6}>
                        <Form.Group className="mb-4">
                          <Form.Label className="fw-semibold text-dark">
                            <i className="fas fa-comment me-2 text-primary"></i>
                            Special Requirements/Remarks
                          </Form.Label>
                          <Form.Control
                            as="textarea"
                            rows={3}
                            name="remarks"
                            value={formData.remarks}
                            onChange={handleInputChange}
                            placeholder="Any special dietary requirements, accessibility needs, or other remarks..."
                            className="form-control-lg border-0 shadow-sm"
                            style={{ borderRadius: '12px' }}
                          />
                        </Form.Group>
                      </Col>
                    </Row>
                  </div>

                  {/* Submit Section */}
                  <div className="text-center">
                    <div className="d-flex gap-3 justify-content-center">
                      <Button 
                        variant="secondary" 
                        size="lg" 
                        onClick={() => navigate('/participants/manage')}
                        disabled={saving}
                        style={{ borderRadius: '12px', minWidth: '150px' }}
                      >
                        <i className="fas fa-times me-2"></i>
                        Cancel
                      </Button>
                      <Button 
                        type="submit" 
                        size="lg" 
                        disabled={saving}
                        style={{ 
                          borderRadius: '12px',
                          background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                          border: 'none',
                          minWidth: '200px'
                        }}
                      >
                        {saving ? (
                          <>
                            <Spinner animation="border" size="sm" className="me-2" />
                            Updating...
                          </>
                        ) : (
                          <>
                            <i className="fas fa-save me-2"></i>
                            Update Participant
                          </>
                        )}
                      </Button>
                    </div>
                    
                    <p className="text-muted mt-3 mb-0">
                      <i className="fas fa-shield-alt me-2"></i>
                      Your information is secure and will only be used for event management purposes.
                    </p>
                  </div>
                </Form>
              </Card.Body>
            </Card>
          </Col>
        </Row>
      </Container>
    </div>
  );
};

export default ParticipantEdit;
