import React from 'react';
import { Form } from 'react-bootstrap';

interface ValidatedFormFieldProps {
  label: string | React.ReactNode;
  name: string;
  type?: string;
  value: string;
  onChange: (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => void;
  onBlur?: (e: React.FocusEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => void;
  error?: string;
  placeholder?: string;
  required?: boolean;
  disabled?: boolean;
  as?: 'input' | 'textarea' | 'select';
  rows?: number;
  children?: React.ReactNode; // For select options
  helpText?: string;
  className?: string;
  size?: 'sm' | 'lg';
}

const ValidatedFormField: React.FC<ValidatedFormFieldProps> = ({
  label,
  name,
  type = 'text',
  value,
  onChange,
  onBlur,
  error,
  placeholder,
  required = false,
  disabled = false,
  as = 'input',
  rows,
  children,
  helpText,
  className = '',
  size
}) => {
  const hasError = !!error;
  
  return (
    <Form.Group className={`mb-3 ${className}`}>
      <Form.Label>
        {label}
        {required && <span className="text-danger ms-1">*</span>}
      </Form.Label>
      
      <Form.Control
        as={as}
        type={type}
        name={name}
        value={value}
        onChange={onChange}
        placeholder={placeholder}
        required={required}
        disabled={disabled}
        isInvalid={hasError}
        {...(as === 'textarea' && rows ? { rows } : {})}
        size={size}
        style={{
          borderRadius: '12px',
          fontSize: '16px',
          padding: '12px 16px',
          border: hasError ? '2px solid #dc3545' : '2px solid #e9ecef',
          transition: 'all 0.2s ease-in-out'
        }}
        onFocus={(e) => {
          if (!hasError) {
            e.target.style.border = '2px solid #0d6efd';
            e.target.style.boxShadow = '0 0 0 0.2rem rgba(13, 110, 253, 0.25)';
          }
        }}
        onBlur={(e) => {
          if (!hasError) {
            e.target.style.border = '2px solid #e9ecef';
            e.target.style.boxShadow = 'none';
          }
          if (onBlur) {
            onBlur(e);
          }
        }}
      >
        {children}
      </Form.Control>
      
      {hasError && (
        <Form.Control.Feedback type="invalid" className="d-block">
          <i className="fas fa-exclamation-circle me-1"></i>
          {error}
        </Form.Control.Feedback>
      )}
      
      {helpText && !hasError && (
        <Form.Text className="text-muted">
          <i className="fas fa-info-circle me-1"></i>
          {helpText}
        </Form.Text>
      )}
    </Form.Group>
  );
};

export default ValidatedFormField;
