from django.core.management.base import BaseCommand
from events.models import EmailTemplate


class Command(BaseCommand):
    help = 'Improve badge notification email template with professional design'

    def handle(self, *args, **options):
        self.stdout.write("Improving badge notification email template...")
        
        # Developer credits HTML
        developer_credits = '''
            <!-- Developer Credits -->
            <div style="margin-top: 20px; padding-top: 15px; border-top: 1px solid #dee2e6;">
                <p style="margin: 0 0 10px 0; font-weight: bold; color: #495057;">Developed by:</p>
                <div style="display: flex; justify-content: center; gap: 20px; flex-wrap: wrap;">
                    <a href="https://www.linkedin.com/in/tewodros-abebaw-chekol/" style="color: #0077b5; text-decoration: none; font-size: 13px;">
                        <strong>Tewodros Abebaw</strong>
                    </a>
                    <a href="https://www.linkedin.com/in/aragaw-mebratu-a3096514a/" style="color: #0077b5; text-decoration: none; font-size: 13px;">
                        <strong>Aragaw Mebratu</strong>
                    </a>
                    <a href="https://www.linkedin.com/in/meseret-teshale-bb24b767/" style="color: #0077b5; text-decoration: none; font-size: 13px;">
                        <strong>Meseret Teshale</strong>
                    </a>
                </div>
            </div>'''
        
        try:
            badge_template, created = EmailTemplate.objects.get_or_create(
                template_type='badge_notification',
                defaults={
                    'name': 'Badge Notification',
                    'subject': 'Your Event Badge is Ready - {event_name}',
                    'is_active': True
                }
            )
            
            badge_template.html_content = f"""<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>Your Event Badge is Ready - University of Gondar</title>
</head>
<body style="font-family: Arial, sans-serif; margin: 0; padding: 20px; background-color: #f5f5f5;">
    <div style="max-width: 700px; margin: 0 auto; background-color: white; border-radius: 10px; overflow: hidden; box-shadow: 0 2px 10px rgba(0,0,0,0.1);">
        
        <!-- Header -->
        <div style="background: linear-gradient(135deg, #6f42c1 0%, #5a32a3 100%); color: white; padding: 30px; text-align: center;">
            <h1 style="margin: 0; font-size: 28px;">🎓 University of Gondar</h1>
            <h2 style="margin: 10px 0 0 0; font-size: 24px;">Your Event Badge is Ready!</h2>
            <div style="background: #28a745; color: white; padding: 10px 20px; border-radius: 25px; font-size: 16px; font-weight: bold; display: inline-block; margin-top: 15px;">
                🎫 Badge Generated Successfully
            </div>
        </div>
        
        <!-- Content -->
        <div style="padding: 30px;">
            <h2 style="color: #333; margin-top: 0;">Hello {{participant_name}}!</h2>
            <p style="color: #666; line-height: 1.6; font-size: 16px;">
                Great news! Your personalized event badge for <strong style="color: #6f42c1;">{{event_name}}</strong> has been generated and is attached to this email.
            </p>
            
            <!-- Badge Information -->
            <div style="background: white; margin: 20px 0; padding: 25px; border-radius: 10px; box-shadow: 0 2px 8px rgba(0,0,0,0.1); border-top: 4px solid #6f42c1;">
                <h3 style="color: #6f42c1; margin-top: 0; font-size: 18px;">🎫 Badge Information</h3>
                
                <!-- Badge Stats -->
                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 15px; margin-bottom: 20px;">
                    <div style="background: #f8f9fa; padding: 15px; border-radius: 8px; border-left: 4px solid #6f42c1; text-align: center;">
                        <div style="font-size: 32px; color: #6f42c1; margin-bottom: 5px;">👤</div>
                        <div style="font-weight: bold; color: #6f42c1; margin-bottom: 5px;">Participant</div>
                        <div style="color: #333; font-size: 14px; font-weight: bold;">{{participant_name}}</div>
                    </div>
                    <div style="background: #f8f9fa; padding: 15px; border-radius: 8px; border-left: 4px solid #28a745; text-align: center;">
                        <div style="font-size: 32px; color: #28a745; margin-bottom: 5px;">📅</div>
                        <div style="font-weight: bold; color: #28a745; margin-bottom: 5px;">Event</div>
                        <div style="color: #333; font-size: 14px; font-weight: bold;">{{event_name}}</div>
                    </div>
                </div>
                
                <!-- Badge Features -->
                <div style="background: #e8f4fd; border: 1px solid #bee5eb; border-radius: 8px; padding: 20px;">
                    <h4 style="color: #0c5460; margin-top: 0; margin-bottom: 15px;">🔍 Badge Features</h4>
                    <ul style="color: #0c5460; margin: 0; padding-left: 20px; line-height: 1.6;">
                        <li><strong>QR Code:</strong> Quick check-in and identification</li>
                        <li><strong>Personal Information:</strong> Name, organization, and participant type</li>
                        <li><strong>Event Details:</strong> Event name, date, and location</li>
                        <li><strong>Security Features:</strong> Unique ID and verification code</li>
                        <li><strong>High Quality:</strong> Print-ready resolution for professional appearance</li>
                    </ul>
                </div>
            </div>
            
            <!-- Printing Instructions -->
            <div style="background: white; margin: 20px 0; padding: 25px; border-radius: 10px; box-shadow: 0 2px 8px rgba(0,0,0,0.1); border-top: 4px solid #28a745;">
                <h3 style="color: #28a745; margin-top: 0; font-size: 18px;">🖨️ Printing Instructions</h3>
                
                <!-- Step by Step -->
                <div style="margin-bottom: 20px;">
                    <div style="display: flex; align-items: center; margin-bottom: 15px;">
                        <div style="background: #28a745; color: white; width: 30px; height: 30px; border-radius: 50%; display: flex; align-items: center; justify-content: center; font-weight: bold; margin-right: 15px;">1</div>
                        <div style="color: #333;"><strong>Download:</strong> Save the attached badge file to your computer</div>
                    </div>
                    <div style="display: flex; align-items: center; margin-bottom: 15px;">
                        <div style="background: #28a745; color: white; width: 30px; height: 30px; border-radius: 50%; display: flex; align-items: center; justify-content: center; font-weight: bold; margin-right: 15px;">2</div>
                        <div style="color: #333;"><strong>Paper:</strong> Use quality cardstock or photo paper for best results</div>
                    </div>
                    <div style="display: flex; align-items: center; margin-bottom: 15px;">
                        <div style="background: #28a745; color: white; width: 30px; height: 30px; border-radius: 50%; display: flex; align-items: center; justify-content: center; font-weight: bold; margin-right: 15px;">3</div>
                        <div style="color: #333;"><strong>Settings:</strong> Print at 100% scale (do not resize)</div>
                    </div>
                    <div style="display: flex; align-items: center; margin-bottom: 15px;">
                        <div style="background: #28a745; color: white; width: 30px; height: 30px; border-radius: 50%; display: flex; align-items: center; justify-content: center; font-weight: bold; margin-right: 15px;">4</div>
                        <div style="color: #333;"><strong>Cut:</strong> Cut along the border lines for a professional look</div>
                    </div>
                    <div style="display: flex; align-items: center;">
                        <div style="background: #28a745; color: white; width: 30px; height: 30px; border-radius: 50%; display: flex; align-items: center; justify-content: center; font-weight: bold; margin-right: 15px;">5</div>
                        <div style="color: #333;"><strong>Protect:</strong> Consider laminating for durability</div>
                    </div>
                </div>
            </div>
            
            <!-- Event Day Instructions -->
            <div style="background: white; margin: 20px 0; padding: 25px; border-radius: 10px; box-shadow: 0 2px 8px rgba(0,0,0,0.1); border-top: 4px solid #ff9800;">
                <h3 style="color: #ff9800; margin-top: 0; font-size: 18px;">📋 Event Day Instructions</h3>
                <ul style="color: #666; line-height: 1.8; font-size: 16px;">
                    <li><strong>🎫 Bring Your Badge:</strong> Your printed badge is required for entry</li>
                    <li><strong>👀 Keep Visible:</strong> Wear your badge throughout the entire event</li>
                    <li><strong>📱 QR Code Ready:</strong> Ensure the QR code is clearly visible for scanning</li>
                    <li><strong>🆔 Backup ID:</strong> Bring a government-issued ID as backup identification</li>
                    <li><strong>⏰ Early Arrival:</strong> Arrive 15 minutes early for smooth check-in</li>
                    <li><strong>🔄 Replacement:</strong> Lost badges can be reprinted at the registration desk</li>
                </ul>
            </div>
            
            <!-- Troubleshooting -->
            <div style="background: white; margin: 20px 0; padding: 25px; border-radius: 10px; box-shadow: 0 2px 8px rgba(0,0,0,0.1); border-top: 4px solid #dc3545;">
                <h3 style="color: #dc3545; margin-top: 0; font-size: 18px;">🔧 Troubleshooting</h3>
                <div style="background: #f8d7da; border: 1px solid #f5c6cb; border-radius: 8px; padding: 15px;">
                    <p style="margin: 0 0 10px 0; color: #721c24; font-weight: bold;">Common Issues & Solutions:</p>
                    <ul style="color: #721c24; margin: 0; padding-left: 20px; line-height: 1.6;">
                        <li><strong>Can't open file:</strong> Try a different PDF viewer or browser</li>
                        <li><strong>Poor print quality:</strong> Check printer settings and use higher quality paper</li>
                        <li><strong>Wrong size:</strong> Ensure "Fit to Page" is disabled in print settings</li>
                        <li><strong>Missing attachment:</strong> Check spam folder or contact support</li>
                    </ul>
                </div>
            </div>
            
            <!-- Contact Information -->
            <div style="background: #e8f5e8; border-left: 4px solid #4CAF50; padding: 20px; margin: 20px 0; border-radius: 5px;">
                <h3 style="color: #2e7d32; margin-top: 0;">📞 Need Help?</h3>
                <p style="color: #666; margin-bottom: 15px;">If you have any issues with your badge or need assistance:</p>
                <ul style="color: #666; line-height: 1.6;">
                    <li><strong>Email:</strong> <EMAIL></li>
                    <li><strong>Phone:</strong> +251-58-114-0481</li>
                    <li><strong>Office Hours:</strong> Monday - Friday, 8:00 AM - 5:00 PM</li>
                    <li><strong>Event Day Support:</strong> Registration desk available from 7:00 AM</li>
                </ul>
            </div>
        </div>
        
        <!-- Footer -->
        <div style="background: #f8f9fa; padding: 20px; text-align: center; color: #666; font-size: 14px;">
            <p style="margin: 0;"><strong>University of Gondar Events</strong></p>
            <p style="margin: 5px 0;">Your badge is ready! We look forward to seeing you at the event.</p>
            <p style="margin: 5px 0;">&copy; {{current_year}} University of Gondar. All rights reserved.</p>
            {developer_credits}
        </div>
    </div>
</body>
</html>"""
            
            badge_template.text_content = """Your Event Badge is Ready - {event_name}

Hello {participant_name},

Great news! Your personalized event badge for {event_name} has been generated and is attached to this email.

Badge Features:
- QR Code for quick check-in and identification
- Personal information (name, organization, participant type)
- Event details (name, date, location)
- Security features (unique ID and verification code)
- High quality, print-ready resolution

Printing Instructions:
1. Download: Save the attached badge file to your computer
2. Paper: Use quality cardstock or photo paper for best results
3. Settings: Print at 100% scale (do not resize)
4. Cut: Cut along the border lines for a professional look
5. Protect: Consider laminating for durability

Event Day Instructions:
- Bring your printed badge (required for entry)
- Keep your badge visible throughout the event
- Ensure QR code is clearly visible for scanning
- Bring government-issued ID as backup
- Arrive 15 minutes early for smooth check-in
- Lost badges can be reprinted at registration desk

Troubleshooting:
- Can't open file: Try a different PDF viewer or browser
- Poor print quality: Check printer settings and use higher quality paper
- Wrong size: Ensure "Fit to Page" is disabled in print settings
- Missing attachment: Check spam folder or contact support

Need help? Contact <NAME_EMAIL> or +251-58-114-0481
Office Hours: Monday - Friday, 8:00 AM - 5:00 PM
Event Day Support: Registration desk available from 7:00 AM

Your badge is ready! We look forward to seeing you at the event.

Best regards,
University of Gondar Events Team"""
            
            badge_template.save()
            action = "Created" if created else "Updated"
            self.stdout.write(f"✓ {action} enhanced badge notification template")
            
        except Exception as e:
            self.stdout.write(f"❌ Error with badge template: {e}")
        
        self.stdout.write("Badge notification template improvement completed!")
