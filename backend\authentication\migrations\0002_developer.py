# Generated by Django 4.2.7 on 2025-07-26 17:40

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("authentication", "0001_initial"),
    ]

    operations = [
        migrations.CreateModel(
            name="Developer",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("full_name", models.CharField(max_length=100)),
                ("profession", models.CharField(max_length=100)),
                ("linkedin_link", models.URLField(blank=True, null=True)),
                (
                    "photo",
                    models.ImageField(blank=True, null=True, upload_to="developers/"),
                ),
                ("bio", models.TextField(blank=True, null=True)),
                (
                    "order",
                    models.PositiveIntegerField(
                        default=0, help_text="Order of appearance"
                    ),
                ),
                ("is_active", models.BooleanField(default=True)),
                ("created_at", models.DateTime<PERSON>ield(auto_now_add=True)),
                ("updated_at", models.DateTime<PERSON>ield(auto_now=True)),
            ],
            options={
                "verbose_name": "Developer",
                "verbose_name_plural": "Developers",
                "db_table": "developers",
                "ordering": ["order", "full_name"],
            },
        ),
    ]
