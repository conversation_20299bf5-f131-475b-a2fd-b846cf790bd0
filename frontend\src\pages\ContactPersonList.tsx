import React, { useState, useEffect } from 'react';
import { Contain<PERSON>, <PERSON>, Col, Card, Button, Table, Badge, Modal, Form, Alert, Spinner } from 'react-bootstrap';
import { contactPersonService, eventService, ContactPerson, Event } from '../services/api';

const ContactPersonList: React.FC = () => {
  const [contactPersons, setContactPersons] = useState<ContactPerson[]>([]);
  const [events, setEvents] = useState<Event[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [showModal, setShowModal] = useState(false);
  const [editingContactPerson, setEditingContactPerson] = useState<ContactPerson | null>(null);
  const [selectedEvent, setSelectedEvent] = useState<number | ''>('');
  const [availabilityFilter, setAvailabilityFilter] = useState<string>('');
  const [showImportModal, setShowImportModal] = useState(false);
  const [importFile, setImportFile] = useState<File | null>(null);
  const [importing, setImporting] = useState(false);

  // Form state
  const [formData, setFormData] = useState({
    first_name: '',
    middle_name: '',
    last_name: '',
    phone: '',
    email: '',
    position: '',
    organization: '',
    is_available: true,
    notes: '',
    event: '',
  });

  useEffect(() => {
    fetchContactPersons();
    fetchEvents();
  }, [selectedEvent, availabilityFilter]);

  const fetchContactPersons = async () => {
    try {
      setLoading(true);
      const availabilityParam = availabilityFilter === 'true' ? true : availabilityFilter === 'false' ? false : undefined;
      const response = await contactPersonService.getContactPersons(
        selectedEvent || undefined,
        availabilityParam
      );
      setContactPersons(Array.isArray(response.data) ? response.data : (response.data as any).results || []);
    } catch (err) {
      console.error('Error fetching contact persons:', err);
      setError('Failed to fetch contact persons. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const fetchEvents = async () => {
    try {
      const response = await eventService.getEvents();
      setEvents(Array.isArray(response.data) ? response.data : (response.data as any).results || []);
    } catch (err) {
      console.error('Error fetching events:', err);
    }
  };

  const handleOpenModal = (contactPerson?: ContactPerson) => {
    if (contactPerson) {
      setEditingContactPerson(contactPerson);
      setFormData({
        first_name: contactPerson.first_name,
        middle_name: contactPerson.middle_name,
        last_name: contactPerson.last_name,
        phone: contactPerson.phone,
        email: contactPerson.email,
        position: contactPerson.position,
        organization: contactPerson.organization,
        is_available: contactPerson.is_available,
        notes: contactPerson.notes,
        event: contactPerson.event.toString(),
      });
    } else {
      setEditingContactPerson(null);
      setFormData({
        first_name: '',
        middle_name: '',
        last_name: '',
        phone: '',
        email: '',
        position: '',
        organization: '',
        is_available: true,
        notes: '',
        event: selectedEvent ? selectedEvent.toString() : '',
      });
    }
    setShowModal(true);
  };

  const handleCloseModal = () => {
    setShowModal(false);
    setEditingContactPerson(null);
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    try {
      const data = {
        ...formData,
        event: parseInt(formData.event),
      };

      if (editingContactPerson) {
        await contactPersonService.updateContactPerson(editingContactPerson.id, data);
        setSuccess('Contact person updated successfully!');
      } else {
        await contactPersonService.createContactPerson(data);
        setSuccess('Contact person created successfully!');
      }

      handleCloseModal();
      fetchContactPersons();
    } catch (err) {
      console.error('Error saving contact person:', err);
      setError('Failed to save contact person. Please try again.');
    }
  };

  const handleDelete = async (id: number) => {
    if (window.confirm('Are you sure you want to delete this contact person?')) {
      try {
        await contactPersonService.deleteContactPerson(id);
        setSuccess('Contact person deleted successfully!');
        fetchContactPersons();
      } catch (err) {
        console.error('Error deleting contact person:', err);
        setError('Failed to delete contact person. Please try again.');
      }
    }
  };

  const handleExport = async () => {
    try {
      const response = await contactPersonService.exportCSV();
      const blob = new Blob([response.data], { type: 'text/csv' });
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = 'contact_persons_export.csv';
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);
    } catch (err) {
      console.error('Error exporting contact persons:', err);
      setError('Failed to export contact persons. Please try again.');
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value, type } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? (e.target as HTMLInputElement).checked : value
    }));
  };

  const handleDownloadSample = async () => {
    try {
      const response = await contactPersonService.downloadSample();
      const blob = new Blob([response.data], { type: 'text/csv' });
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = 'contact_persons_sample.csv';
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);
    } catch (err) {
      console.error('Error downloading sample:', err);
      setError('Failed to download sample file. Please try again.');
    }
  };

  const handleImport = async () => {
    if (!importFile) {
      setError('Please select a file to import.');
      return;
    }

    try {
      setImporting(true);
      const response = await contactPersonService.importCSV(importFile);

      if (response.data.errors && response.data.errors.length > 0) {
        setError(`Import completed with warnings: ${response.data.errors.join(', ')}`);
      } else {
        setSuccess(response.data.message);
      }

      setShowImportModal(false);
      setImportFile(null);
      fetchContactPersons();
    } catch (err: any) {
      console.error('Error importing file:', err);
      const errorMessage = err.response?.data?.error || 'Failed to import file. Please try again.';
      setError(errorMessage);
    } finally {
      setImporting(false);
    }
  };

  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      if (file.type !== 'text/csv' && !file.name.endsWith('.csv')) {
        setError('Please select a CSV file.');
        return;
      }
      setImportFile(file);
    }
  };

  if (loading) {
    return (
      <Container className="d-flex justify-content-center align-items-center" style={{ minHeight: '400px' }}>
        <Spinner animation="border" role="status">
          <span className="visually-hidden">Loading...</span>
        </Spinner>
      </Container>
    );
  }

  return (
    <Container fluid className="py-4">
      <Row className="mb-4">
        <Col>
          <div className="d-flex justify-content-between align-items-center">
            <div>
              <h2 className="mb-0">
                <i className="fas fa-address-book me-2"></i>
                Contact Persons Management
              </h2>
              <p className="text-muted mb-0">Manage event contact persons and assignments</p>
            </div>
            <div>
              <Button variant="outline-primary" onClick={handleExport} className="me-2">
                <i className="fas fa-download me-1"></i>
                Export CSV
              </Button>
              <Button variant="outline-secondary" onClick={handleDownloadSample} className="me-2">
                <i className="fas fa-file-csv me-1"></i>
                Sample CSV
              </Button>
              <Button variant="outline-info" onClick={() => setShowImportModal(true)} className="me-2">
                <i className="fas fa-upload me-1"></i>
                Import CSV
              </Button>
              <Button variant="primary" onClick={() => handleOpenModal()}>
                <i className="fas fa-plus me-1"></i>
                Add Contact Person
              </Button>
            </div>
          </div>
        </Col>
      </Row>

      {error && (
        <Alert variant="danger" dismissible onClose={() => setError(null)}>
          {error}
        </Alert>
      )}

      {success && (
        <Alert variant="success" dismissible onClose={() => setSuccess(null)}>
          {success}
        </Alert>
      )}

      {/* Filters */}
      <Row className="mb-4">
        <Col md={6}>
          <Form.Group>
            <Form.Label>Filter by Event</Form.Label>
            <Form.Select
              value={selectedEvent}
              onChange={(e) => setSelectedEvent(e.target.value as number | '')}
            >
              <option value="">All Events</option>
              {events.map((event) => (
                <option key={event.id} value={event.id}>
                  {event.name}
                </option>
              ))}
            </Form.Select>
          </Form.Group>
        </Col>
        <Col md={6}>
          <Form.Group>
            <Form.Label>Filter by Availability</Form.Label>
            <Form.Select
              value={availabilityFilter}
              onChange={(e) => setAvailabilityFilter(e.target.value)}
            >
              <option value="">All</option>
              <option value="true">Available</option>
              <option value="false">Unavailable</option>
            </Form.Select>
          </Form.Group>
        </Col>
      </Row>

      {/* Statistics Cards */}
      <Row className="mb-4">
        <Col md={3}>
          <Card className="text-center">
            <Card.Body>
              <h5 className="text-muted">Total Contact Persons</h5>
              <h2 className="text-primary">{contactPersons.length}</h2>
            </Card.Body>
          </Card>
        </Col>
        <Col md={3}>
          <Card className="text-center">
            <Card.Body>
              <h5 className="text-muted">Available</h5>
              <h2 className="text-success">{contactPersons.filter(cp => cp.is_available).length}</h2>
            </Card.Body>
          </Card>
        </Col>
        <Col md={3}>
          <Card className="text-center">
            <Card.Body>
              <h5 className="text-muted">Unavailable</h5>
              <h2 className="text-danger">{contactPersons.filter(cp => !cp.is_available).length}</h2>
            </Card.Body>
          </Card>
        </Col>
        <Col md={3}>
          <Card className="text-center">
            <Card.Body>
              <h5 className="text-muted">Organizations</h5>
              <h2 className="text-info">{new Set(contactPersons.map(cp => cp.organization).filter(Boolean)).size}</h2>
            </Card.Body>
          </Card>
        </Col>
      </Row>

      {/* Contact Persons Table */}
      <Row>
        <Col>
          <Card>
            <Card.Header>
              <h5 className="mb-0">Contact Persons ({contactPersons.length})</h5>
            </Card.Header>
            <Card.Body className="p-0">
              {contactPersons.length > 0 ? (
                <Table responsive striped hover className="mb-0">
                  <thead className="table-dark">
                    <tr>
                      <th>Photo</th>
                      <th>Name</th>
                      <th>Contact</th>
                      <th>Position</th>
                      <th>Organization</th>
                      <th>Event</th>
                      <th>Status</th>
                      <th>Actions</th>
                    </tr>
                  </thead>
                  <tbody>
                    {contactPersons.map(person => (
                      <tr key={person.id}>
                        <td>
                          {person.photo ? (
                            <img
                              src={person.photo}
                              alt={person.full_name}
                              className="rounded-circle"
                              style={{ width: '40px', height: '40px', objectFit: 'cover' }}
                            />
                          ) : (
                            <div
                              className="rounded-circle bg-secondary d-flex align-items-center justify-content-center text-white"
                              style={{ width: '40px', height: '40px' }}
                            >
                              <i className="fas fa-user"></i>
                            </div>
                          )}
                        </td>
                        <td>
                          <strong>{person.full_name}</strong>
                        </td>
                        <td>
                          <div>
                            <i className="fas fa-phone me-1"></i>
                            {person.phone}
                          </div>
                          <div>
                            <i className="fas fa-envelope me-1"></i>
                            {person.email}
                          </div>
                        </td>
                        <td>{person.position || '-'}</td>
                        <td>{person.organization || '-'}</td>
                        <td>{person.event_name}</td>
                        <td>
                          <Badge bg={person.is_available ? 'success' : 'secondary'}>
                            {person.is_available ? 'Available' : 'Unavailable'}
                          </Badge>
                        </td>
                        <td>
                          <Button
                            variant="outline-primary"
                            size="sm"
                            onClick={() => handleOpenModal(person)}
                            className="me-1"
                          >
                            <i className="fas fa-edit"></i>
                          </Button>
                          <Button
                            variant="outline-danger"
                            size="sm"
                            onClick={() => handleDelete(person.id)}
                          >
                            <i className="fas fa-trash"></i>
                          </Button>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </Table>
              ) : (
                <div className="text-center py-5">
                  <i className="fas fa-address-book fa-3x text-muted mb-3"></i>
                  <h5>No Contact Persons Found</h5>
                  <p className="text-muted">Add your first contact person to get started.</p>
                  <Button variant="primary" onClick={() => handleOpenModal()}>
                    <i className="fas fa-plus me-1"></i>
                    Add Contact Person
                  </Button>
                </div>
              )}
            </Card.Body>
          </Card>
        </Col>
      </Row>

      {/* Add/Edit Modal */}
      <Modal show={showModal} onHide={handleCloseModal} size="lg">
        <Modal.Header closeButton>
          <Modal.Title>
            {editingContactPerson ? 'Edit Contact Person' : 'Add Contact Person'}
          </Modal.Title>
        </Modal.Header>
        <Form onSubmit={handleSubmit}>
          <Modal.Body>
            <Row>
              <Col md={6}>
                <Form.Group className="mb-3">
                  <Form.Label>First Name *</Form.Label>
                  <Form.Control
                    type="text"
                    name="first_name"
                    value={formData.first_name}
                    onChange={handleInputChange}
                    required
                  />
                </Form.Group>
              </Col>
              <Col md={6}>
                <Form.Group className="mb-3">
                  <Form.Label>Last Name *</Form.Label>
                  <Form.Control
                    type="text"
                    name="last_name"
                    value={formData.last_name}
                    onChange={handleInputChange}
                    required
                  />
                </Form.Group>
              </Col>
            </Row>
            <Row>
              <Col md={6}>
                <Form.Group className="mb-3">
                  <Form.Label>Middle Name</Form.Label>
                  <Form.Control
                    type="text"
                    name="middle_name"
                    value={formData.middle_name}
                    onChange={handleInputChange}
                  />
                </Form.Group>
              </Col>
              <Col md={6}>
                <Form.Group className="mb-3">
                  <Form.Label>Phone *</Form.Label>
                  <Form.Control
                    type="tel"
                    name="phone"
                    value={formData.phone}
                    onChange={handleInputChange}
                    required
                  />
                </Form.Group>
              </Col>
            </Row>
            <Form.Group className="mb-3">
              <Form.Label>Email *</Form.Label>
              <Form.Control
                type="email"
                name="email"
                value={formData.email}
                onChange={handleInputChange}
                required
              />
            </Form.Group>
            <Row>
              <Col md={6}>
                <Form.Group className="mb-3">
                  <Form.Label>Position</Form.Label>
                  <Form.Control
                    type="text"
                    name="position"
                    value={formData.position}
                    onChange={handleInputChange}
                  />
                </Form.Group>
              </Col>
              <Col md={6}>
                <Form.Group className="mb-3">
                  <Form.Label>Organization</Form.Label>
                  <Form.Control
                    type="text"
                    name="organization"
                    value={formData.organization}
                    onChange={handleInputChange}
                  />
                </Form.Group>
              </Col>
            </Row>
            <Form.Group className="mb-3">
              <Form.Label>Event *</Form.Label>
              <Form.Select
                name="event"
                value={formData.event}
                onChange={handleInputChange}
                required
              >
                <option value="">Select an event</option>
                {events.map(event => (
                  <option key={event.id} value={event.id}>
                    {event.name}
                  </option>
                ))}
              </Form.Select>
            </Form.Group>
            <Form.Group className="mb-3">
              <Form.Check
                type="checkbox"
                name="is_available"
                label="Available for assignment"
                checked={formData.is_available}
                onChange={handleInputChange}
              />
            </Form.Group>
            <Form.Group className="mb-3">
              <Form.Label>Notes</Form.Label>
              <Form.Control
                as="textarea"
                rows={3}
                name="notes"
                value={formData.notes}
                onChange={handleInputChange}
              />
            </Form.Group>
          </Modal.Body>
          <Modal.Footer>
            <Button variant="secondary" onClick={handleCloseModal}>
              Cancel
            </Button>
            <Button variant="primary" type="submit">
              {editingContactPerson ? 'Update' : 'Add'} Contact Person
            </Button>
          </Modal.Footer>
        </Form>
      </Modal>

      {/* Import Modal */}
      <Modal show={showImportModal} onHide={() => setShowImportModal(false)}>
        <Modal.Header closeButton>
          <Modal.Title>Import Contact Persons from CSV</Modal.Title>
        </Modal.Header>
        <Modal.Body>
          <p className="text-muted">
            Upload a CSV file to import contact persons. The file should contain the following columns:
          </p>
          <div className="mb-3">
            <strong>Required:</strong> First Name, Last Name, Phone, Email, Event Name<br />
            <strong>Optional:</strong> Middle Name, Position, Organization, Available (Yes/No), Notes
          </div>

          <Form.Group className="mb-3">
            <Form.Label>Select CSV File</Form.Label>
            <Form.Control
              type="file"
              accept=".csv"
              onChange={handleFileChange}
            />
          </Form.Group>

          {importFile && (
            <Alert variant="info">
              Selected file: {importFile.name}
            </Alert>
          )}

          <div className="mt-3">
            <Button
              variant="outline-secondary"
              onClick={handleDownloadSample}
              size="sm"
            >
              <i className="fas fa-download me-1"></i>
              Download Sample CSV
            </Button>
          </div>
        </Modal.Body>
        <Modal.Footer>
          <Button variant="secondary" onClick={() => setShowImportModal(false)}>
            Cancel
          </Button>
          <Button
            variant="primary"
            onClick={handleImport}
            disabled={!importFile || importing}
          >
            {importing ? (
              <>
                <Spinner animation="border" size="sm" className="me-2" />
                Importing...
              </>
            ) : (
              <>
                <i className="fas fa-upload me-1"></i>
                Import
              </>
            )}
          </Button>
        </Modal.Footer>
      </Modal>
    </Container>
  );
};

export default ContactPersonList;
