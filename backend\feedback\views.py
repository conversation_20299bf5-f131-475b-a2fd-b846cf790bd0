from django.db.models import Avg, Count, Q
from django.utils import timezone
from rest_framework import viewsets, status, permissions
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework.parsers import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>ars<PERSON>, J<PERSON>NParser
from django_filters.rest_framework import DjangoFilterBackend
from django.db.models import Case, When, IntegerField
from collections import Counter

from .models import EventFeedback, SessionFeedback, FeedbackTemplate
from .serializers import (
    EventFeedbackSerializer, EventFeedbackListSerializer, SessionFeedbackSerializer,
    FeedbackTemplateSerializer, EventFeedbackStatsSerializer, PublicEventFeedbackSerializer
)
from events.models import Event, EventSchedule
from participants.models import Participant


class EventFeedbackViewSet(viewsets.ModelViewSet):
    """ViewSet for managing event feedback"""
    queryset = EventFeedback.objects.all()
    serializer_class = EventFeedbackSerializer
    parser_classes = [<PERSON>Part<PERSON>ars<PERSON>, FormParser, JSONParser]
    filter_backends = [DjangoFilterBackend]
    filterset_fields = ['event', 'overall_satisfaction', 'met_expectations', 'is_anonymous']
    
    def get_serializer_class(self):
        if self.action == 'list':
            return EventFeedbackListSerializer
        return EventFeedbackSerializer
    
    def get_permissions(self):
        """
        Allow anonymous users to create feedback and check existing feedback,
        but require authentication for other actions
        """
        if self.action in ['create', 'check_existing']:
            permission_classes = [permissions.AllowAny]
        elif self.action in ['list', 'retrieve']:
            permission_classes = [permissions.IsAuthenticated]
        else:
            permission_classes = [permissions.IsAdminUser]

        return [permission() for permission in permission_classes]

    def create(self, request, *args, **kwargs):
        """Create new feedback for unregistered participants"""
        # Make a mutable copy of the request data
        data = request.data.copy()

        # Always set participant to None for unregistered participants
        # This allows feedback from unregistered participants
        data['participant'] = None

        # Create serializer with modified data
        serializer = self.get_serializer(data=data)
        serializer.is_valid(raise_exception=True)
        self.perform_create(serializer)
        headers = self.get_success_headers(serializer.data)
        return Response(serializer.data, status=status.HTTP_201_CREATED, headers=headers)

    def get_queryset(self):
        queryset = EventFeedback.objects.select_related('event', 'participant')
        
        # Filter by event if specified
        event_id = self.request.query_params.get('event')
        if event_id:
            queryset = queryset.filter(event_id=event_id)
        
        # Filter by participant if specified
        participant_id = self.request.query_params.get('participant')
        if participant_id:
            queryset = queryset.filter(participant_id=participant_id)
        
        return queryset
    
    @action(detail=False, methods=['get'])
    def statistics(self, request):
        """Get feedback statistics for an event"""
        event_id = request.query_params.get('event')
        if not event_id:
            return Response(
                {'error': 'Event ID is required'}, 
                status=status.HTTP_400_BAD_REQUEST
            )
        
        try:
            event = Event.objects.get(id=event_id)
        except Event.DoesNotExist:
            return Response(
                {'error': 'Event not found'}, 
                status=status.HTTP_404_NOT_FOUND
            )
        
        feedback_queryset = EventFeedback.objects.filter(event=event)
        
        if not feedback_queryset.exists():
            return Response({
                'total_feedback': 0,
                'message': 'No feedback available for this event'
            })
        
        # Calculate statistics
        stats = feedback_queryset.aggregate(
            total_feedback=Count('id'),
            average_overall_satisfaction=Avg('overall_satisfaction'),
            average_session_relevance=Avg('session_relevance'),
            average_speaker_quality=Avg('speaker_quality'),
            average_logistics_rating=Avg(
                Case(
                    When(venue_facilities__isnull=False, then='venue_facilities'),
                    default=0,
                    output_field=IntegerField()
                )
            )
        )
        
        # Rating distributions
        satisfaction_dist = dict(feedback_queryset.values_list('overall_satisfaction').annotate(count=Count('id')))
        expectations_dist = dict(feedback_queryset.values_list('met_expectations').annotate(count=Count('id')))
        networking_dist = dict(feedback_queryset.values_list('sufficient_networking').annotate(count=Count('id')))
        
        # Top themes (simplified - in production you might want to use NLP)
        valuable_aspects = [f.most_valuable_aspect for f in feedback_queryset if f.most_valuable_aspect]
        improvement_suggestions = [f.improvement_suggestions for f in feedback_queryset if f.improvement_suggestions]
        future_topics = [f.future_topics for f in feedback_queryset if f.future_topics]
        
        stats.update({
            'satisfaction_distribution': satisfaction_dist,
            'expectations_met_distribution': expectations_dist,
            'networking_satisfaction_distribution': networking_dist,
            'top_valuable_aspects': valuable_aspects[:10],  # Top 10
            'top_improvement_suggestions': improvement_suggestions[:10],
            'top_future_topics': future_topics[:10],
        })
        
        serializer = EventFeedbackStatsSerializer(stats)
        return Response(serializer.data)
    
    @action(detail=False, methods=['get'])
    def public_feedback(self, request):
        """Get public feedback for display (with consent)"""
        event_id = request.query_params.get('event')
        if not event_id:
            return Response(
                {'error': 'Event ID is required'}, 
                status=status.HTTP_400_BAD_REQUEST
            )
        
        # Only show feedback where consent is given
        queryset = EventFeedback.objects.filter(
            event_id=event_id,
            consent_given=True
        ).select_related('event', 'participant')
        
        serializer = PublicEventFeedbackSerializer(queryset, many=True)
        return Response(serializer.data)
    
    @action(detail=False, methods=['get'])
    def check_existing(self, request):
        """Check if feedback already exists for participant and event"""
        event_id = request.query_params.get('event')
        participant_id = request.query_params.get('participant')
        email = request.query_params.get('email')
        
        if not event_id:
            return Response(
                {'error': 'Event ID is required'}, 
                status=status.HTTP_400_BAD_REQUEST
            )
        
        exists = False
        feedback_id = None
        
        # Check by participant ID
        if participant_id:
            feedback = EventFeedback.objects.filter(
                event_id=event_id, 
                participant_id=participant_id
            ).first()
            if feedback:
                exists = True
                feedback_id = feedback.id
        
        # Check by email if no participant ID
        elif email:
            feedback = EventFeedback.objects.filter(
                event_id=event_id, 
                participant_email=email
            ).first()
            if feedback:
                exists = True
                feedback_id = feedback.id
        
        return Response({
            'exists': exists,
            'feedback_id': feedback_id,
            'message': 'Feedback already submitted' if exists else 'No feedback found'
        })


class SessionFeedbackViewSet(viewsets.ModelViewSet):
    """ViewSet for managing session feedback"""
    queryset = SessionFeedback.objects.all()
    serializer_class = SessionFeedbackSerializer
    permission_classes = [permissions.IsAuthenticated]
    filter_backends = [DjangoFilterBackend]
    filterset_fields = ['event_feedback__event', 'session']
    
    def get_queryset(self):
        queryset = SessionFeedback.objects.select_related('event_feedback', 'session')
        
        # Filter by event if specified
        event_id = self.request.query_params.get('event')
        if event_id:
            queryset = queryset.filter(event_feedback__event_id=event_id)
        
        return queryset


class FeedbackTemplateViewSet(viewsets.ModelViewSet):
    """ViewSet for managing feedback templates"""
    queryset = FeedbackTemplate.objects.all()
    serializer_class = FeedbackTemplateSerializer
    permission_classes = [permissions.IsAdminUser]
    filter_backends = [DjangoFilterBackend]
    filterset_fields = ['template_type', 'is_active', 'is_default']
    
    @action(detail=False, methods=['get'])
    def default_template(self, request):
        """Get default template for a specific type"""
        template_type = request.query_params.get('type', 'conference')
        
        template = FeedbackTemplate.objects.filter(
            template_type=template_type,
            is_default=True,
            is_active=True
        ).first()
        
        if not template:
            # Return a basic default configuration
            default_config = {
                'sections': [
                    {
                        'title': 'Overall Experience',
                        'fields': ['overall_satisfaction', 'met_expectations', 'most_valuable_aspect']
                    },
                    {
                        'title': 'Sessions & Content',
                        'fields': ['session_relevance', 'most_valuable_sessions']
                    },
                    {
                        'title': 'Speakers & Moderators',
                        'fields': ['speaker_quality', 'speaker_comments']
                    },
                    {
                        'title': 'Logistics & Organization',
                        'fields': ['venue_facilities', 'technical_setup', 'time_management']
                    }
                ]
            }
            return Response({
                'name': f'Default {template_type.title()} Template',
                'template_type': template_type,
                'form_config': default_config
            })
        
        serializer = FeedbackTemplateSerializer(template)
        return Response(serializer.data)
