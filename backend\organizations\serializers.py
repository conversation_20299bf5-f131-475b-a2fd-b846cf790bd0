from rest_framework import serializers
from .models import Organization, OrganizationSettings


class OrganizationSettingsSerializer(serializers.ModelSerializer):
    """Serializer for organization settings"""
    
    class Meta:
        model = OrganizationSettings
        fields = [
            'id', 'default_event_duration_hours', 'default_registration_fee',
            'email_signature', 'primary_color', 'secondary_color',
            'send_welcome_emails', 'send_confirmation_emails', 'send_reminder_emails',
            'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'created_at', 'updated_at']


class OrganizationListSerializer(serializers.ModelSerializer):
    """Simplified serializer for organization list views"""
    
    class Meta:
        model = Organization
        fields = [
            'id', 'name', 'short_name', 'logo', 'motto', 'email', 'phone',
            'city', 'country', 'is_active', 'is_primary', 'created_at'
        ]


class OrganizationDetailSerializer(serializers.ModelSerializer):
    """Detailed serializer for organization with all fields"""
    
    settings = OrganizationSettingsSerializer(read_only=True)
    full_address = serializers.ReadOnlyField()
    display_name = serializers.ReadOnlyField()
    
    class Meta:
        model = Organization
        fields = [
            'id', 'name', 'short_name', 'logo', 'motto', 'description',
            'email', 'phone', 'website',
            'address_line_1', 'address_line_2', 'city', 'state_province',
            'postal_code', 'country', 'full_address',
            'facebook_url', 'twitter_url', 'linkedin_url', 'instagram_url',
            'is_active', 'is_primary', 'display_name',
            'created_at', 'updated_at', 'settings'
        ]
        read_only_fields = ['id', 'created_at', 'updated_at', 'full_address', 'display_name']


class OrganizationCreateUpdateSerializer(serializers.ModelSerializer):
    """Serializer for creating and updating organizations"""
    
    class Meta:
        model = Organization
        fields = [
            'name', 'short_name', 'logo', 'motto', 'description',
            'email', 'phone', 'website',
            'address_line_1', 'address_line_2', 'city', 'state_province',
            'postal_code', 'country',
            'facebook_url', 'twitter_url', 'linkedin_url', 'instagram_url',
            'is_active', 'is_primary'
        ]
    
    def validate_email(self, value):
        """Validate email format"""
        if not value:
            raise serializers.ValidationError("Email is required.")
        return value
    
    def validate_name(self, value):
        """Validate organization name"""
        if not value or len(value.strip()) < 2:
            raise serializers.ValidationError("Organization name must be at least 2 characters long.")
        return value.strip()
    
    def validate(self, data):
        """Custom validation for organization data"""
        # Ensure primary organization logic
        if data.get('is_primary', False):
            # Check if another organization is already primary (excluding current instance)
            instance_id = self.instance.id if self.instance else None
            existing_primary = Organization.objects.filter(is_primary=True)
            if instance_id:
                existing_primary = existing_primary.exclude(id=instance_id)
            
            if existing_primary.exists():
                # This will be handled in the model's save method, but we can warn here
                pass
        
        return data
