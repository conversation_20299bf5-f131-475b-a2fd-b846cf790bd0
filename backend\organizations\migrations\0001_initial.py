# Generated by Django 4.2.7 on 2025-07-26 13:44

import django.core.validators
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = []

    operations = [
        migrations.CreateModel(
            name="Organization",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "name",
                    models.CharField(
                        help_text="Official name of the organization", max_length=200
                    ),
                ),
                (
                    "short_name",
                    models.CharField(
                        blank=True,
                        help_text="Short name or abbreviation",
                        max_length=50,
                    ),
                ),
                (
                    "logo",
                    models.ImageField(
                        blank=True,
                        help_text="Organization logo",
                        null=True,
                        upload_to="organizations/logos/",
                    ),
                ),
                (
                    "motto",
                    models.TextField(
                        blank=True, help_text="Organization motto or tagline"
                    ),
                ),
                (
                    "description",
                    models.TextField(
                        blank=True, help_text="Brief description of the organization"
                    ),
                ),
                (
                    "email",
                    models.<PERSON><PERSON><PERSON>ield(
                        help_text="Primary contact email",
                        max_length=254,
                        validators=[django.core.validators.EmailValidator()],
                    ),
                ),
                (
                    "phone",
                    models.CharField(
                        blank=True,
                        help_text="Primary contact phone number",
                        max_length=20,
                    ),
                ),
                (
                    "website",
                    models.URLField(
                        blank=True,
                        help_text="Organization website URL",
                        validators=[django.core.validators.URLValidator()],
                    ),
                ),
                (
                    "address_line_1",
                    models.CharField(
                        blank=True, help_text="Street address line 1", max_length=255
                    ),
                ),
                (
                    "address_line_2",
                    models.CharField(
                        blank=True, help_text="Street address line 2", max_length=255
                    ),
                ),
                (
                    "city",
                    models.CharField(blank=True, help_text="City", max_length=100),
                ),
                (
                    "state_province",
                    models.CharField(
                        blank=True, help_text="State or Province", max_length=100
                    ),
                ),
                (
                    "postal_code",
                    models.CharField(
                        blank=True, help_text="Postal or ZIP code", max_length=20
                    ),
                ),
                (
                    "country",
                    models.CharField(blank=True, help_text="Country", max_length=100),
                ),
                (
                    "facebook_url",
                    models.URLField(blank=True, help_text="Facebook page URL"),
                ),
                (
                    "twitter_url",
                    models.URLField(blank=True, help_text="Twitter profile URL"),
                ),
                (
                    "linkedin_url",
                    models.URLField(blank=True, help_text="LinkedIn page URL"),
                ),
                (
                    "instagram_url",
                    models.URLField(blank=True, help_text="Instagram profile URL"),
                ),
                (
                    "is_active",
                    models.BooleanField(
                        default=True, help_text="Whether this organization is active"
                    ),
                ),
                (
                    "is_primary",
                    models.BooleanField(
                        default=False,
                        help_text="Whether this is the primary organization",
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
            ],
            options={
                "verbose_name": "Organization",
                "verbose_name_plural": "Organizations",
                "ordering": ["-is_primary", "name"],
            },
        ),
        migrations.CreateModel(
            name="OrganizationSettings",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "default_event_duration_hours",
                    models.PositiveIntegerField(
                        default=8, help_text="Default duration for events in hours"
                    ),
                ),
                (
                    "default_registration_fee",
                    models.DecimalField(
                        decimal_places=2,
                        default=0.0,
                        help_text="Default registration fee for events",
                        max_digits=10,
                    ),
                ),
                (
                    "email_signature",
                    models.TextField(
                        blank=True,
                        help_text="Default email signature for communications",
                    ),
                ),
                (
                    "primary_color",
                    models.CharField(
                        default="#007bff",
                        help_text="Primary brand color (hex code)",
                        max_length=7,
                    ),
                ),
                (
                    "secondary_color",
                    models.CharField(
                        default="#6c757d",
                        help_text="Secondary brand color (hex code)",
                        max_length=7,
                    ),
                ),
                (
                    "send_welcome_emails",
                    models.BooleanField(
                        default=True,
                        help_text="Send welcome emails to new participants",
                    ),
                ),
                (
                    "send_confirmation_emails",
                    models.BooleanField(
                        default=True,
                        help_text="Send confirmation emails for registrations",
                    ),
                ),
                (
                    "send_reminder_emails",
                    models.BooleanField(
                        default=True, help_text="Send reminder emails before events"
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "organization",
                    models.OneToOneField(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="settings",
                        to="organizations.organization",
                    ),
                ),
            ],
            options={
                "verbose_name": "Organization Settings",
                "verbose_name_plural": "Organization Settings",
            },
        ),
    ]
