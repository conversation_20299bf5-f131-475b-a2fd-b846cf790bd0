from django.core.management.base import BaseCommand
from events.email_service import get_email_service
from participants.models import Participant
from events.models import EmailTemplate


class Command(BaseCommand):
    help = 'Test email functionality'

    def handle(self, *args, **options):
        self.stdout.write("Testing email system...")
        
        # Get email service
        email_service = get_email_service()
        self.stdout.write(f"Email service initialized: {email_service.config}")
        
        # Check the registration template
        self.stdout.write("Checking registration template...")
        try:
            reg_template = EmailTemplate.objects.get(template_type='registration_confirmation')
            self.stdout.write(f"✓ Registration template found: {len(reg_template.html_content)} characters")
        except Exception as e:
            self.stdout.write(f"Error checking template: {e}")
        
        # Test with a participant
        participant = Participant.objects.first()
        if not participant:
            self.stdout.write("No participants found for testing")
            return
            
        self.stdout.write(f"Testing with participant: {participant.full_name} ({participant.email})")
        
        # Test registration confirmation
        self.stdout.write("Testing registration confirmation email...")
        try:
            result = email_service.send_registration_confirmation(participant)
            self.stdout.write(f"Registration confirmation result: {result}")
        except Exception as e:
            self.stdout.write(f"Registration email error: {e}")
        
        # Test approval email
        self.stdout.write("Testing approval email...")
        try:
            approval_result = email_service.send_event_details(participant)
            self.stdout.write(f"Approval email result: {approval_result}")
        except Exception as e:
            self.stdout.write(f"Approval email error: {e}")
        
        # Show recent email logs
        self.stdout.write("\nRecent email logs:")
        from events.models import EmailLog
        recent_logs = EmailLog.objects.filter(recipient_email=participant.email).order_by('-created_at')[:5]
        for log in recent_logs:
            self.stdout.write(f"- {log.template_type}: {log.status} - {log.error_message or 'Success'}")
