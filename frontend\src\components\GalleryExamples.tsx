import React from 'react';
import { Container, Row, Col, Card } from 'react-bootstrap';
import Gallery from './Gallery';
import { EventGallery } from '../services/api';

// Example component showing different ways to use the Gallery component
const GalleryExamples: React.FC = () => {
  // Mock data for demonstration
  const mockImages: EventGallery[] = [
    {
      id: 1,
      title: "Sample Image 1",
      description: "This is a sample image description",
      image: "/path/to/image1.jpg",
      uploaded_at: "2024-01-15T10:30:00Z",
      is_featured: true,
      event: 1
    },
    {
      id: 2,
      title: "Sample Image 2",
      description: "Another sample image",
      image: "/path/to/image2.jpg",
      uploaded_at: "2024-01-16T14:20:00Z",
      is_featured: false,
      event: 1
    }
  ];

  return (
    <Container className="py-5">
      <h2 className="text-center mb-5">Gallery Component Examples</h2>
      
      {/* Example 1: Simple Grid Gallery */}
      <Row className="mb-5">
        <Col>
          <Card className="p-4">
            <h4 className="mb-3">Simple Grid Gallery</h4>
            <p className="text-muted mb-4">
              Basic gallery without date grouping, perfect for showcasing images in any section.
            </p>
            <Gallery 
              images={mockImages}
              title="Featured Photos"
              showDateGrouping={false}
              gridColumns={{ lg: 4, md: 6, sm: 12 }}
              imageHeight="180px"
              downloadPrefix="featured"
            />
          </Card>
        </Col>
      </Row>

      {/* Example 2: Date-Grouped Gallery */}
      <Row className="mb-5">
        <Col>
          <Card className="p-4">
            <h4 className="mb-3">Date-Grouped Gallery</h4>
            <p className="text-muted mb-4">
              Gallery with date grouping, ideal for event photos or time-based content.
            </p>
            <Gallery 
              images={mockImages}
              title="Event Timeline"
              showDateGrouping={true}
              gridColumns={{ lg: 3, md: 4, sm: 6 }}
              downloadPrefix="event"
            />
          </Card>
        </Col>
      </Row>

      {/* Example 3: Compact Gallery */}
      <Row className="mb-5">
        <Col>
          <Card className="p-4">
            <h4 className="mb-3">Compact Gallery</h4>
            <p className="text-muted mb-4">
              Smaller images in a compact layout, good for sidebars or smaller sections.
            </p>
            <Gallery 
              images={mockImages}
              showDateGrouping={false}
              showDownloadButton={false}
              showFeaturedBadge={false}
              gridColumns={{ lg: 6, md: 6, sm: 12 }}
              imageHeight="120px"
              emptyStateMessage="No thumbnails available"
              className="compact-gallery"
            />
          </Card>
        </Col>
      </Row>

      {/* Example 4: Portfolio Style */}
      <Row className="mb-5">
        <Col>
          <Card className="p-4">
            <h4 className="mb-3">Portfolio Style Gallery</h4>
            <p className="text-muted mb-4">
              Larger images for portfolio or showcase purposes.
            </p>
            <Gallery 
              images={mockImages}
              title="Portfolio"
              showDateGrouping={false}
              gridColumns={{ lg: 2, md: 3, sm: 6 }}
              imageHeight="300px"
              downloadPrefix="portfolio"
              emptyStateMessage="No portfolio items"
              emptyStateIcon="fas fa-briefcase"
            />
          </Card>
        </Col>
      </Row>

      {/* Usage Examples */}
      <Row>
        <Col>
          <Card className="p-4 bg-light">
            <h4 className="mb-3">Usage Examples</h4>
            <div className="code-examples">
              <h6>1. Simple Gallery for any section:</h6>
              <pre className="bg-white p-3 rounded">
{`<Gallery 
  images={images}
  title="Section Gallery"
  showDateGrouping={false}
  gridColumns={{ lg: 4, md: 6, sm: 12 }}
  downloadPrefix="section"
/>`}
              </pre>

              <h6 className="mt-4">2. Event Gallery with date grouping:</h6>
              <pre className="bg-white p-3 rounded">
{`<Gallery 
  images={eventImages}
  showDateGrouping={true}
  downloadPrefix={eventName}
  emptyStateMessage="No event photos"
/>`}
              </pre>

              <h6 className="mt-4">3. Compact gallery without downloads:</h6>
              <pre className="bg-white p-3 rounded">
{`<Gallery 
  images={thumbnails}
  showDownloadButton={false}
  showFeaturedBadge={false}
  gridColumns={{ lg: 6, md: 8, sm: 12 }}
  imageHeight="100px"
/>`}
              </pre>
            </div>
          </Card>
        </Col>
      </Row>
    </Container>
  );
};

export default GalleryExamples;
