from django.urls import path, include
from rest_framework.routers import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from .views import EventFeedbackViewSet, SessionFeedbackViewSet, FeedbackTemplateViewSet

router = DefaultRouter()
router.register(r'event-feedback', EventFeedbackViewSet, basename='event-feedback')
router.register(r'session-feedback', SessionFeedbackViewSet, basename='session-feedback')
router.register(r'feedback-templates', FeedbackTemplateViewSet, basename='feedback-templates')

urlpatterns = [
    path('', include(router.urls)),
]
