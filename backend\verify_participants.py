#!/usr/bin/env python3

import os
import sys
import django

# Add the project root to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'event_management.settings')
django.setup()

from participants.models import Participant

def verify_participants():
    """Verify that participants have been added successfully"""
    
    print("=== PARTICIPANT DATABASE VERIFICATION ===")
    print(f"Total participants: {Participant.objects.count()}")
    
    # Check ID ranges
    print("\nParticipants by ID range:")
    print(f"IDs 1-100: {Participant.objects.filter(id__range=(1, 100)).count()}")
    print(f"IDs 101-200: {Participant.objects.filter(id__range=(101, 200)).count()}")
    print(f"IDs 300-400: {Participant.objects.filter(id__range=(300, 400)).count()}")
    print(f"IDs 500-600: {Participant.objects.filter(id__range=(500, 600)).count()}")
    
    # Check some specific emails from the latest batch
    test_emails = [
        '<EMAIL>',
        '<EMAIL>', 
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>'
    ]
    
    print("\nVerifying specific participants from latest batch:")
    found_count = 0
    for email in test_emails:
        participant = Participant.objects.filter(email=email).first()
        if participant:
            print(f"✓ Found: {participant.first_name} {participant.last_name} ({email}) - ID: {participant.id}")
            found_count += 1
        else:
            print(f"✗ Not found: {email}")
    
    print(f"\nFound {found_count}/{len(test_emails)} test participants")
    
    # Show highest ID participants
    print("\nHighest 10 participants by ID:")
    high_participants = Participant.objects.order_by('-id')[:10]
    for p in high_participants:
        print(f"ID: {p.id}, Name: {p.first_name} {p.last_name}, Email: {p.email}")
    
    # Show participant type distribution
    print("\nParticipant type distribution:")
    from participants.models import ParticipantType
    for pt in ParticipantType.objects.all():
        count = Participant.objects.filter(participant_type=pt).count()
        print(f"{pt.name}: {count} participants")

if __name__ == "__main__":
    verify_participants()
