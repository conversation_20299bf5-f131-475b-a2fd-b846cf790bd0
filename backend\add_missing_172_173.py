#!/usr/bin/env python3

import os
import sys
import django
from datetime import datetime

# Add the project root to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'event_management.settings')
django.setup()

from participants.models import Participant, ParticipantType
from events.models import Event
from django.db import connection

def add_missing_172_173():
    """Add missing participants 172 and 173 with unique emails"""
    
    print("=== ADDING MISSING PARTICIPANTS 172 AND 173 ===")
    
    # Get Event ID 1
    try:
        event = Event.objects.get(id=1)
    except Event.DoesNotExist:
        print("Error: Event with ID 1 not found")
        return
    
    # Get participant types
    participant_types = {}
    for pt in ParticipantType.objects.all():
        participant_types[pt.id] = pt
    
    # Missing participants with unique emails
    missing_participants = [
        # (id, first_name, last_name, middle_name, email, phone, institution_name, participant_type_id, arrival_date, departure_date, profile_photo, remarks, status)
        (172, 'Bedilu', '<PERSON><PERSON><PERSON>', 'Teka', '<EMAIL>', '093 7003501', 'Mattu University', 23, '2025-08-03 12:56:00', '2025-08-07 12:57:00', 'event-registration/profiles/profile_3e619b1d-7f31-440e-a56c-7d73936d860a.jpg', 'My arrival and return date is filled in GeG.C not in Ethiopia calendar.', 'active'),
        (173, 'Berhanu', 'Ali', 'Shibeshi', '<EMAIL>', '094 1741764', 'WACHEMO UNIVERSITY', 27, '2025-08-04 14:29:00', '2025-08-07 14:29:00', 'event-registration/profiles/profile_5868e969-a1d6-49e8-a2b5-485eb5ed6fe7.png', '', 'active'),
    ]
    
    added_count = 0
    
    with connection.cursor() as cursor:
        for p_data in missing_participants:
            # Check if participant already exists
            if Participant.objects.filter(id=p_data[0]).exists():
                print(f"Participant with ID {p_data[0]} already exists, skipping...")
                continue
            
            # Get participant type
            participant_type = participant_types.get(p_data[7])
            if not participant_type:
                print(f"Warning: Participant type {p_data[7]} not found for {p_data[1]} {p_data[2]}")
                continue
            
            # Parse dates
            arrival_date = None
            departure_date = None
            
            if p_data[8]:  # arrival_date
                try:
                    arrival_date = datetime.strptime(p_data[8], "%Y-%m-%d %H:%M:%S")
                except:
                    arrival_date = None
            
            if p_data[9]:  # departure_date
                try:
                    departure_date = datetime.strptime(p_data[9], "%Y-%m-%d %H:%M:%S")
                except:
                    departure_date = None
            
            # Create participant
            try:
                participant = Participant(
                    first_name=p_data[1],
                    last_name=p_data[2],
                    middle_name=p_data[3] or "",
                    email=p_data[4],
                    phone=p_data[5],
                    institution_name=p_data[6],
                    position="Participant",
                    participant_type=participant_type,
                    arrival_date=arrival_date,
                    departure_date=departure_date,
                    profile_photo=p_data[10],
                    remarks=p_data[11] or "",
                    status=p_data[12],
                    event=event
                )
                participant.save()
                
                # Set the exact ID using raw SQL
                cursor.execute(
                    "UPDATE participants_participant SET id = %s WHERE id = %s",
                    [p_data[0], participant.id]
                )
                
                added_count += 1
                print(f"Added: {p_data[1]} {p_data[2]} with ID {p_data[0]} and email {p_data[4]}")
                
            except Exception as e:
                print(f"Error adding {p_data[1]} {p_data[2]}: {str(e)}")
    
    print(f"\nSummary:")
    print(f"Added {added_count} missing participants")
    print(f"Total participants now: {Participant.objects.count()}")

if __name__ == "__main__":
    add_missing_172_173()
