#!/usr/bin/env python3

import os
import sys
import django
from datetime import datetime

# Add the project root to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'event_management.settings')
django.setup()

from participants.models import Participant, ParticipantType
from events.models import Event

def create_latest_participants():
    """Create latest batch of participants with exact IDs"""
    
    # Get the event (assuming Event ID 1)
    try:
        event = Event.objects.get(id=1)
    except Event.DoesNotExist:
        print("Error: Event with ID 1 not found")
        return
    
    # Get all participant types
    participant_types = {pt.id: pt for pt in ParticipantType.objects.all()}
    
    # Latest participants data (IDs 551-602)
    latest_participants = [
        # (id, first_name, last_name, middle_name, email, phone, institution_name, participant_type_id, arrival_date, departure_date, profile_photo, remarks, status)
        (551, "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<EMAIL>", "091 9818743", "Diplomat podcast", 24, "2025-08-03 14:00:00", "2025-08-07 14:00:00", "event-registration/profiles/profile_9e9be7d3-883f-", "", "active"),
        (552, "Habetamu", "G. Mikaele", "Miteku", "<EMAIL>", "093 1548289", "Debark Universkty", 3, "2025-08-03 14:08:00", "2025-08-06 14:09:00", None, "", "active"),
        (553, "Abulie", "Takele", "Melku", "<EMAIL>", "094 8862338", "Ministry of Education", 28, "2025-08-04 00:31:00", "2025-08-07 14:16:00", None, "I am from Ministry of Education, Research Ethics D...", "active"),
        (554, "Ferew", "Adera", "Abebe", "<EMAIL>", "911617935", "Diretube ,Media", 24, "2025-08-03 14:18:00", "2025-08-03 18:19:00", "event-registration/profiles/profile_1cd1e060-5100-", "-", "active"),
        (555, "Siraw", "Tesfa", "Yenesew", "<EMAIL>", "091 3314342", "Mekelle University", 27, "2025-08-03 14:33:00", "2025-08-06 14:33:00", "event-registration/profiles/profile_f51f8589-0fdb-", "", "active"),
        (556, "Dr. Tafere", "Yalew", "melaku", "<EMAIL>", "098 7990457", "Forum for Higher Education Institutions in Amhara", 2, "2025-08-05 08:45:00", "2025-08-07 08:10:00", None, "", "active"),
        (557, "Fekade", "Fetene", "Desalegn", "<EMAIL>", "091 1176828", "MoE", 8, "2025-08-03 08:32:00", "2025-08-07 14:36:00", None, "", "active"),
        (558, "Gebeyehu", "Tiruneh", "Ashagrie", "<EMAIL>", "091 1287533", "Bule Hora University", 23, "2025-08-03 13:35:00", "2025-08-05 12:10:00", None, "", "active"),
        (559, "Dr. Jejaw", "Mebirat", "Demamu", "<EMAIL>", "098 7259301", "Forum for Higher Education Institutions in Amhara", 2, "2025-08-05 08:43:00", "2025-08-07 08:43:00", None, "", "active"),
        (560, "Tamirat", "Wetite", "Beyene", "<EMAIL>", "090 3124988", "Dilla University", 23, "2025-08-03 05:28:00", "2025-08-06 08:28:00", "event-registration/profiles/profile_9b933aeb-e40e-", "", "active"),
        (561, "Mulugeta", "Bedaso", "Burka", "<EMAIL>", "093 0278966", "29234948", 27, "2025-08-03 13:35:00", "2025-08-07 02:08:00", "event-registration/profiles/profile_0de5e87e-a3b1-", "", "active"),
        (562, "Ephrem", "Megersa", "Alemayehu", "<EMAIL>", "+251 918120244", "Assosa University", 28, "2025-08-03 15:30:00", "2025-08-07 15:30:00", "event-registration/profiles/profile_1eedba92-398c-", "When you add university presdants protocol offers...", "active"),
        (563, "ALEMNESH", "KUMSA", "REGA", "<EMAIL>", "+251 911645458", "DYNAMIC INTERAIMENET", 24, "2025-08-02 08:56:00", "2025-08-02 14:59:00", None, "", "active"),
        (564, "Tegegn", "Ejigu", "Dagne", "<EMAIL>", "091 1419278", "MoE", 28, "2025-08-04 15:21:00", "2025-08-04 15:28:00", "event-registration/profiles/profile_6a025fcd-17ea-", "No", "active"),
        (565, "Habtye", "Adane", "Nigussie", "<EMAIL>", "091 8770944", "University of Gondar", 32, "2025-07-31 23:58:00", "2025-08-01 23:58:00", None, "", "active"),
        (566, "Tilahun", "Retta", "Teshome", "<EMAIL>", "091 1684491", "Jimma University", 29, "2025-08-03 03:55:00", "2025-08-07 08:30:00", None, "", "active"),
        (567, "Meseret", "Getahun", "Deribu", "<EMAIL>", "091 1154712", "Ministry of Education", 33, "2025-08-03 07:00:00", "2025-08-07 14:00:00", "event-registration/profiles/profile_650536fd-dde7-", "Participant from Ministry of Education. (Assigned...", "active"),
        (568, "teshome", "Ayele", "Nigussie", "<EMAIL>", "091 8807913", "University of Gondar", 32, "2025-08-01 00:17:00", "2025-08-02 00:18:00", None, "", "active"),
        (569, "Abun", "Yisma", "Arega", "<EMAIL>", "+251 911308189", "MoE", 8, "2025-08-03 07:05:00", "2025-08-06 07:06:00", None, "", "active"),
        (570, "Mesafint", "Demlie", "Fentie", "<EMAIL>", "091 8592149", "University of Gondar", 32, "2025-08-01 00:21:00", "2025-08-02 00:21:00", None, "", "active"),
        (571, "Damena", "Moreda", "Bikila", "<EMAIL>", "091 2206819", "MoE", 25, "2025-08-03 15:56:00", "2025-08-06 16:56:00", None, "", "active"),
        (572, "Lielina", "Asfaw", "Emiru", "<EMAIL>", "091 8736942", "University of Gondar", 32, "2025-08-01 00:28:00", "2025-08-02 00:28:00", None, "", "active"),
        (573, "Misganaw", "Tegegne", "Tilahun", "<EMAIL>", "093 2822355", "University of Gondar", 25, "2025-08-04 16:12:00", "2027-08-08 13:10:00", "event-registration/profiles/profile_b760f97e-9a95-", "", "active"),
        (574, "Shimelis", "Abo", "Lemma", "<EMAIL>", "096 4396877", "Ministry of education", 8, "2025-08-03 16:14:00", "2025-08-06 16:17:00", None, "", "active"),
        (575, "Prof Teketel", "Ashebo", "Yohannes", "<EMAIL>", "+251 911408839", "University of Gondar", 29, "2025-08-03 17:55:00", "2025-08-07 16:25:00", None, "", "active"),
        (576, "Tsegaye", "Mulate", "Endale", "<EMAIL>", "091 1098756", "Ministry of Education", 33, "2025-08-03 06:23:00", "2025-08-07 08:24:00", None, "", "active"),
        (577, "Begna", "Mulugeta", "Dejene", "<EMAIL>", "090 1951680", "Oromia State University", 8, "2025-08-05 15:00:00", "2025-08-07 09:00:00", "event-registration/profiles/profile_d0b49219-5ee5-", "I'm ICT Director of Oromia State University", "active"),
        (578, "Seid", "Yemer", "Mohammed", "<EMAIL>", "091 1048883", "Ministry of Education", 33, "2025-08-03 00:20:00", "2025-08-07 04:20:00", None, "Happy to visit UoG", "active"),
        (579, "Tsega", "Yinesu", "Mekonnen", "<EMAIL>", "091 2059936", "Ministry of Education", 8, "2025-08-03 16:30:00", "2025-08-05 16:30:00", None, "", "active"),
        (580, "Chara", "Disasa", "Bekele", "<EMAIL>", "921174462", "Dambidollo university", 27, "2025-08-03 17:21:00", "2025-08-07 17:21:00", None, "", "active"),
        (581, "Wubshet", "Tadele", "Alula", "<EMAIL>", "094 4363227", "FDRE Education and Training Authority", 1, "2025-08-03 15:45:00", "2025-08-07 14:35:00", None, "", "active"),
        (582, "Meseret Alemneh", "Banti", "", "<EMAIL>", "091 8402648", "University of Gondar", 24, "2025-08-02 01:00:00", "2025-08-05 11:00:00", None, "", "active"),
        (583, "Yidagnu Mandefro", "Zeleke", "", "<EMAIL>", "091 2759569", "University of Gondar", 25, "2025-08-01 17:42:00", "2025-08-01 22:40:00", None, "", "active"),
        (584, "Molla", "Hagos", "Tsegaye", "<EMAIL>", "+251 911241260", "Admas University", 2, "2025-08-03 18:00:00", "2025-08-07 18:02:00", None, "", "active"),
        (585, "Temesgen", "Abiyu", "Assefa", "<EMAIL>", "091 1832932", "Ministry of Education", 8, "2025-08-03 08:30:00", "2025-08-07 02:00:00", None, "", "active"),
        (586, "Dereje", "Woldemariam", "Awgichew", "<EMAIL>", "091 1164076", "Ministry of Education", 28, "2025-08-03 07:20:00", "2025-08-07 16:30:00", "event-registration/profiles/profile_7ceee310-4c13-", "", "active"),
        (587, "Akiber", "Chufo", "", "<EMAIL>", "+251 911813633", "Wolaita Sodo University", 23, "2025-08-04 07:34:00", "2025-08-06 08:30:00", "event-registration/profiles/profile_f4245829-7739-", "", "active"),
        (588, "Mulat", "Legesse", "Abegaz", "<EMAIL>", "093 0177610", "AASTU Board", 29, "2025-08-03 08:20:00", "2025-08-05 04:20:00", "event-registration/profiles/profile_8d94d300-58b6-", "", "active"),
        (589, "Engdaw", "Hailu", "Mewesha", "<EMAIL>", "097 0691450", "UOG", 24, "2025-08-04 20:24:00", "2025-08-07 20:24:00", None, "Pr", "active"),
        (590, "Belay", "Ayenew", "Mesfin", "<EMAIL>", "091 8805970", "university of gondar", 24, "2025-08-01 20:31:00", "2025-08-04 20:31:00", "event-registration/profiles/profile_2d6cdf95-d13b-", "", "active"),
        (591, "Lemlem", "Hailemariyam", "Demissie", "<EMAIL>", "0911928439", "MoE", 25, "2025-08-03 07:30:00", "2025-08-07 16:10:00", "event-registration/profiles/profile_5317ef2c-7bcf-", "", "active"),
        (592, "Hermela", "Fekade", "Belayhun", "<EMAIL>", "091 2179860", "NBC Ethiopia", 24, "2025-08-03 01:40:00", "2025-08-07 10:40:00", None, "", "active"),
        (593, "Tesfahun", "Yilma", "Melese", "<EMAIL>", "091 8779820", "University of Gondar", 25, "2025-08-01 22:32:00", "2025-08-02 22:32:00", "event-registration/profiles/profile_b8a273e9-e268-", "", "active"),
        (594, "Sileshi", "Assefa", "Abbi", "<EMAIL>", "+251 930355379", "Mekdela Amba university", 23, "2025-08-04 22:45:00", "2025-08-07 22:47:00", None, "", "active"),
        (595, "Tewodros", "Chekol", "Abebaw", "<EMAIL>", "091 0153087", "Universtiy of Gondar", 32, None, None, "event-registration/profiles/profile_2217eeef-85f5-", "", "deleted"),
        (596, "Reagan", "Jennings", "Nehru Underwood", "<EMAIL>", "094 3616975", "Delaney Alvarez Associates", 4, None, None, None, "Anim tempore neque", "deleted"),
        (597, "Solomon", "Gebretsadik", "Fantaw", "<EMAIL>", "093 7402246", "University of Gondar", 25, None, None, "event-registration/profiles/profile_bc54bbed-dc8f-", "", "active"),
        (598, "Dr. Yitayal", "Mengistu", "Alemu", "<EMAIL>", "091 1601812", "University of Gondar", 25, None, None, None, "", "active"),
        (599, "Chanie", "Faris", "Adefris", "<EMAIL>", "+251 911167250", "Ministry of education", 25, None, None, None, "", "active"),
        (600, "Demiss", "Geberu", "Mulatu", "<EMAIL>", "092 4110714", "University of Gondar", 25, None, None, "event-registration/profiles/profile_328dcfd2-59e7-", "", "active"),
        (601, "Tadesse", "Baymot", "Weldegebreal", "<EMAIL>", "091 8778670", "University of Gondar", 25, None, None, None, "", "active"),
        (602, "Getasew", "Ayalew", "Abebaw", "<EMAIL>", "913336448", "UoG", 25, None, None, "event-registration/profiles/profile_835e9bae-6366-", "", "active"),
        (338, "Gemechis", "Duressa", "", "<EMAIL>", "094 2565096", "Jimma University", 23, "2025-08-03 16:48:00", "2025-08-07 20:48:00", None, "NA", "active"),
        (339, "Andargachew", "Deata", "Baylie", "<EMAIL>", "092 8558827", "Debre Markos University", 23, "2025-08-03 23:50:00", "2025-09-03 22:50:00", None, "", "active"),
        (340, "Pal", "Dol", "Both", "<EMAIL>", "091 7303194", "Gambella University", 23, "2025-09-03 17:54:00", "2025-09-06 05:55:00", None, "", "active"),
        (342, "Yohannes", "Ejigu", "Yitbarek", "<EMAIL>", "+251 920437380", "Jinka University", 2, "2025-08-03 15:40:00", "2025-08-07 08:35:00", None, "", "active"),
        (343, "Tadiyose", "Arba", "Arba", "<EMAIL>", "091 0153087", "Prof Kindy's Security", 9, "2025-07-31 17:47:00", "2025-08-06 17:47:00", None, "", "active"),
        (344, "Mengesha", "Mersha", "Admasu", "<EMAIL>", "091 8350278", "Raya University", 29, "2025-08-03 15:55:00", "2025-08-07 08:30:00", None, "", "active"),
        (346, "Tilahun", "Retta", "Teshome", "<EMAIL>", "091 1684491", "AAU", 29, "2025-08-03 17:34:00", "2025-08-07 17:36:00", None, "", "deleted"),
        (347, "Tesfahun", "Yilma", "Melese", "<EMAIL>", "091 8779820", "University of Gondar", 25, "2025-07-31 17:30:00", "2025-07-31 22:30:00", None, "", "deleted"),
        (348, "Endris", "Ahmed", "Seid", "<EMAIL>", "091 1034154", "Samara University", 27, "2025-08-02 14:30:00", "2025-08-07 18:45:00", None, "", "active"),
        (349, "Zerihun", "Assefa", "", "<EMAIL>", "098 3001916", "Jimma University", 23, "2025-08-03 12:10:00", "2025-08-06 12:00:00", None, "The arrival and departure times may change.", "active"),
        (350, "Engidaw", "Muche", "Awoke", "<EMAIL>", "+251 918030593", "UoG", 4, "2025-07-30 17:11:00", "2025-07-31 17:11:00", None, "", "deleted"),
        (351, "Tesfaye", "Feyissa", "Tolu", "<EMAIL>", "+251 911677704", "Ethiopian Defence University", 23, "2025-08-03 02:45:00", "2025-08-07 08:45:00", None, "", "active"),
        (352, "Lemma", "Angessa", "Gudissa", "<EMAIL>", "091 1968772", "Ethiopian Civil Service University", 23, "2025-08-03 11:56:00", "2025-08-07 11:56:00", None, "", "active"),
        (353, "Dr. Shimelis", "Admassie", "Zewdie", "<EMAIL>", "091 1718823", "Kotebe University of Education", 23, "2025-08-03 10:50:00", "2025-08-06 04:25:00", None, "", "active"),
        (354, "Meba Tadesse", "Delle", "", "<EMAIL>", "091 1114759", "Addis Ababa University", 27, "2025-08-03 11:38:00", "2025-08-07 11:38:00", None, "", "active"),
        (355, "Dr. Samuel", "Kidane", "Kifle", "<EMAIL>", "+251 911244079", "Addis Ababa University", 2, "2025-08-03 11:27:00", "2025-08-06 11:26:00", None, "", "active"),
        (356, "Kebede", "Gerbi", "Regassa", "<EMAIL>", "+251 932180457", "Ethiopian Defence University", 2, "2025-08-03 02:45:00", "2025-08-07 08:45:00", None, "", "active"),
        (511, "Carly", "Gentry", "Alice Ortiz", "<EMAIL>", "099 0542178", "Anderson Maddox Associates", 6, "2025-08-01 16:28:00", "2025-08-08 23:16:00", "event-registration/profiles/profile_9c38531b-9305-", "Ipsam reiciendis ali", "deleted"),
        (512, "Adem", "Kabo", "Ali", "<EMAIL>", "091 2127133", "Samara university", 23, "2025-08-03 15:39:00", "2025-08-06 15:40:00", "event-registration/profiles/profile_d1974986-bdd2-", "Ok", "active"),
        (513, "Matebu", "Jabessa", "Gezahegn", "<EMAIL>", "094 2632924", "Jimma University", 27, "2025-08-03 16:31:00", "2025-08-07 16:31:00", None, "", "active"),
        (514, "Asmamaw", "Workneh", "Zegeye", "<EMAIL>", "096 0100825", "Debark university", 2, "2025-08-03 17:16:00", "2025-08-07 17:16:00", "event-registration/profiles/profile_5d540ecb-6723-", "", "active"),
        (515, "Dr.Megersa", "Hussen", "Kasim", "<EMAIL>", "099 3440333", "Dire Dawa University", 23, "2025-08-03 00:30:00", "2025-09-06 16:25:00", "event-registration/profiles/profile_797e48f7-ae85-", "", "active"),
        (516, "LAMESGIN", "TIZAZU", "AYELE", "<EMAIL>", "091 2768313", "Dire dawa University", 27, "2025-08-03 14:03:00", "2025-08-06 04:25:00", None, "", "active"),
        (517, "Woldeamlak", "Alemayehu", "Bewket", "<EMAIL>", "091 1608122", "Ambo University", 29, "2025-08-03 13:35:00", "2025-08-07 10:20:00", None, "", "active"),
        (518, "Derbew", "Yohannes", "Belew", "<EMAIL>", "+251 946511627", "Wollega University", 29, "2025-08-03 13:25:00", "2025-08-07 10:20:00", "event-registration/profiles/profile_a5240abe-4745-", "", "active"),
        (519, "Bizunesh", "Borena", "Mideksa", "<EMAIL>", "+251 944741626", "Ambo University", 23, "2025-08-03 08:40:00", "2025-09-07 08:30:00", "event-registration/profiles/profile_58750392-4301-", "", "active"),
        (520, "LIDETU", "GOBENA", "GIZAW", "<EMAIL>", "+251 913307451", "BONGA UNIVERSITY", 27, "2025-08-03 12:15:00", "2025-08-06 10:05:00", None, "Thanks!", "active"),
        (521, "Alemayehu", "Mekonnen", "Debebe", "<EMAIL>", "+251 935998680", "Ethiopian Civil Service University", 23, "2025-08-03 13:00:00", "2025-08-07 10:00:00", None, "None", "active"),
        (522, "Alemayehu", "Mekonnen", "Debebe", "<EMAIL>", "+251 935998680", "Ethiopian Civil Service University", 23, "2025-08-03 13:00:00", "2025-08-07 10:00:00", None, "None", "active"),
        (523, "Asnake", "Ede", "Gudisa", "<EMAIL>", "096 5568511", "Kotebe University of Education", 27, "2025-08-03 12:15:00", "2025-08-06 05:45:00", "event-registration/profiles/profile_c441fb03-985b-", "", "active"),
        (524, "Ochan", "Agwa", "Okello", "<EMAIL>", "091 1766076", "Gambella University", 27, "2025-08-03 15:55:00", "2025-08-06 08:30:00", "event-registration/profiles/profile_0b9af6ae-b73c-", "", "active"),
        (525, "Dr Abdiselam", "Mohamed", "Abdulahi", "<EMAIL>", "091 5213670", "University Of Kabridahar", 23, "2025-08-02 00:30:00", "2025-08-06 00:30:00", "event-registration/profiles/profile_06801326-372a-", "", "active"),
        (526, "Shelleme", "Jiru", "Beyene", "<EMAIL>", "092 6319890", "Samara University", 29, "2025-08-03 08:39:00", "2025-08-07 12:30:00", "event-registration/profiles/profile_a2d5cfb5-7370-", "", "active"),
        (527, "Nega", "Tessemma", "Berhane", "<EMAIL>", "091 8149759", "UoG", 25, "2025-08-01 09:59:00", "2025-08-01 22:02:00", None, "", "active"),
        (528, "Dawit", "Bezabih", "Dargie", "<EMAIL>", "091 8704123", "University of Gondar", 25, "2025-08-03 09:58:00", "2025-08-05 09:58:00", "event-registration/profiles/profile_e16b9a77-c9c7-", "", "active"),
        (529, "Aynishet", "Gebremariam", "Adane", "<EMAIL>", "091 0914356", "University of Gondar", 23, "2025-08-03 07:07:00", "2025-08-07 10:10:00", "event-registration/profiles/profile_913361e0-d834-", "", "active"),
        (530, "Lijalem", "Abate", "Gashaw", "<EMAIL>", "091 1330595", "Uuniversity of Gondar", 23, "2025-08-01 10:07:00", "2025-08-02 10:08:00", None, "", "active"),
        (531, "John", "Firrisa", "Tesfaye", "<EMAIL>", "091 2120903", "Mattu University", 23, "2025-08-03 12:15:00", "2025-08-07 16:30:00", "event-registration/profiles/profile_528c12fa-7164-", "", "active"),
        (532, "Adane", "Tesega", "Kebede", "<EMAIL>", "091 8775976", "UoG", 25, "2025-08-01 09:59:00", "2025-08-01 22:02:00", None, "", "active"),
        (533, "Biniyam", "Jimma", "Ero", "<EMAIL>", "091 0571891", "Education and Training Authority", 1, "2025-08-03 04:40:00", "2025-08-13 04:40:00", None, "", "active"),
        (534, "Serawit", "Melkato", "Handiso", "<EMAIL>", "091 3733068", "MoE", 8, "2025-08-03 15:30:00", "2025-08-07 16:44:00", None, "", "active"),
        (535, "Hana", "Kumera", "", "<EMAIL>", "092 2000651", "Ministry of Education", 28, "2025-08-02 12:00:00", "2025-08-07 10:00:00", None, "", "active"),
        (536, "Dr. Getinet", "Ashenafi", "", "<EMAIL>", "092 4305405", "Debre Berhan Uiversity", 23, "2025-08-03 12:15:00", "2025-08-05 08:30:00", None, "", "active"),
        (537, "KEBEDE", "SHIFERAW", "GIZAW", "<EMAIL>", "091 1461305", "MoE", 8, "2025-08-03 21:30:00", "2025-08-07 16:30:00", "event-registration/profiles/profile_f0aabc44-1117-", "", "active"),
        (538, "Zaid", "Zewde", "Negash", "<EMAIL>", "091 4302163", "Adigrat University", 2, "2025-08-03 11:09:00", "2025-08-05 11:09:00", None, "", "active"),
        (539, "Dunkana", "Kenie", "Negussa", "<EMAIL>", "096 1839375", "Education and Training Authority", 1, "2025-08-03 11:24:00", "2025-08-06 11:24:00", None, "", "active"),
        (540, "Birhan", "Miheretu", "Asmame", "<EMAIL>", "091 1568311", "Wollo University", 23, "2025-08-03 15:55:00", "2025-08-07 08:30:00", "event-registration/profiles/profile_76816072-3684-", "", "active"),
        (541, "Dr Alemu", "Ayano", "Aylate", "<EMAIL>", "091 1763017", "Jinka University", 23, "2025-08-04 12:16:00", "2025-08-06 12:16:00", "event-registration/profiles/profile_d9c2309b-4921-", "", "active"),
        (542, "Fana Hagos", "Berhane", "", "<EMAIL>", "+251 911285461", "Mekelle university", 2, "2025-08-03 15:31:00", "2025-08-06 15:42:00", "event-registration/profiles/profile_12e700ba-01d1-", "", "active"),
        (543, "Ataklti", "Gebrehiwot", "Gebrekidan", "<EMAIL>", "091 1855562", "MoE/HEDS", 28, "2025-08-03 10:22:00", "2025-08-07 13:28:00", None, "", "active"),
        (544, "Asalf", "Wondemgezahu", "Habtegeorgis", "<EMAIL>", "091 1733899", "MoE,HEDS", 28, "2025-08-03 13:33:00", "2025-08-07 13:33:00", "event-registration/profiles/profile_e537f047-2725-", "Special Advisor to the State Minister of HED", "active"),
        (545, "Tesfaye", "Jimma", "Negewo", "<EMAIL>", "+251 911937270", "Ministry of Education", 28, "2025-08-03 13:30:00", "2025-08-07 13:31:00", None, "I am from MoE, Desk Head for Curriculum and Progra...", "active"),
        (546, "Nebiyu", "Teklie", "Yohannes", "<EMAIL>", "091 1820729", "Fana Media corporation", 8, "2025-08-01 13:48:00", "2025-08-07 13:50:00", None, "", "active"),
        (547, "Henoke", "demese", "Seyuom", "<EMAIL>", "091 1144964", "University of Gondar", 25, "2025-08-03 13:46:00", "2025-08-06 13:46:00", "event-registration/profiles/profile_5328dc03-eacf-", "", "active"),
        (548, "Zewdu", "Worku", "Bekele", "<EMAIL>", "091 1903709", "Addis Admass", 24, "2025-08-03 11:00:00", "2025-08-04 10:00:00", None, "", "active"),
        (549, "Lediya", "Negussie", "Abebe", "<EMAIL>", "091 1632358", "Ahadu radio 94.3", 24, "2025-08-05 13:56:00", "2025-08-06 13:56:00", None, "", "active"),
        (550, "Walelign", "Tilahun", "Ayele", "<EMAIL>", "092 2341587", "Ethiopian Press agency", 24, "2025-08-03 13:55:00", "2025-08-03 13:57:00", "event-registration/profiles/profile_50c6ccb9-aa32-", "", "active"),
    ]
    
    created_count = 0
    
    for p_data in latest_participants:
        # Check if participant type exists
        if p_data[7] not in participant_types:
            print(f"Warning: Participant type {p_data[7]} not found, skipping participant {p_data[1]} {p_data[2]}")
            continue
            
        # Check if participant already exists
        if Participant.objects.filter(id=p_data[0]).exists():
            print(f"Participant with ID {p_data[0]} already exists, skipping {p_data[1]} {p_data[2]}")
            continue
            
        # Check if email already exists
        if Participant.objects.filter(email=p_data[4]).exists():
            print(f"Email {p_data[4]} already exists, skipping participant {p_data[1]} {p_data[2]}")
            continue
            
        try:
            participant = Participant(
                first_name=p_data[1],
                last_name=p_data[2],
                middle_name=p_data[3],
                email=p_data[4],
                phone=p_data[5],
                institution_name=p_data[6],
                position="Participant",  # Default position
                participant_type=participant_types[p_data[7]],
                arrival_date=p_data[8],
                departure_date=p_data[9],
                profile_photo=p_data[10],
                remarks=p_data[11] or "",
                status=p_data[12],
                event=event
            )
            participant.save()
            
            # Set the ID after saving to preserve exact IDs
            participant.id = p_data[0]
            participant.save()
            
            created_count += 1
            print(f"Created participant: {p_data[1]} {p_data[2]} (ID: {p_data[0]})")
            
        except Exception as e:
            print(f"Error creating participant {p_data[1]} {p_data[2]}: {str(e)}")
    
    print(f"\nCreated {created_count} latest participants.")

if __name__ == "__main__":
    create_latest_participants()
