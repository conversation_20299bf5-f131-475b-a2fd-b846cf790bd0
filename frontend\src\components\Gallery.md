# Gallery Component

A reusable gallery component that can be used in any section of the application to display images with various customization options.

## Features

- **Flexible Layout**: Configurable grid columns for different screen sizes
- **Date Grouping**: Optional grouping of images by upload date
- **Download Functionality**: Optional download buttons for individual images
- **Featured Badge**: Optional featured image indicators
- **Modal View**: Click to view images in a larger modal
- **Customizable Styling**: Configurable image height, empty states, and more
- **Responsive Design**: Works on all screen sizes

## Props

| Prop | Type | Default | Description |
|------|------|---------|-------------|
| `images` | `EventGallery[]` | Required | Array of images to display |
| `title` | `string` | `undefined` | Optional title for the gallery section |
| `showDateGrouping` | `boolean` | `false` | Group images by upload date |
| `showDownloadButton` | `boolean` | `true` | Show download buttons on images |
| `showFeaturedBadge` | `boolean` | `true` | Show featured badges on images |
| `gridColumns` | `object` | `{lg: 3, md: 4, sm: 6}` | Bootstrap grid columns for different screen sizes |
| `imageHeight` | `string` | `'200px'` | Height of the images in the grid |
| `emptyStateMessage` | `string` | `'No Images Available'` | Message shown when no images |
| `emptyStateIcon` | `string` | `'fas fa-images'` | Icon shown in empty state |
| `downloadPrefix` | `string` | `'image'` | Prefix for downloaded file names |
| `className` | `string` | `''` | Additional CSS classes |

## Usage Examples

### 1. Simple Gallery for Any Section

```tsx
import Gallery from '../components/Gallery';

<Gallery 
  images={sectionImages}
  title="Section Gallery"
  showDateGrouping={false}
  gridColumns={{ lg: 4, md: 6, sm: 12 }}
  downloadPrefix="section"
/>
```

### 2. Event Gallery with Date Grouping

```tsx
<Gallery 
  images={eventImages}
  title="Event Photos"
  showDateGrouping={true}
  downloadPrefix={eventName}
  emptyStateMessage="No event photos available"
  emptyStateIcon="fas fa-camera"
/>
```

### 3. Compact Gallery (No Downloads)

```tsx
<Gallery 
  images={thumbnails}
  showDownloadButton={false}
  showFeaturedBadge={false}
  gridColumns={{ lg: 6, md: 8, sm: 12 }}
  imageHeight="120px"
  className="compact-gallery"
/>
```

### 4. Portfolio Style Gallery

```tsx
<Gallery 
  images={portfolioImages}
  title="Portfolio"
  showDateGrouping={false}
  gridColumns={{ lg: 2, md: 3, sm: 6 }}
  imageHeight="300px"
  downloadPrefix="portfolio"
  emptyStateMessage="No portfolio items"
  emptyStateIcon="fas fa-briefcase"
/>
```

### 5. News/Article Gallery

```tsx
<Gallery 
  images={articleImages}
  title="Article Images"
  showDateGrouping={false}
  showDownloadButton={false}
  gridColumns={{ lg: 3, md: 4, sm: 6 }}
  imageHeight="180px"
  emptyStateMessage="No article images"
  emptyStateIcon="fas fa-newspaper"
/>
```

## Grid Column Configuration

The `gridColumns` prop uses Bootstrap's grid system:

```tsx
gridColumns={{
  lg: 3,  // 4 images per row on large screens (12/3 = 4)
  md: 4,  // 3 images per row on medium screens (12/4 = 3)
  sm: 6   // 2 images per row on small screens (12/6 = 2)
}}
```

Common configurations:
- **4 per row**: `{lg: 3, md: 4, sm: 6}`
- **3 per row**: `{lg: 4, md: 6, sm: 12}`
- **2 per row**: `{lg: 6, md: 6, sm: 12}`
- **6 per row**: `{lg: 2, md: 3, sm: 4}`

## Styling

The component includes built-in hover effects and transitions. You can add custom styles using the `className` prop:

```css
.compact-gallery .gallery-card {
  border: none;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.compact-gallery .gallery-card:hover {
  transform: translateY(-2px);
}
```

## Integration with Different Sections

### News Section
```tsx
<Gallery 
  images={newsImages}
  title="News Gallery"
  showDateGrouping={false}
  showDownloadButton={false}
  gridColumns={{ lg: 4, md: 6, sm: 12 }}
  downloadPrefix="news"
/>
```

### About Section
```tsx
<Gallery 
  images={aboutImages}
  title="About Us"
  showDateGrouping={false}
  gridColumns={{ lg: 3, md: 4, sm: 6 }}
  downloadPrefix="about"
/>
```

### Department Gallery
```tsx
<Gallery 
  images={departmentImages}
  title="Department Photos"
  showDateGrouping={false}
  gridColumns={{ lg: 4, md: 6, sm: 12 }}
  imageHeight="160px"
  downloadPrefix="department"
/>
```

## Notes

- The component expects images to follow the `EventGallery` interface
- Images are displayed using the `getMediaUrl` helper function
- The modal view supports both image viewing and downloading
- All interactive elements are keyboard accessible
- The component is fully responsive and works on all screen sizes
