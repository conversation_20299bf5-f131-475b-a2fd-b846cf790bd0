# Firewall Configuration Guide for Let's Encrypt SSL
## University of Gondar Event Management System

## 🔍 Current DNS Status ✅

Your DNS configuration is **PERFECT**:

### External DNS (From Internet)
```
Name:    event.uog.edu.et
Address: *************  ← Your public IP
```

### Internal DNS (From LAN)
```
Name:    event.uog.edu.et  
Address: ************   ← Your server IP
```

## 🚫 Current Issue

Let's Encrypt cannot reach your server because **port 80 is blocked** from the internet.

## 🔧 Required Firewall Configuration

### Step 1: External Firewall/Router Configuration

You need to configure your **external firewall/router** to allow:

1. **Inbound Port 80 (HTTP)**
   - Source: Any (0.0.0.0/0)
   - Destination: ************* (your public IP)
   - Protocol: TCP
   - Port: 80

2. **Inbound Port 443 (HTTPS)**
   - Source: Any (0.0.0.0/0)
   - Destination: ************* (your public IP)
   - Protocol: TCP
   - Port: 443

### Step 2: Port Forwarding Configuration

Configure **port forwarding** on your router:

1. **HTTP Port Forwarding**
   - External Port: 80
   - Internal IP: ************
   - Internal Port: 80
   - Protocol: TCP

2. **HTTPS Port Forwarding**
   - External Port: 443
   - Internal IP: ************
   - Internal Port: 443
   - Protocol: TCP

## 🧪 Testing External Access

### From Outside Your Network

Ask someone outside your network to test:

```bash
# Test HTTP access
telnet event.uog.edu.et 80

# Or use online tools:
# https://www.yougetsignal.com/tools/open-ports/
# Check if port 80 is open on event.uog.edu.et
```

### Expected Result

When port 80 is properly configured, you should get:
```
Connected to event.uog.edu.et.
Escape character is '^]'.
```

## 🔥 Common Firewall Configurations

### Cisco ASA Example
```
access-list OUTSIDE_IN extended permit tcp any host ************* eq 80
access-list OUTSIDE_IN extended permit tcp any host ************* eq 443

static (inside,outside) tcp ************* 80 ************ 80 netmask ***************
static (inside,outside) tcp ************* 443 ************ 443 netmask ***************
```

### pfSense Example
```
1. Go to Firewall > Rules > WAN
2. Add rule:
   - Action: Pass
   - Interface: WAN
   - Protocol: TCP
   - Source: Any
   - Destination: WAN Address
   - Destination Port: 80, 443

3. Go to Firewall > NAT > Port Forward
4. Add port forward:
   - Interface: WAN
   - Protocol: TCP
   - Destination: WAN Address
   - Destination Port: 80
   - Redirect Target IP: ************
   - Redirect Target Port: 80
```

### Windows Server Firewall (Already Done ✅)
```powershell
# These rules are already created
New-NetFirewallRule -DisplayName "Allow HTTP Inbound" -Direction Inbound -Protocol TCP -LocalPort 80 -Action Allow
New-NetFirewallRule -DisplayName "Allow HTTPS Inbound" -Direction Inbound -Protocol TCP -LocalPort 443 -Action Allow
```

## 🚀 After Firewall Configuration

Once the firewall is configured, test external access:

### Test 1: External Port Check
```powershell
# From outside your network
Test-NetConnection -ComputerName event.uog.edu.et -Port 80
# Should show: TcpTestSucceeded: True
```

### Test 2: HTTP Response
```bash
# From outside your network
curl -I http://event.uog.edu.et
# Should return HTTP headers
```

### Test 3: Let's Encrypt SSL Setup
```powershell
# Run this after firewall configuration
.\setup-ssl-direct.ps1
```

## 🔍 Troubleshooting

### If Port 80 Still Blocked

1. **Check ISP Blocking**
   - Some ISPs block port 80 on residential connections
   - Contact your ISP to verify

2. **Check Multiple Firewall Layers**
   - Router firewall
   - Network firewall
   - Server firewall (already configured)

3. **Verify Port Forwarding**
   - Ensure internal IP is correct: ************
   - Ensure ports match: 80 → 80, 443 → 443

### Alternative Solutions

If port 80 cannot be opened:

1. **Use DNS Challenge**
   ```bash
   docker-compose -f docker-compose.prod.yml run --rm certbot certonly \
     --manual \
     --preferred-challenges dns \
     --email <EMAIL> \
     --agree-tos \
     --no-eff-email \
     -d event.uog.edu.et \
     -d www.event.uog.edu.et
   ```

2. **Use CloudFlare SSL**
   - Point DNS to CloudFlare
   - Use CloudFlare's SSL certificates
   - Configure origin certificates

3. **Use Alternative Port**
   - Configure on port 8080 or 8443
   - Update DNS to point to alternative ports

## 📋 Configuration Checklist

- [ ] **External Firewall**: Allow inbound ports 80/443
- [ ] **Port Forwarding**: 80/443 → ************:80/443
- [ ] **ISP Check**: Verify ISP doesn't block port 80
- [ ] **External Test**: Port 80 accessible from internet
- [ ] **DNS Working**: Both internal and external ✅
- [ ] **Server Ready**: Application running on HTTP ✅
- [ ] **Windows Firewall**: HTTP/HTTPS rules enabled ✅

## 📞 Next Steps

1. **Contact Network Administrator**
   - Show them this guide
   - Request firewall configuration for ports 80/443
   - Configure port forwarding to ************

2. **Test External Access**
   - Use external network to test connectivity
   - Verify ports 80/443 are reachable

3. **Run SSL Setup**
   - Execute: `.\setup-ssl-direct.ps1`
   - Let's Encrypt will validate domain ownership
   - SSL certificates will be generated automatically

## 🎯 Expected Timeline

- **Firewall Configuration**: 15-30 minutes
- **Testing**: 5-10 minutes  
- **SSL Setup**: 5-10 minutes
- **Total**: 30-60 minutes

---

**Current Status**: DNS ✅, Server ✅, Application ✅
**Blocking Issue**: External firewall configuration
**Next Action**: Configure external firewall for ports 80/443
