import axios from 'axios';
import { Driver, CreateDriverData, UpdateDriverData, FocalPerson, Hotel, ParticipantType, PlaceToVisit, Assignment } from '../types/driver';

const API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:8000/api';

const api = axios.create({
  baseURL: API_BASE_URL,
});

// Add auth token to requests
api.interceptors.request.use((config) => {
  const token = localStorage.getItem('token');
  if (token) {
    config.headers.Authorization = `Bearer ${token}`;
  }
  return config;
});

export const driverService = {
  // Driver CRUD operations
  async getDrivers(): Promise<Driver[]> {
    const response = await api.get('/drivers/');
    return response.data.results || response.data;
  },

  async getDriver(id: number): Promise<Driver> {
    const response = await api.get(`/drivers/${id}/`);
    return response.data;
  },

  async createDriver(data: CreateDriverData): Promise<Driver> {
    const formData = new FormData();
    
    Object.entries(data).forEach(([key, value]) => {
      if (value !== undefined && value !== null) {
        if (key === 'photo' && value instanceof File) {
          formData.append(key, value);
        } else {
          formData.append(key, value.toString());
        }
      }
    });

    const response = await api.post('/drivers/', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
    return response.data;
  },

  async updateDriver(data: UpdateDriverData): Promise<Driver> {
    const { id, ...updateData } = data;
    const formData = new FormData();
    
    Object.entries(updateData).forEach(([key, value]) => {
      if (value !== undefined && value !== null) {
        if (key === 'photo' && value instanceof File) {
          formData.append(key, value);
        } else {
          formData.append(key, value.toString());
        }
      }
    });

    const response = await api.patch(`/drivers/${id}/`, formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
    return response.data;
  },

  async deleteDriver(id: number): Promise<void> {
    await api.delete(`/drivers/${id}/`);
  },

  // Bulk create drivers
  async bulkCreateDrivers(drivers: CreateDriverData[]): Promise<Driver[]> {
    const response = await api.post('/drivers/bulk/', { drivers });
    return response.data;
  },

  // Focal Person operations
  async getFocalPersons(): Promise<FocalPerson[]> {
    const response = await api.get('/focal-persons/');
    return response.data.results || response.data;
  },

  async createFocalPerson(data: Partial<FocalPerson>): Promise<FocalPerson> {
    const response = await api.post('/focal-persons/', data);
    return response.data;
  },

  // Hotel operations
  async getHotels(): Promise<Hotel[]> {
    const response = await api.get('/hotels/');
    return response.data.results || response.data;
  },

  async createHotel(data: Partial<Hotel>): Promise<Hotel> {
    const response = await api.post('/hotels/', data);
    return response.data;
  },

  // Participant Type operations
  async getParticipantTypes(): Promise<ParticipantType[]> {
    const response = await api.get('/participant-types/');
    return response.data.results || response.data;
  },

  async createParticipantType(data: Partial<ParticipantType>): Promise<ParticipantType> {
    const response = await api.post('/participant-types/', data);
    return response.data;
  },

  // Place to Visit operations
  async getPlacesToVisit(): Promise<PlaceToVisit[]> {
    const response = await api.get('/places-to-visit/');
    return response.data.results || response.data;
  },

  async createPlaceToVisit(data: Partial<PlaceToVisit>): Promise<PlaceToVisit> {
    const response = await api.post('/places-to-visit/', data);
    return response.data;
  },

  // Assignment operations
  async getAssignments(): Promise<Assignment[]> {
    const response = await api.get('/assignments/');
    return response.data.results || response.data;
  },

  async createAssignment(data: Partial<Assignment>): Promise<Assignment> {
    const response = await api.post('/assignments/', data);
    return response.data;
  },

  async finalizeAssignment(id: number): Promise<Assignment> {
    const response = await api.patch(`/assignments/${id}/finalize/`);
    return response.data;
  },
};
