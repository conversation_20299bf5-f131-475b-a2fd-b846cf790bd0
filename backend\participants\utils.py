from django.core.mail import EmailMultiAlternatives
from django.template.loader import render_to_string
from django.conf import settings
from django.utils.html import strip_tags
import os


def send_participant_details_email(participant):
    """
    Send detailed email to participant with event information, badge, hotel and driver details
    """
    
    # Email subject
    subject = f'Event Details & Badge - {participant.event.name}'
    
    # Email context
    context = {
        'participant': participant,
        'event': participant.event,
        'hotel': participant.assigned_hotel,
        'room': participant.assigned_room,
        'driver': participant.assigned_driver,
        'participant_type': participant.participant_type,
    }
    
    # Render HTML email template
    html_content = render_to_string('emails/participant_details.html', context)
    text_content = strip_tags(html_content)
    
    # Create email
    email = EmailMultiAlternatives(
        subject=subject,
        body=text_content,
        from_email=settings.DEFAULT_FROM_EMAIL,
        to=[participant.email]
    )
    
    # Attach HTML version
    email.attach_alternative(html_content, "text/html")
    
    # Attach badge if available
    if participant.badge_file and os.path.exists(participant.badge_file.path):
        email.attach_file(participant.badge_file.path)
    
    # Send email
    email.send()
    
    return True


def generate_participant_qr_data(participant):
    """
    Generate QR code data for participant
    """
    return {
        'uuid': str(participant.uuid),
        'name': participant.full_name,
        'email': participant.email,
        'event': participant.event.name,
        'type': participant.participant_type.name
    }


def bulk_import_participants_from_csv(csv_file, event, default_participant_type):
    """
    Bulk import participants from CSV file
    """
    import csv
    import io
    from .models import Participant, ParticipantType
    from django.core.files.uploadedfile import InMemoryUploadedFile
    
    # Read CSV content
    if isinstance(csv_file, InMemoryUploadedFile):
        csv_content = csv_file.read().decode('utf-8')
    else:
        csv_content = csv_file
    
    csv_reader = csv.DictReader(io.StringIO(csv_content))
    
    created_participants = []
    errors = []
    
    for row_num, row in enumerate(csv_reader, start=2):
        try:
            # Get or create participant type
            participant_type_name = row.get('participant_type', '').strip()
            if participant_type_name:
                participant_type, _ = ParticipantType.objects.get_or_create(
                    name=participant_type_name,
                    defaults={'color': '#007bff'}
                )
            else:
                participant_type = default_participant_type
            
            # Create participant
            participant = Participant.objects.create(
                first_name=row['first_name'].strip(),
                last_name=row['last_name'].strip(),
                middle_name=row.get('middle_name', '').strip(),
                email=row['email'].strip(),
                phone=row['phone'].strip(),
                institution_name=row['institution_name'].strip(),
                position=row['position'].strip(),
                event=event,
                participant_type=participant_type,
                arrival_date=row['arrival_date'],
                departure_date=row['departure_date'],
                remarks=row.get('remarks', '').strip(),
                status='pending'
            )
            
            created_participants.append(participant)
            
        except Exception as e:
            errors.append(f"Row {row_num}: {str(e)}")
    
    return created_participants, errors


def bulk_export_participants_to_csv(participants):
    """
    Export participants to CSV format
    """
    import csv
    import io
    
    output = io.StringIO()
    writer = csv.writer(output)
    
    # Write header
    writer.writerow([
        'first_name', 'last_name', 'middle_name', 'email', 'phone',
        'institution_name', 'position', 'participant_type', 'event',
        'arrival_date', 'departure_date', 'status', 'hotel', 'room',
        'driver', 'badge_generated', 'email_sent', 'remarks'
    ])
    
    # Write participant data
    for participant in participants:
        writer.writerow([
            participant.first_name,
            participant.last_name,
            participant.middle_name,
            participant.email,
            participant.phone,
            participant.institution_name,
            participant.position,
            participant.participant_type.name,
            participant.event.name,
            participant.arrival_date.strftime('%Y-%m-%d %H:%M:%S'),
            participant.departure_date.strftime('%Y-%m-%d %H:%M:%S'),
            participant.get_status_display(),
            participant.assigned_hotel.name if participant.assigned_hotel else '',
            participant.assigned_room.room_number if participant.assigned_room else '',
            participant.assigned_driver.name if participant.assigned_driver else '',
            'Yes' if participant.badge_generated else 'No',
            'Yes' if participant.details_email_sent else 'No',
            participant.remarks
        ])
    
    return output.getvalue()
