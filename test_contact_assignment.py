#!/usr/bin/env python3
"""
Test script to verify contact person assignment functionality
"""

import requests
import json

# Configuration
BASE_URL = "http://localhost:8000/api"

def test_contact_person_assignment():
    """Test the contact person assignment endpoint"""
    
    print("🧪 Testing Contact Person Assignment Functionality")
    print("=" * 60)
    
    # Test 1: Check if contact persons endpoint exists
    print("\n1. Testing Contact Persons API endpoint...")
    try:
        response = requests.get(f"{BASE_URL}/contact-persons/")
        if response.status_code == 200:
            contact_persons = response.json()
            print(f"✅ Contact Persons API working - Found {len(contact_persons.get('results', contact_persons))} contact persons")
        else:
            print(f"❌ Contact Persons API failed - Status: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Contact Persons API error: {e}")
        return False
    
    # Test 2: Check if participants endpoint exists
    print("\n2. Testing Participants API endpoint...")
    try:
        response = requests.get(f"{BASE_URL}/participants/")
        if response.status_code == 200:
            participants = response.json()
            participants_data = participants.get('results', participants)
            print(f"✅ Participants API working - Found {len(participants_data)} participants")
            
            if len(participants_data) > 0:
                test_participant = participants_data[0]
                participant_id = test_participant['id']
                print(f"📝 Using participant: {test_participant.get('full_name', 'Unknown')} (ID: {participant_id})")
                
                # Test 3: Test contact person assignment
                print(f"\n3. Testing contact person assignment for participant {participant_id}...")
                
                # Try to assign contact person (using ID 1 as test)
                assignment_data = {"contact_person_id": 1}
                response = requests.post(
                    f"{BASE_URL}/participants/{participant_id}/assign_contact_person/",
                    json=assignment_data
                )
                
                if response.status_code == 200:
                    print("✅ Contact person assignment endpoint working!")
                    result = response.json()
                    print(f"📋 Assignment result: {json.dumps(result.get('assigned_contact_person_name', 'No name'), indent=2)}")
                else:
                    print(f"❌ Contact person assignment failed - Status: {response.status_code}")
                    print(f"Response: {response.text}")
                
                # Test 4: Test contact person removal
                print(f"\n4. Testing contact person removal for participant {participant_id}...")
                removal_data = {"contact_person_id": 0}
                response = requests.post(
                    f"{BASE_URL}/participants/{participant_id}/assign_contact_person/",
                    json=removal_data
                )
                
                if response.status_code == 200:
                    print("✅ Contact person removal endpoint working!")
                else:
                    print(f"❌ Contact person removal failed - Status: {response.status_code}")
                    
            else:
                print("⚠️  No participants found to test assignment")
                
        else:
            print(f"❌ Participants API failed - Status: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Participants API error: {e}")
        return False
    
    print("\n" + "=" * 60)
    print("🎉 Contact Person Assignment Test Complete!")
    return True

if __name__ == "__main__":
    test_contact_person_assignment()
