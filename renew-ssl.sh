#!/bin/bash

# University of Gondar Event Management System
# SSL Certificate Renewal Script
# This script renews Let's Encrypt SSL certificates

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}🔐 University of Gondar Event Management System${NC}"
echo -e "${BLUE}🔄 SSL Certificate Renewal${NC}"
echo -e "${BLUE}================================${NC}"

# Check if certificates exist
DOMAIN="event.uog.edu.et"
if [ ! -d "./certbot/conf/live/$DOMAIN" ]; then
    echo -e "${RED}❌ Error: No SSL certificates found for $DOMAIN${NC}"
    echo -e "${RED}   Please run ./init-letsencrypt.sh first${NC}"
    exit 1
fi

# Show current certificate status
echo -e "\n${YELLOW}📋 Current certificate status:${NC}"
docker-compose -f docker-compose.prod.yml run --rm certbot certificates

# Attempt renewal
echo -e "\n${YELLOW}🔄 Attempting certificate renewal...${NC}"
docker-compose -f docker-compose.prod.yml run --rm certbot renew

# Reload nginx to use new certificates
echo -e "\n${YELLOW}🔄 Reloading nginx configuration...${NC}"
docker-compose -f docker-compose.prod.yml exec nginx nginx -s reload

# Test HTTPS connection
echo -e "\n${YELLOW}🧪 Testing HTTPS connection...${NC}"
if curl -f -s https://$DOMAIN/health/ > /dev/null; then
    echo -e "${GREEN}✅ HTTPS is working correctly after renewal!${NC}"
else
    echo -e "${YELLOW}⚠️  HTTPS test failed, please check manually${NC}"
fi

echo -e "\n${GREEN}🎉 SSL certificate renewal completed!${NC}"
echo -e "${GREEN}🌐 Your site: https://$DOMAIN${NC}"
