import React, { useState } from 'react';
import { <PERSON>, <PERSON>, <PERSON>, Badge, <PERSON>lap<PERSON>, Button } from 'react-bootstrap';
import { EventSchedule } from '../services/api';

interface DailyScheduleProps {
  schedules: EventSchedule[];
  eventName: string;
}

const DailySchedule: React.FC<DailyScheduleProps> = ({ schedules, eventName }) => {
  // State for managing collapsed/expanded days
  const [expandedDays, setExpandedDays] = useState<{ [key: string]: boolean }>({});

  // Group schedules by date
  const groupSchedulesByDate = (schedules: EventSchedule[]) => {
    const grouped: { [key: string]: EventSchedule[] } = {};

    schedules.forEach(schedule => {
      // Safely handle date parsing
      const startTime = schedule.start_time;
      if (!startTime) return;

      const date = new Date(startTime);
      if (isNaN(date.getTime())) return; // Skip invalid dates

      const dateKey = date.toISOString().split('T')[0]; // Use YYYY-MM-DD format
      if (!grouped[dateKey]) {
        grouped[dateKey] = [];
      }
      grouped[dateKey].push(schedule);
    });

    return grouped;
  };

  const formatTime = (timeString: string) => {
    if (!timeString) return 'Invalid Time';

    try {
      // Handle time format like "14:30" or "14:30:00"
      if (timeString && timeString.includes(':')) {
        // If it's just time format (HH:MM or HH:MM:SS), create a date object for today
        const timeParts = timeString.split(':');
        if (timeParts.length >= 2) {
          const hours = parseInt(timeParts[0]);
          const minutes = parseInt(timeParts[1]);

          if (!isNaN(hours) && !isNaN(minutes) && hours >= 0 && hours <= 23 && minutes >= 0 && minutes <= 59) {
            // Create a date object for formatting
            const date = new Date();
            date.setHours(hours, minutes, 0, 0);
            return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
          }
        }
      }

      // Fallback: try to parse as full date string
      const date = new Date(timeString);
      if (isNaN(date.getTime())) {
        return 'Invalid Time';
      }
      return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
    } catch (error) {
      console.error('Error formatting time:', timeString, error);
      return 'Invalid Time';
    }
  };

  const formatDuration = (startTime: string, endTime: string) => {
    if (!startTime || !endTime) return 'N/A';

    try {
      let start: Date, end: Date;

      // Helper function to parse time strings
      const parseTime = (timeStr: string): Date => {
        if (timeStr.includes(':') && !timeStr.includes('T')) {
          // Handle time format like "14:30" or "14:30:00"
          const timeParts = timeStr.split(':');
          if (timeParts.length >= 2) {
            const hours = parseInt(timeParts[0]);
            const minutes = parseInt(timeParts[1]);
            if (!isNaN(hours) && !isNaN(minutes)) {
              const date = new Date();
              date.setHours(hours, minutes, 0, 0);
              return date;
            }
          }
        }
        // Fallback to full date parsing
        return new Date(timeStr);
      };

      start = parseTime(startTime);
      end = parseTime(endTime);

      if (isNaN(start.getTime()) || isNaN(end.getTime())) return 'N/A';

      const durationMs = end.getTime() - start.getTime();
      const durationMinutes = Math.floor(durationMs / (1000 * 60));

      if (durationMinutes < 0) return 'N/A';

      if (durationMinutes < 60) {
        return `${durationMinutes} min`;
      } else {
        const hours = Math.floor(durationMinutes / 60);
        const minutes = durationMinutes % 60;
        return minutes > 0 ? `${hours}h ${minutes}m` : `${hours}h`;
      }
    } catch (error) {
      console.error('Error calculating duration:', startTime, endTime, error);
      return 'N/A';
    }
  };

  const getScheduleIcon = (schedule: EventSchedule) => {
    if (schedule.is_break) {
      return 'fas fa-coffee';
    } else if (schedule.title.toLowerCase().includes('keynote')) {
      return 'fas fa-microphone';
    } else if (schedule.title.toLowerCase().includes('panel')) {
      return 'fas fa-users';
    } else if (schedule.title.toLowerCase().includes('workshop')) {
      return 'fas fa-tools';
    } else if (schedule.title.toLowerCase().includes('lunch') || schedule.title.toLowerCase().includes('dinner')) {
      return 'fas fa-utensils';
    } else {
      return 'fas fa-presentation';
    }
  };

  const getScheduleColor = (schedule: EventSchedule) => {
    if (schedule.is_break) {
      return 'warning';
    } else if (schedule.title.toLowerCase().includes('keynote')) {
      return 'primary';
    } else if (schedule.title.toLowerCase().includes('panel')) {
      return 'info';
    } else if (schedule.title.toLowerCase().includes('workshop')) {
      return 'success';
    } else {
      return 'secondary';
    }
  };

  const groupedSchedules = groupSchedulesByDate(schedules);

  // Toggle day expansion
  const toggleDay = (dateKey: string) => {
    setExpandedDays(prev => ({
      ...prev,
      [dateKey]: !prev[dateKey]
    }));
  };

  // Initialize all days as expanded on first load
  React.useEffect(() => {
    const initialState: { [key: string]: boolean } = {};
    Object.keys(groupedSchedules).forEach(dateKey => {
      initialState[dateKey] = true; // Start with all days expanded
    });
    setExpandedDays(initialState);
  }, [schedules]);

  if (schedules.length === 0) {
    return (
      <div className="text-center py-5">
        <i className="fas fa-calendar-times text-muted" style={{ fontSize: '4rem' }}></i>
        <h4 className="mt-3 text-muted">No Schedule Available</h4>
        <p className="text-muted">The event schedule has not been published yet.</p>
      </div>
    );
  }

  return (
    <div>
      {Object.entries(groupedSchedules).map(([dateKey, daySchedules]) => {
        // Parse the date safely
        const date = new Date(dateKey + 'T00:00:00'); // Add time to ensure proper parsing
        const isValidDate = !isNaN(date.getTime());

        return (
          <div key={dateKey} className="mb-5">
            <Card className="border-0 shadow-sm">
              <Card.Header
                className="bg-primary text-white cursor-pointer"
                onClick={() => toggleDay(dateKey)}
                style={{ cursor: 'pointer' }}
              >
                <div className="d-flex justify-content-between align-items-center">
                  <h4 className="mb-0 text-white">
                    <i className="fas fa-calendar-day me-2"></i>
                    {isValidDate ? date.toLocaleDateString('en-US', {
                      weekday: 'long',
                      year: 'numeric',
                      month: 'long',
                      day: 'numeric'
                    }) : 'Invalid Date'}
                  </h4>
                  <div className="d-flex align-items-center gap-3">
                    <Badge bg="light" text="dark" className="fs-6">
                      {daySchedules.length} sessions
                    </Badge>
                    <Button
                      variant="link"
                      className="text-white p-0 border-0"
                      onClick={(e) => {
                        e.stopPropagation();
                        toggleDay(dateKey);
                      }}
                    >
                      <i className={`fas fa-chevron-${expandedDays[dateKey] ? 'up' : 'down'}`}></i>
                    </Button>
                  </div>
                </div>
              </Card.Header>

              <Collapse in={expandedDays[dateKey]}>
                <div>
                  <Card.Body className="p-4">
                    <div className="timeline-container">
                      {daySchedules.map((schedule, index) => (
              <div key={schedule.id} className="timeline-item mb-4">
                <Row className="align-items-center">
                  <Col md={2} className="text-center timeline-time">
                    <div className="time-block p-3 rounded bg-light border">
                      <div className="h5 text-primary mb-1 fw-bold">
                        {formatTime(schedule.start_time)}
                      </div>
                      <small className="text-muted d-block">
                        {formatTime(schedule.end_time)}
                      </small>
                      <Badge bg="secondary" className="small mt-2">
                        {formatDuration(schedule.start_time, schedule.end_time)}
                      </Badge>
                    </div>
                  </Col>
                  
                  <Col md={10}>
                    <Card className={`shadow-sm border-start border-${getScheduleColor(schedule)} border-4 schedule-card`}>
                      <Card.Body className="p-4">
                        <div className="d-flex align-items-start justify-content-between mb-3">
                          <div className="d-flex align-items-center">
                            <div className={`text-${getScheduleColor(schedule)} me-3`}>
                              <i className={`${getScheduleIcon(schedule)} fa-2x`}></i>
                            </div>
                            <div>
                              <h5 className="mb-1 fw-bold">{schedule.title}</h5>
                              {schedule.is_break && (
                                <Badge bg="warning" className="me-2">
                                  <i className="fas fa-coffee me-1"></i>
                                  Break
                                </Badge>
                              )}
                            </div>
                          </div>
                        </div>
                        
                        {schedule.description && (
                          <p className="text-muted mb-3 fs-6">{schedule.description}</p>
                        )}
                        
                        <div className="row g-3">
                          {schedule.location && (
                            <div className="col-md-6">
                              <div className="d-flex align-items-center text-muted">
                                <i className="fas fa-map-marker-alt me-2 text-primary"></i>
                                <span className="fw-medium">{schedule.location}</span>
                              </div>
                            </div>
                          )}
                          {schedule.speaker && (
                            <div className="col-md-6">
                              <div className="d-flex align-items-center text-muted">
                                <i className="fas fa-user me-2 text-primary"></i>
                                <span className="fw-medium">{schedule.speaker}</span>
                              </div>
                            </div>
                          )}
                        </div>
                        
                        {!schedule.is_break && (
                          <div className="mt-3 pt-3 border-top">
                            <div className="d-flex justify-content-between align-items-center">
                              <small className="text-muted">
                                <i className="fas fa-clock me-1"></i>
                                Duration: {formatDuration(schedule.start_time, schedule.end_time)}
                              </small>
                              <div className="d-flex gap-2">
                                <Badge bg="outline-primary" className="px-3 py-2">
                                  <i className="fas fa-calendar-check me-1"></i>
                                  Scheduled
                                </Badge>
                              </div>
                            </div>
                          </div>
                        )}
                      </Card.Body>
                    </Card>
                  </Col>
                </Row>
                
                {/* Timeline connector */}
                {index < daySchedules.length - 1 && (
                  <div className="timeline-connector">
                    <div className="connector-line"></div>
                  </div>
                      )}
                    </div>
                  ))}
                    </div>
                  </Card.Body>
                </div>
              </Collapse>
            </Card>
          </div>
        );
      })}

      {/* Custom Styles */}
      <style>{`
        .timeline-container {
          position: relative;
        }
        
        .timeline-item {
          position: relative;
        }
        
        .timeline-time {
          position: relative;
          z-index: 2;
        }
        
        .schedule-card {
          transition: transform 0.2s ease, box-shadow 0.2s ease;
        }
        
        .schedule-card:hover {
          transform: translateX(5px);
          box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1) !important;
        }
        
        .timeline-connector {
          position: absolute;
          left: 50%;
          transform: translateX(-50%);
          width: 2px;
          height: 30px;
          z-index: 1;
        }
        
        .connector-line {
          width: 100%;
          height: 100%;
          background: linear-gradient(to bottom, #dee2e6, transparent);
        }
        
        @media (max-width: 768px) {
          .timeline-time {
            margin-bottom: 1rem;
          }
          
          .schedule-card:hover {
            transform: none;
          }
        }
        
        .time-block {
          background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%) !important;
          border: 2px solid #dee2e6 !important;
        }
        
        .border-primary {
          border-color: #0d6efd !important;
        }
        
        .border-warning {
          border-color: #ffc107 !important;
        }
        
        .border-info {
          border-color: #0dcaf0 !important;
        }
        
        .border-success {
          border-color: #198754 !important;
        }
        
        .border-secondary {
          border-color: #6c757d !important;
        }
      `}</style>
    </div>
  );
};

export default DailySchedule;
