from django.core.management.base import BaseCommand
from events.models import EmailTemplate


class Command(BaseCommand):
    help = 'Fix email templates with proper HTML'

    def handle(self, *args, **options):
        self.stdout.write("Fixing email templates...")
        
        # Fix registration confirmation template
        try:
            reg_template = EmailTemplate.objects.get(template_type='registration_confirmation')
            reg_template.html_content = """
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>Registration Confirmed</title>
</head>
<body style="font-family: Arial, sans-serif; margin: 0; padding: 20px; background-color: #f5f5f5;">
    <div style="max-width: 600px; margin: 0 auto; background-color: white; border-radius: 10px; overflow: hidden; box-shadow: 0 2px 10px rgba(0,0,0,0.1);">
        
        <!-- Header -->
        <div style="background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%); color: white; padding: 30px; text-align: center;">
            <h1 style="margin: 0; font-size: 28px;">🎓 University of Gondar</h1>
            <h2 style="margin: 10px 0 0 0; font-size: 24px;">Registration Confirmed!</h2>
            <div style="background: #4CAF50; color: white; padding: 8px 16px; border-radius: 20px; font-size: 14px; font-weight: bold; display: inline-block; margin-top: 15px;">
                ✓ Successfully Registered
            </div>
        </div>
        
        <!-- Content -->
        <div style="padding: 30px;">
            <h2 style="color: #333; margin-top: 0;">Welcome {participant_name}!</h2>
            <p style="color: #666; line-height: 1.6; font-size: 16px;">
                Thank you for registering for <strong style="color: #1e3c72;">{event_name}</strong>. 
                Your registration has been successfully submitted and is currently under review.
            </p>
            
            <!-- Event Info Box -->
            <div style="background: #e8f4fd; border-left: 4px solid #2196F3; padding: 20px; margin: 20px 0; border-radius: 5px;">
                <h3 style="color: #1e3c72; margin-top: 0;">📅 Event Information</h3>
                <table style="width: 100%; border-collapse: collapse;">
                    <tr>
                        <td style="padding: 8px 0; font-weight: bold; color: #1e3c72; width: 30%;">Event Name:</td>
                        <td style="padding: 8px 0; color: #333;">{event_name}</td>
                    </tr>
                    <tr>
                        <td style="padding: 8px 0; font-weight: bold; color: #1e3c72;">Date:</td>
                        <td style="padding: 8px 0; color: #333;">{event_date}</td>
                    </tr>
                    <tr>
                        <td style="padding: 8px 0; font-weight: bold; color: #1e3c72;">Location:</td>
                        <td style="padding: 8px 0; color: #333;">{event_location}</td>
                    </tr>
                    <tr>
                        <td style="padding: 8px 0; font-weight: bold; color: #1e3c72;">Registration Date:</td>
                        <td style="padding: 8px 0; color: #333;">{registration_date}</td>
                    </tr>
                </table>
            </div>
            
            <!-- What's Next Box -->
            <div style="background: #fff3e0; border-left: 4px solid #ff9800; padding: 20px; margin: 20px 0; border-radius: 5px;">
                <h3 style="color: #e65100; margin-top: 0;">📋 What Happens Next?</h3>
                <ol style="color: #666; line-height: 1.6;">
                    <li><strong>Review Process:</strong> Our team will review your registration within 24-48 hours</li>
                    <li><strong>Approval Notification:</strong> You will receive an email once your registration is approved</li>
                    <li><strong>Event Details:</strong> Upon approval, you will receive detailed event information including:
                        <ul style="margin-top: 10px;">
                            <li>Complete event schedule and agenda</li>
                            <li>Hotel and accommodation details (if applicable)</li>
                            <li>Transportation arrangements</li>
                            <li>Contact person information</li>
                            <li>Your personalized event badge</li>
                        </ul>
                    </li>
                </ol>
            </div>
            
            <!-- Contact Info -->
            <div style="background: #e8f5e8; border-left: 4px solid #4CAF50; padding: 20px; margin: 20px 0; border-radius: 5px;">
                <h3 style="color: #2e7d32; margin-top: 0;">📞 Need Help?</h3>
                <p style="color: #666; margin-bottom: 15px;">If you have any questions or need to make changes to your registration, please contact us:</p>
                <ul style="color: #666; line-height: 1.6;">
                    <li><strong>Email:</strong> <EMAIL></li>
                    <li><strong>Phone:</strong> +251-58-114-0481</li>
                    <li><strong>Office Hours:</strong> Monday - Friday, 8:00 AM - 5:00 PM</li>
                </ul>
            </div>
        </div>
        
        <!-- Footer -->
        <div style="background: #f8f9fa; padding: 20px; text-align: center; color: #666; font-size: 14px;">
            <p style="margin: 0;"><strong>University of Gondar Events</strong></p>
            <p style="margin: 5px 0;">This is an automated message. Please do not reply to this email.</p>
            <p style="margin: 5px 0;">&copy; {current_year} University of Gondar. All rights reserved.</p>
        </div>
    </div>
</body>
</html>
"""
            reg_template.save()
            self.stdout.write("✓ Fixed registration confirmation template")
        except Exception as e:
            self.stdout.write(f"Error fixing registration template: {e}")
        
        # Fix event details template
        try:
            event_template = EmailTemplate.objects.get(template_type='event_details')
            event_template.html_content = """
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>Event Details - Approved</title>
</head>
<body style="font-family: Arial, sans-serif; margin: 0; padding: 20px; background-color: #f5f5f5;">
    <div style="max-width: 700px; margin: 0 auto; background-color: white; border-radius: 10px; overflow: hidden; box-shadow: 0 2px 10px rgba(0,0,0,0.1);">
        
        <!-- Header -->
        <div style="background: linear-gradient(135deg, #4CAF50 0%, #45a049 100%); color: white; padding: 30px; text-align: center;">
            <h1 style="margin: 0; font-size: 28px;">🎓 University of Gondar</h1>
            <h2 style="margin: 10px 0 0 0; font-size: 24px;">Registration Approved!</h2>
            <div style="background: #2196F3; color: white; padding: 10px 20px; border-radius: 25px; font-size: 16px; font-weight: bold; display: inline-block; margin-top: 15px;">
                ✅ Welcome to {event_name}
            </div>
        </div>
        
        <!-- Content -->
        <div style="padding: 30px;">
            <h2 style="color: #333; margin-top: 0;">Congratulations {participant_name}!</h2>
            <p style="color: #666; line-height: 1.6; font-size: 16px;">
                We are pleased to inform you that your registration for <strong style="color: #4CAF50;">{event_name}</strong> 
                has been approved. Below you will find all the essential information for your participation.
            </p>
            
            <!-- Event Information -->
            <div style="background: white; margin: 20px 0; padding: 25px; border-radius: 10px; box-shadow: 0 2px 8px rgba(0,0,0,0.1); border-top: 4px solid #2196F3;">
                <h3 style="color: #2196F3; margin-top: 0; font-size: 18px;">📅 Event Information</h3>
                <table style="width: 100%; border-collapse: collapse;">
                    <tr>
                        <td style="padding: 10px 0; font-weight: bold; color: #1e3c72; width: 30%;">Event Name:</td>
                        <td style="padding: 10px 0; color: #333;">{event_name}</td>
                    </tr>
                    <tr>
                        <td style="padding: 10px 0; font-weight: bold; color: #1e3c72;">Start Date & Time:</td>
                        <td style="padding: 10px 0; color: #333;">{event_start_date}</td>
                    </tr>
                    <tr>
                        <td style="padding: 10px 0; font-weight: bold; color: #1e3c72;">End Date & Time:</td>
                        <td style="padding: 10px 0; color: #333;">{event_end_date}</td>
                    </tr>
                    <tr>
                        <td style="padding: 10px 0; font-weight: bold; color: #1e3c72;">Location:</td>
                        <td style="padding: 10px 0; color: #333;">{event_location}</td>
                    </tr>
                </table>
                <div style="margin-top: 15px; padding-top: 15px; border-top: 1px solid #eee;">
                    <strong style="color: #1e3c72;">Event Description:</strong>
                    <p style="color: #666; margin: 5px 0 0 0;">{event_description}</p>
                </div>
            </div>
            
            {hotel_section}
            {driver_section}
            {contact_person_section}
            
            <!-- Badge Information -->
            <div style="background: linear-gradient(135deg, #fce4ec 0%, #f8bbd9 100%); padding: 20px; border-radius: 10px; border: 2px solid #e91e63; text-align: center; margin: 20px 0;">
                <h3 style="color: #c2185b; margin-top: 0;">🏷️ Your Event Badge</h3>
                <p style="color: #333; font-weight: bold;">Your personalized event badge is attached to this email!</p>
                <ul style="text-align: left; display: inline-block; color: #666;">
                    <li>Print your badge on quality paper (preferably cardstock)</li>
                    <li>Bring your printed badge to the event</li>
                    <li>Keep your badge visible throughout the event</li>
                    <li>Your badge contains a QR code for quick check-in</li>
                    <li>Lost badges can be reprinted at the registration desk</li>
                </ul>
            </div>
            
            <!-- Important Information -->
            <div style="background: white; margin: 20px 0; padding: 25px; border-radius: 10px; box-shadow: 0 2px 8px rgba(0,0,0,0.1); border-top: 4px solid #ff9800;">
                <h3 style="color: #ff9800; margin-top: 0; font-size: 18px;">⚠️ Important Information</h3>
                <ul style="color: #666; line-height: 1.6;">
                    <li><strong>Arrival:</strong> Please arrive 30 minutes before the event start time</li>
                    <li><strong>Check-in:</strong> Present your badge at the registration desk</li>
                    <li><strong>Dress Code:</strong> Business casual or formal attire recommended</li>
                    <li><strong>Materials:</strong> Notebooks and pens will be provided</li>
                    <li><strong>Meals:</strong> Refreshments and meals will be served as per schedule</li>
                    <li><strong>Parking:</strong> Free parking available on campus</li>
                    <li><strong>WiFi:</strong> Guest network details will be provided at check-in</li>
                </ul>
            </div>
            
            <!-- Organizer Contact -->
            <div style="background: #e8f5e8; border-left: 4px solid #4CAF50; padding: 20px; margin: 20px 0; border-radius: 5px;">
                <h3 style="color: #2e7d32; margin-top: 0;">📞 Event Organizer Contact</h3>
                <table style="width: 100%; border-collapse: collapse;">
                    <tr>
                        <td style="padding: 8px 0; font-weight: bold; color: #2e7d32; width: 30%;">Organizer:</td>
                        <td style="padding: 8px 0; color: #333;">{organizer_name}</td>
                    </tr>
                    <tr>
                        <td style="padding: 8px 0; font-weight: bold; color: #2e7d32;">Email:</td>
                        <td style="padding: 8px 0; color: #333;">{organizer_email}</td>
                    </tr>
                    <tr>
                        <td style="padding: 8px 0; font-weight: bold; color: #2e7d32;">Phone:</td>
                        <td style="padding: 8px 0; color: #333;">{organizer_phone}</td>
                    </tr>
                    <tr>
                        <td style="padding: 8px 0; font-weight: bold; color: #2e7d32;">Office Hours:</td>
                        <td style="padding: 8px 0; color: #333;">Monday - Friday, 8:00 AM - 5:00 PM</td>
                    </tr>
                </table>
            </div>
        </div>
        
        <!-- Footer -->
        <div style="background: #f8f9fa; padding: 20px; text-align: center; color: #666; font-size: 14px;">
            <p style="margin: 0;"><strong>University of Gondar Events</strong></p>
            <p style="margin: 5px 0;">We look forward to seeing you at the event!</p>
            <p style="margin: 5px 0;">&copy; {current_year} University of Gondar. All rights reserved.</p>
        </div>
    </div>
</body>
</html>
"""
            event_template.save()
            self.stdout.write("✓ Fixed event details template")
        except Exception as e:
            self.stdout.write(f"Error fixing event template: {e}")
        
        self.stdout.write("Email templates fixed successfully!")
