# Production Deployment Script for UoG Event Management System
# This script deploys the application with Nginx as a reverse proxy

param(
    [switch]$Build,
    [switch]$Stop,
    [switch]$Restart,
    [switch]$Logs,
    [switch]$Status,
    [switch]$Clean
)

$ComposeFile = "docker-compose.prod.yml"
$EnvFile = ".env"

Write-Host "UoG Event Management System - Production Deployment" -ForegroundColor Green
Write-Host "============================================================" -ForegroundColor Green

# Check if Docker is running
try {
    docker version | Out-Null
} catch {
    Write-Host "❌ Docker is not running. Please start Docker and try again." -ForegroundColor Red
    exit 1
}

# Check if environment file exists
if (-not (Test-Path $EnvFile)) {
    Write-Host "Environment file $EnvFile not found." -ForegroundColor Yellow
    Write-Host "Creating a sample environment file..." -ForegroundColor Yellow
    Copy-Item ".env.prod" ".env" -Force
    Write-Host "Please edit .env file with your production settings before continuing." -ForegroundColor Green
    exit 0
}

# Stop services
if ($Stop) {
    Write-Host "Stopping production services..." -ForegroundColor Yellow
    docker-compose -f $ComposeFile --env-file $EnvFile down
    Write-Host "Services stopped." -ForegroundColor Green
    exit 0
}

# Clean up (remove volumes and images)
if ($Clean) {
    Write-Host "Cleaning up Docker resources..." -ForegroundColor Yellow
    docker-compose -f $ComposeFile --env-file $EnvFile down -v --rmi all
    Write-Host "Cleanup completed." -ForegroundColor Green
    exit 0
}

# Show logs
if ($Logs) {
    Write-Host "Showing service logs..." -ForegroundColor Cyan
    docker-compose -f $ComposeFile --env-file $EnvFile logs -f
    exit 0
}

# Show status
if ($Status) {
    Write-Host "Service Status:" -ForegroundColor Cyan
    docker-compose -f $ComposeFile --env-file $EnvFile ps
    Write-Host "`nAccess URLs:" -ForegroundColor Cyan
    Write-Host "   Frontend: http://************" -ForegroundColor White
    Write-Host "   Backend API: http://************/api" -ForegroundColor White
    Write-Host "   Admin Panel: http://************/admin" -ForegroundColor White
    Write-Host "   Health Check: http://************/health" -ForegroundColor White
    exit 0
}

# Restart services
if ($Restart) {
    Write-Host "Restarting production services..." -ForegroundColor Yellow
    docker-compose -f $ComposeFile --env-file $EnvFile restart
    Write-Host "Services restarted." -ForegroundColor Green
    exit 0
}

# Build and deploy
Write-Host "Building and deploying production environment..." -ForegroundColor Cyan

# Build images if requested
if ($Build) {
    Write-Host "Building Docker images..." -ForegroundColor Yellow
    docker-compose -f $ComposeFile --env-file $EnvFile build --no-cache
}

# Start services
Write-Host "Starting production services..." -ForegroundColor Yellow
docker-compose -f $ComposeFile --env-file $EnvFile up -d

# Wait for services to be ready
Write-Host "Waiting for services to be ready..." -ForegroundColor Yellow
Start-Sleep -Seconds 30

# Check service status
Write-Host "Checking service status..." -ForegroundColor Cyan
docker-compose -f $ComposeFile --env-file $EnvFile ps

# Test health endpoints
Write-Host "Testing health endpoints..." -ForegroundColor Cyan
try {
    $response = Invoke-WebRequest -Uri "http://************/health/" -TimeoutSec 10
    if ($response.StatusCode -eq 200) {
        Write-Host "Backend health check: PASSED" -ForegroundColor Green
    }
} catch {
    Write-Host "Backend health check: FAILED" -ForegroundColor Red
}

try {
    $response = Invoke-WebRequest -Uri "http://************/" -TimeoutSec 10
    if ($response.StatusCode -eq 200) {
        Write-Host "Frontend health check: PASSED" -ForegroundColor Green
    }
} catch {
    Write-Host "Frontend health check: FAILED" -ForegroundColor Red
}

Write-Host "`nProduction deployment completed!" -ForegroundColor Green
Write-Host "============================================================" -ForegroundColor Green
Write-Host "Access URLs:" -ForegroundColor Cyan
Write-Host "   Frontend: http://************" -ForegroundColor White
Write-Host "   Backend API: http://************/api" -ForegroundColor White
Write-Host "   Admin Panel: http://************/admin" -ForegroundColor White
Write-Host "   Health Check: http://************/health" -ForegroundColor White
Write-Host "`nUseful commands:" -ForegroundColor Cyan
Write-Host "   View logs: .\deploy-production.ps1 -Logs" -ForegroundColor White
Write-Host "   Check status: .\deploy-production.ps1 -Status" -ForegroundColor White
Write-Host "   Restart: .\deploy-production.ps1 -Restart" -ForegroundColor White
Write-Host "   Stop: .\deploy-production.ps1 -Stop" -ForegroundColor White
