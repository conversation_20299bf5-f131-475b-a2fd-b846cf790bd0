import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, Spin<PERSON>, Badge } from 'react-bootstrap';
import { useParams } from 'react-router-dom';
import { participantService, getMediaUrl } from '../services/api';

interface ParticipantData {
  id: number;
  uuid: string;
  full_name: string;
  email: string;
  phone: string;
  institution_name: string;
  position: string;
  participant_type_name: string;
  participant_type_color: string;
  event_name: string;
  arrival_date: string;
  departure_date: string;
  is_confirmed: boolean;
  registration_date: string;
}

const ParticipantVerify: React.FC = () => {
  const { uuid } = useParams<{ uuid: string }>();
  const [participant, setParticipant] = useState<ParticipantData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');

  useEffect(() => {
    if (uuid) {
      verifyParticipant();
    }
  }, [uuid]);

  const verifyParticipant = async () => {
    try {
      const response = await participantService.verifyParticipant(uuid!);
      setParticipant(response.data);
    } catch (error: any) {
      setError('Participant not found or invalid QR code');
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <Container className="text-center mt-5">
        <Spinner animation="border" role="status">
          <span className="visually-hidden">Loading...</span>
        </Spinner>
        <p className="mt-3">Verifying participant...</p>
      </Container>
    );
  }

  if (error) {
    return (
      <Container className="py-4">
        <Row className="justify-content-center">
          <Col lg={6}>
            <Alert variant="danger" className="text-center">
              <i className="fas fa-exclamation-triangle fa-3x mb-3"></i>
              <h4>Verification Failed</h4>
              <p>{error}</p>
            </Alert>
          </Col>
        </Row>
      </Container>
    );
  }

  if (!participant) {
    return (
      <Container className="py-4">
        <Row className="justify-content-center">
          <Col lg={6}>
            <Alert variant="warning" className="text-center">
              <i className="fas fa-question-circle fa-3x mb-3"></i>
              <h4>No Data Found</h4>
              <p>No participant data available.</p>
            </Alert>
          </Col>
        </Row>
      </Container>
    );
  }

  return (
    <Container className="py-4">
      <Row className="justify-content-center">
        <Col lg={8}>
          <Card className="shadow">
            <Card.Header className="bg-success text-white text-center">
              <h3 className="mb-0">
                <i className="fas fa-check-circle me-2"></i>
                Participant Verified
              </h3>
            </Card.Header>
            <Card.Body className="p-4">
              <Row>
                <Col md={8}>
                  <h4 className="text-primary mb-3">{participant.full_name}</h4>

                  <div className="mb-3">
                    <Badge
                      style={{ backgroundColor: participant.participant_type_color }}
                      className="fs-6 px-3 py-2"
                    >
                      {participant.participant_type_name}
                    </Badge>
                    {participant.is_confirmed && (
                      <Badge bg="success" className="ms-2 fs-6 px-3 py-2">
                        Confirmed
                      </Badge>
                    )}
                  </div>

                  <div className="row g-3">
                    <div className="col-sm-6">
                      <div className="border-start border-primary border-3 ps-3">
                        <small className="text-muted">Email</small>
                        <div className="fw-medium">{participant.email}</div>
                      </div>
                    </div>

                    <div className="col-sm-6">
                      <div className="border-start border-primary border-3 ps-3">
                        <small className="text-muted">Phone</small>
                        <div className="fw-medium">{participant.phone}</div>
                      </div>
                    </div>

                    <div className="col-sm-6">
                      <div className="border-start border-primary border-3 ps-3">
                        <small className="text-muted">Institution</small>
                        <div className="fw-medium">{participant.institution_name}</div>
                      </div>
                    </div>

                    <div className="col-sm-6">
                      <div className="border-start border-primary border-3 ps-3">
                        <small className="text-muted">Position</small>
                        <div className="fw-medium">{participant.position}</div>
                      </div>
                    </div>

                    <div className="col-12">
                      <div className="border-start border-primary border-3 ps-3">
                        <small className="text-muted">Event</small>
                        <div className="fw-medium">{participant.event_name}</div>
                      </div>
                    </div>

                    <div className="col-sm-6">
                      <div className="border-start border-primary border-3 ps-3">
                        <small className="text-muted">Arrival</small>
                        <div className="fw-medium">
                          {new Date(participant.arrival_date).toLocaleString()}
                        </div>
                      </div>
                    </div>

                    <div className="col-sm-6">
                      <div className="border-start border-primary border-3 ps-3">
                        <small className="text-muted">Departure</small>
                        <div className="fw-medium">
                          {new Date(participant.departure_date).toLocaleString()}
                        </div>
                      </div>
                    </div>

                    <div className="col-12">
                      <div className="border-start border-primary border-3 ps-3">
                        <small className="text-muted">Registration Date</small>
                        <div className="fw-medium">
                          {new Date(participant.registration_date).toLocaleString()}
                        </div>
                      </div>
                    </div>
                  </div>
                </Col>

                <Col md={4} className="text-center">
                  <div className="bg-light rounded p-3">
                    <i className="fas fa-qrcode fa-4x text-muted mb-3"></i>
                    <p className="small text-muted mb-0">
                      QR Code Verified Successfully
                    </p>
                    <p className="small text-muted">
                      UUID: {participant.uuid}
                    </p>
                  </div>
                </Col>
              </Row>
            </Card.Body>
          </Card>
        </Col>
      </Row>
    </Container>
  );
};

export default ParticipantVerify;
