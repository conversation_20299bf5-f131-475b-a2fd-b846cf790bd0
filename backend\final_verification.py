#!/usr/bin/env python3

import os
import sys
import django

# Add the project root to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'event_management.settings')
django.setup()

from participants.models import Participant

def final_verification():
    """Final verification of all participants"""
    
    print("=== FINAL PARTICIPANT DATABASE VERIFICATION ===")
    
    total_participants = Participant.objects.count()
    print(f"Total participants in database: {total_participants}")
    
    # Check ID ranges
    ranges = [
        (1, 100),
        (101, 200),
        (300, 400),
        (500, 600),
        (600, 700)
    ]
    
    print(f"\nParticipants by ID range:")
    for start, end in ranges:
        count = Participant.objects.filter(id__range=(start, end-1)).count()
        if count > 0:
            print(f"IDs {start}-{end-1}: {count} participants")
    
    # Check highest IDs
    print(f"\nHighest 10 participants by ID:")
    highest_participants = Participant.objects.order_by('-id')[:10]
    for p in highest_participants:
        print(f"ID: {p.id}, Name: {p.first_name} {p.last_name}, Email: {p.email}")
    
    # Check specific ranges we added
    print(f"\nVerifying specific ranges:")
    
    # Check 551-602
    range_551_602 = Participant.objects.filter(id__range=(551, 602)).count()
    print(f"IDs 551-602: {range_551_602}/52 participants")
    
    # Check 603-638
    range_603_638 = Participant.objects.filter(id__range=(603, 638)).count()
    print(f"IDs 603-638: {range_603_638}/36 participants")
    
    # Show status distribution
    print(f"\nStatus distribution:")
    active_count = Participant.objects.filter(status='active').count()
    approved_count = Participant.objects.filter(status='approved').count()
    deleted_count = Participant.objects.filter(status='deleted').count()
    
    print(f"Active: {active_count}")
    print(f"Approved: {approved_count}")
    print(f"Deleted: {deleted_count}")
    
    # Check for any missing IDs in our target ranges
    print(f"\nChecking for missing IDs in target ranges:")
    
    # Check 551-602
    missing_551_602 = []
    for i in range(551, 603):
        if not Participant.objects.filter(id=i).exists():
            missing_551_602.append(i)
    
    if missing_551_602:
        print(f"Missing IDs in 551-602: {missing_551_602}")
    else:
        print("✓ All IDs 551-602 present")
    
    # Check 603-638
    missing_603_638 = []
    for i in range(603, 639):
        if not Participant.objects.filter(id=i).exists():
            missing_603_638.append(i)
    
    if missing_603_638:
        print(f"Missing IDs in 603-638: {missing_603_638}")
    else:
        print("✓ All IDs 603-638 present")
    
    print(f"\n🎉 Database migration completed successfully!")
    print(f"Total participants: {total_participants}")

if __name__ == "__main__":
    final_verification()
