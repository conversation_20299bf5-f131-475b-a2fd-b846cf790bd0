from django.core.management.base import BaseCommand
from events.models import EmailTemplate


class Command(BaseCommand):
    help = 'Add developer credits footer to all email templates'

    def handle(self, *args, **options):
        self.stdout.write("Adding developer credits to email templates...")
        
        # Developer credits HTML
        developer_credits = '''
            <!-- Developer Credits -->
            <div style="margin-top: 20px; padding-top: 15px; border-top: 1px solid #dee2e6;">
                <p style="margin: 0 0 10px 0; font-weight: bold; color: #495057;">Developed by:</p>
                <div style="display: flex; justify-content: center; gap: 20px; flex-wrap: wrap;">
                    <a href="https://www.linkedin.com/in/tewodros-abebaw-chekol/" style="color: #0077b5; text-decoration: none; font-size: 13px;">
                        <strong>Tewodros Abebaw</strong>
                    </a>
                    <a href="https://www.linkedin.com/in/aragaw-mebratu-a3096514a/" style="color: #0077b5; text-decoration: none; font-size: 13px;">
                        <strong>Aragaw Mebratu</strong>
                    </a>
                    <a href="https://www.linkedin.com/in/meseret-teshale-bb24b767/" style="color: #0077b5; text-decoration: none; font-size: 13px;">
                        <strong>Meseret Teshale</strong>
                    </a>
                </div>
            </div>'''
        
        # Update event details template
        try:
            event_template = EmailTemplate.objects.get(template_type='event_details')
            
            # Check if developer credits already exist
            if 'Developed by:' not in event_template.html_content:
                # Find the footer and add developer credits before the closing div
                if '</div>' in event_template.html_content and 'University of Gondar' in event_template.html_content:
                    # Find the last occurrence of University of Gondar footer
                    content = event_template.html_content
                    
                    # Look for the footer pattern and insert developer credits
                    footer_patterns = [
                        '&copy; {current_year} University of Gondar. All rights reserved.</p>',
                        '© {current_year} University of Gondar. All rights reserved.</p>'
                    ]
                    
                    for pattern in footer_patterns:
                        if pattern in content:
                            # Insert developer credits after the copyright line
                            content = content.replace(pattern, pattern + developer_credits)
                            break
                    
                    event_template.html_content = content
                    event_template.save()
                    self.stdout.write("✓ Added developer credits to event details template")
                else:
                    self.stdout.write("⚠️ Could not find footer pattern in event details template")
            else:
                self.stdout.write("✓ Developer credits already exist in event details template")
                
        except EmailTemplate.DoesNotExist:
            self.stdout.write("❌ Event details template not found")
        except Exception as e:
            self.stdout.write(f"❌ Error updating event details template: {e}")
        
        # Update other templates that might exist
        template_types = [
            'registration_confirmation',
            'badge_notification', 
            'schedule_update',
            'daily_gallery',
            'event_reminder'
        ]
        
        for template_type in template_types:
            try:
                template = EmailTemplate.objects.get(template_type=template_type)
                
                # Check if developer credits already exist
                if 'Developed by:' not in template.html_content:
                    # Find the footer and add developer credits
                    content = template.html_content
                    
                    footer_patterns = [
                        '&copy; {current_year} University of Gondar. All rights reserved.</p>',
                        '© {current_year} University of Gondar. All rights reserved.</p>',
                        'University of Gondar Events Team</p>',
                        'Best regards,<br>University of Gondar Events Team</p>'
                    ]
                    
                    for pattern in footer_patterns:
                        if pattern in content:
                            content = content.replace(pattern, pattern + developer_credits)
                            break
                    
                    # If no pattern found, try to add before closing body or div
                    if content == template.html_content:  # No replacement made
                        if '</body>' in content:
                            content = content.replace('</body>', f'<div style="text-align: center; padding: 20px; background: #f8f9fa; color: #666; font-size: 14px;">{developer_credits}</div></body>')
                        elif '</div>' in content:
                            # Add before the last closing div
                            last_div_pos = content.rfind('</div>')
                            if last_div_pos != -1:
                                content = content[:last_div_pos] + f'<div style="text-align: center; padding: 20px; background: #f8f9fa; color: #666; font-size: 14px;">{developer_credits}</div>' + content[last_div_pos:]
                    
                    template.html_content = content
                    template.save()
                    self.stdout.write(f"✓ Added developer credits to {template_type} template")
                else:
                    self.stdout.write(f"✓ Developer credits already exist in {template_type} template")
                    
            except EmailTemplate.DoesNotExist:
                self.stdout.write(f"⚠️ {template_type} template not found")
            except Exception as e:
                self.stdout.write(f"❌ Error updating {template_type} template: {e}")
        
        self.stdout.write("Developer credits update completed!")
