import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { authService, User, UserPermissions } from '../services/auth';

interface AuthContextType {
  user: User | null;
  permissions: UserPermissions | null;
  loading: boolean;
  login: (username: string, password: string) => Promise<void>;
  logout: () => Promise<void>;
  updateProfile: (userData: Partial<User>) => Promise<void>;

  isAuthenticated: boolean;
  refreshUserData: () => Promise<void>;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

interface AuthProviderProps {
  children: ReactNode;
}

export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null);
  const [permissions, setPermissions] = useState<UserPermissions | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    initializeAuth();
  }, []);

  const initializeAuth = async () => {
    try {
      if (authService.isAuthenticated()) {
        const currentUser = authService.getCurrentUser();
        if (currentUser) {
          setUser(currentUser);
          await fetchUserPermissions();
        } else {
          // Token exists but no user data, fetch from API
          await refreshUserData();
        }
      } else {
        // Not authenticated, clear state
        setUser(null);
        setPermissions(null);
      }
    } catch (error) {
      console.error('Auth initialization error:', error);
      await logout();
    } finally {
      setLoading(false);
    }
  };

  const fetchUserPermissions = async () => {
    try {
      // Only fetch permissions if user is authenticated
      if (!authService.isAuthenticated()) {
        setPermissions(null);
        return;
      }

      const userPermissions = await authService.getUserPermissions();
      setPermissions(userPermissions);
    } catch (error: any) {
      console.error('Error fetching permissions:', error);
      // If permissions fetch fails, clear auth state
      if (error.response?.status === 401) {
        await logout();
      }
    }
  };

  const refreshUserData = async () => {
    try {
      const userData = await authService.getUserProfile();
      setUser(userData);
      await fetchUserPermissions();
    } catch (error) {
      console.error('Error refreshing user data:', error);
      throw error;
    }
  };

  const login = async (username: string, password: string) => {
    try {
      setLoading(true);
      const response = await authService.login({ username, password });
      setUser(response.user);
      await fetchUserPermissions();
    } catch (error) {
      console.error('Login error:', error);
      throw error;
    } finally {
      setLoading(false);
    }
  };

  const logout = async () => {
    try {
      setLoading(true);
      await authService.logout();
    } catch (error) {
      console.error('Logout error:', error);
    } finally {
      setUser(null);
      setPermissions(null);
      setLoading(false);
    }
  };

  const updateProfile = async (userData: Partial<User>) => {
    try {
      const updatedUser = await authService.updateProfile(userData);
      setUser(updatedUser);
    } catch (error) {
      console.error('Profile update error:', error);
      throw error;
    }
  };



  const value: AuthContextType = {
    user,
    permissions,
    loading,
    login,
    logout,
    updateProfile,

    isAuthenticated: !!user,
    refreshUserData,
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};

export const useAuth = (): AuthContextType => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

export default AuthContext;
