#!/usr/bin/env python
"""
Check badge data to see if real participant info is being used
"""
import os
import sys
import django

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'event_management.settings')
django.setup()

from badges.models import Badge
from participants.models import Participant

def check_badge_data():
    """Check if badges are using real participant data"""
    print("🔍 CHECKING BADGE DATA")
    print("=" * 50)
    
    try:
        # Get participant
        participant = Participant.objects.first()
        if not participant:
            print("❌ No participants found")
            return
        
        print(f"👤 Participant: {participant.full_name}")
        print(f"📧 Email: {getattr(participant, 'email', 'N/A')}")
        print(f"🏢 Institution: {getattr(participant, 'institution_name', 'N/A')}")
        print(f"💼 Position: {getattr(participant, 'position', 'N/A')}")
        print(f"🎯 Type: {participant.participant_type.name if participant.participant_type else 'N/A'}")
        
        # Get badge
        badge, created = Badge.objects.get_or_create(participant=participant)
        print(f"\n🎫 Badge: {'Created' if created else 'Found'}")
        print(f"📁 Badge file: {badge.badge_image.name if badge.badge_image else 'None'}")
        print(f"✅ Generated: {badge.is_generated}")
        
        # Check if badge file exists and get its path
        if badge.badge_image:
            try:
                badge_path = badge.badge_image.path
                print(f"📂 Badge path: {badge_path}")
                
                # Check file size
                import os
                if os.path.exists(badge_path):
                    file_size = os.path.getsize(badge_path)
                    print(f"📊 File size: {file_size} bytes")
                    
                    # Check image dimensions
                    from PIL import Image
                    img = Image.open(badge_path)
                    print(f"📏 Dimensions: {img.size}")
                    
                    if img.size == (600, 1200):
                        print("✅ CONFIRMED: Using new SVG template dimensions!")
                    else:
                        print(f"⚠️  Old dimensions: {img.size}")
                else:
                    print("❌ Badge file does not exist on disk")
            except Exception as e:
                print(f"❌ Error checking badge file: {e}")
        
        # Test the badge generation methods
        print(f"\n🧪 TESTING BADGE METHODS:")
        
        # Check if the participant info method uses real data
        from PIL import Image, ImageDraw
        test_img = Image.new('RGB', (600, 1200), color='#002D72')
        test_draw = ImageDraw.Draw(test_img)
        
        print("🎨 Testing participant info method...")
        badge._add_svg_participant_info(test_draw, 600, 1200)
        print("✅ Method executed successfully")
        
        # Force regenerate to ensure latest code
        print(f"\n🔄 FORCE REGENERATING BADGE...")
        if badge.badge_image:
            badge.badge_image.delete(save=False)
        if badge.qr_code_image:
            badge.qr_code_image.delete(save=False)
        
        badge.is_generated = False
        badge.generated_at = None
        badge.save()
        
        # Generate new badge
        badge_img = badge.generate_badge()
        print(f"✅ Badge regenerated: {badge_img.size}")
        print(f"📁 New file: {badge.badge_image.name}")
        
        print(f"\n🎉 VERIFICATION COMPLETE!")
        print(f"✅ Badge should now contain real data for: {participant.full_name}")
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    check_badge_data()
