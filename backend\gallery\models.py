from django.db import models
from django.core.validators import FileExtensionValidator
from django.conf import settings
from PIL import Image
import os


class GalleryCategory(models.Model):
    """Categories for organizing gallery images"""
    name = models.CharField(max_length=100, unique=True)
    description = models.TextField(blank=True)
    slug = models.SlugField(unique=True)
    icon = models.CharField(max_length=50, blank=True, help_text="FontAwesome icon class (e.g., fas fa-camera)")
    color = models.CharField(max_length=7, default="#667eea", help_text="Hex color code for category theme")
    is_active = models.BooleanField(default=True)
    order = models.PositiveIntegerField(default=0, help_text="Display order (lower numbers first)")
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = "Gallery Category"
        verbose_name_plural = "Gallery Categories"
        ordering = ['order', 'name']

    def __str__(self):
        return self.name

    @property
    def image_count(self):
        return self.images.filter(is_active=True).count()


class GalleryImage(models.Model):
    """Individual gallery images with categorization and metadata"""
    title = models.CharField(max_length=200)
    description = models.TextField(blank=True)
    image = models.ImageField(
        upload_to='gallery/%Y/%m/',
        validators=[FileExtensionValidator(allowed_extensions=['jpg', 'jpeg', 'png', 'webp'])]
    )
    thumbnail = models.ImageField(upload_to='gallery/thumbnails/%Y/%m/', blank=True)
    
    # Categorization
    category = models.ForeignKey(
        GalleryCategory, 
        on_delete=models.CASCADE, 
        related_name='images'
    )
    tags = models.CharField(max_length=500, blank=True, help_text="Comma-separated tags")
    
    # Display options
    is_featured = models.BooleanField(default=False, help_text="Show in featured sections")
    is_slider = models.BooleanField(default=False, help_text="Include in homepage slider")
    is_campus = models.BooleanField(default=False, help_text="Campus/University related")
    is_active = models.BooleanField(default=True)
    
    # Metadata
    photographer = models.CharField(max_length=100, blank=True)
    date_taken = models.DateField(blank=True, null=True)
    location = models.CharField(max_length=200, blank=True)
    
    # SEO and accessibility
    alt_text = models.CharField(max_length=200, blank=True, help_text="Alt text for accessibility")
    
    # Ordering and timestamps
    order = models.PositiveIntegerField(default=0, help_text="Display order within category")
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = "Gallery Image"
        verbose_name_plural = "Gallery Images"
        ordering = ['-is_featured', 'order', '-created_at']

    def __str__(self):
        return f"{self.title} ({self.category.name})"

    def save(self, *args, **kwargs):
        super().save(*args, **kwargs)
        
        # Generate thumbnail if not exists
        if self.image and not self.thumbnail:
            self.create_thumbnail()

    def create_thumbnail(self):
        """Create a thumbnail for the image"""
        if not self.image:
            return
            
        try:
            # Open the image
            img = Image.open(self.image.path)
            
            # Convert to RGB if necessary
            if img.mode in ('RGBA', 'LA', 'P'):
                img = img.convert('RGB')
            
            # Create thumbnail
            img.thumbnail((400, 300), Image.Resampling.LANCZOS)
            
            # Save thumbnail
            thumbnail_path = self.image.path.replace('/gallery/', '/gallery/thumbnails/')
            os.makedirs(os.path.dirname(thumbnail_path), exist_ok=True)
            img.save(thumbnail_path, 'JPEG', quality=85)
            
            # Update thumbnail field
            self.thumbnail = thumbnail_path.replace(settings.MEDIA_ROOT, '').lstrip('/')
            self.save(update_fields=['thumbnail'])
            
        except Exception as e:
            print(f"Error creating thumbnail for {self.title}: {e}")

    @property
    def tag_list(self):
        """Return tags as a list"""
        if self.tags:
            return [tag.strip() for tag in self.tags.split(',') if tag.strip()]
        return []


class EventSlider(models.Model):
    """Slider configuration for events on homepage"""
    event = models.OneToOneField(
        'events.Event',
        on_delete=models.CASCADE,
        related_name='slider_config'
    )
    
    # Slider-specific content
    slider_title = models.CharField(max_length=200, blank=True, help_text="Custom title for slider (optional)")
    slider_subtitle = models.CharField(max_length=200, blank=True, help_text="Custom subtitle for slider (optional)")
    slider_description = models.TextField(blank=True, help_text="Custom description for slider (optional)")
    
    # Visual customization
    background_image = models.ImageField(
        upload_to='slider/backgrounds/%Y/%m/',
        blank=True,
        validators=[FileExtensionValidator(allowed_extensions=['jpg', 'jpeg', 'png', 'webp'])]
    )
    background_color = models.CharField(
        max_length=7, 
        default="#667eea", 
        help_text="Hex color code for gradient start"
    )
    background_color_end = models.CharField(
        max_length=7, 
        default="#764ba2", 
        help_text="Hex color code for gradient end"
    )
    
    # Display settings
    is_active = models.BooleanField(default=True, help_text="Show in homepage slider")
    order = models.PositiveIntegerField(default=0, help_text="Display order in slider")
    
    # Call-to-action
    cta_text = models.CharField(max_length=50, default="Register Now", help_text="Call-to-action button text")
    cta_url = models.URLField(blank=True, help_text="Custom CTA URL (optional)")
    
    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = "Event Slider"
        verbose_name_plural = "Event Sliders"
        ordering = ['order', '-created_at']

    def __str__(self):
        return f"Slider: {self.event.name}"

    @property
    def display_title(self):
        """Return custom title or event name"""
        return self.slider_title or self.event.name

    @property
    def display_subtitle(self):
        """Return custom subtitle or event location"""
        return self.slider_subtitle or f"{self.event.city}, {self.event.country}"

    @property
    def display_description(self):
        """Return custom description or event description"""
        return self.slider_description or self.event.description

    @property
    def background_gradient(self):
        """Return CSS gradient string"""
        return f"linear-gradient(135deg, {self.background_color} 0%, {self.background_color_end} 100%)"
