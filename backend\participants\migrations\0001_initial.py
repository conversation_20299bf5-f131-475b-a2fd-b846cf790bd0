# Generated by Django 4.2.7 on 2025-07-26 04:21

from django.db import migrations, models
import django.db.models.deletion
import uuid


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ("events", "0001_initial"),
    ]

    operations = [
        migrations.CreateModel(
            name="ParticipantType",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("name", models.CharField(max_length=100, unique=True)),
                ("description", models.TextField(blank=True)),
                (
                    "color",
                    models.Char<PERSON>ield(
                        default="#007bff",
                        help_text="Hex color code for badge",
                        max_length=7,
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
            ],
        ),
        migrations.CreateModel(
            name="Participant",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "uuid",
                    models.UUIDField(default=uuid.uuid4, editable=False, unique=True),
                ),
                ("first_name", models.CharField(max_length=100)),
                ("last_name", models.CharField(max_length=100)),
                ("middle_name", models.CharField(blank=True, max_length=100)),
                ("email", models.EmailField(max_length=254, unique=True)),
                ("phone", models.CharField(max_length=20)),
                ("institution_name", models.CharField(max_length=200)),
                ("position", models.CharField(max_length=200)),
                ("arrival_date", models.DateTimeField()),
                ("departure_date", models.DateTimeField()),
                ("profile_photo", models.ImageField(upload_to="participant_photos/")),
                ("badge_generated", models.BooleanField(default=False)),
                (
                    "badge_file",
                    models.FileField(blank=True, null=True, upload_to="badges/"),
                ),
                ("remarks", models.TextField(blank=True)),
                ("registration_date", models.DateTimeField(auto_now_add=True)),
                ("is_confirmed", models.BooleanField(default=False)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "event",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="participants",
                        to="events.event",
                    ),
                ),
                (
                    "participant_type",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="participants.participanttype",
                    ),
                ),
            ],
            options={
                "ordering": ["last_name", "first_name"],
            },
        ),
        migrations.CreateModel(
            name="Attendance",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("checked_in_at", models.DateTimeField(auto_now_add=True)),
                ("checked_in_by", models.CharField(blank=True, max_length=100)),
                ("notes", models.TextField(blank=True)),
                (
                    "event_schedule",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="attendances",
                        to="events.eventschedule",
                    ),
                ),
                (
                    "participant",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="attendances",
                        to="participants.participant",
                    ),
                ),
            ],
            options={
                "ordering": ["-checked_in_at"],
                "unique_together": {("participant", "event_schedule")},
            },
        ),
    ]
