#!/bin/bash
# Bash script to switch between different environment configurations
# Usage: ./switch-env.sh [localhost|ip|domain]

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
MAGENTA='\033[0;35m'
WHITE='\033[1;37m'
NC='\033[0m' # No Color

if [ $# -eq 0 ]; then
    echo -e "${RED}❌ Error: Environment parameter required${NC}"
    echo -e "${WHITE}Usage: $0 [localhost|ip|domain]${NC}"
    echo -e "${WHITE}Available environments:${NC}"
    echo -e "${WHITE}  • localhost - For internal/same machine access${NC}"
    echo -e "${WHITE}  • ip - For external access via IP (************)${NC}"
    echo -e "${WHITE}  • domain - For domain access (event.uog.edu.et)${NC}"
    exit 1
fi

ENVIRONMENT=$1

# Validate environment parameter
case $ENVIRONMENT in
    localhost|ip|domain)
        ;;
    *)
        echo -e "${RED}❌ Invalid environment: $ENVIRONMENT${NC}"
        echo -e "${WHITE}Valid options: localhost, ip, domain${NC}"
        exit 1
        ;;
esac

echo -e "${BLUE}🔧 Switching to $ENVIRONMENT environment configuration...${NC}"

# Define environment files
case $ENVIRONMENT in
    localhost)
        SOURCE_FILE=".env.localhost"
        ;;
    ip)
        SOURCE_FILE=".env.ip"
        ;;
    domain)
        SOURCE_FILE=".env.domain"
        ;;
esac

if [ ! -f "$SOURCE_FILE" ]; then
    echo -e "${RED}❌ Environment file $SOURCE_FILE not found!${NC}"
    exit 1
fi

# Backup current .env if it exists
if [ -f ".env" ]; then
    TIMESTAMP=$(date +"%Y%m%d_%H%M%S")
    BACKUP_FILE=".env.backup.$TIMESTAMP"
    cp ".env" "$BACKUP_FILE"
    echo -e "${YELLOW}📦 Backed up current .env to $BACKUP_FILE${NC}"
fi

# Copy the selected environment file to .env
cp "$SOURCE_FILE" ".env"

# Read the new configuration
echo -e "${GREEN}✅ Environment switched to: $ENVIRONMENT${NC}"
echo -e "${CYAN}📋 Frontend API Configuration:${NC}"

grep "REACT_APP_" ".env" | while read -r line; do
    if [[ $line =~ ^(REACT_APP_[^=]+)=(.+)$ ]]; then
        echo -e "${WHITE}   ${BASH_REMATCH[1]}: ${BASH_REMATCH[2]}${NC}"
    fi
done

echo ""
echo -e "${YELLOW}🔄 Next steps:${NC}"
echo -e "${WHITE}   1. Restart Docker services: docker-compose -f docker-compose.local.yml down && docker-compose -f docker-compose.local.yml up -d${NC}"
echo -e "${WHITE}   2. Wait for services to start (2-3 minutes)${NC}"

case $ENVIRONMENT in
    localhost)
        echo -e "${WHITE}   3. Access frontend: http://localhost:3000${NC}"
        echo -e "${WHITE}   4. Access backend: http://localhost:8000${NC}"
        ;;
    ip)
        echo -e "${WHITE}   3. Access frontend: http://************:3000${NC}"
        echo -e "${WHITE}   4. Access backend: http://************:8000${NC}"
        ;;
    domain)
        echo -e "${WHITE}   3. Access frontend: http://event.uog.edu.et:3000${NC}"
        echo -e "${WHITE}   4. Access backend: http://event.uog.edu.et:8000${NC}"
        ;;
esac

echo ""
echo -e "${MAGENTA}💡 Environment configurations available:${NC}"
echo -e "${WHITE}   • localhost - For internal/same machine access${NC}"
echo -e "${WHITE}   • ip - For external access via IP (************)${NC}"
echo -e "${WHITE}   • domain - For domain access (event.uog.edu.et)${NC}"
