#!/usr/bin/env python
"""
<PERSON><PERSON><PERSON> to create a test multi-day event for testing the schedule management feature.
"""
import os
import sys
import django
from datetime import datetime, timedelta

# Setup Django
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'event_management.settings')
django.setup()

from events.models import Event
from django.utils import timezone

def create_test_event():
    """Create a test multi-day event"""
    
    # Check if test event already exists
    if Event.objects.filter(name="Test Multi-Day Conference").exists():
        print("✅ Test event already exists!")
        event = Event.objects.get(name="Test Multi-Day Conference")
        print(f"   Event: {event.name}")
        print(f"   Dates: {event.start_date.date()} to {event.end_date.date()}")
        return event
    
    # Create a 3-day event starting tomorrow
    start_date = timezone.now().replace(hour=9, minute=0, second=0, microsecond=0) + timedelta(days=1)
    end_date = start_date + timedelta(days=2, hours=9)  # 3 days total
    
    event = Event.objects.create(
        name="Test Multi-Day Conference",
        description="A test conference spanning multiple days to test the schedule management feature.",
        start_date=start_date,
        end_date=end_date,
        location="University of Gondar",
        city="Gondar",
        country="Ethiopia",
        organizer_name="Test Organizer",
        organizer_email="<EMAIL>",
        organizer_phone="+251911234567",
        is_active=True
    )
    
    print("✅ Created test multi-day event!")
    print(f"   Event: {event.name}")
    print(f"   Dates: {event.start_date.date()} to {event.end_date.date()}")
    print(f"   Duration: {(event.end_date.date() - event.start_date.date()).days + 1} days")
    
    return event

if __name__ == "__main__":
    create_test_event()
