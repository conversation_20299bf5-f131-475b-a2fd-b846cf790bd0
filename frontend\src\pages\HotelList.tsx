import React, { useState, useEffect } from 'react';
import { Container, Row, Col, Card, Badge, Form, InputGroup, Alert, Spinner, Button, Modal } from 'react-bootstrap';
import { Link } from 'react-router-dom';
import { hotelService, Hotel } from '../services/api';

const HotelList: React.FC = () => {
  const [hotels, setHotels] = useState<Hotel[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [filterActive, setFilterActive] = useState<boolean | undefined>(undefined);
  const [showImportModal, setShowImportModal] = useState(false);
  const [importFile, setImportFile] = useState<File | null>(null);
  const [importLoading, setImportLoading] = useState(false);
  const [importResult, setImportResult] = useState<any>(null);

  useEffect(() => {
    fetchHotels();
  }, [filterActive]); // eslint-disable-line react-hooks/exhaustive-deps

  const fetchHotels = async () => {
    try {
      setLoading(true);
      const response = await hotelService.getHotels(undefined, filterActive);
      const hotelsData = Array.isArray(response.data) ? response.data : 
                        ((response.data as any)?.results ? (response.data as any).results : []);
      setHotels(hotelsData);
      setError(null);
    } catch (err) {
      console.error('Error fetching hotels:', err);
      setError('Failed to load hotels. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const filteredHotels = hotels.filter(hotel =>
    hotel.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    hotel.address.toLowerCase().includes(searchTerm.toLowerCase()) ||
    hotel.contact_person.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const getStarRating = (rating?: number) => {
    if (!rating || rating < 0 || rating > 5) return 'Not Rated';
    const filledStars = Math.max(0, Math.min(5, Math.floor(rating)));
    const emptyStars = Math.max(0, 5 - filledStars);
    return '★'.repeat(filledStars) + '☆'.repeat(emptyStars);
  };

  const handleExport = async () => {
    try {
      const response = await hotelService.exportCSV();
      const blob = new Blob([response.data], { type: 'text/csv' });
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = 'hotels_export.csv';
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);
    } catch (err) {
      console.error('Error exporting hotels:', err);
      setError('Failed to export hotels. Please try again.');
    }
  };

  const handleDownloadSample = async () => {
    try {
      const response = await hotelService.downloadSample();
      const blob = new Blob([response.data], { type: 'text/csv' });
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = 'hotels_sample.csv';
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);
    } catch (err) {
      console.error('Error downloading sample:', err);
      setError('Failed to download sample. Please try again.');
    }
  };

  const handleImport = async () => {
    if (!importFile) {
      setError('Please select a file to import.');
      return;
    }

    try {
      setImportLoading(true);
      const response = await hotelService.importCSV(importFile);
      setImportResult(response.data);
      await fetchHotels(); // Refresh the list
      setImportFile(null);
    } catch (err) {
      console.error('Error importing hotels:', err);
      setError('Failed to import hotels. Please try again.');
      if (err instanceof Error) {
        setImportResult({ error: err.message });
      }
    } finally {
      setImportLoading(false);
    }
  };

  if (loading) {
    return (
      <Container className="text-center mt-5">
        <Spinner animation="border" role="status">
          <span className="visually-hidden">Loading...</span>
        </Spinner>
        <p className="mt-3">Loading hotels...</p>
      </Container>
    );
  }

  return (
    <Container className="py-4">
      <Row className="mb-4">
        <Col>
          <div className="d-flex justify-content-between align-items-center mb-4">
            <div>
              <h1 className="display-5 fw-bold text-primary">
                <i className="fas fa-hotel me-3"></i>
                Hotel Management
              </h1>
              <p className="lead text-muted">Manage accommodations for event participants</p>
            </div>
            <div className="d-flex gap-2">
              <Button variant="outline-success" onClick={handleExport}>
                <i className="fas fa-download me-2"></i>
                Export CSV
              </Button>
              <Button variant="outline-info" onClick={handleDownloadSample}>
                <i className="fas fa-file-csv me-2"></i>
                Sample CSV
              </Button>
              <Button variant="outline-warning" onClick={() => setShowImportModal(true)}>
                <i className="fas fa-upload me-2"></i>
                Import CSV
              </Button>
              <Link to="/hotels/new" className="btn btn-primary btn-lg">
                <i className="fas fa-plus me-2"></i>
                Add New Hotel
              </Link>
            </div>
          </div>

          {/* Search and Filter Controls */}
          <Row className="mb-4">
            <Col md={6}>
              <InputGroup>
                <InputGroup.Text>
                  <i className="fas fa-search"></i>
                </InputGroup.Text>
                <Form.Control
                  type="text"
                  placeholder="Search hotels by name, address, or contact person..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                />
              </InputGroup>
            </Col>
            <Col md={6}>
              <Form.Select
                value={filterActive === undefined ? '' : filterActive.toString()}
                onChange={(e) => {
                  const value = e.target.value;
                  setFilterActive(value === '' ? undefined : value === 'true');
                }}
              >
                <option value="">All Hotels</option>
                <option value="true">Active Hotels</option>
                <option value="false">Inactive Hotels</option>
              </Form.Select>
            </Col>
          </Row>

          {error && (
            <Alert variant="danger" className="mb-4">
              <i className="fas fa-exclamation-triangle me-2"></i>
              {error}
            </Alert>
          )}
        </Col>
      </Row>

      {/* Hotels Grid */}
      <Row>
        {filteredHotels.length === 0 ? (
          <Col>
            <Card className="text-center py-5">
              <Card.Body>
                <i className="fas fa-hotel fa-4x text-muted mb-3"></i>
                <h4 className="text-muted">No Hotels Found</h4>
                <p className="text-muted">
                  {searchTerm ? 'No hotels match your search criteria.' : 'No hotels have been added yet.'}
                </p>
                <Link to="/hotels/new" className="btn btn-primary">
                  <i className="fas fa-plus me-2"></i>
                  Add First Hotel
                </Link>
              </Card.Body>
            </Card>
          </Col>
        ) : (
          filteredHotels.map((hotel) => (
            <Col lg={4} md={6} key={hotel.id} className="mb-4">
              <Card className="h-100 shadow-sm hotel-card">
                <Card.Header className="bg-primary text-white">
                  <div className="d-flex justify-content-between align-items-center">
                    <h5 className="mb-0">{hotel.name}</h5>
                    <Badge bg={hotel.is_active ? 'success' : 'secondary'}>
                      {hotel.is_active ? 'Active' : 'Inactive'}
                    </Badge>
                  </div>
                </Card.Header>
                
                <Card.Body className="d-flex flex-column">
                  <div className="mb-3">
                    <div className="text-warning mb-2">
                      {getStarRating(hotel.star_rating)}
                      {hotel.star_rating && (
                        <span className="text-muted ms-2">({hotel.star_rating} stars)</span>
                      )}
                    </div>
                    
                    <p className="text-muted mb-2">
                      <i className="fas fa-map-marker-alt me-2"></i>
                      {hotel.address}
                    </p>
                    
                    <p className="text-muted mb-2">
                      <i className="fas fa-user me-2"></i>
                      Contact: {hotel.contact_person}
                    </p>
                    
                    <p className="text-muted mb-2">
                      <i className="fas fa-phone me-2"></i>
                      {hotel.phone}
                    </p>
                    
                    <p className="text-muted mb-3">
                      <i className="fas fa-envelope me-2"></i>
                      {hotel.email}
                    </p>

                    {hotel.description && (
                      <p className="text-muted small">
                        {hotel.description.length > 100 
                          ? `${hotel.description.substring(0, 100)}...` 
                          : hotel.description}
                      </p>
                    )}
                  </div>

                  <div className="mt-auto">
                    <div className="d-flex justify-content-between align-items-center mb-3">
                      <div>
                        {hotel.website && (
                          <a
                            href={hotel.website}
                            target="_blank"
                            rel="noopener noreferrer"
                            className="btn btn-sm btn-outline-primary"
                          >
                            <i className="fas fa-external-link-alt me-1"></i>
                            Website
                          </a>
                        )}
                      </div>
                    </div>

                    <div className="d-grid gap-2">
                      <Link
                        to={`/hotels/${hotel.id}`}
                        className="btn btn-primary"
                      >
                        <i className="fas fa-eye me-2"></i>
                        View Details
                      </Link>
                      <Link
                        to={`/hotels/${hotel.id}/edit`}
                        className="btn btn-outline-secondary"
                      >
                        <i className="fas fa-edit me-2"></i>
                        Edit Hotel
                      </Link>
                    </div>
                  </div>
                </Card.Body>
              </Card>
            </Col>
          ))
        )}
      </Row>

      {/* Statistics */}
      {hotels.length > 0 && (
        <Row className="mt-5">
          <Col>
            <Card className="bg-light">
              <Card.Body>
                <Row className="text-center">
                  <Col md={3}>
                    <h4 className="text-primary">{hotels.length}</h4>
                    <p className="text-muted mb-0">Total Hotels</p>
                  </Col>
                  <Col md={4}>
                    <h4 className="text-success">{hotels.filter(h => h.is_active).length}</h4>
                    <p className="text-muted mb-0">Active Hotels</p>
                  </Col>
                  <Col md={4}>
                    <h4 className="text-secondary">{hotels.filter(h => !h.is_active).length}</h4>
                    <p className="text-muted mb-0">Inactive Hotels</p>
                  </Col>
                  <Col md={4}>
                    <h4 className="text-warning">
                      {hotels.filter(h => h.star_rating && h.star_rating >= 4).length}
                    </h4>
                    <p className="text-muted mb-0">4+ Star Hotels</p>
                  </Col>
                </Row>
              </Card.Body>
            </Card>
          </Col>
        </Row>
      )}

      <style>{`
        .hotel-card {
          transition: transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
        }
        
        .hotel-card:hover {
          transform: translateY(-5px);
          box-shadow: 0 8px 25px rgba(0,0,0,0.15) !important;
        }
        
        .btn-group .btn {
          flex: 1;
        }
      `}</style>

      {/* Import Modal */}
      <Modal show={showImportModal} onHide={() => setShowImportModal(false)} size="lg">
        <Modal.Header closeButton>
          <Modal.Title>
            <i className="fas fa-upload me-2"></i>
            Import Hotels from CSV
          </Modal.Title>
        </Modal.Header>
        <Modal.Body>
          <div className="mb-3">
            <p className="text-muted">
              Upload a CSV file to import multiple hotels at once.
              <Button
                variant="link"
                className="p-0 ms-1"
                onClick={handleDownloadSample}
              >
                Download sample CSV
              </Button> to see the required format.
            </p>
          </div>

          <Form.Group className="mb-3">
            <Form.Label>Select CSV File</Form.Label>
            <Form.Control
              type="file"
              accept=".csv"
              onChange={(e) => {
                const file = (e.target as HTMLInputElement).files?.[0];
                setImportFile(file || null);
                setImportResult(null);
              }}
            />
          </Form.Group>

          {importResult && (
            <Alert variant={importResult.errors?.length > 0 ? "warning" : "success"}>
              <h6>{importResult.message}</h6>
              {importResult.errors?.length > 0 && (
                <div>
                  <strong>Errors:</strong>
                  <ul className="mb-0 mt-2">
                    {importResult.errors.map((error: string, index: number) => (
                      <li key={index}>{error}</li>
                    ))}
                  </ul>
                </div>
              )}
            </Alert>
          )}
        </Modal.Body>
        <Modal.Footer>
          <Button variant="secondary" onClick={() => setShowImportModal(false)}>
            Cancel
          </Button>
          <Button
            variant="primary"
            onClick={handleImport}
            disabled={!importFile || importLoading}
          >
            {importLoading ? (
              <>
                <Spinner animation="border" size="sm" className="me-2" />
                Importing...
              </>
            ) : (
              <>
                <i className="fas fa-upload me-2"></i>
                Import Hotels
              </>
            )}
          </Button>
        </Modal.Footer>
      </Modal>
    </Container>
  );
};

export default HotelList;
