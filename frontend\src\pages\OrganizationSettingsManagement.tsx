import React, { useState, useEffect } from 'react';
import { Container, Row, Col, Card, Button, Alert, Form, Modal, Badge, Table } from 'react-bootstrap';
import { useAuth } from '../contexts/AuthContext';
import { organizationService, Organization, OrganizationSettings } from '../services/api';

const OrganizationSettingsManagement: React.FC = () => {
  const { isAuthenticated } = useAuth();
  const [organizations, setOrganizations] = useState<Organization[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');

  // Modal states
  const [showSettingsModal, setShowSettingsModal] = useState(false);
  const [selectedOrganization, setSelectedOrganization] = useState<Organization | null>(null);
  const [settings, setSettings] = useState<OrganizationSettings | null>(null);

  // Form state
  const [formData, setFormData] = useState({
    default_event_duration_hours: 8,
    default_registration_fee: 0,
    email_signature: '',
    primary_color: '#007bff',
    secondary_color: '#6c757d',
    send_welcome_emails: true,
    send_confirmation_emails: true,
    send_reminder_emails: true
  });

  useEffect(() => {
    if (isAuthenticated) {
      fetchOrganizations();
    }
  }, [isAuthenticated]);

  const fetchOrganizations = async () => {
    setLoading(true);
    try {
      const response = await organizationService.getOrganizations();
      const data = Array.isArray(response.data)
        ? response.data
        : (response.data as any).results || [];
      setOrganizations(data);
    } catch (err: any) {
      setError('Failed to fetch organizations');
    } finally {
      setLoading(false);
    }
  };

  const fetchSettings = async (organizationId: number) => {
    try {
      const response = await organizationService.getSettings(organizationId);
      setSettings(response.data);
      setFormData({
        default_event_duration_hours: response.data.default_event_duration_hours,
        default_registration_fee: response.data.default_registration_fee,
        email_signature: response.data.email_signature,
        primary_color: response.data.primary_color,
        secondary_color: response.data.secondary_color,
        send_welcome_emails: response.data.send_welcome_emails,
        send_confirmation_emails: response.data.send_confirmation_emails,
        send_reminder_emails: response.data.send_reminder_emails
      });
    } catch (err: any) {
      // Settings might not exist yet, use defaults
      setSettings(null);
      setFormData({
        default_event_duration_hours: 8,
        default_registration_fee: 0,
        email_signature: '',
        primary_color: '#007bff',
        secondary_color: '#6c757d',
        send_welcome_emails: true,
        send_confirmation_emails: true,
        send_reminder_emails: true
      });
    }
  };

  const handleManageSettings = async (organization: Organization) => {
    if (!organization.id) return;

    setSelectedOrganization(organization);
    await fetchSettings(organization.id);
    setShowSettingsModal(true);
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!selectedOrganization) return;

    setLoading(true);
    try {
      if (settings && selectedOrganization.id) {
        // Update existing settings
        await organizationService.updateSettings(selectedOrganization.id, formData);
        setSuccess('Settings updated successfully!');
      } else if (selectedOrganization.id) {
        // Create new settings
        await organizationService.createSettings(selectedOrganization.id, formData);
        setSuccess('Settings created successfully!');
      }
      
      setShowSettingsModal(false);
      fetchOrganizations();
    } catch (err: any) {
      setError(err.response?.data?.detail || 'Failed to save settings');
    } finally {
      setLoading(false);
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value, type } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? (e.target as HTMLInputElement).checked : 
               type === 'number' ? parseFloat(value) || 0 : value
    }));
  };

  return (
    <Container className="py-4">
      {/* Header */}
      <Row className="mb-4">
        <Col>
          <div className="d-flex justify-content-between align-items-center">
            <div>
              <h1 className="display-6 fw-bold text-primary">
                <i className="fas fa-cogs me-3"></i>
                Organization Settings
              </h1>
              <p className="lead text-muted">Configure settings for each organization</p>
            </div>
          </div>
        </Col>
      </Row>

      {/* Alerts */}
      {error && (
        <Alert variant="danger" onClose={() => setError('')} dismissible>
          {error}
        </Alert>
      )}
      {success && (
        <Alert variant="success" onClose={() => setSuccess('')} dismissible>
          {success}
        </Alert>
      )}

      {/* Organizations Table */}
      <Card>
        <Card.Header>
          <h5 className="mb-0">
            <i className="fas fa-building me-2"></i>
            Organizations ({organizations.length})
          </h5>
        </Card.Header>
        <Card.Body className="p-0">
          {loading ? (
            <div className="text-center py-4">
              <i className="fas fa-spinner fa-spin me-2"></i>
              Loading organizations...
            </div>
          ) : organizations.length === 0 ? (
            <div className="text-center py-5">
              <i className="fas fa-building fa-3x text-muted mb-3"></i>
              <h5 className="text-muted">No Organizations Found</h5>
              <p className="text-muted">Create organizations first to manage their settings.</p>
            </div>
          ) : (
            <Table responsive hover className="mb-0">
              <thead className="table-light">
                <tr>
                  <th>Organization</th>
                  <th>Contact</th>
                  <th>Location</th>
                  <th>Status</th>
                  <th>Settings</th>
                  <th>Actions</th>
                </tr>
              </thead>
              <tbody>
                {organizations.map((organization) => (
                  <tr key={organization.id}>
                    <td>
                      <div className="d-flex align-items-center">
                        {organization.logo && (
                          <img 
                            src={organization.logo} 
                            alt={organization.name}
                            style={{ width: '40px', height: '40px', objectFit: 'cover' }}
                            className="rounded me-3"
                          />
                        )}
                        <div>
                          <div className="fw-bold">{organization.name}</div>
                          <small className="text-muted">{organization.short_name}</small>
                        </div>
                      </div>
                    </td>
                    <td>
                      <div>
                        <div>{organization.email}</div>
                        <small className="text-muted">{organization.phone}</small>
                      </div>
                    </td>
                    <td>
                      <div>
                        <div>{organization.city}</div>
                        <small className="text-muted">{organization.country}</small>
                      </div>
                    </td>
                    <td>
                      <div className="d-flex flex-column gap-1">
                        <Badge bg={organization.is_active ? 'success' : 'secondary'}>
                          {organization.is_active ? 'Active' : 'Inactive'}
                        </Badge>
                        {organization.is_primary && (
                          <Badge bg="primary">Primary</Badge>
                        )}
                      </div>
                    </td>
                    <td>
                      {organization.settings ? (
                        <Badge bg="success">Configured</Badge>
                      ) : (
                        <Badge bg="warning">Not Configured</Badge>
                      )}
                    </td>
                    <td>
                      <Button
                        variant="outline-primary"
                        size="sm"
                        onClick={() => handleManageSettings(organization)}
                        disabled={loading}
                      >
                        <i className="fas fa-cogs me-1"></i>
                        Settings
                      </Button>
                    </td>
                  </tr>
                ))}
              </tbody>
            </Table>
          )}
        </Card.Body>
      </Card>

      {/* Settings Modal */}
      <Modal show={showSettingsModal} onHide={() => setShowSettingsModal(false)} size="lg">
        <Modal.Header closeButton>
          <Modal.Title>
            <i className="fas fa-cogs me-2"></i>
            Settings for {selectedOrganization?.name}
          </Modal.Title>
        </Modal.Header>
        <Form onSubmit={handleSubmit}>
          <Modal.Body>
            {/* Event Settings */}
            <Card className="mb-3">
              <Card.Header>
                <h6 className="mb-0">
                  <i className="fas fa-calendar me-2"></i>
                  Event Settings
                </h6>
              </Card.Header>
              <Card.Body>
                <Row>
                  <Col md={6}>
                    <Form.Group className="mb-3">
                      <Form.Label>Default Event Duration (hours)</Form.Label>
                      <Form.Control
                        type="number"
                        name="default_event_duration_hours"
                        value={formData.default_event_duration_hours}
                        onChange={handleInputChange}
                        min="1"
                        max="168"
                      />
                    </Form.Group>
                  </Col>
                  <Col md={6}>
                    <Form.Group className="mb-3">
                      <Form.Label>Default Registration Fee (ETB)</Form.Label>
                      <Form.Control
                        type="number"
                        name="default_registration_fee"
                        value={formData.default_registration_fee}
                        onChange={handleInputChange}
                        min="0"
                        step="0.01"
                      />
                    </Form.Group>
                  </Col>
                </Row>
              </Card.Body>
            </Card>

            {/* Email Settings */}
            <Card className="mb-3">
              <Card.Header>
                <h6 className="mb-0">
                  <i className="fas fa-envelope me-2"></i>
                  Email Settings
                </h6>
              </Card.Header>
              <Card.Body>
                <Form.Group className="mb-3">
                  <Form.Label>Email Signature</Form.Label>
                  <Form.Control
                    as="textarea"
                    rows={4}
                    name="email_signature"
                    value={formData.email_signature}
                    onChange={handleInputChange}
                    placeholder="Enter email signature..."
                  />
                </Form.Group>

                <Row>
                  <Col md={4}>
                    <Form.Group className="mb-3">
                      <Form.Check
                        type="checkbox"
                        name="send_welcome_emails"
                        checked={formData.send_welcome_emails}
                        onChange={handleInputChange}
                        label="Send Welcome Emails"
                      />
                    </Form.Group>
                  </Col>
                  <Col md={4}>
                    <Form.Group className="mb-3">
                      <Form.Check
                        type="checkbox"
                        name="send_confirmation_emails"
                        checked={formData.send_confirmation_emails}
                        onChange={handleInputChange}
                        label="Send Confirmation Emails"
                      />
                    </Form.Group>
                  </Col>
                  <Col md={4}>
                    <Form.Group className="mb-3">
                      <Form.Check
                        type="checkbox"
                        name="send_reminder_emails"
                        checked={formData.send_reminder_emails}
                        onChange={handleInputChange}
                        label="Send Reminder Emails"
                      />
                    </Form.Group>
                  </Col>
                </Row>
              </Card.Body>
            </Card>

            {/* Branding Settings */}
            <Card className="mb-3">
              <Card.Header>
                <h6 className="mb-0">
                  <i className="fas fa-palette me-2"></i>
                  Branding Settings
                </h6>
              </Card.Header>
              <Card.Body>
                <Row>
                  <Col md={6}>
                    <Form.Group className="mb-3">
                      <Form.Label>Primary Color</Form.Label>
                      <div className="d-flex align-items-center gap-2">
                        <Form.Control
                          type="color"
                          name="primary_color"
                          value={formData.primary_color}
                          onChange={handleInputChange}
                          style={{ width: '60px' }}
                        />
                        <Form.Control
                          type="text"
                          value={formData.primary_color}
                          onChange={handleInputChange}
                          name="primary_color"
                        />
                      </div>
                    </Form.Group>
                  </Col>
                  <Col md={6}>
                    <Form.Group className="mb-3">
                      <Form.Label>Secondary Color</Form.Label>
                      <div className="d-flex align-items-center gap-2">
                        <Form.Control
                          type="color"
                          name="secondary_color"
                          value={formData.secondary_color}
                          onChange={handleInputChange}
                          style={{ width: '60px' }}
                        />
                        <Form.Control
                          type="text"
                          value={formData.secondary_color}
                          onChange={handleInputChange}
                          name="secondary_color"
                        />
                      </div>
                    </Form.Group>
                  </Col>
                </Row>

                {/* Color Preview */}
                <div className="border rounded p-3 bg-light">
                  <h6>Color Preview:</h6>
                  <div className="d-flex gap-3">
                    <div 
                      className="px-3 py-2 rounded text-white fw-bold"
                      style={{ backgroundColor: formData.primary_color }}
                    >
                      Primary Color
                    </div>
                    <div 
                      className="px-3 py-2 rounded text-white fw-bold"
                      style={{ backgroundColor: formData.secondary_color }}
                    >
                      Secondary Color
                    </div>
                  </div>
                </div>
              </Card.Body>
            </Card>
          </Modal.Body>
          <Modal.Footer>
            <Button variant="secondary" onClick={() => setShowSettingsModal(false)}>
              Cancel
            </Button>
            <Button variant="primary" type="submit" disabled={loading}>
              {loading ? (
                <>
                  <i className="fas fa-spinner fa-spin me-2"></i>
                  Saving...
                </>
              ) : (
                <>
                  <i className="fas fa-save me-2"></i>
                  Save Settings
                </>
              )}
            </Button>
          </Modal.Footer>
        </Form>
      </Modal>
    </Container>
  );
};

export default OrganizationSettingsManagement;
