from rest_framework import serializers
from rest_framework_simplejwt.serializers import TokenObtainPairSerializer
from django.contrib.auth import authenticate
from .models import CustomUser, UserRole, UserSession, LoginAttempt, Developer

class UserRoleSerializer(serializers.ModelSerializer):
    class Meta:
        model = UserRole
        fields = '__all__'

class CustomUserSerializer(serializers.ModelSerializer):
    role_name = serializers.CharField(read_only=True)
    role_display_name = serializers.CharField(read_only=True)
    role_color = serializers.Char<PERSON>ield(read_only=True)
    full_name = serializers.CharField(source='get_full_name', read_only=True)
    
    class Meta:
        model = CustomUser
        fields = [
            'id', 'username', 'email', 'first_name', 'last_name', 'full_name',
            'phone', 'position', 'institution', 'profile_picture',
            'role', 'role_name', 'role_display_name', 'role_color',
            'is_active', 'is_staff', 'is_superuser', 'date_joined',
            'last_login', 'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'date_joined', 'created_at', 'updated_at']

class UserCreateSerializer(serializers.ModelSerializer):
    password = serializers.CharField(write_only=True, min_length=8)
    password_confirm = serializers.CharField(write_only=True)
    
    class Meta:
        model = CustomUser
        fields = [
            'username', 'email', 'first_name', 'last_name',
            'phone', 'position', 'institution', 'role',
            'password', 'password_confirm'
        ]
    
    def validate(self, attrs):
        if attrs['password'] != attrs['password_confirm']:
            raise serializers.ValidationError("Passwords don't match")
        return attrs
    
    def create(self, validated_data):
        validated_data.pop('password_confirm')
        password = validated_data.pop('password')
        user = CustomUser.objects.create_user(**validated_data)
        user.set_password(password)
        user.save()
        return user

class UserUpdateSerializer(serializers.ModelSerializer):
    class Meta:
        model = CustomUser
        fields = [
            'first_name', 'last_name', 'email', 'phone',
            'position', 'institution', 'profile_picture', 'role'
        ]

class PasswordChangeSerializer(serializers.Serializer):
    old_password = serializers.CharField(required=True)
    new_password = serializers.CharField(required=True, min_length=8)
    new_password_confirm = serializers.CharField(required=True)
    
    def validate(self, attrs):
        if attrs['new_password'] != attrs['new_password_confirm']:
            raise serializers.ValidationError("New passwords don't match")
        return attrs
    
    def validate_old_password(self, value):
        user = self.context['request'].user
        if not user.check_password(value):
            raise serializers.ValidationError("Old password is incorrect")
        return value

class CustomTokenObtainPairSerializer(TokenObtainPairSerializer):
    def validate(self, attrs):
        # Get IP address and user agent
        request = self.context.get('request')
        ip_address = self.get_client_ip(request)
        user_agent = request.META.get('HTTP_USER_AGENT', '')
        
        # Log login attempt
        LoginAttempt.objects.create(
            username=attrs['username'],
            ip_address=ip_address,
            user_agent=user_agent,
            success=False  # Will be updated if successful
        )
        
        try:
            data = super().validate(attrs)
            
            # Update login attempt as successful
            latest_attempt = LoginAttempt.objects.filter(
                username=attrs['username'],
                ip_address=ip_address
            ).order_by('-timestamp').first()
            if latest_attempt:
                latest_attempt.success = True
                latest_attempt.save()
            
            # Add user info to token
            user = self.user
            data['user'] = CustomUserSerializer(user).data
            
            # Create or update user session
            UserSession.objects.update_or_create(
                user=user,
                session_key=data['access'][:40],
                defaults={
                    'ip_address': ip_address,
                    'user_agent': user_agent,
                    'is_active': True
                }
            )
            
            # Update user's last login IP
            user.last_login_ip = ip_address
            user.save(update_fields=['last_login_ip'])
            
            return data
            
        except Exception as e:
            # Login failed, attempt already logged as failed
            raise e
    
    def get_client_ip(self, request):
        x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
        if x_forwarded_for:
            ip = x_forwarded_for.split(',')[0]
        else:
            ip = request.META.get('REMOTE_ADDR')
        return ip

class LoginSerializer(serializers.Serializer):
    username = serializers.CharField()
    password = serializers.CharField()
    
    def validate(self, attrs):
        username = attrs.get('username')
        password = attrs.get('password')
        
        if username and password:
            user = authenticate(username=username, password=password)
            if user:
                if not user.is_active:
                    raise serializers.ValidationError('User account is disabled.')
                attrs['user'] = user
                return attrs
            else:
                raise serializers.ValidationError('Invalid username or password.')
        else:
            raise serializers.ValidationError('Must include username and password.')

class UserSessionSerializer(serializers.ModelSerializer):
    user_name = serializers.CharField(source='user.get_full_name', read_only=True)
    
    class Meta:
        model = UserSession
        fields = [
            'id', 'user', 'user_name', 'session_key', 'ip_address',
            'user_agent', 'created_at', 'last_activity', 'is_active'
        ]

class LoginAttemptSerializer(serializers.ModelSerializer):
    class Meta:
        model = LoginAttempt
        fields = '__all__'

class DeveloperSerializer(serializers.ModelSerializer):
    photo_url = serializers.SerializerMethodField()

    class Meta:
        model = Developer
        fields = [
            'id', 'full_name', 'profession', 'linkedin_link',
            'photo', 'photo_url', 'bio', 'order'
        ]

    def get_photo_url(self, obj):
        if obj.photo:
            request = self.context.get('request')
            if request:
                return request.build_absolute_uri(obj.photo.url)
            return obj.photo.url
        return None
