<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>QR Code Debug Tool</title>
    <script src="https://unpkg.com/html5-qrcode" type="text/javascript"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .qr-reader {
            width: 100%;
            max-width: 400px;
            margin: 20px auto;
        }
        .result {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 15px;
            margin: 10px 0;
            word-break: break-all;
        }
        .success { border-color: #28a745; background: #d4edda; }
        .error { border-color: #dc3545; background: #f8d7da; }
        .info { border-color: #17a2b8; background: #d1ecf1; }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background: #0056b3; }
        button:disabled { background: #6c757d; cursor: not-allowed; }
        .debug-info {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 5px;
            padding: 15px;
            margin: 10px 0;
        }
        pre {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 5px;
            overflow-x: auto;
            white-space: pre-wrap;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 QR Code Debug Tool</h1>
        <p>This tool will help us debug what's inside the QR codes.</p>
        
        <div>
            <button id="start-btn" onclick="startScanning()">📷 Start Camera</button>
            <button id="stop-btn" onclick="stopScanning()" disabled>⏹️ Stop Camera</button>
        </div>
        
        <div id="qr-reader" class="qr-reader"></div>
        
        <div id="results"></div>
    </div>

    <script>
        let html5QrcodeScanner = null;
        let isScanning = false;

        function startScanning() {
            if (isScanning) return;
            
            const resultsDiv = document.getElementById('results');
            resultsDiv.innerHTML = '<div class="result info">Starting camera...</div>';
            
            html5QrcodeScanner = new Html5QrcodeScanner(
                "qr-reader",
                { 
                    fps: 10, 
                    qrbox: { width: 250, height: 250 },
                    aspectRatio: 1.0,
                    videoConstraints: {
                        facingMode: { exact: "environment" }
                    }
                },
                false
            );
            
            html5QrcodeScanner.render(onScanSuccess, onScanFailure);
            isScanning = true;
            
            document.getElementById('start-btn').disabled = true;
            document.getElementById('stop-btn').disabled = false;
        }

        function stopScanning() {
            if (html5QrcodeScanner && isScanning) {
                html5QrcodeScanner.clear();
                isScanning = false;
                document.getElementById('start-btn').disabled = false;
                document.getElementById('stop-btn').disabled = true;
                
                const resultsDiv = document.getElementById('results');
                resultsDiv.innerHTML += '<div class="result info">Camera stopped.</div>';
            }
        }

        function onScanSuccess(decodedText, decodedResult) {
            console.log('QR Code scanned:', decodedText);
            
            const resultsDiv = document.getElementById('results');
            
            // Clear previous results
            resultsDiv.innerHTML = '';
            
            // Show raw data
            resultsDiv.innerHTML += `
                <div class="result success">
                    <h3>✅ QR Code Scanned Successfully!</h3>
                    <h4>📋 Raw QR Data:</h4>
                    <pre>${decodedText}</pre>
                </div>
            `;
            
            // Analyze the data
            analyzeQRData(decodedText, resultsDiv);
            
            // Test verification API
            testVerificationAPI(decodedText, resultsDiv);
            
            // Stop scanning after successful scan
            stopScanning();
        }

        function onScanFailure(error) {
            // Ignore scan failures (they happen continuously)
        }

        function analyzeQRData(data, resultsDiv) {
            let analysis = '<div class="debug-info"><h4>🔍 QR Data Analysis:</h4><ul>';
            
            // Check data type
            analysis += `<li><strong>Data Length:</strong> ${data.length} characters</li>`;
            analysis += `<li><strong>Contains @:</strong> ${data.includes('@') ? '✅ Yes' : '❌ No'}</li>`;
            analysis += `<li><strong>Contains "email":</strong> ${data.toLowerCase().includes('email') ? '✅ Yes' : '❌ No'}</li>`;
            analysis += `<li><strong>Looks like JSON:</strong> ${data.trim().startsWith('{') ? '✅ Yes' : '❌ No'}</li>`;
            analysis += `<li><strong>Looks like URL:</strong> ${data.includes('http') ? '✅ Yes' : '❌ No'}</li>`;
            
            // Try to extract email
            const emailPattern = /([a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,})/;
            const emailMatch = data.match(emailPattern);
            if (emailMatch) {
                analysis += `<li><strong>Email Found:</strong> ✅ ${emailMatch[1]}</li>`;
            } else {
                analysis += `<li><strong>Email Found:</strong> ❌ No valid email detected</li>`;
            }
            
            // Try JSON parsing
            try {
                const jsonData = JSON.parse(data);
                analysis += `<li><strong>JSON Parse:</strong> ✅ Valid JSON</li>`;
                analysis += `<li><strong>JSON Keys:</strong> ${Object.keys(jsonData).join(', ')}</li>`;
            } catch (e) {
                analysis += `<li><strong>JSON Parse:</strong> ❌ Not valid JSON</li>`;
            }
            
            analysis += '</ul></div>';
            resultsDiv.innerHTML += analysis;
        }

        async function testVerificationAPI(data, resultsDiv) {
            resultsDiv.innerHTML += '<div class="result info">🔄 Testing verification API...</div>';
            
            try {
                const response = await fetch(`http://localhost:8000/api/participants/verify/?id=${encodeURIComponent(data)}`);
                const result = await response.json();
                
                if (response.ok) {
                    resultsDiv.innerHTML += `
                        <div class="result success">
                            <h4>✅ API Verification Success!</h4>
                            <ul>
                                <li><strong>Name:</strong> ${result.full_name || 'N/A'}</li>
                                <li><strong>Email:</strong> ${result.email || 'N/A'}</li>
                                <li><strong>Status:</strong> ${result.participant_status || 'N/A'}</li>
                                <li><strong>Can Check In:</strong> ${result.can_check_in ? '✅ Yes' : '❌ No'}</li>
                                <li><strong>Verification Status:</strong> ${result.verification_status || 'N/A'}</li>
                            </ul>
                        </div>
                    `;
                } else {
                    resultsDiv.innerHTML += `
                        <div class="result error">
                            <h4>❌ API Verification Failed</h4>
                            <p><strong>Error:</strong> ${result.error || 'Unknown error'}</p>
                            <p><strong>Status Code:</strong> ${response.status}</p>
                        </div>
                    `;
                }
            } catch (error) {
                resultsDiv.innerHTML += `
                    <div class="result error">
                        <h4>❌ API Request Failed</h4>
                        <p><strong>Error:</strong> ${error.message}</p>
                    </div>
                `;
            }
        }
    </script>
</body>
</html>
