/* Professional Full-Width Hero Section */
.hero-section-fullwidth {
  position: relative;
  width: 100vw;
  margin-left: calc(-50vw + 50%);
  min-height: 100vh;
  display: flex;
  align-items: center;
  overflow: hidden;
}

/* Shooting Stars */
.hero-section-fullwidth .shooting-star {
  position: absolute;
  width: 2px;
  height: 2px;
  background: linear-gradient(45deg, #ffd700, #ffffff);
  border-radius: 50%;
  box-shadow: 0 0 10px #ffd700;
  animation: shooting 3s linear infinite;
}

.hero-section-fullwidth .shooting-star:nth-child(1) {
  top: 20%;
  left: 10%;
  animation-delay: 0s;
  animation-duration: 3s;
}

.hero-section-fullwidth .shooting-star:nth-child(2) {
  top: 40%;
  left: 30%;
  animation-delay: 1s;
  animation-duration: 4s;
}

.hero-section-fullwidth .shooting-star:nth-child(3) {
  top: 60%;
  left: 70%;
  animation-delay: 2s;
  animation-duration: 2.5s;
}

.hero-section-fullwidth .shooting-star:nth-child(4) {
  top: 80%;
  left: 20%;
  animation-delay: 0.5s;
  animation-duration: 3.5s;
}

@keyframes shooting {
  0% {
    transform: translateX(0) translateY(0) scale(0);
    opacity: 0;
  }
  10% {
    transform: translateX(10px) translateY(-10px) scale(1);
    opacity: 1;
  }
  90% {
    transform: translateX(300px) translateY(-300px) scale(1);
    opacity: 1;
  }
  100% {
    transform: translateX(400px) translateY(-400px) scale(0);
    opacity: 0;
  }
}

.hero-background-fullwidth {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1;
}

.hero-bg-image {
  width: 100%;
  height: 100%;
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
}

.hero-overlay-professional {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg,
    rgba(102, 126, 234, 0.9) 0%,
    rgba(118, 75, 162, 0.8) 50%,
    rgba(40, 53, 147, 0.9) 100%);
  backdrop-filter: blur(2px);
  overflow: hidden;
}

/* Magical Patterns */
.hero-overlay-professional::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image:
    radial-gradient(2px 2px at 20px 30px, rgba(255, 215, 0, 0.8), transparent),
    radial-gradient(2px 2px at 40px 70px, rgba(255, 215, 0, 0.6), transparent),
    radial-gradient(1px 1px at 90px 40px, rgba(255, 215, 0, 0.9), transparent),
    radial-gradient(1px 1px at 130px 80px, rgba(255, 215, 0, 0.7), transparent),
    radial-gradient(2px 2px at 160px 30px, rgba(255, 215, 0, 0.8), transparent);
  background-repeat: repeat;
  background-size: 200px 100px;
  animation: sparkle 4s linear infinite;
}

.hero-overlay-professional::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image:
    radial-gradient(circle at 25% 25%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 75% 75%, rgba(255, 255, 255, 0.05) 0%, transparent 50%),
    radial-gradient(circle at 50% 50%, rgba(255, 215, 0, 0.1) 0%, transparent 50%);
  animation: float 6s ease-in-out infinite;
}

@keyframes sparkle {
  0%, 100% {
    transform: translateY(0px) rotate(0deg);
    opacity: 1;
  }
  50% {
    transform: translateY(-10px) rotate(180deg);
    opacity: 0.8;
  }
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px) scale(1);
  }
  50% {
    transform: translateY(-20px) scale(1.05);
  }
}

.hero-container-fullwidth {
  position: relative;
  z-index: 2;
  width: 100%;
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 2rem;
}

.hero-content-wrapper {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: 4rem;
  align-items: center;
  min-height: 100vh;
  padding: 0;
  padding-top: 80px;
}

.hero-main-content {
  color: white;
}

.hero-title-wrapper {
  margin-bottom: 3rem;
}

.hero-title-professional {
  font-size: 4rem;
  font-weight: 800;
  line-height: 1.1;
  margin-bottom: 1rem;
  background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  text-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
}

.hero-tagline {
  font-size: 1.3rem;
  font-weight: 300;
  color: rgba(255, 255, 255, 0.9);
  letter-spacing: 0.5px;
  border-left: 4px solid #ffd700;
  padding-left: 1.5rem;
  margin-left: 0.5rem;
}

.featured-event-professional {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 20px;
  padding: 2rem;
  margin: 2rem 0;
  transition: all 0.3s ease;
}

.featured-event-professional:hover {
  background: rgba(255, 255, 255, 0.15);
  transform: translateY(-5px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
}

.event-status-professional .status {
  display: inline-block;
  padding: 0.5rem 1rem;
  border-radius: 25px;
  font-size: 0.9rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.event-status-professional .status.upcoming {
  background: linear-gradient(135deg, #28a745, #20c997);
  color: white;
}

.event-status-professional .status.live {
  background: linear-gradient(135deg, #dc3545, #fd7e14);
  color: white;
  animation: pulse 2s infinite;
}

.event-status-professional .status.past {
  background: linear-gradient(135deg, #6c757d, #495057);
  color: white;
}

.event-title-professional {
  font-size: 2rem;
  font-weight: 700;
  color: white;
  margin: 1rem 0;
}

.event-description-professional {
  font-size: 1.1rem;
  color: rgba(255, 255, 255, 0.9);
  line-height: 1.6;
  margin-bottom: 1.5rem;
}

.event-meta-professional {
  display: flex;
  gap: 2rem;
  flex-wrap: wrap;
}

.event-meta-professional span {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: rgba(255, 255, 255, 0.8);
  font-size: 0.95rem;
}

.event-meta-professional i {
  color: #ffd700;
}

.hero-actions-professional {
  display: flex;
  gap: 1.5rem;
  margin-top: 2rem;
  flex-wrap: wrap;
}

.btn-professional {
  display: inline-flex;
  align-items: center;
  gap: 0.75rem;
  padding: 1rem 2rem;
  border-radius: 50px;
  text-decoration: none;
  font-weight: 600;
  font-size: 1rem;
  transition: all 0.3s ease;
  border: 2px solid transparent;
  position: relative;
  overflow: hidden;
}

.btn-primary-professional {
  background: linear-gradient(135deg, #ffd700, #ffed4e);
  color: #2c3e50;
  box-shadow: 0 8px 25px rgba(255, 215, 0, 0.3);
}

.btn-primary-professional:hover {
  transform: translateY(-3px);
  box-shadow: 0 12px 35px rgba(255, 215, 0, 0.4);
  color: #2c3e50;
}

.btn-secondary-professional {
  background: transparent;
  color: white;
  border: 2px solid rgba(255, 255, 255, 0.3);
  backdrop-filter: blur(10px);
}

.btn-secondary-professional:hover {
  background: rgba(255, 255, 255, 0.1);
  border-color: rgba(255, 255, 255, 0.5);
  transform: translateY(-3px);
  color: white;
}

.hero-side-content {
  display: flex;
  align-items: center;
  justify-content: center;
}

.hero-events-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 2rem;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 25px;
  padding: 2.5rem;
  position: relative;
  overflow: hidden;
}

.hero-events-grid::before {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: radial-gradient(circle, rgba(255, 215, 0, 0.1) 0%, transparent 70%);
  animation: rotate 20s linear infinite;
}

.event-stat-item {
  text-align: center;
  color: white;
  position: relative;
  z-index: 2;
}

.event-stat-number {
  font-size: 2.5rem;
  font-weight: 800;
  background: linear-gradient(135deg, #ffd700, #ffed4e);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  margin-bottom: 0.5rem;
  text-shadow: 0 0 20px rgba(255, 215, 0, 0.3);
}

.event-stat-label {
  font-size: 0.9rem;
  color: rgba(255, 255, 255, 0.8);
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

@keyframes rotate {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

@keyframes pulse {
  0% { opacity: 1; }
  50% { opacity: 0.7; }
  100% { opacity: 1; }
}

/* Responsive Design for Hero */
@media (max-width: 1200px) {
  .hero-content-wrapper {
    grid-template-columns: 1fr;
    gap: 3rem;
    text-align: center;
  }

  .hero-title-professional {
    font-size: 3rem;
  }
}

@media (max-width: 768px) {
  .hero-section-fullwidth {
    margin-left: calc(-50vw + 50%);
    width: 100vw;
  }

  .hero-container-fullwidth {
    padding: 0 1rem;
  }

  .hero-title-professional {
    font-size: 2.5rem;
  }

  .hero-tagline {
    font-size: 1.1rem;
  }

  .hero-events-grid {
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
    padding: 1.5rem;
  }

  .event-stat-number {
    font-size: 2rem;
  }

  .hero-actions-professional {
    justify-content: center;
  }
}

@media (max-width: 576px) {
  .hero-events-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .event-meta-professional {
    flex-direction: column;
    gap: 1rem;
  }

  .btn-professional {
    padding: 0.8rem 1.5rem;
    font-size: 0.9rem;
  }
}

/* Gondar City Section Styles */
.gondar-city-section {
  padding: 100px 0;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  position: relative;
  overflow: hidden;
}

.gondar-city-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="%23000" opacity="0.02"/><circle cx="75" cy="75" r="1" fill="%23000" opacity="0.02"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
  pointer-events: none;
}

.city-hero-image img {
  transition: transform 0.3s ease;
  border-radius: 20px !important;
}

.city-hero-image:hover img {
  transform: scale(1.05);
}

.city-content {
  padding: 40px;
}

.city-title {
  font-size: 2.5rem;
  font-weight: 700;
  color: #2c3e50;
  margin-bottom: 20px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.city-description {
  font-size: 1.1rem;
  line-height: 1.8;
  color: #6c757d;
  margin-bottom: 30px;
}

.city-highlights {
  display: grid;
  gap: 15px;
}

.highlight-item {
  display: flex;
  align-items: center;
  gap: 15px;
  padding: 15px;
  background: rgba(255, 255, 255, 0.8);
  border-radius: 12px;
  border-left: 4px solid #667eea;
  transition: all 0.3s ease;
}

.highlight-item:hover {
  transform: translateX(10px);
  box-shadow: 0 5px 20px rgba(102, 126, 234, 0.2);
}

.highlight-item i {
  font-size: 1.5rem;
  width: 30px;
  text-align: center;
}

.highlight-item span {
  font-weight: 600;
  color: #2c3e50;
}

/* Attractions Grid */
.attractions-grid {
  margin-top: 60px;
}

.attraction-card {
  background: white;
  border-radius: 20px;
  overflow: hidden;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  height: 100%;
}

.attraction-card:hover {
  transform: translateY(-10px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
}

.attraction-image {
  position: relative;
  height: 250px;
  overflow: hidden;
}

.attraction-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.attraction-card:hover .attraction-image img {
  transform: scale(1.1);
}

.attraction-overlay {
  position: absolute;
  top: 20px;
  right: 20px;
  width: 50px;
  height: 50px;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  backdrop-filter: blur(10px);
}

.attraction-overlay i {
  font-size: 1.5rem;
  color: #667eea;
}

.attraction-content {
  padding: 30px;
}

.attraction-content h4 {
  font-size: 1.5rem;
  font-weight: 700;
  color: #2c3e50;
  margin-bottom: 15px;
}

.attraction-content p {
  color: #6c757d;
  line-height: 1.6;
  margin-bottom: 20px;
}

.attraction-meta {
  display: flex;
  gap: 20px;
  flex-wrap: wrap;
}

.attraction-meta span {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 0.9rem;
  color: #667eea;
  font-weight: 600;
}

.attraction-meta i {
  font-size: 0.8rem;
}

/* University Section Styles */
.university-section {
  padding: 100px 0;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  position: relative;
  overflow: hidden;
}

.university-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="dots" width="20" height="20" patternUnits="userSpaceOnUse"><circle cx="10" cy="10" r="1" fill="%23fff" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23dots)"/></svg>');
  pointer-events: none;
}

.university-section .section-title {
  color: white;
}

.university-section .section-subtitle {
  color: rgba(255, 255, 255, 0.9);
}

/* University Stats */
.university-stats {
  margin: 60px 0;
}

.stat-card {
  background: rgba(255, 255, 255, 0.15);
  backdrop-filter: blur(15px);
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: 20px;
  padding: 30px 15px;
  text-align: center;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  height: 100%;
  position: relative;
  overflow: hidden;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  cursor: pointer;
}

.stat-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.6s ease;
}

.stat-card:hover::before {
  left: 100%;
}

.stat-card:hover {
  transform: translateY(-10px) scale(1.02);
  background: rgba(255, 255, 255, 0.25);
  box-shadow: 0 15px 45px rgba(0, 0, 0, 0.2);
  border-color: rgba(255, 215, 0, 0.5);
}

.stat-icon {
  width: 70px;
  height: 70px;
  background: rgba(255, 215, 0, 0.2);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 20px;
  transition: all 0.3s ease;
  animation: iconFloat 3s ease-in-out infinite;
  border: 2px solid rgba(255, 215, 0, 0.3);
}

.stat-card:hover .stat-icon {
  background: rgba(255, 215, 0, 0.3);
  transform: scale(1.15) rotate(5deg);
  border-color: rgba(255, 215, 0, 0.6);
  animation-play-state: paused;
}

.stat-icon i {
  font-size: 2rem;
  color: #ffd700;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);
}

.stat-card:hover .stat-icon i {
  color: #ffed4e;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
}

@keyframes iconFloat {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-5px);
  }
}

.stat-number {
  font-size: 3.2rem;
  font-weight: 800;
  color: #ffed4e;
  margin-bottom: 15px;
  line-height: 1;
  text-shadow: 3px 3px 8px rgba(0, 0, 0, 0.9);
  transition: all 0.3s ease;
  position: relative;
  animation: countUp 2s ease-out;
}

.stat-card:hover .stat-number {
  transform: scale(1.1);
  color: #ffd700;
  text-shadow: 4px 4px 10px rgba(0, 0, 0, 1);
}

.stat-label {
  font-size: 1.1rem;
  color: #ffffff;
  font-weight: 700;
  text-transform: uppercase;
  letter-spacing: 1.2px;
  text-shadow: 2px 2px 6px rgba(0, 0, 0, 0.9);
  transition: all 0.3s ease;
}

.stat-card:hover .stat-label {
  color: #ffed4e;
  transform: translateY(-2px);
  text-shadow: 3px 3px 8px rgba(0, 0, 0, 1);
}

@keyframes countUp {
  from {
    opacity: 0;
    transform: translateY(20px) scale(0.8);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

/* Animation for stats */
.animated-stat {
  opacity: 0;
  transform: translateY(30px);
  animation: fadeInUp 0.8s ease forwards;
}

@keyframes fadeInUp {
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animated-stat:nth-child(1) { animation-delay: 0.1s; }
.animated-stat:nth-child(2) { animation-delay: 0.2s; }
.animated-stat:nth-child(3) { animation-delay: 0.3s; }
.animated-stat:nth-child(4) { animation-delay: 0.4s; }
.animated-stat:nth-child(5) { animation-delay: 0.5s; }

/* University Highlights */
.university-highlights {
  margin-top: 60px;
}

.highlight-card {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 20px;
  overflow: hidden;
  transition: all 0.3s ease;
  height: 100%;
}

.highlight-card:hover {
  transform: translateY(-10px);
  background: rgba(255, 255, 255, 0.15);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
}

.highlight-image {
  height: 200px;
  overflow: hidden;
}

.highlight-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.highlight-card:hover .highlight-image img {
  transform: scale(1.1);
}

.highlight-content {
  padding: 30px;
}

.highlight-content h4 {
  font-size: 1.5rem;
  font-weight: 700;
  color: white;
  margin-bottom: 15px;
}

.highlight-content p {
  color: rgba(255, 255, 255, 0.9);
  line-height: 1.6;
}

/* Responsive Design */
@media (max-width: 768px) {
  .city-title {
    font-size: 2rem;
  }
  
  .city-content {
    padding: 20px;
  }
  
  .attraction-content {
    padding: 20px;
  }
  
  .stat-number {
    font-size: 2.5rem;
  }
  
  .highlight-content {
    padding: 20px;
  }
}

@media (max-width: 576px) {
  .gondar-city-section,
  .university-section {
    padding: 60px 0;
  }
  
  .city-title {
    font-size: 1.8rem;
  }
  
  .attraction-meta {
    flex-direction: column;
    gap: 10px;
  }
  
  .stat-number {
    font-size: 2rem;
  }
}

/* Developer Credits Section */
.developer-credits-section {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  position: relative;
  overflow: hidden;
}

.developer-credits-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.1);
  z-index: 1;
}

.developer-credits-section .container {
  position: relative;
  z-index: 2;
}

.developer-title {
  font-size: 2.5rem;
  font-weight: 700;
  margin-bottom: 3rem;
  text-align: center;
  background: linear-gradient(135deg, #ffd700, #ffed4e);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.developers-grid {
  display: flex;
  justify-content: center;
  gap: 2rem;
  flex-wrap: wrap;
}

.developer-card {
  position: relative;
  cursor: pointer;
  transition: all 0.3s ease;
}

.developer-photo-container {
  position: relative;
  width: 120px;
  height: 120px;
  margin: 0 auto;
  border-radius: 50%;
  overflow: hidden;
  border: 4px solid rgba(255, 255, 255, 0.3);
  transition: all 0.3s ease;
}

.developer-photo-container:hover {
  transform: scale(1.1);
  border-color: #ffd700;
  box-shadow: 0 10px 30px rgba(255, 215, 0, 0.3);
}

.developer-photo {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.developer-avatar {
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, #667eea, #764ba2);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 2rem;
  font-weight: bold;
  color: white;
}

.developer-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.9);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: all 0.3s ease;
  border-radius: 50%;
}

.developer-photo-container:hover .developer-overlay {
  opacity: 1;
}

.developer-info {
  text-align: center;
  padding: 1rem;
}

.developer-info h5 {
  font-size: 1rem;
  font-weight: 600;
  margin-bottom: 0.25rem;
  color: #ffd700;
}

.developer-info p {
  font-size: 0.8rem;
  margin-bottom: 0.5rem;
  color: rgba(255, 255, 255, 0.8);
}

.developer-info small {
  font-size: 0.7rem;
  color: rgba(255, 255, 255, 0.6);
  display: block;
  margin-bottom: 0.5rem;
}

.linkedin-link {
  color: #0077b5;
  text-decoration: none;
  font-size: 0.8rem;
  font-weight: 500;
  transition: all 0.3s ease;
}

.linkedin-link:hover {
  color: #ffd700;
  text-decoration: underline;
}

.default-credits p {
  font-size: 1.2rem;
  margin-bottom: 2rem;
  color: rgba(255, 255, 255, 0.9);
}

.default-developers {
  display: flex;
  justify-content: center;
  gap: 2rem;
  flex-wrap: wrap;
}

.default-developers .developer-card {
  text-align: center;
}

.default-developers .developer-avatar {
  width: 80px;
  height: 80px;
  margin: 0 auto 1rem;
  font-size: 1.5rem;
}

.developer-name {
  font-size: 1rem;
  font-weight: 500;
  color: rgba(255, 255, 255, 0.9);
}

/* Events Section */
.events-section {
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  position: relative;
}

.events-section .section-title {
  font-size: 3rem;
  font-weight: 700;
  color: #2c3e50;
  margin-bottom: 1rem;
  background: linear-gradient(135deg, #667eea, #764ba2);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.events-section .section-subtitle {
  font-size: 1.2rem;
  color: #6c757d;
  margin-bottom: 3rem;
}

.event-card {
  background: white;
  border-radius: 20px;
  overflow: hidden;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.event-card:hover {
  transform: translateY(-10px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
}

.event-image {
  position: relative;
  height: 200px;
  overflow: hidden;
}

.event-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.event-card:hover .event-image img {
  transform: scale(1.05);
}

.event-status {
  position: absolute;
  top: 15px;
  right: 15px;
  z-index: 2;
}

.event-status .badge {
  font-size: 0.8rem;
  padding: 0.5rem 1rem;
  border-radius: 20px;
}

.event-content {
  padding: 1.5rem;
  flex: 1;
  display: flex;
  flex-direction: column;
}

.event-date {
  color: #667eea;
  font-weight: 600;
  font-size: 0.9rem;
  margin-bottom: 1rem;
}

.event-title {
  font-size: 1.3rem;
  font-weight: 700;
  color: #2c3e50;
  margin-bottom: 1rem;
  line-height: 1.3;
}

.event-description {
  color: #6c757d;
  font-size: 0.95rem;
  line-height: 1.6;
  margin-bottom: 1.5rem;
  flex: 1;
}

.event-meta {
  margin-bottom: 1.5rem;
}

.event-location,
.event-participants {
  color: #6c757d;
  font-size: 0.9rem;
  margin-bottom: 0.5rem;
}

.event-location i,
.event-participants i {
  color: #667eea;
  width: 16px;
}

.event-actions {
  margin-top: auto;
}

.event-actions .btn {
  border-radius: 20px;
  font-weight: 600;
  padding: 0.5rem 1.5rem;
}

.no-events {
  padding: 4rem 2rem;
}

.no-events i {
  color: #dee2e6;
}

@media (max-width: 768px) {
  .events-section .section-title {
    font-size: 2.5rem;
  }

  .event-image {
    height: 180px;
  }

  .event-content {
    padding: 1rem;
  }

  .event-title {
    font-size: 1.2rem;
  }
}
