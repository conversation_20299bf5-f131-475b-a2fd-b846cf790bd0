#!/usr/bin/env python
"""
Script to fix Event ID 6 schedule locations and ensure proper date formatting
"""
import os
import sys
import django

# Setup Django
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'event_management.settings')
django.setup()

from events.models import EventSchedule

def fix_schedule_locations():
    """Fix schedule locations for Event ID 6"""
    
    print("🔧 Fixing schedule locations for Event ID 6...")
    
    # Get all schedules for event 6
    schedules = EventSchedule.objects.filter(event_id=6)
    
    print(f"Found {schedules.count()} schedule items")
    
    # Location mapping
    location_mapping = {
        'Conference Center Lobby': 'Atse Tewodros Campus - President Hall Lobby',
        'Main Conference Hall': 'Atse Tewodros Campus - President Hall',
        'Conference Center Terrace': 'Atse Tewodros Campus - President Hall Terrace',
        'University Dining Hall': 'Atse Tewodros Campus - University Dining Hall',
    }
    
    updated_count = 0
    
    for schedule in schedules:
        old_location = schedule.location
        
        # Update location based on mapping
        if old_location in location_mapping:
            schedule.location = location_mapping[old_location]
        elif 'Conference' in old_location or 'Hall' in old_location:
            # Default any conference/hall location to President <PERSON>
            schedule.location = 'Atse Tewodros Campus - President Hall'
        
        # Save if location changed
        if schedule.location != old_location:
            schedule.save()
            updated_count += 1
            print(f"   ✓ Updated: {old_location} → {schedule.location}")
    
    print(f"✅ Updated {updated_count} schedule locations")
    
    # Display summary of all locations
    print("\n📍 Current schedule locations:")
    unique_locations = schedules.values_list('location', flat=True).distinct()
    for location in unique_locations:
        count = schedules.filter(location=location).count()
        print(f"   • {location}: {count} sessions")

def verify_schedule_dates():
    """Verify schedule dates are properly formatted"""
    
    print("\n📅 Verifying schedule dates...")
    
    schedules = EventSchedule.objects.filter(event_id=6).order_by('start_time')
    
    for schedule in schedules[:5]:  # Check first 5
        print(f"   ✓ {schedule.start_time.strftime('%Y-%m-%d %H:%M')} - {schedule.end_time.strftime('%Y-%m-%d %H:%M')}: {schedule.title}")
    
    print(f"   ... and {schedules.count() - 5} more items")

if __name__ == "__main__":
    fix_schedule_locations()
    verify_schedule_dates()
    print("\n🎉 Schedule locations and dates verified successfully!")
