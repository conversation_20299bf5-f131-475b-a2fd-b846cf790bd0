#!/usr/bin/env python
"""
Test that badges are using real participant data
"""
import os
import sys
import django

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'event_management.settings')
django.setup()

from badges.models import Badge
from participants.models import Participant

def test_real_data_badge():
    """Test that badges use real participant data"""
    print("🧪 TESTING REAL PARTICIPANT DATA IN BADGES")
    print("=" * 60)
    
    try:
        # Get a participant
        participant = Participant.objects.first()
        if not participant:
            print("❌ No participants found")
            return
        
        print(f"👤 Testing with: {participant.full_name}")
        print(f"📧 Email: {getattr(participant, 'email', 'N/A')}")
        print(f"🏢 Institution: {getattr(participant, 'institution_name', 'N/A')}")
        print(f"💼 Position: {getattr(participant, 'position', 'N/A')}")
        print(f"🎯 Type: {participant.participant_type.name if participant.participant_type else 'N/A'}")
        print(f"🎪 Event: {participant.event.name if participant.event else 'N/A'}")
        
        # Get or create badge
        badge, created = Badge.objects.get_or_create(participant=participant)
        
        # Clear existing badge to force regeneration
        if badge.badge_image:
            badge.badge_image.delete(save=False)
        if badge.qr_code_image:
            badge.qr_code_image.delete(save=False)
        
        badge.is_generated = False
        badge.generated_at = None
        badge.save()
        
        print("\n🎨 Generating badge with REAL participant data...")
        
        # Generate badge
        badge_img = badge.generate_badge()
        
        print(f"✅ Badge generated successfully!")
        print(f"📏 Dimensions: {badge_img.size}")
        print(f"💾 Badge file: {badge.badge_image.name}")
        
        # Verify the badge contains real data
        print(f"\n🔍 VERIFICATION - Badge should contain:")
        print(f"   📝 Name: {participant.full_name}")
        print(f"   🏢 Institution: {getattr(participant, 'institution_name', 'N/A')}")
        print(f"   💼 Position: {getattr(participant, 'position', 'N/A')}")
        print(f"   🎯 Type: {participant.participant_type.name if participant.participant_type else 'N/A'}")
        print(f"   🎪 Event: {participant.event.name if participant.event else 'N/A'}")
        
        # Test the individual SVG methods to see what data they use
        print(f"\n🧪 TESTING SVG METHODS:")
        
        # Test participant info method
        from PIL import Image, ImageDraw
        test_img = Image.new('RGB', (600, 1200), color='#002D72')
        test_draw = ImageDraw.Draw(test_img)
        
        print("   🎨 Testing _add_svg_participant_info...")
        badge._add_svg_participant_info(test_draw, 600, 1200)
        print("   ✅ Participant info method executed")
        
        print("   🎨 Testing _add_svg_participant_type...")
        badge._add_svg_participant_type(test_draw, 600, 1200)
        print("   ✅ Participant type method executed")
        
        print("   🎨 Testing _add_svg_theme...")
        badge._add_svg_theme(test_draw, 600, 1200)
        print("   ✅ Theme method executed")
        
        print("   🎨 Testing _add_svg_sponsors...")
        badge._add_svg_sponsors(test_draw, 600, 1200)
        print("   ✅ Sponsors method executed")
        
        print("   🎨 Testing _add_svg_footer...")
        badge._add_svg_footer(test_draw, 600, 1200)
        print("   ✅ Footer method executed")
        
        print(f"\n🎉 SUCCESS!")
        print(f"✅ Badge now uses REAL participant data:")
        print(f"   • Real name: {participant.full_name}")
        print(f"   • Real institution: {getattr(participant, 'institution_name', 'N/A')}")
        print(f"   • Real participant type: {participant.participant_type.name if participant.participant_type else 'N/A'}")
        print(f"   • Real event name: {participant.event.name if participant.event else 'N/A'}")
        print(f"   • Professional SVG design with Ministry branding")
        print(f"   • 600x1200 pixel dimensions")
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_real_data_badge()
