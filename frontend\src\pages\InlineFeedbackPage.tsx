import React, { useState, useEffect } from 'react';
import { useParams, useNavigate, useLocation } from 'react-router-dom';
import { Container, Row, Col, Card, Form, <PERSON>ton, Al<PERSON>, Spinner, ProgressBar, Badge, Modal } from 'react-bootstrap';
import { eventService, feedbackService, Event, EventFeedback } from '../services/api';
import { useToast } from '../contexts/ToastContext';

const InlineFeedbackPage: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const location = useLocation();
  const { showToast } = useToast();
  
  const [event, setEvent] = useState<Event | null>(null);
  const [loading, setLoading] = useState(true);
  const [submitting, setSubmitting] = useState(false);
  const [error, setError] = useState('');
  const [currentStep, setCurrentStep] = useState(1);

  const [existingFeedback, setExistingFeedback] = useState<EventFeedback | null>(null);

  const totalSteps = 6;

  // Form data state
  const [formData, setFormData] = useState({
    // Participant information
    participant_name: '',
    participant_email: '',
    institution_name: '',
    position_title: '',
    
    // Overall experience
    overall_satisfaction: 5,
    met_expectations: 'yes' as 'yes' | 'somewhat' | 'no',
    most_valuable_aspect: '',
    improvement_suggestions: '',
    
    // Sessions & content
    session_relevance: 5,
    most_valuable_sessions: '',
    future_topics_suggestions: '',
    
    // Speakers & moderators
    speaker_quality: 5,
    speaker_comments: '',
    
    // Logistics & organization
    venue_facilities: 5,
    technical_setup: 5,
    time_management: 5,
    transportation_accessibility: 5,
    pre_event_communication: 5,
    logistics_comments: '',
    
    // Networking
    sufficient_networking: 'yes' as 'yes' | 'somewhat' | 'no',
    networking_improvements: '',
    
    // Future suggestions
    future_topics: '',
    additional_feedback: '',
    
    // Consent
    consent_given: false,
    is_anonymous: false,
  });

  const [uploadedFile, setUploadedFile] = useState<File | null>(null);

  useEffect(() => {
    if (id) {
      fetchEvent();
    }
  }, [id]);

  useEffect(() => {
    if (event && formData.participant_email) {
      checkExistingFeedback();
    }
  }, [event, formData.participant_email]);

  const fetchEvent = async () => {
    try {
      setLoading(true);
      const response = await eventService.getPublicEvent(parseInt(id!));
      setEvent(response.data);
    } catch (error: any) {
      console.error('Error fetching event:', error);
      setError('Event not found or not accessible.');
    } finally {
      setLoading(false);
    }
  };

  const checkExistingFeedback = async () => {
    if (!event || !formData.participant_email) return;
    
    try {
      const response = await feedbackService.checkExistingFeedback(
        event.id,
        undefined,
        formData.participant_email
      );
      
      if (response.data.exists) {
        const feedback = await feedbackService.getFeedback(response.data.feedback_id);
        setExistingFeedback(feedback.data);
      }
    } catch (error) {
      console.error('Error checking existing feedback:', error);
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value, type } = e.target;
    
    if (type === 'checkbox') {
      const checked = (e.target as HTMLInputElement).checked;
      setFormData(prev => ({ ...prev, [name]: checked }));
    } else if (type === 'range' || name.includes('satisfaction') || name.includes('quality') || name.includes('facilities') || name.includes('setup') || name.includes('management') || name.includes('accessibility') || name.includes('communication') || name.includes('relevance')) {
      setFormData(prev => ({ ...prev, [name]: parseInt(value) }));
    } else {
      setFormData(prev => ({ ...prev, [name]: value }));
    }
  };

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0] || null;
    setUploadedFile(file);
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!formData.consent_given) {
      setError('Please provide consent to submit your feedback.');
      return;
    }

    if (!formData.most_valuable_aspect.trim()) {
      setError('Please provide information about the most valuable aspect of the event.');
      return;
    }

    setSubmitting(true);
    setError('');

    try {
      const submitData = new FormData();
      
      // Add event data
      submitData.append('event', event!.id.toString());

      // Don't send participant field - this allows unregistered participants
      // The backend will handle setting participant to null

      // Add form data
      Object.entries(formData).forEach(([key, value]) => {
        if (value !== null && value !== undefined) {
          submitData.append(key, value.toString());
        }
      });
      
      // Add file if uploaded
      if (uploadedFile) {
        submitData.append('uploaded_file', uploadedFile);
      }

      const response = await feedbackService.createFeedback(submitData);
      
      showToast({
        type: 'success',
        title: 'Thank You for Your Participation!',
        message: 'Your feedback is invaluable in shaping the future of higher education conferences in Ethiopia. Together, we can continue to strengthen academic excellence and collaboration.'
      });
      
      // Redirect to success page or event details
      navigate(`/events/${id}`, {
        state: { feedbackSubmitted: true }
      });
      
    } catch (error: any) {
      console.error('Error submitting feedback:', error);
      setError(error.response?.data?.detail || 'Failed to submit feedback. Please try again.');
      showToast({
        type: 'error',
        title: 'Submission Failed',
        message: 'Failed to submit feedback. Please try again.'
      });
    } finally {
      setSubmitting(false);
    }
  };

  const nextStep = () => {
    if (currentStep < totalSteps) {
      setCurrentStep(currentStep + 1);
    }
  };

  const prevStep = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1);
    }
  };



  const renderStarRating = (name: string, value: number, label: string) => (
    <Form.Group className="mb-3">
      <Form.Label>{label} *</Form.Label>
      <div className="d-flex align-items-center">
        <Form.Range
          name={name}
          min={1}
          max={5}
          value={value}
          onChange={handleInputChange}
          className="me-3"
        />
        <div className="star-display">
          {'⭐'.repeat(value)} ({value}/5)
        </div>
      </div>
    </Form.Group>
  );

  if (loading) {
    return (
      <Container className="py-5">
        <Row className="justify-content-center">
          <Col lg={8} className="text-center">
            <Spinner animation="border" variant="primary" />
            <p className="mt-3 text-muted">Loading event information...</p>
          </Col>
        </Row>
      </Container>
    );
  }

  if (error && !event) {
    return (
      <Container className="py-5">
        <Row className="justify-content-center">
          <Col lg={8}>
            <Alert variant="danger" className="text-center">
              <i className="fas fa-exclamation-triangle fa-2x mb-3"></i>
              <h4>Event Not Found</h4>
              <p>{error}</p>
              <Button variant="primary" onClick={() => navigate('/')}>
                <i className="fas fa-home me-2"></i>
                Return to Home
              </Button>
            </Alert>
          </Col>
        </Row>
      </Container>
    );
  }

  if (existingFeedback) {
    return (
      <Container className="py-5">
        <Row className="justify-content-center">
          <Col lg={8}>
            <Card className="shadow-sm text-center">
              <Card.Body className="p-5">
                <div className="mb-4">
                  <i className="fas fa-check-circle text-success" style={{ fontSize: '4rem' }}></i>
                </div>
                <h2 className="text-success mb-3">Feedback Already Submitted</h2>
                <p className="text-muted mb-4">
                  You have already provided feedback for <strong>{event?.name}</strong>.
                  Thank you for your valuable input!
                </p>
                <div className="d-flex justify-content-center">
                  <Button variant="primary" onClick={() => navigate(`/events/${id}`)}>
                    <i className="fas fa-arrow-left me-2"></i>
                    Back to Event
                  </Button>
                </div>
              </Card.Body>
            </Card>
          </Col>
        </Row>
      </Container>
    );
  }

  return (
    <Container className="py-4">
      <Row className="justify-content-center">
        <Col lg={10}>
          {/* Header */}
          <div className="text-center mb-4">
            <div className="d-flex justify-content-between align-items-center mb-3">
              <Button
                variant="outline-secondary"
                onClick={() => navigate(`/events/${id}`)}
                className="d-flex align-items-center"
              >
                <i className="fas fa-arrow-left me-2"></i>
                Back to Event
              </Button>
            </div>
            
            <h1 className="display-6 fw-bold text-primary mb-2">
              <i className="fas fa-comment-alt me-3"></i>
              Event Feedback
            </h1>
            <p className="lead text-muted mb-3">
              Share your experience with <strong>{event?.name}</strong>
            </p>
            
            {/* Event Info Badge */}
            <div className="bg-light rounded p-3 d-inline-block mb-4">
              <div className="d-flex align-items-center justify-content-center gap-4 text-muted">
                <div>
                  <i className="fas fa-calendar me-2"></i>
                  {new Date(event?.start_date || '').toLocaleDateString()} - {new Date(event?.end_date || '').toLocaleDateString()}
                </div>
                <div>
                  <i className="fas fa-map-marker-alt me-2"></i>
                  {event?.location}, {event?.city}
                </div>
              </div>
            </div>
          </div>

          {/* Progress Bar */}
          <div className="mb-4">
            <div className="d-flex justify-content-between align-items-center mb-2">
              <span className="text-muted">Step {currentStep} of {totalSteps}</span>
              <span className="text-muted">{Math.round((currentStep / totalSteps) * 100)}% Complete</span>
            </div>
            <ProgressBar 
              now={(currentStep / totalSteps) * 100} 
              variant="success"
              style={{ height: '8px' }}
            />
          </div>

          {/* Main Form Card */}
          <Card className="shadow-sm border-0">
            <Card.Body className="p-4">
              {error && (
                <Alert variant="danger" className="mb-4">
                  <i className="fas fa-exclamation-triangle me-2"></i>
                  {error}
                </Alert>
              )}

              <Form onSubmit={handleSubmit}>
                {/* Step 1: Introduction & Participant Information */}
                {currentStep === 1 && (
                  <div>
                    <div className="mb-4 p-4 bg-light border rounded shadow-sm">
                      <h4 className="text-dark mb-3">
                        <i className="fas fa-info-circle me-2 text-primary"></i>
                        Welcome to the Feedback Form
                      </h4>
                      <p className="mb-2">
                        Thank you for attending <strong>{event?.name}</strong>.
                        Your feedback is essential to help us improve future events and enhance the quality of higher education conferences in Ethiopia.
                      </p>
                      <p className="mb-0 text-muted">
                        This survey will take approximately <strong>3–5 minutes</strong> to complete.
                        All responses are confidential unless you choose to share your contact details.
                      </p>
                    </div>

                    <h5 className="text-primary mb-3">
                      <i className="fas fa-user me-2"></i>
                      Participant Information (Optional)
                    </h5>

                    <Row>
                      <Col md={6}>
                        <Form.Group className="mb-3">
                          <Form.Label>Full Name</Form.Label>
                          <Form.Control
                            type="text"
                            name="participant_name"
                            value={formData.participant_name}
                            onChange={handleInputChange}
                            placeholder="Your full name"
                          />
                        </Form.Group>
                      </Col>
                      <Col md={6}>
                        <Form.Group className="mb-3">
                          <Form.Label>Email Address</Form.Label>
                          <Form.Control
                            type="email"
                            name="participant_email"
                            value={formData.participant_email}
                            onChange={handleInputChange}
                            placeholder="<EMAIL>"
                          />
                          <Form.Text className="text-muted">
                            For follow-up communications and event updates
                          </Form.Text>
                        </Form.Group>
                      </Col>
                    </Row>

                    <Row>
                      <Col md={6}>
                        <Form.Group className="mb-3">
                          <Form.Label>Institution/Organization</Form.Label>
                          <Form.Control
                            type="text"
                            name="institution_name"
                            value={formData.institution_name}
                            onChange={handleInputChange}
                            placeholder="Your institution or organization"
                          />
                        </Form.Group>
                      </Col>
                      <Col md={6}>
                        <Form.Group className="mb-3">
                          <Form.Label>Position/Title</Form.Label>
                          <Form.Control
                            type="text"
                            name="position_title"
                            value={formData.position_title}
                            onChange={handleInputChange}
                            placeholder="Your position or title"
                          />
                        </Form.Group>
                      </Col>
                    </Row>

                    <div className="bg-light rounded p-3">
                      <Form.Group className="mb-0">
                        <Form.Check
                          type="checkbox"
                          name="is_anonymous"
                          checked={formData.is_anonymous}
                          onChange={handleInputChange}
                          label="Submit this feedback anonymously"
                        />
                        <Form.Text className="text-muted">
                          Your personal information will not be associated with your responses
                        </Form.Text>
                      </Form.Group>
                    </div>
                  </div>
                )}

                {/* Step 2: Overall Experience */}
                {currentStep === 2 && (
                  <div>
                    <h4 className="text-primary mb-4">
                      <i className="fas fa-star me-2"></i>
                      Overall Experience
                    </h4>

                    {renderStarRating('overall_satisfaction', formData.overall_satisfaction, 'How would you rate your overall satisfaction with the conference?')}

                    <Form.Group className="mb-4">
                      <Form.Label className="fw-bold">Did the conference meet your expectations? *</Form.Label>
                      <div className="mt-2">
                        {[
                          { value: 'yes', label: '✅ Yes, exceeded my expectations', color: 'success' },
                          { value: 'somewhat', label: '🤔 Somewhat, met most expectations', color: 'warning' },
                          { value: 'no', label: '❌ No, did not meet expectations', color: 'danger' }
                        ].map((option) => (
                          <div key={option.value} className="mb-2">
                            <Form.Check
                              type="radio"
                              name="met_expectations"
                              value={option.value}
                              checked={formData.met_expectations === option.value}
                              onChange={handleInputChange}
                              label={option.label}
                              className={`text-${option.color}`}
                            />
                          </div>
                        ))}
                      </div>
                    </Form.Group>

                    <Form.Group className="mb-3">
                      <Form.Label className="fw-bold">What was the most valuable aspect of the event? *</Form.Label>
                      <Form.Control
                        as="textarea"
                        rows={4}
                        name="most_valuable_aspect"
                        value={formData.most_valuable_aspect}
                        onChange={handleInputChange}
                        placeholder="Please describe what you found most valuable about this conference (e.g., specific sessions, networking opportunities, speakers, etc.)..."
                        required
                      />
                    </Form.Group>

                    <Form.Group className="mb-3">
                      <Form.Label className="fw-bold">What could be improved for future conferences?</Form.Label>
                      <Form.Control
                        as="textarea"
                        rows={3}
                        name="improvement_suggestions"
                        value={formData.improvement_suggestions}
                        onChange={handleInputChange}
                        placeholder="Please share your suggestions for improvement..."
                      />
                    </Form.Group>
                  </div>
                )}

                {/* Step 3: Sessions & Content */}
                {currentStep === 3 && (
                  <div>
                    <h4 className="text-primary mb-4">
                      <i className="fas fa-chalkboard-teacher me-2"></i>
                      Sessions & Content Quality
                    </h4>

                    {renderStarRating('session_relevance', formData.session_relevance, 'How relevant were the sessions to your professional interests and needs?')}

                    <Form.Group className="mb-3">
                      <Form.Label className="fw-bold">Which session(s) did you find most valuable?</Form.Label>
                      <Form.Control
                        as="textarea"
                        rows={3}
                        name="most_valuable_sessions"
                        value={formData.most_valuable_sessions}
                        onChange={handleInputChange}
                        placeholder="Please list the sessions that were most valuable to you and explain why..."
                      />
                    </Form.Group>

                    <Form.Group className="mb-3">
                      <Form.Label className="fw-bold">Suggestions for future topics or session formats:</Form.Label>
                      <Form.Control
                        as="textarea"
                        rows={3}
                        name="future_topics_suggestions"
                        value={formData.future_topics_suggestions}
                        onChange={handleInputChange}
                        placeholder="What topics, formats, or approaches would you like to see in future events?"
                      />
                    </Form.Group>
                  </div>
                )}

                {/* Step 4: Speakers & Moderators */}
                {currentStep === 4 && (
                  <div>
                    <h4 className="text-primary mb-4">
                      <i className="fas fa-microphone me-2"></i>
                      Speakers & Moderators
                    </h4>

                    {renderStarRating('speaker_quality', formData.speaker_quality, 'How would you rate the overall quality of speakers and moderators?')}

                    <Form.Group className="mb-3">
                      <Form.Label className="fw-bold">Comments on speaker expertise, engagement, or delivery:</Form.Label>
                      <Form.Control
                        as="textarea"
                        rows={4}
                        name="speaker_comments"
                        value={formData.speaker_comments}
                        onChange={handleInputChange}
                        placeholder="Please share your thoughts on the speakers and moderators (expertise, presentation skills, engagement with audience, etc.)..."
                      />
                    </Form.Group>
                  </div>
                )}

                {/* Step 5: Logistics & Organization */}
                {currentStep === 5 && (
                  <div>
                    <h4 className="text-primary mb-4">
                      <i className="fas fa-cogs me-2"></i>
                      Logistics & Organization
                    </h4>

                    <p className="text-muted mb-4">Please rate the following aspects of the event organization:</p>

                    <Row>
                      <Col md={6}>
                        {renderStarRating('venue_facilities', formData.venue_facilities, 'Venue & Facilities')}
                        {renderStarRating('technical_setup', formData.technical_setup, 'Technical Setup (Audio/Visual)')}
                        {renderStarRating('time_management', formData.time_management, 'Time Management & Scheduling')}
                      </Col>
                      <Col md={6}>
                        {renderStarRating('transportation_accessibility', formData.transportation_accessibility, 'Transportation & Accessibility')}
                        {renderStarRating('pre_event_communication', formData.pre_event_communication, 'Pre-event Communication')}
                      </Col>
                    </Row>

                    <Form.Group className="mb-3">
                      <Form.Label className="fw-bold">Additional comments on logistics and organization:</Form.Label>
                      <Form.Control
                        as="textarea"
                        rows={3}
                        name="logistics_comments"
                        value={formData.logistics_comments}
                        onChange={handleInputChange}
                        placeholder="Any additional feedback on the event organization, logistics, facilities, or technical aspects..."
                      />
                    </Form.Group>
                  </div>
                )}

                {/* Step 6: Networking & Final Thoughts */}
                {currentStep === 6 && (
                  <div>
                    <h4 className="text-primary mb-4">
                      <i className="fas fa-users me-2"></i>
                      Networking & Future Suggestions
                    </h4>

                    <Form.Group className="mb-4">
                      <Form.Label className="fw-bold">Did you have sufficient networking opportunities?</Form.Label>
                      <div className="mt-2">
                        {[
                          { value: 'yes', label: '✅ Yes, plenty of networking opportunities', color: 'success' },
                          { value: 'somewhat', label: '🤔 Somewhat, could use more networking time', color: 'warning' },
                          { value: 'no', label: '❌ No, insufficient networking opportunities', color: 'danger' }
                        ].map((option) => (
                          <div key={option.value} className="mb-2">
                            <Form.Check
                              type="radio"
                              name="sufficient_networking"
                              value={option.value}
                              checked={formData.sufficient_networking === option.value}
                              onChange={handleInputChange}
                              label={option.label}
                              className={`text-${option.color}`}
                            />
                          </div>
                        ))}
                      </div>
                    </Form.Group>

                    <Form.Group className="mb-3">
                      <Form.Label className="fw-bold">How could networking be improved in future events?</Form.Label>
                      <Form.Control
                        as="textarea"
                        rows={3}
                        name="networking_improvements"
                        value={formData.networking_improvements}
                        onChange={handleInputChange}
                        placeholder="Suggestions for improving networking opportunities (e.g., structured networking sessions, longer breaks, social events, etc.)..."
                      />
                    </Form.Group>

                    <Form.Group className="mb-3">
                      <Form.Label className="fw-bold">What topics would you like to see in future conferences?</Form.Label>
                      <Form.Control
                        as="textarea"
                        rows={3}
                        name="future_topics"
                        value={formData.future_topics}
                        onChange={handleInputChange}
                        placeholder="Topics, themes, or areas of focus for future higher education conferences..."
                      />
                    </Form.Group>

                    <Form.Group className="mb-4">
                      <Form.Label className="fw-bold">Any other feedback or recommendations?</Form.Label>
                      <Form.Control
                        as="textarea"
                        rows={3}
                        name="additional_feedback"
                        value={formData.additional_feedback}
                        onChange={handleInputChange}
                        placeholder="Any additional thoughts, suggestions, or feedback you'd like to share..."
                      />
                    </Form.Group>

                    <Form.Group className="mb-4">
                      <Form.Label className="fw-bold">Optional: Upload supporting materials</Form.Label>
                      <Form.Control
                        type="file"
                        onChange={handleFileChange}
                        accept=".pdf,.doc,.docx,.jpg,.jpeg,.png,.txt"
                      />
                      <Form.Text className="text-muted">
                        You can upload session notes, photos, or other relevant materials (Max 10MB)
                        <br />
                        Accepted formats: PDF, DOC, DOCX, JPG, PNG, TXT
                      </Form.Text>
                    </Form.Group>

                    <div className="border rounded p-4 bg-success bg-opacity-10">
                      <h5 className="text-success mb-3">
                        <i className="fas fa-shield-alt me-2"></i>
                        Consent & Privacy
                      </h5>
                      <Form.Group className="mb-3">
                        <Form.Check
                          type="checkbox"
                          name="consent_given"
                          checked={formData.consent_given}
                          onChange={handleInputChange}
                          label="I consent to my feedback being used for event improvement, reporting, and research purposes."
                          required
                          className="fw-bold"
                        />
                      </Form.Group>
                      <div className="text-success">
                        <small>
                          <i className="fas fa-info-circle me-1"></i>
                          Your feedback will be used to improve future conferences and may be included in aggregate reports.
                          Personal information will be kept confidential and used only for follow-up communications if provided.
                        </small>
                      </div>
                    </div>


                  </div>
                )}
                
                {/* Navigation Buttons */}
                <div className="d-flex justify-content-between mt-4 pt-4 border-top">
                  <div>
                    {currentStep > 1 && (
                      <Button variant="outline-secondary" onClick={prevStep}>
                        <i className="fas fa-arrow-left me-2"></i>
                        Previous
                      </Button>
                    )}
                  </div>
                  
                  <div className="d-flex gap-2">
                    {currentStep < totalSteps ? (
                      <Button variant="primary" onClick={nextStep}>
                        Next
                        <i className="fas fa-arrow-right ms-2"></i>
                      </Button>
                    ) : (
                      <Button 
                        type="submit" 
                        variant="success" 
                        disabled={submitting || !formData.consent_given}
                        className="px-4"
                      >
                        {submitting ? (
                          <>
                            <Spinner size="sm" className="me-2" />
                            Submitting...
                          </>
                        ) : (
                          <>
                            <i className="fas fa-paper-plane me-2"></i>
                            Submit Feedback
                          </>
                        )}
                      </Button>
                    )}
                  </div>
                </div>
              </Form>
            </Card.Body>
          </Card>
        </Col>
      </Row>


    </Container>
  );
};

export default InlineFeedbackPage;
