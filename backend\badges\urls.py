from django.urls import path, include
from rest_framework.routers import <PERSON><PERSON>ult<PERSON><PERSON><PERSON>
from .views import BadgeViewSet, BadgeTemplateViewSet, DriverBadgeViewSet, ContactPersonBadgeViewSet, test_svg_badge, badge_demo, badge_status, force_regenerate_badge, regenerate_all_badges, api_test

router = DefaultRouter()
router.register(r'badges', BadgeViewSet)
router.register(r'badge-templates', BadgeTemplateViewSet)
router.register(r'driver-badges', DriverBadgeViewSet)
router.register(r'contact-person-badges', ContactPersonBadgeViewSet)

urlpatterns = [
    path('', include(router.urls)),
    path('test-svg-badge/', test_svg_badge, name='test_svg_badge'),
    path('demo/', badge_demo, name='badge_demo'),
    path('status/', badge_status, name='badge_status'),
    path('force-regenerate/<int:participant_id>/', force_regenerate_badge, name='force_regenerate_badge'),
    path('regenerate-all/', regenerate_all_badges, name='regenerate_all_badges'),
    path('api-test/', api_test, name='api_test'),
]
