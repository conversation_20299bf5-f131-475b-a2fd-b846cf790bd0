# UoG Event Management System - Complete Backup Script
# Creates a comprehensive backup before SSL migration to ZeroSSL

$BackupDate = Get-Date -Format "yyyy-MM-dd_HH-mm-ss"
$BackupDir = "backup_$BackupDate"
$CurrentDir = Get-Location

Write-Host "🔄 Creating comprehensive backup..." -ForegroundColor Green
Write-Host "Backup directory: $BackupDir" -ForegroundColor Yellow

# Create backup directory
New-Item -ItemType Directory -Path $BackupDir -Force | Out-Null

# 1. Backup Docker containers and data
Write-Host "`n📦 Backing up Docker containers..." -ForegroundColor Cyan
docker-compose down
docker save uogevent-copy5-backend -o "$BackupDir\backend-image.tar"
docker save uogevent-copy5-frontend -o "$BackupDir\frontend-image.tar"

# 2. Backup database
Write-Host "`n🗄️ Backing up database..." -ForegroundColor Cyan
docker-compose up -d db
Start-Sleep -Seconds 10
docker exec uogevent-copy5-db-1 pg_dump -U postgres uog_event > "$BackupDir\database_backup.sql"

# 3. Backup all source code and configuration
Write-Host "`n📁 Backing up source code and configuration..." -ForegroundColor Cyan
$ExcludeDirs = @('.git', 'node_modules', '__pycache__', '.vscode', 'logs')
$SourceItems = Get-ChildItem -Path . | Where-Object { $_.Name -notin $ExcludeDirs -and $_.Name -ne $BackupDir }

foreach ($item in $SourceItems) {
    Copy-Item -Path $item.FullName -Destination "$BackupDir\" -Recurse -Force
    Write-Host "✅ Backed up: $($item.Name)" -ForegroundColor Green
}

# 4. Backup SSL certificates and nginx config
Write-Host "`n🔐 Backing up SSL certificates..." -ForegroundColor Cyan
if (Test-Path "certbot") {
    Copy-Item -Path "certbot" -Destination "$BackupDir\certbot_backup" -Recurse -Force
    Write-Host "✅ SSL certificates backed up" -ForegroundColor Green
}

if (Test-Path "nginx") {
    Copy-Item -Path "nginx" -Destination "$BackupDir\nginx_backup" -Recurse -Force
    Write-Host "✅ Nginx configuration backed up" -ForegroundColor Green
}

# 5. Backup media files
Write-Host "`n🖼️ Backing up media files..." -ForegroundColor Cyan
if (Test-Path "backend\media") {
    Copy-Item -Path "backend\media" -Destination "$BackupDir\media_backup" -Recurse -Force
    Write-Host "✅ Media files backed up" -ForegroundColor Green
}

# 6. Create backup manifest
Write-Host "`n📋 Creating backup manifest..." -ForegroundColor Cyan
$Manifest = @"
UoG Event Management System Backup
Created: $BackupDate
Location: $CurrentDir\$BackupDir

Contents:
- backend-image.tar: Docker backend image
- frontend-image.tar: Docker frontend image  
- database_backup.sql: PostgreSQL database dump
- Source code and configuration files
- SSL certificates (certbot_backup/)
- Nginx configuration (nginx_backup/)
- Media files (media_backup/)

Restore Instructions:
1. Load Docker images: docker load -i backend-image.tar && docker load -i frontend-image.tar
2. Restore database: docker exec -i container_name psql -U postgres uog_event < database_backup.sql
3. Copy source files back to project directory
4. Restore SSL certificates to certbot/ directory
5. Restore nginx configuration
6. Run docker-compose up -d

Pre-ZeroSSL Migration Backup
Ready for SSL provider change from Let's Encrypt to ZeroSSL
"@

$Manifest | Out-File -FilePath "$BackupDir\BACKUP_MANIFEST.txt" -Encoding UTF8

# 7. Compress backup (optional)
Write-Host "`n📦 Compressing backup..." -ForegroundColor Cyan
Compress-Archive -Path "$BackupDir\*" -DestinationPath "$BackupDir.zip" -Force

# 8. Restart services
Write-Host "`n🚀 Restarting services..." -ForegroundColor Cyan
docker-compose up -d

Write-Host "`n✅ BACKUP COMPLETED SUCCESSFULLY!" -ForegroundColor Green
Write-Host "Backup location: $CurrentDir\$BackupDir" -ForegroundColor Yellow
Write-Host "Compressed backup: $CurrentDir\$BackupDir.zip" -ForegroundColor Yellow
Write-Host "`nBackup includes:" -ForegroundColor White
Write-Host "- Complete source code" -ForegroundColor Gray
Write-Host "- Database dump" -ForegroundColor Gray
Write-Host "- Docker images" -ForegroundColor Gray
Write-Host "- SSL certificates" -ForegroundColor Gray
Write-Host "- Media files" -ForegroundColor Gray
Write-Host "- Configuration files" -ForegroundColor Gray

Write-Host "`n🔄 Ready to proceed with ZeroSSL migration!" -ForegroundColor Green
