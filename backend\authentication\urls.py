from django.urls import path
from rest_framework_simplejwt.views import TokenRefreshView
from . import views

urlpatterns = [
    # Authentication
    path('login/', views.LoginView.as_view(), name='login'),
    path('logout/', views.LogoutView.as_view(), name='logout'),
    path('token/refresh/', TokenRefreshView.as_view(), name='token_refresh'),
    
    # User management
    path('profile/', views.UserProfileView.as_view(), name='user_profile'),
    path('change-password/', views.ChangePasswordView.as_view(), name='change_password'),
    path('users/', views.UserListCreateView.as_view(), name='user_list_create'),
    path('users/<int:pk>/', views.UserDetailView.as_view(), name='user_detail'),
    
    # Roles and permissions
    path('roles/', views.UserRoleListView.as_view(), name='user_roles'),
    path('permissions/', views.user_permissions, name='user_permissions'),
    
    # Security and monitoring
    path('sessions/', views.UserSessionListView.as_view(), name='user_sessions'),
    path('login-attempts/', views.LoginAttemptListView.as_view(), name='login_attempts'),
    
    # Dashboard
    path('dashboard/stats/', views.dashboard_stats, name='dashboard_stats'),

    # Public endpoints
    path('developers/', views.DeveloperListView.as_view(), name='developers'),
]
