import React, { useEffect, useState } from 'react';
import { useBranding } from '../hooks/useBranding';
import { getMediaUrl, developerService, Developer } from '../services/api';

const ModernFooter: React.FC = () => {
  const { organization } = useBranding();
  const [currentYear] = useState(new Date().getFullYear());
  const [developers, setDevelopers] = useState<Developer[]>([]);

  useEffect(() => {
    const loadDevelopers = async () => {
      try {
        const response = await developerService.getDevelopers();
        setDevelopers(response.data.results);
      } catch (error) {
        console.error('Failed to load developers:', error);
      }
    };

    loadDevelopers();
  }, []);

  useEffect(() => {
    // Initialize tooltips if Bootstrap is available
    if (typeof window !== 'undefined' && (window as any).bootstrap) {
      const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
      tooltipTriggerList.map((tooltipTriggerEl) => {
        return new (window as any).bootstrap.Tooltip(tooltipTriggerEl);
      });
    }
  }, [developers]);

  const quickLinks = [
    { label: 'All Events', href: '/#events', icon: 'fas fa-calendar-alt' },
    { label: 'Register', href: '/register', icon: 'fas fa-user-plus' },
    { label: 'Event Gallery', href: '/#gallery', icon: 'fas fa-images' },
    { label: 'About Us', href: '/#about', icon: 'fas fa-info-circle' },
  ];

  const socialLinks = [
    { platform: 'facebook', icon: 'fab fa-facebook-f', url: organization?.facebook_url },
    { platform: 'twitter', icon: 'fab fa-twitter', url: organization?.twitter_url },
    { platform: 'linkedin', icon: 'fab fa-linkedin-in', url: organization?.linkedin_url },
    { platform: 'instagram', icon: 'fab fa-instagram', url: organization?.instagram_url },
    { platform: 'youtube', icon: 'fab fa-youtube', url: organization?.youtube_url },
  ];

  return (
    <>
      <style>{`
        .modern-footer {
          background: linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%);
          position: relative;
          overflow: hidden;
        }

        .modern-footer::before {
          content: '';
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1000 100" fill="%23ffffff" opacity="0.03"><polygon points="1000,100 1000,0 0,100"/></svg>');
          background-size: cover;
          pointer-events: none;
        }

        .footer-wave {
          position: absolute;
          top: -1px;
          left: 0;
          width: 100%;
          height: 60px;
          background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1200 120" preserveAspectRatio="none"><path d="M0,0V46.29c47.79,22.2,103.59,32.17,158,28,70.36-5.37,136.33-33.31,206.8-37.5C438.64,32.43,512.34,53.67,583,72.05c69.27,18,138.3,24.88,209.4,13.08,36.15-6,69.85-17.84,104.45-29.34C989.49,25,1113-14.29,1200,52.47V0Z" fill="%23ffffff" opacity="0.1"/></svg>');
          background-size: cover;
          background-repeat: no-repeat;
        }

        .footer-content {
          position: relative;
          z-index: 2;
          padding: 4rem 0 2rem;
        }

        .footer-brand-modern {
          text-align: center;
          margin-bottom: 2rem;
        }

        .footer-logo-modern {
          width: 80px;
          height: 80px;
          border-radius: 20px;
          object-fit: cover;
          margin-bottom: 1rem;
          transition: all 0.3s ease;
          box-shadow: 0 10px 30px rgba(0, 123, 255, 0.3);
        }

        .footer-logo-modern:hover {
          transform: translateY(-5px) scale(1.05);
          box-shadow: 0 15px 40px rgba(0, 123, 255, 0.4);
        }

        .footer-title-modern {
          font-size: 1.8rem;
          font-weight: 700;
          background: linear-gradient(135deg, #ffffff, #e3f2fd);
          -webkit-background-clip: text;
          -webkit-text-fill-color: transparent;
          background-clip: text;
          margin-bottom: 0.5rem;
        }

        .footer-subtitle-modern {
          color: #b3c6e7;
          font-size: 1rem;
          margin-bottom: 1rem;
          font-weight: 500;
        }

        .footer-description-modern {
          color: #d1e3ff;
          line-height: 1.6;
          max-width: 600px;
          margin: 0 auto 2rem;
          text-align: center;
        }

        .footer-sections {
          display: grid;
          grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
          gap: 2rem;
          margin-bottom: 3rem;
        }

        .footer-section {
          background: rgba(255, 255, 255, 0.05);
          backdrop-filter: blur(10px);
          border-radius: 16px;
          padding: 1.5rem;
          border: 1px solid rgba(255, 255, 255, 0.1);
          transition: all 0.3s ease;
        }

        .footer-section:hover {
          transform: translateY(-5px);
          background: rgba(255, 255, 255, 0.08);
          box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
        }

        .footer-section-title {
          color: #ffffff;
          font-size: 1.1rem;
          font-weight: 600;
          margin-bottom: 1rem;
          display: flex;
          align-items: center;
          gap: 0.5rem;
        }

        .footer-section-icon {
          color: #007bff;
          font-size: 1.2rem;
        }

        .footer-link-modern {
          display: flex;
          align-items: center;
          gap: 0.75rem;
          color: #d1e3ff;
          text-decoration: none;
          padding: 0.5rem 0;
          transition: all 0.3s ease;
          border-radius: 8px;
          margin-bottom: 0.25rem;
        }

        .footer-link-modern:hover {
          color: #007bff;
          transform: translateX(5px);
          background: rgba(0, 123, 255, 0.1);
          padding-left: 0.5rem;
          text-decoration: none;
        }

        .footer-link-icon {
          color: #007bff;
          width: 16px;
          text-align: center;
        }

        .contact-item-modern {
          display: flex;
          align-items: center;
          gap: 0.75rem;
          color: #d1e3ff;
          margin-bottom: 0.75rem;
          padding: 0.5rem;
          border-radius: 8px;
          transition: all 0.3s ease;
        }

        .contact-item-modern:hover {
          background: rgba(255, 255, 255, 0.05);
          transform: translateX(5px);
        }

        .contact-icon-modern {
          color: #007bff;
          width: 20px;
          text-align: center;
          font-size: 1.1rem;
        }

        .social-links-modern {
          display: flex;
          justify-content: center;
          gap: 1rem;
          margin: 2rem 0;
        }

        .social-link-modern {
          display: flex;
          align-items: center;
          justify-content: center;
          width: 50px;
          height: 50px;
          background: rgba(255, 255, 255, 0.1);
          border-radius: 12px;
          color: #ffffff;
          text-decoration: none;
          transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
          backdrop-filter: blur(10px);
          border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .social-link-modern:hover {
          background: #007bff;
          color: #ffffff;
          transform: translateY(-3px) scale(1.1);
          box-shadow: 0 10px 25px rgba(0, 123, 255, 0.4);
          text-decoration: none;
        }

        .footer-bottom-modern {
          border-top: 1px solid rgba(255, 255, 255, 0.1);
          padding: 2rem 0;
          text-align: center;
        }

        .footer-copyright-modern {
          color: #b3c6e7;
          margin-bottom: 1rem;
          font-size: 0.9rem;
        }

        .developers-section-modern {
          display: flex;
          align-items: center;
          justify-content: center;
          gap: 1rem;
          flex-wrap: wrap;
        }

        .developers-label-modern {
          color: #d1e3ff;
          font-weight: 500;
        }

        .developers-list-modern {
          display: flex;
          gap: 0.5rem;
          align-items: center;
        }

        .developer-item-modern {
          position: relative;
          transition: all 0.3s ease;
        }

        .developer-item-modern:hover {
          transform: translateY(-2px);
        }

        .developer-photo-modern {
          width: 35px;
          height: 35px;
          border-radius: 50%;
          object-fit: cover;
          border: 2px solid rgba(255, 255, 255, 0.2);
          transition: all 0.3s ease;
        }

        .developer-photo-modern:hover {
          border-color: #007bff;
          box-shadow: 0 5px 15px rgba(0, 123, 255, 0.3);
        }

        .developer-avatar-modern {
          width: 35px;
          height: 35px;
          border-radius: 50%;
          background: linear-gradient(135deg, #007bff, #0056b3);
          display: flex;
          align-items: center;
          justify-content: center;
          color: white;
          font-weight: 600;
          font-size: 0.8rem;
          border: 2px solid rgba(255, 255, 255, 0.2);
          transition: all 0.3s ease;
        }

        .developer-avatar-modern:hover {
          transform: scale(1.1);
          box-shadow: 0 5px 15px rgba(0, 123, 255, 0.3);
        }

        @media (max-width: 768px) {
          .footer-sections {
            grid-template-columns: 1fr;
            gap: 1.5rem;
          }

          .footer-content {
            padding: 3rem 0 1.5rem;
          }

          .footer-title-modern {
            font-size: 1.5rem;
          }

          .social-links-modern {
            gap: 0.75rem;
          }

          .social-link-modern {
            width: 45px;
            height: 45px;
          }

          .developers-section-modern {
            flex-direction: column;
            gap: 0.75rem;
          }
        }

        .floating-particles {
          position: absolute;
          top: 0;
          left: 0;
          width: 100%;
          height: 100%;
          overflow: hidden;
          pointer-events: none;
        }

        .particle {
          position: absolute;
          background: rgba(255, 255, 255, 0.1);
          border-radius: 50%;
          animation: float 6s ease-in-out infinite;
        }

        @keyframes float {
          0%, 100% { transform: translateY(0px) rotate(0deg); }
          50% { transform: translateY(-20px) rotate(180deg); }
        }
      `}</style>

      <footer className="modern-footer">
        <div className="footer-wave"></div>
        <div className="floating-particles">
          {[...Array(6)].map((_, i) => (
            <div
              key={i}
              className="particle"
              style={{
                left: `${Math.random() * 100}%`,
                top: `${Math.random() * 100}%`,
                width: `${Math.random() * 10 + 5}px`,
                height: `${Math.random() * 10 + 5}px`,
                animationDelay: `${Math.random() * 6}s`,
                animationDuration: `${Math.random() * 4 + 4}s`
              }}
            />
          ))}
        </div>

        <div className="footer-content">
          <div className="container">
            {/* Brand Section */}
            <div className="footer-brand-modern">
              {organization?.logo ? (
                <img
                  src={getMediaUrl(organization.logo)}
                  alt={organization.name}
                  className="footer-logo-modern"
                />
              ) : (
                <div className="footer-logo-modern bg-primary d-flex align-items-center justify-content-center">
                  <i className="fas fa-university text-white" style={{ fontSize: '2rem' }}></i>
                </div>
              )}
              <h2 className="footer-title-modern">
                {organization?.name || 'University of Gondar'}
              </h2>
              <p className="footer-subtitle-modern">
                {organization?.motto || 'Excellence in Education, Research and Community Service'}
              </p>
              <p className="footer-description-modern">
                {organization?.description || 'A comprehensive platform for managing academic events, conferences, and institutional gatherings at Ethiopia\'s premier university.'}
              </p>
            </div>

            {/* Footer Sections */}
            <div className="footer-sections">
              {/* Quick Links */}
              <div className="footer-section">
                <h3 className="footer-section-title">
                  <i className="fas fa-link footer-section-icon"></i>
                  Quick Links
                </h3>
                {quickLinks.map((link, index) => (
                  <a
                    key={index}
                    href={link.href}
                    target={link.href.startsWith('http') ? '_blank' : '_self'}
                    rel={link.href.startsWith('http') ? 'noopener noreferrer' : undefined}
                    className="footer-link-modern"
                  >
                    <i className={`${link.icon} footer-link-icon`}></i>
                    <span>{link.label}</span>
                  </a>
                ))}
              </div>

              {/* Contact Information */}
              <div className="footer-section">
                <h3 className="footer-section-title">
                  <i className="fas fa-address-book footer-section-icon"></i>
                  Contact Information
                </h3>
                <div className="contact-item-modern">
                  <i className="fas fa-envelope contact-icon-modern"></i>
                  <span>{organization?.email || '<EMAIL>'}</span>
                </div>
                <div className="contact-item-modern">
                  <i className="fas fa-phone contact-icon-modern"></i>
                  <span>{organization?.phone || '+251-58-114-1240'}</span>
                </div>
                <div className="contact-item-modern">
                  <i className="fas fa-map-marker-alt contact-icon-modern"></i>
                  <span>{organization?.full_address || 'Gondar, Amhara Region, Ethiopia'}</span>
                </div>
                <div className="contact-item-modern">
                  <i className="fas fa-globe contact-icon-modern"></i>
                  <span>{organization?.website || 'www.uog.edu.et'}</span>
                </div>
              </div>

              {/* University Info */}
              <div className="footer-section">
                <h3 className="footer-section-title">
                  <i className="fas fa-university footer-section-icon"></i>
                  University
                </h3>
                <a href="/#about" className="footer-link-modern">
                  <i className="fas fa-info-circle footer-link-icon"></i>
                  <span>About University</span>
                </a>
                <a href="/#gallery" className="footer-link-modern">
                  <i className="fas fa-graduation-cap footer-link-icon"></i>
                  <span>Campus Life</span>
                </a>
                <a href="/events" target="_blank" rel="noopener noreferrer" className="footer-link-modern">
                  <i className="fas fa-calendar-alt footer-link-icon"></i>
                  <span>Academic Events</span>
                </a>
                <a href="/#contact" className="footer-link-modern">
                  <i className="fas fa-envelope footer-link-icon"></i>
                  <span>Contact Us</span>
                </a>
              </div>
            </div>

            {/* Social Links */}
            <div className="social-links-modern">
              {socialLinks.map((social) => 
                social.url ? (
                  <a
                    key={social.platform}
                    href={social.url}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="social-link-modern"
                    title={social.platform.charAt(0).toUpperCase() + social.platform.slice(1)}
                  >
                    <i className={social.icon}></i>
                  </a>
                ) : null
              )}
            </div>

            {/* Footer Bottom */}
            <div className="footer-bottom-modern">
              <p className="footer-copyright-modern">
                © {currentYear} {organization?.name || 'University of Gondar'}. All rights reserved.
              </p>
              
              <div className="developers-section-modern">
                <span className="developers-label-modern">Developed by</span>
                <div className="developers-list-modern">
                  {developers && developers.length > 0 ? developers.map((developer) => (
                    <div
                      key={developer.id}
                      className="developer-item-modern"
                      data-bs-toggle="tooltip"
                      data-bs-placement="top"
                      title={`${developer.full_name} - ${developer.profession}`}
                    >
                      {developer.photo ? (
                        <img
                          src={getMediaUrl(developer.photo)}
                          alt={developer.full_name}
                          className="developer-photo-modern"
                        />
                      ) : (
                        <div className="developer-avatar-modern">
                          {developer.full_name.split(' ').map(n => n[0]).join('').substring(0, 2)}
                        </div>
                      )}
                    </div>
                  )) : (
                    <div className="text-white">
                      <small>University of Gondar Development Team</small>
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>
        </div>
      </footer>
    </>
  );
};

export default ModernFooter;
