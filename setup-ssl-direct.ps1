# Direct Let's Encrypt SSL Setup (Skip External Tests)
# University of Gondar Event Management System

Write-Host "Direct Let's Encrypt SSL Setup for UoG Event Management System" -ForegroundColor Blue
Write-Host "=============================================================" -ForegroundColor Blue

# Configuration
$DOMAIN = "event.uog.edu.et"
$EMAIL = "<EMAIL>"
$COMPOSE_FILE = "docker-compose.prod.yml"

Write-Host "Configuration:" -ForegroundColor Yellow
Write-Host "  Domain: $DOMAIN" -ForegroundColor White
Write-Host "  Email: $EMAIL" -ForegroundColor White
Write-Host "  Internal IP: ************" -ForegroundColor White
Write-Host "  External IP: *************" -ForegroundColor White

# Check if running as Administrator
if (-NOT ([Security.Principal.WindowsPrincipal] [Security.Principal.WindowsIdentity]::GetCurrent()).IsInRole([Security.Principal.WindowsBuiltInRole] "Administrator")) {
    Write-Host "ERROR: This script must be run as Administrator" -ForegroundColor Red
    exit 1
}

# Check Docker
try {
    docker version | Out-Null
    Write-Host "SUCCESS: Docker is running" -ForegroundColor Green
} catch {
    Write-Host "ERROR: Docker is not running" -ForegroundColor Red
    exit 1
}

# Check local application
try {
    $response = Invoke-WebRequest -Uri "http://localhost" -Method Head -TimeoutSec 10
    Write-Host "SUCCESS: Application is accessible locally" -ForegroundColor Green
} catch {
    Write-Host "ERROR: Application is not accessible locally" -ForegroundColor Red
    exit 1
}

# Create directories
Write-Host "Creating certificate directories..." -ForegroundColor Yellow
New-Item -ItemType Directory -Force -Path ".\certbot\conf" | Out-Null
New-Item -ItemType Directory -Force -Path ".\certbot\www" | Out-Null
New-Item -ItemType Directory -Force -Path ".\logs\certbot" | Out-Null

Write-Host "Attempting Let's Encrypt certificate generation..." -ForegroundColor Yellow
Write-Host "Note: External connectivity test skipped (normal for internal networks)" -ForegroundColor Cyan

# Try Let's Encrypt certificate generation
Write-Host "Running certbot for domain validation..." -ForegroundColor Yellow

try {
    # Run certbot with detailed output
    $certbotOutput = docker-compose -f $COMPOSE_FILE run --rm certbot certonly `
        --webroot `
        --webroot-path=/var/www/certbot `
        --email $EMAIL `
        --agree-tos `
        --no-eff-email `
        --force-renewal `
        -d $DOMAIN `
        -d "www.$DOMAIN" 2>&1
    
    Write-Host "Certbot output:" -ForegroundColor Cyan
    Write-Host $certbotOutput -ForegroundColor White
    
    # Check if certificates were created
    if (Test-Path ".\certbot\conf\live\$DOMAIN\fullchain.pem") {
        Write-Host "SUCCESS: SSL certificates generated!" -ForegroundColor Green
        
        # Create SSL nginx configuration
        Write-Host "Creating SSL nginx configuration..." -ForegroundColor Yellow
        
        # Backup current config
        Copy-Item ".\nginx\conf.d\production.conf" ".\nginx\conf.d\production.conf.backup" -Force
        
        # SSL configuration
        $sslConfig = @"
# Production Nginx Configuration with Let's Encrypt SSL

# HTTP Server - Redirects to HTTPS
server {
    listen 80;
    server_name $DOMAIN www.$DOMAIN ************ localhost;

    # Let's Encrypt ACME challenge
    location /.well-known/acme-challenge/ {
        root /var/www/certbot;
        try_files `$uri =404;
    }

    # Redirect all other HTTP traffic to HTTPS
    location / {
        return 301 https://`$server_name`$request_uri;
    }
}

# HTTPS Server Configuration
server {
    listen 443 ssl http2;
    server_name $DOMAIN www.$DOMAIN ************ localhost;

    # SSL Configuration
    ssl_certificate /etc/letsencrypt/live/$DOMAIN/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/$DOMAIN/privkey.pem;

    # SSL Security Settings
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512:ECDHE-RSA-AES256-GCM-SHA384;
    ssl_prefer_server_ciphers off;
    ssl_session_cache shared:SSL:10m;
    ssl_session_timeout 10m;

    # Security Headers
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;
    add_header X-Frame-Options DENY always;
    add_header X-Content-Type-Options nosniff always;

    # Root directory
    root /usr/share/nginx/html;
    index index.html index.htm;

    # Logging
    access_log /var/log/nginx/access.log;
    error_log /var/log/nginx/error.log;

    # Backend API routes
    location /api/ {
        proxy_pass http://backend:8000;
        proxy_set_header Host `$host;
        proxy_set_header X-Real-IP `$remote_addr;
        proxy_set_header X-Forwarded-For `$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto `$scheme;
        proxy_connect_timeout 300s;
        proxy_read_timeout 300s;
        proxy_send_timeout 300s;
    }

    # Django Admin
    location /admin/ {
        proxy_pass http://backend:8000;
        proxy_set_header Host `$host;
        proxy_set_header X-Real-IP `$remote_addr;
        proxy_set_header X-Forwarded-For `$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto `$scheme;
        proxy_connect_timeout 300s;
        proxy_read_timeout 300s;
        proxy_send_timeout 300s;
    }

    # Health check
    location /health/ {
        proxy_pass http://backend:8000;
        proxy_set_header Host `$host;
        proxy_set_header X-Real-IP `$remote_addr;
        proxy_set_header X-Forwarded-For `$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto `$scheme;
        proxy_connect_timeout 300s;
        proxy_read_timeout 300s;
        proxy_send_timeout 300s;
    }

    # Static files
    location /static/ {
        root /usr/share/nginx/html;
        expires 1y;
        add_header Cache-Control "public, immutable";
    }

    # Django static files
    location /django-static/ {
        alias /var/www/static/;
        expires 1y;
        add_header Cache-Control "public, immutable";
    }

    # Media files
    location /media/ {
        alias /var/www/media/;
        expires 1M;
        add_header Cache-Control "public";
    }

    # Frontend application
    location / {
        try_files `$uri `$uri/ /index.html;
    }
}
"@

        # Write SSL configuration
        $sslConfig | Out-File -FilePath ".\nginx\conf.d\production.conf" -Encoding UTF8
        
        Write-Host "SUCCESS: SSL configuration created" -ForegroundColor Green
        
        # Restart services
        Write-Host "Restarting services with SSL..." -ForegroundColor Yellow
        docker-compose -f $COMPOSE_FILE down
        docker-compose -f $COMPOSE_FILE up -d
        
        # Wait for startup
        Write-Host "Waiting for services to start..." -ForegroundColor Yellow
        Start-Sleep -Seconds 30
        
        # Test HTTPS
        Write-Host "Testing HTTPS access..." -ForegroundColor Yellow
        try {
            $httpsResponse = Invoke-WebRequest -Uri "https://$DOMAIN" -Method Head -TimeoutSec 15
            Write-Host "SUCCESS: HTTPS is working!" -ForegroundColor Green
        } catch {
            Write-Host "INFO: HTTPS test from internal network may fail due to certificate domain mismatch" -ForegroundColor Yellow
            Write-Host "Try accessing https://$DOMAIN from a browser" -ForegroundColor Yellow
        }
        
        Write-Host ""
        Write-Host "SSL SETUP COMPLETED SUCCESSFULLY!" -ForegroundColor Green
        Write-Host "=================================" -ForegroundColor Green
        Write-Host "Internal Access: https://$DOMAIN" -ForegroundColor Green
        Write-Host "External Access: https://$DOMAIN" -ForegroundColor Green
        Write-Host "Certificate Auto-Renewal: Enabled" -ForegroundColor Green
        
    } else {
        Write-Host "WARNING: Certificate files not found after generation" -ForegroundColor Yellow
        Write-Host "This might be due to Let's Encrypt validation failure" -ForegroundColor Yellow
        Write-Host ""
        Write-Host "Possible causes:" -ForegroundColor Yellow
        Write-Host "1. External firewall blocking port 80" -ForegroundColor Yellow
        Write-Host "2. Port forwarding not configured" -ForegroundColor Yellow
        Write-Host "3. Domain not accessible from internet" -ForegroundColor Yellow
        Write-Host ""
        Write-Host "Check certbot logs:" -ForegroundColor Yellow
        Write-Host "docker-compose -f $COMPOSE_FILE logs certbot" -ForegroundColor Cyan
    }
    
} catch {
    Write-Host "ERROR: Certificate generation failed" -ForegroundColor Red
    Write-Host "Error: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host ""
    Write-Host "Check logs with:" -ForegroundColor Yellow
    Write-Host "docker-compose -f $COMPOSE_FILE logs certbot" -ForegroundColor Cyan
}

Write-Host ""
Write-Host "Setup completed. Check the output above for results." -ForegroundColor Blue
