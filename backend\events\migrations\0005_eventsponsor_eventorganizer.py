# Generated by Django 4.2.7 on 2025-07-28 20:21

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('events', '0004_auto_20250727_0048'),
    ]

    operations = [
        migrations.CreateModel(
            name='EventSponsor',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=200)),
                ('logo', models.ImageField(upload_to='sponsor_logos/')),
                ('website', models.URLField(blank=True)),
                ('description', models.TextField(blank=True)),
                ('sponsor_type', models.CharField(choices=[('platinum', 'Platinum Sponsor'), ('gold', 'Gold Sponsor'), ('silver', 'Silver Sponsor'), ('bronze', 'Bronze Sponsor'), ('partner', 'Partner'), ('supporter', 'Supporter')], default='supporter', max_length=50)),
                ('display_order', models.PositiveIntegerField(default=0)),
                ('is_active', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('event', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='sponsors', to='events.event')),
            ],
            options={
                'ordering': ['display_order', 'name'],
            },
        ),
        migrations.CreateModel(
            name='EventOrganizer',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=200)),
                ('title', models.CharField(blank=True, max_length=200)),
                ('photo', models.ImageField(upload_to='organizer_photos/')),
                ('email', models.EmailField(blank=True, max_length=254)),
                ('phone', models.CharField(blank=True, max_length=20)),
                ('bio', models.TextField(blank=True)),
                ('is_primary', models.BooleanField(default=False)),
                ('display_order', models.PositiveIntegerField(default=0)),
                ('is_active', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('event', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='organizers', to='events.event')),
            ],
            options={
                'ordering': ['display_order', 'name'],
            },
        ),
    ]
