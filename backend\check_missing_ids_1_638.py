#!/usr/bin/env python3

import os
import sys
import django

# Add the project root to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'event_management.settings')
django.setup()

from participants.models import Participant

def check_missing_ids_1_638():
    """Check which IDs are missing from 1 to 638"""
    
    print("=== CHECKING MISSING IDs FROM 1 TO 638 ===")
    
    # Get all existing IDs
    existing_ids = set(Participant.objects.values_list('id', flat=True))
    
    # Find missing IDs
    missing_ids = []
    for i in range(1, 639):  # 1 to 638 inclusive
        if i not in existing_ids:
            missing_ids.append(i)
    
    print(f"Total participants in database: {Participant.objects.count()}")
    print(f"Expected participants (1-638): 638")
    print(f"Missing participants: {len(missing_ids)}")
    
    if missing_ids:
        print(f"\nMissing IDs:")
        
        # Group consecutive missing IDs for better readability
        ranges = []
        start = missing_ids[0]
        end = missing_ids[0]
        
        for i in range(1, len(missing_ids)):
            if missing_ids[i] == missing_ids[i-1] + 1:
                end = missing_ids[i]
            else:
                if start == end:
                    ranges.append(str(start))
                else:
                    ranges.append(f"{start}-{end}")
                start = missing_ids[i]
                end = missing_ids[i]
        
        # Add the last range
        if start == end:
            ranges.append(str(start))
        else:
            ranges.append(f"{start}-{end}")
        
        print(f"Missing ID ranges: {', '.join(ranges)}")
        
        # Show first 50 missing IDs individually
        print(f"\nFirst 50 missing IDs: {missing_ids[:50]}")
        
        if len(missing_ids) > 50:
            print(f"... and {len(missing_ids) - 50} more")
    else:
        print("\n✅ No missing IDs! All participants from 1-638 are present.")
    
    # Show some statistics
    print(f"\nStatistics:")
    print(f"Coverage: {((638 - len(missing_ids)) / 638) * 100:.1f}%")
    
    # Check specific ranges
    ranges_to_check = [
        (1, 100),
        (101, 200),
        (201, 300),
        (301, 400),
        (401, 500),
        (501, 600),
        (601, 638)
    ]
    
    print(f"\nCoverage by range:")
    for start, end in ranges_to_check:
        count = len([i for i in range(start, end + 1) if i in existing_ids])
        total = end - start + 1
        percentage = (count / total) * 100
        print(f"IDs {start}-{end}: {count}/{total} ({percentage:.1f}%)")

if __name__ == "__main__":
    check_missing_ids_1_638()
