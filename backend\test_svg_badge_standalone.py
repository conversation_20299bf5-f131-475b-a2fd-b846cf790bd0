#!/usr/bin/env python
"""
Standalone test for SVG badge generation without database
"""
from PIL import Image, ImageDraw, ImageFont
import qrcode
from io import BytesIO

def create_svg_style_badge():
    """Create a badge matching the SVG template exactly"""
    
    # SVG template dimensions (600x1200)
    width = 600
    height = 1200

    # Create badge with gradient background
    badge_img = Image.new('RGB', (width, height), color='#002D72')
    draw = ImageDraw.Draw(badge_img)

    # Add gradient background
    add_gradient_background(draw, width, height)

    # Add decorative elements
    add_decorative_elements(draw, width, height)

    # Add top decorative ribbon
    add_top_ribbon(draw, width, height)

    # Add ministry logo
    add_ministry_logo(draw, width, height)

    # Add ministry title
    add_ministry_title(draw, width, height)

    # Add theme
    add_theme(draw, width, height)

    # Add photo area
    add_photo_area(draw, width, height)

    # Add participant info
    add_participant_info(draw, width, height)

    # Add participant type badge
    add_participant_type(draw, width, height)

    # Add QR code
    add_qr_code(draw, width, height, badge_img)

    # Add sponsors
    add_sponsors(draw, width, height)

    # Add footer
    add_footer(draw, width, height)

    return badge_img

def add_gradient_background(draw, width, height):
    """Add gradient background like SVG template"""
    # Create gradient from #002D72 to #021B3A
    for y in range(height):
        progress = y / height
        # Interpolate between colors
        r = int(0 + (2 - 0) * progress)
        g = int(45 + (27 - 45) * progress)
        b = int(114 + (58 - 114) * progress)
        draw.line([(0, y), (width, y)], fill=(r, g, b))

def add_decorative_elements(draw, width, height):
    """Add decorative dots like SVG template"""
    # Left side dots
    dots = [(50, 150), (70, 170), (90, 190), (110, 210), (130, 230)]
    for x, y in dots:
        draw.ellipse([x-3, y-3, x+3, y+3], fill=(255, 255, 255, 38))  # 15% opacity
    
    # Right side dots
    dots = [(550, 180), (530, 200), (510, 220), (490, 240)]
    for x, y in dots:
        draw.ellipse([x-3, y-3, x+3, y+3], fill=(255, 255, 255, 38))  # 15% opacity

def add_top_ribbon(draw, width, height):
    """Add top decorative ribbon like SVG template"""
    # Main ribbon
    points = [(0, 0), (width, 0), (width, 120), (0, 300)]
    draw.polygon(points, fill=(0, 75, 145, 102))  # #004B91 with 40% opacity
    
    # Secondary ribbon
    points = [(0, 300), (width, 120), (width, 140), (0, 320)]
    draw.polygon(points, fill=(0, 75, 145, 77))  # #004B91 with 30% opacity

def add_ministry_logo(draw, width, height):
    """Add ministry logo area like SVG template"""
    # White circle background
    center_x, center_y = 300, 90
    radius = 50
    draw.ellipse([center_x-radius, center_y-radius, center_x+radius, center_y+radius], 
                fill='white')
    
    # Placeholder for logo (education icon)
    try:
        logo_font = ImageFont.truetype("arial.ttf", 40)
    except:
        logo_font = ImageFont.load_default()
    
    # Draw education symbol
    draw.text((center_x, center_y), "🎓", fill='#002D72', font=logo_font, anchor='mm')

def add_ministry_title(draw, width, height):
    """Add ministry title like SVG template"""
    try:
        title_font = ImageFont.truetype("arial.ttf", 24)
        subtitle_font = ImageFont.truetype("arial.ttf", 20)
    except:
        title_font = ImageFont.load_default()
        subtitle_font = ImageFont.load_default()
    
    # Main title
    title = "MINISTRY OF EDUCATION OF FDRE"
    draw.text((width//2, 165), title, fill='white', font=title_font, anchor='mm')
    
    # Subtitle
    subtitle = "HIGHER EDUCATION DEVELOPMENT SECTOR"
    draw.text((width//2, 195), subtitle, fill='white', font=subtitle_font, anchor='mm')

def add_theme(draw, width, height):
    """Add theme section like SVG template"""
    # Background rectangle
    draw.rounded_rectangle([150, 210, 450, 240], radius=15, fill=(255, 255, 255, 38))
    
    try:
        theme_font = ImageFont.truetype("arial.ttf", 18)
    except:
        theme_font = ImageFont.load_default()
    
    theme_text = "HIGHER EDUCATION FOR HIGHER IMPACT"
    draw.text((width//2, 230), theme_text, fill='white', font=theme_font, anchor='mm')

def add_photo_area(draw, width, height):
    """Add photo area like SVG template"""
    # Outer frame
    draw.rounded_rectangle([150, 260, 450, 540], radius=20, 
                         fill=(255, 255, 255, 20), outline=(255, 255, 255, 140), width=2)
    
    # Inner frame
    draw.rounded_rectangle([175, 285, 425, 515], radius=15, 
                         fill=(255, 255, 255, 38), outline='white', width=2)
    
    # Placeholder text
    try:
        photo_font = ImageFont.truetype("arial.ttf", 16)
    except:
        photo_font = ImageFont.load_default()
    
    draw.text((300, 420), "Participant Photo", fill=(255, 255, 255, 128), 
             font=photo_font, anchor='mm')

def add_participant_info(draw, width, height):
    """Add participant information like SVG template"""
    try:
        name_font = ImageFont.truetype("arial.ttf", 28)
        position_font = ImageFont.truetype("arial.ttf", 22)
        institution_font = ImageFont.truetype("arial.ttf", 20)
    except:
        name_font = ImageFont.load_default()
        position_font = ImageFont.load_default()
        institution_font = ImageFont.load_default()
    
    # Full name
    full_name = "Full Name"
    draw.text((width//2, 560), full_name, fill='white', font=name_font, anchor='mm')
    
    # Position
    position = "Position Title"
    draw.text((width//2, 590), position, fill=(255, 255, 255, 221), font=position_font, anchor='mm')
    
    # Institution
    institution = "Institution Name"
    draw.text((width//2, 620), institution, fill=(255, 255, 255, 170), font=institution_font, anchor='mm')

def add_participant_type(draw, width, height):
    """Add participant type badge like SVG template"""
    # Badge background
    draw.rounded_rectangle([150, 650, 450, 700], radius=25, 
                         fill='white', outline='#002D72', width=2)
    
    try:
        type_font = ImageFont.truetype("arial.ttf", 20)
    except:
        type_font = ImageFont.load_default()
    
    # Participant type
    ptype = "PARTICIPANT TYPE"
    draw.text((300, 675), ptype, fill='#002D72', font=type_font, anchor='mm')

def add_qr_code(draw, width, height, badge_img):
    """Add QR code like SVG template"""
    # QR code frame
    draw.rounded_rectangle([175, 720, 425, 970], radius=15, 
                         fill=(255, 255, 255, 26), outline=(255, 255, 255, 77), width=1)
    
    # QR code background
    draw.rectangle([200, 745, 400, 945], fill='white')
    
    # Generate QR code
    qr = qrcode.QRCode(version=1, box_size=10, border=5)
    qr.add_data("https://example.com/verify/123")
    qr.make(fit=True)
    qr_img = qr.make_image(fill_color="black", back_color="white")
    
    # Resize and paste QR code
    qr_resized = qr_img.resize((200, 200), Image.Resampling.LANCZOS)
    badge_img.paste(qr_resized, (200, 745))
    
    # QR code label
    try:
        qr_font = ImageFont.truetype("arial.ttf", 16)
    except:
        qr_font = ImageFont.load_default()
    
    draw.text((300, 980), "Scan QR Code for Verification", 
             fill=(255, 255, 255, 170), font=qr_font, anchor='mm')

def add_sponsors(draw, width, height):
    """Add sponsors section like SVG template"""
    # Separator line
    draw.line([(100, 1000), (500, 1000)], fill=(255, 255, 255, 77), width=1)
    
    try:
        sponsor_font = ImageFont.truetype("arial.ttf", 18)
        sponsor_label_font = ImageFont.truetype("arial.ttf", 14)
    except:
        sponsor_font = ImageFont.load_default()
        sponsor_label_font = ImageFont.load_default()
    
    # Sponsors title
    draw.text((300, 1030), "SPONSORED BY", fill=(255, 255, 255, 204), 
             font=sponsor_font, anchor='mm')
    
    # Sponsor boxes
    sponsor_boxes = [
        (100, 1050, 220, 1130, "Sponsor 1"),
        (240, 1050, 360, 1130, "Sponsor 2"),
        (380, 1050, 500, 1130, "Sponsor 3")
    ]
    
    for x1, y1, x2, y2, label in sponsor_boxes:
        draw.rounded_rectangle([x1, y1, x2, y2], radius=10, 
                             fill=(255, 255, 255, 26), outline=(255, 255, 255, 77), width=1)
        center_x = (x1 + x2) // 2
        center_y = (y1 + y2) // 2
        draw.text((center_x, center_y), label, fill='white', 
                 font=sponsor_label_font, anchor='mm')

def add_footer(draw, width, height):
    """Add footer like SVG template"""
    # Separator line
    draw.line([(100, 1150), (500, 1150)], fill=(255, 255, 255, 77), width=1)
    
    try:
        footer_font = ImageFont.truetype("arial.ttf", 16)
        social_font = ImageFont.truetype("arial.ttf", 14)
    except:
        footer_font = ImageFont.load_default()
        social_font = ImageFont.load_default()
    
    # Contact info
    draw.text((300, 1180), "www.moe.gov.et | <EMAIL>", 
             fill='white', font=footer_font, anchor='mm')
    
    # Social media
    draw.text((300, 1210), "Follow us: @MoEEthiopia", 
             fill=(255, 255, 255, 170), font=social_font, anchor='mm')

if __name__ == "__main__":
    print("🎨 Creating SVG-style badge...")
    badge = create_svg_style_badge()
    
    # Save the badge
    output_path = "test_svg_badge.png"
    badge.save(output_path)
    print(f"✅ Badge saved to: {output_path}")
    print(f"📏 Badge dimensions: {badge.size}")
