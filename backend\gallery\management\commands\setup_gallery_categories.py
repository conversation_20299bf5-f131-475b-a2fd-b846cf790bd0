from django.core.management.base import BaseCommand
from django.utils.text import slugify
from gallery.models import GalleryCategory


class Command(BaseCommand):
    help = 'Setup default gallery categories'

    def handle(self, *args, **options):
        self.stdout.write('Setting up default gallery categories...')
        
        categories = [
            {
                'name': 'Campus Life',
                'description': 'Images showcasing daily life on campus',
                'icon': 'fas fa-university',
                'color': '#007bff',
                'order': 1
            },
            {
                'name': 'Events',
                'description': 'University events and ceremonies',
                'icon': 'fas fa-calendar-alt',
                'color': '#28a745',
                'order': 2
            },
            {
                'name': 'Academic',
                'description': 'Academic activities and achievements',
                'icon': 'fas fa-graduation-cap',
                'color': '#ffc107',
                'order': 3
            },
            {
                'name': 'Research',
                'description': 'Research activities and facilities',
                'icon': 'fas fa-microscope',
                'color': '#17a2b8',
                'order': 4
            },
            {
                'name': 'Sports',
                'description': 'Sports and recreational activities',
                'icon': 'fas fa-futbol',
                'color': '#fd7e14',
                'order': 5
            },
            {
                'name': 'Facilities',
                'description': 'University buildings and facilities',
                'icon': 'fas fa-building',
                'color': '#6f42c1',
                'order': 6
            }
        ]

        created_count = 0
        for cat_data in categories:
            cat_data['slug'] = slugify(cat_data['name'])
            category, created = GalleryCategory.objects.get_or_create(
                name=cat_data['name'],
                defaults=cat_data
            )
            if created:
                created_count += 1
                self.stdout.write(
                    self.style.SUCCESS(f"Created category: {category.name}")
                )
            else:
                self.stdout.write(
                    self.style.WARNING(f"Category already exists: {category.name}")
                )

        self.stdout.write(
            self.style.SUCCESS(f'Gallery categories setup complete! Created {created_count} new categories.')
        )
