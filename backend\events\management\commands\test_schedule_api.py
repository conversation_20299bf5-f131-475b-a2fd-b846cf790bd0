from django.core.management.base import BaseCommand
from events.models import Event, EventSchedule
from django.utils import timezone
from datetime import timedelta
import json


class Command(BaseCommand):
    help = 'Test schedule API functionality'

    def handle(self, *args, **options):
        self.stdout.write("🧪 Testing Schedule API...")
        
        # Get or create test event
        event = Event.objects.first()
        if not event:
            self.stdout.write("❌ No events found for testing")
            return
            
        self.stdout.write(f"📅 Using event: {event.name}")
        
        # Test schedule creation data
        test_schedule_data = {
            'event': event.id,
            'title': 'Test Schedule Item',
            'description': 'Test description for schedule item',
            'start_time': timezone.now() + timedelta(hours=1),
            'end_time': timezone.now() + timedelta(hours=2),
            'location': 'Test Location',
            'speaker': 'Test Speaker',
            'session_type': 'presentation',
            'is_break': False
        }
        
        self.stdout.write("\n📋 Test Schedule Data:")
        for key, value in test_schedule_data.items():
            self.stdout.write(f"  {key}: {value}")
        
        # Test creating schedule
        try:
            schedule = EventSchedule.objects.create(**test_schedule_data)
            self.stdout.write(f"\n✅ Schedule created successfully: {schedule.id}")
            
            # Test serialization
            from events.serializers import EventScheduleSerializer
            serializer = EventScheduleSerializer(schedule)
            self.stdout.write(f"✅ Serialization successful")
            self.stdout.write(f"   Serialized data: {json.dumps(serializer.data, indent=2, default=str)}")
            
            # Clean up
            schedule.delete()
            self.stdout.write("🧹 Test schedule cleaned up")
            
        except Exception as e:
            self.stdout.write(f"❌ Error creating schedule: {e}")
            import traceback
            traceback.print_exc()
        
        # Test existing schedules
        existing_schedules = EventSchedule.objects.filter(event=event)
        self.stdout.write(f"\n📊 Existing schedules for event: {existing_schedules.count()}")
        
        for schedule in existing_schedules[:3]:  # Show first 3
            self.stdout.write(f"  - {schedule.title} ({schedule.start_time} - {schedule.end_time})")
        
        # Test API endpoint simulation
        self.stdout.write("\n🔗 Testing API endpoint simulation...")
        
        # Simulate POST data that frontend might send
        frontend_data = {
            'event': event.id,
            'title': 'Frontend Test Schedule',
            'description': 'Test from frontend simulation',
            'start_time': (timezone.now() + timedelta(hours=3)).isoformat(),
            'end_time': (timezone.now() + timedelta(hours=4)).isoformat(),
            'location': 'Frontend Test Location',
            'speaker': 'Frontend Test Speaker',
            'session_type': 'workshop',
            'is_break': False
        }
        
        self.stdout.write("📤 Frontend simulation data:")
        for key, value in frontend_data.items():
            self.stdout.write(f"  {key}: {value}")
        
        # Test serializer validation
        try:
            from events.serializers import EventScheduleSerializer
            serializer = EventScheduleSerializer(data=frontend_data)
            
            if serializer.is_valid():
                self.stdout.write("✅ Frontend data validation passed")
                # Don't actually save, just validate
                # schedule = serializer.save()
                # schedule.delete()
            else:
                self.stdout.write("❌ Frontend data validation failed:")
                for field, errors in serializer.errors.items():
                    self.stdout.write(f"  {field}: {errors}")
                    
        except Exception as e:
            self.stdout.write(f"❌ Error in frontend simulation: {e}")
            import traceback
            traceback.print_exc()
        
        # Check model fields
        self.stdout.write("\n🔍 EventSchedule Model Fields:")
        for field in EventSchedule._meta.fields:
            field_info = f"  {field.name}: {field.__class__.__name__}"
            if hasattr(field, 'choices') and field.choices:
                field_info += f" (choices: {[choice[0] for choice in field.choices]})"
            if field.null:
                field_info += " (nullable)"
            if field.blank:
                field_info += " (blank)"
            self.stdout.write(field_info)
        
        self.stdout.write("\n✅ Schedule API test completed!")
