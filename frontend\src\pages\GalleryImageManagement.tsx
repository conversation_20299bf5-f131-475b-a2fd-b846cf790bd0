import React, { useState, useEffect } from 'react';
import { Container, Row, Col, Card, Button, Alert, Form, Modal, Badge, Table, Image } from 'react-bootstrap';
import { useAuth } from '../contexts/AuthContext';
import { galleryService, GalleryCategory, GalleryImage, getMediaUrl } from '../services/api';

const GalleryImageManagement: React.FC = () => {
  const { isAuthenticated } = useAuth();
  const [images, setImages] = useState<GalleryImage[]>([]);
  const [categories, setCategories] = useState<GalleryCategory[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');

  // Modal states
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);
  const [editingImage, setEditingImage] = useState<GalleryImage | null>(null);

  // Form state
  const [formData, setFormData] = useState({
    title: '',
    description: '',
    category: '',
    tags: '',
    is_featured: false,
    is_slider: false,
    is_campus: false,
    is_active: true,
    photographer: '',
    location: '',
    alt_text: '',
    order: 0
  });
  const [imageFile, setImageFile] = useState<File | null>(null);

  // Filters
  const [filters, setFilters] = useState({
    category: '',
    is_featured: '',
    is_slider: '',
    is_campus: '',
    is_active: ''
  });

  useEffect(() => {
    if (isAuthenticated) {
      fetchImages();
      fetchCategories();
    }
  }, [isAuthenticated]);

  useEffect(() => {
    fetchImages();
  }, [filters]);

  const fetchImages = async () => {
    setLoading(true);
    try {
      const params = Object.fromEntries(
        Object.entries(filters).filter(([_, value]) => value !== '')
      );
      const response = await galleryService.getImages(params);
      setImages(response.data.results || response.data);
    } catch (err: any) {
      setError('Failed to fetch gallery images');
    } finally {
      setLoading(false);
    }
  };

  const fetchCategories = async () => {
    try {
      const response = await galleryService.getCategories();
      setCategories(response.data.results || response.data);
    } catch (err: any) {
      console.error('Failed to fetch categories:', err);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!imageFile && !editingImage) {
      setError('Please select an image file');
      return;
    }

    setLoading(true);
    try {
      const formDataToSend = new FormData();
      
      // Add all form fields
      Object.entries(formData).forEach(([key, value]) => {
        if (key === 'is_featured' || key === 'is_slider' || key === 'is_campus' || key === 'is_active') {
          formDataToSend.append(key, value.toString());
        } else {
          formDataToSend.append(key, value.toString());
        }
      });

      // Add image file if provided
      if (imageFile) {
        formDataToSend.append('image', imageFile);
      }

      if (editingImage) {
        // Update existing image
        await galleryService.updateImage(editingImage.id, formDataToSend);
        setSuccess('Image updated successfully!');
        setShowEditModal(false);
      } else {
        // Create new image
        await galleryService.createImage(formDataToSend);
        setSuccess('Image uploaded successfully!');
        setShowCreateModal(false);
      }
      
      resetForm();
      fetchImages();
    } catch (err: any) {
      setError(err.response?.data?.detail || 'Failed to save image');
    } finally {
      setLoading(false);
    }
  };

  const handleEdit = (image: GalleryImage) => {
    setEditingImage(image);
    setFormData({
      title: image.title,
      description: image.description,
      category: image.category.toString(),
      tags: image.tags,
      is_featured: image.is_featured,
      is_slider: image.is_slider,
      is_campus: image.is_campus,
      is_active: image.is_active,
      photographer: image.photographer,
      location: image.location,
      alt_text: image.alt_text,
      order: image.order
    });
    setShowEditModal(true);
  };

  const handleDelete = async (id: number) => {
    if (!window.confirm('Are you sure you want to delete this image?')) return;
    
    setLoading(true);
    try {
      await galleryService.deleteImage(id);
      setSuccess('Image deleted successfully!');
      fetchImages();
    } catch (err: any) {
      setError(err.response?.data?.detail || 'Failed to delete image');
    } finally {
      setLoading(false);
    }
  };

  const resetForm = () => {
    setFormData({
      title: '',
      description: '',
      category: '',
      tags: '',
      is_featured: false,
      is_slider: false,
      is_campus: false,
      is_active: true,
      photographer: '',
      location: '',
      alt_text: '',
      order: 0
    });
    setImageFile(null);
    setEditingImage(null);
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value, type } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? (e.target as HTMLInputElement).checked : value
    }));
  };

  const handleFilterChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFilters(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const getCategoryName = (categoryId: number) => {
    const category = categories.find(cat => cat.id === categoryId);
    return category ? category.name : 'Unknown';
  };

  return (
    <Container className="py-4">
      {/* Header */}
      <Row className="mb-4">
        <Col>
          <div className="d-flex justify-content-between align-items-center">
            <div>
              <h1 className="display-6 fw-bold text-primary">
                <i className="fas fa-images me-3"></i>
                Gallery Images
              </h1>
              <p className="lead text-muted">Manage your gallery image collection</p>
            </div>
            <Button 
              variant="primary" 
              onClick={() => setShowCreateModal(true)}
              disabled={loading}
            >
              <i className="fas fa-plus me-2"></i>
              Upload Image
            </Button>
          </div>
        </Col>
      </Row>

      {/* Alerts */}
      {error && (
        <Alert variant="danger" onClose={() => setError('')} dismissible>
          {error}
        </Alert>
      )}
      {success && (
        <Alert variant="success" onClose={() => setSuccess('')} dismissible>
          {success}
        </Alert>
      )}

      {/* Filters */}
      <Card className="mb-4">
        <Card.Header>
          <h6 className="mb-0">
            <i className="fas fa-filter me-2"></i>
            Filters
          </h6>
        </Card.Header>
        <Card.Body>
          <Row>
            <Col md={3}>
              <Form.Group>
                <Form.Label>Category</Form.Label>
                <Form.Select name="category" value={filters.category} onChange={handleFilterChange}>
                  <option value="">All Categories</option>
                  {categories.map(category => (
                    <option key={category.id} value={category.id}>{category.name}</option>
                  ))}
                </Form.Select>
              </Form.Group>
            </Col>
            <Col md={2}>
              <Form.Group>
                <Form.Label>Featured</Form.Label>
                <Form.Select name="is_featured" value={filters.is_featured} onChange={handleFilterChange}>
                  <option value="">All</option>
                  <option value="true">Featured</option>
                  <option value="false">Not Featured</option>
                </Form.Select>
              </Form.Group>
            </Col>
            <Col md={2}>
              <Form.Group>
                <Form.Label>Slider</Form.Label>
                <Form.Select name="is_slider" value={filters.is_slider} onChange={handleFilterChange}>
                  <option value="">All</option>
                  <option value="true">In Slider</option>
                  <option value="false">Not in Slider</option>
                </Form.Select>
              </Form.Group>
            </Col>
            <Col md={2}>
              <Form.Group>
                <Form.Label>Campus</Form.Label>
                <Form.Select name="is_campus" value={filters.is_campus} onChange={handleFilterChange}>
                  <option value="">All</option>
                  <option value="true">Campus</option>
                  <option value="false">Not Campus</option>
                </Form.Select>
              </Form.Group>
            </Col>
            <Col md={2}>
              <Form.Group>
                <Form.Label>Status</Form.Label>
                <Form.Select name="is_active" value={filters.is_active} onChange={handleFilterChange}>
                  <option value="">All</option>
                  <option value="true">Active</option>
                  <option value="false">Inactive</option>
                </Form.Select>
              </Form.Group>
            </Col>
            <Col md={1} className="d-flex align-items-end">
              <Button 
                variant="outline-secondary" 
                onClick={() => setFilters({
                  category: '',
                  is_featured: '',
                  is_slider: '',
                  is_campus: '',
                  is_active: ''
                })}
              >
                <i className="fas fa-times"></i>
              </Button>
            </Col>
          </Row>
        </Card.Body>
      </Card>

      {/* Images Table */}
      <Card>
        <Card.Header>
          <h5 className="mb-0">
            <i className="fas fa-list me-2"></i>
            Images ({images.length})
          </h5>
        </Card.Header>
        <Card.Body className="p-0">
          {loading ? (
            <div className="text-center py-4">
              <i className="fas fa-spinner fa-spin me-2"></i>
              Loading images...
            </div>
          ) : images.length === 0 ? (
            <div className="text-center py-5">
              <i className="fas fa-images fa-3x text-muted mb-3"></i>
              <h5 className="text-muted">No Images Found</h5>
              <p className="text-muted">Upload your first image to get started.</p>
              <Button variant="primary" onClick={() => setShowCreateModal(true)}>
                <i className="fas fa-plus me-2"></i>
                Upload First Image
              </Button>
            </div>
          ) : (
            <Table responsive hover className="mb-0">
              <thead className="table-light">
                <tr>
                  <th>Image</th>
                  <th>Title</th>
                  <th>Category</th>
                  <th>Tags</th>
                  <th>Status</th>
                  <th>Actions</th>
                </tr>
              </thead>
              <tbody>
                {images.map((image) => (
                  <tr key={image.id}>
                    <td>
                      <Image 
                        src={getMediaUrl(image.image)} 
                        alt={image.title}
                        style={{ width: '60px', height: '60px', objectFit: 'cover' }}
                        rounded
                      />
                    </td>
                    <td>
                      <div>
                        <div className="fw-bold">{image.title}</div>
                        <small className="text-muted text-truncate d-block" style={{ maxWidth: '200px' }}>
                          {image.description}
                        </small>
                      </div>
                    </td>
                    <td>
                      <Badge bg="info">{getCategoryName(image.category)}</Badge>
                    </td>
                    <td>
                      <div className="text-truncate" style={{ maxWidth: '150px' }}>
                        {image.tags || <em className="text-muted">No tags</em>}
                      </div>
                    </td>
                    <td>
                      <div className="d-flex flex-column gap-1">
                        {image.is_featured && <Badge bg="warning" className="small">Featured</Badge>}
                        {image.is_slider && <Badge bg="primary" className="small">Slider</Badge>}
                        {image.is_campus && <Badge bg="success" className="small">Campus</Badge>}
                        <Badge bg={image.is_active ? 'success' : 'secondary'} className="small">
                          {image.is_active ? 'Active' : 'Inactive'}
                        </Badge>
                      </div>
                    </td>
                    <td>
                      <div className="d-flex gap-2">
                        <Button
                          variant="outline-primary"
                          size="sm"
                          onClick={() => handleEdit(image)}
                          disabled={loading}
                        >
                          <i className="fas fa-edit"></i>
                        </Button>
                        <Button
                          variant="outline-danger"
                          size="sm"
                          onClick={() => handleDelete(image.id)}
                          disabled={loading}
                        >
                          <i className="fas fa-trash"></i>
                        </Button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </Table>
          )}
        </Card.Body>
      </Card>

      {/* Create/Edit Modal */}
      <Modal show={showCreateModal || showEditModal} onHide={() => {
        setShowCreateModal(false);
        setShowEditModal(false);
        resetForm();
      }} size="lg">
        <Modal.Header closeButton>
          <Modal.Title>
            <i className="fas fa-image me-2"></i>
            {editingImage ? 'Edit Image' : 'Upload New Image'}
          </Modal.Title>
        </Modal.Header>
        <Form onSubmit={handleSubmit}>
          <Modal.Body>
            <Row>
              <Col md={6}>
                <Form.Group className="mb-3">
                  <Form.Label>Title *</Form.Label>
                  <Form.Control
                    type="text"
                    name="title"
                    value={formData.title}
                    onChange={handleInputChange}
                    required
                    placeholder="Enter image title"
                  />
                </Form.Group>
              </Col>
              <Col md={6}>
                <Form.Group className="mb-3">
                  <Form.Label>Category *</Form.Label>
                  <Form.Select
                    name="category"
                    value={formData.category}
                    onChange={handleInputChange}
                    required
                  >
                    <option value="">Select category</option>
                    {categories.map(category => (
                      <option key={category.id} value={category.id}>{category.name}</option>
                    ))}
                  </Form.Select>
                </Form.Group>
              </Col>
            </Row>

            <Form.Group className="mb-3">
              <Form.Label>Description</Form.Label>
              <Form.Control
                as="textarea"
                rows={3}
                name="description"
                value={formData.description}
                onChange={handleInputChange}
                placeholder="Enter image description"
              />
            </Form.Group>

            <Form.Group className="mb-3">
              <Form.Label>Image File {!editingImage && '*'}</Form.Label>
              <Form.Control
                type="file"
                accept="image/*"
                onChange={(e) => {
                  const file = (e.target as HTMLInputElement).files?.[0];
                  setImageFile(file || null);
                }}
                required={!editingImage}
              />
              <Form.Text className="text-muted">
                Supported formats: JPG, PNG, WebP. Max size: 10MB
              </Form.Text>
            </Form.Group>

            <Row>
              <Col md={6}>
                <Form.Group className="mb-3">
                  <Form.Label>Tags</Form.Label>
                  <Form.Control
                    type="text"
                    name="tags"
                    value={formData.tags}
                    onChange={handleInputChange}
                    placeholder="Comma-separated tags"
                  />
                </Form.Group>
              </Col>
              <Col md={6}>
                <Form.Group className="mb-3">
                  <Form.Label>Order</Form.Label>
                  <Form.Control
                    type="number"
                    name="order"
                    value={formData.order}
                    onChange={handleInputChange}
                    min="0"
                  />
                </Form.Group>
              </Col>
            </Row>

            <Row>
              <Col md={6}>
                <Form.Group className="mb-3">
                  <Form.Label>Photographer</Form.Label>
                  <Form.Control
                    type="text"
                    name="photographer"
                    value={formData.photographer}
                    onChange={handleInputChange}
                    placeholder="Photographer name"
                  />
                </Form.Group>
              </Col>
              <Col md={6}>
                <Form.Group className="mb-3">
                  <Form.Label>Location</Form.Label>
                  <Form.Control
                    type="text"
                    name="location"
                    value={formData.location}
                    onChange={handleInputChange}
                    placeholder="Photo location"
                  />
                </Form.Group>
              </Col>
            </Row>

            <Form.Group className="mb-3">
              <Form.Label>Alt Text</Form.Label>
              <Form.Control
                type="text"
                name="alt_text"
                value={formData.alt_text}
                onChange={handleInputChange}
                placeholder="Alternative text for accessibility"
              />
            </Form.Group>

            <Row>
              <Col md={3}>
                <Form.Group className="mb-3">
                  <Form.Check
                    type="checkbox"
                    name="is_featured"
                    checked={formData.is_featured}
                    onChange={handleInputChange}
                    label="Featured"
                  />
                </Form.Group>
              </Col>
              <Col md={3}>
                <Form.Group className="mb-3">
                  <Form.Check
                    type="checkbox"
                    name="is_slider"
                    checked={formData.is_slider}
                    onChange={handleInputChange}
                    label="Include in Slider"
                  />
                </Form.Group>
              </Col>
              <Col md={3}>
                <Form.Group className="mb-3">
                  <Form.Check
                    type="checkbox"
                    name="is_campus"
                    checked={formData.is_campus}
                    onChange={handleInputChange}
                    label="Campus Related"
                  />
                </Form.Group>
              </Col>
              <Col md={3}>
                <Form.Group className="mb-3">
                  <Form.Check
                    type="checkbox"
                    name="is_active"
                    checked={formData.is_active}
                    onChange={handleInputChange}
                    label="Active"
                  />
                </Form.Group>
              </Col>
            </Row>
          </Modal.Body>
          <Modal.Footer>
            <Button variant="secondary" onClick={() => {
              setShowCreateModal(false);
              setShowEditModal(false);
              resetForm();
            }}>
              Cancel
            </Button>
            <Button variant="primary" type="submit" disabled={loading}>
              {loading ? (
                <>
                  <i className="fas fa-spinner fa-spin me-2"></i>
                  {editingImage ? 'Updating...' : 'Uploading...'}
                </>
              ) : (
                <>
                  <i className="fas fa-save me-2"></i>
                  {editingImage ? 'Update Image' : 'Upload Image'}
                </>
              )}
            </Button>
          </Modal.Footer>
        </Form>
      </Modal>
    </Container>
  );
};

export default GalleryImageManagement;
