#!/bin/bash

# Production Deployment Script for UoG Event Management System
# This script deploys the application with Nginx as a reverse proxy

set -e

COMPOSE_FILE="docker-compose.prod.yml"
ENV_FILE=".env.prod"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
CYAN='\033[0;36m'
WHITE='\033[1;37m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}🚀 UoG Event Management System - Production Deployment${NC}"
    echo -e "${GREEN}============================================================${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_info() {
    echo -e "${CYAN}📋 $1${NC}"
}

# Check if Docker is running
check_docker() {
    if ! docker version >/dev/null 2>&1; then
        print_error "Docker is not running. Please start Docker and try again."
        exit 1
    fi
}

# Check if environment file exists
check_env() {
    if [ ! -f "$ENV_FILE" ]; then
        print_warning "Environment file $ENV_FILE not found."
        print_info "Creating a sample environment file..."
        cp ".env.prod" ".env"
        print_success "Please edit .env file with your production settings before continuing."
        exit 0
    fi
}

# Show help
show_help() {
    echo "Usage: $0 [OPTIONS]"
    echo ""
    echo "Options:"
    echo "  --build     Build Docker images before deployment"
    echo "  --stop      Stop all services"
    echo "  --restart   Restart all services"
    echo "  --logs      Show service logs"
    echo "  --status    Show service status"
    echo "  --clean     Clean up Docker resources"
    echo "  --help      Show this help message"
    echo ""
}

# Parse command line arguments
BUILD=false
STOP=false
RESTART=false
LOGS=false
STATUS=false
CLEAN=false

while [[ $# -gt 0 ]]; do
    case $1 in
        --build)
            BUILD=true
            shift
            ;;
        --stop)
            STOP=true
            shift
            ;;
        --restart)
            RESTART=true
            shift
            ;;
        --logs)
            LOGS=true
            shift
            ;;
        --status)
            STATUS=true
            shift
            ;;
        --clean)
            CLEAN=true
            shift
            ;;
        --help)
            show_help
            exit 0
            ;;
        *)
            echo "Unknown option $1"
            show_help
            exit 1
            ;;
    esac
done

print_status
check_docker

# Stop services
if [ "$STOP" = true ]; then
    print_warning "Stopping production services..."
    docker-compose -f $COMPOSE_FILE --env-file $ENV_FILE down
    print_success "Services stopped."
    exit 0
fi

# Clean up
if [ "$CLEAN" = true ]; then
    print_warning "Cleaning up Docker resources..."
    docker-compose -f $COMPOSE_FILE --env-file $ENV_FILE down -v --rmi all
    print_success "Cleanup completed."
    exit 0
fi

# Show logs
if [ "$LOGS" = true ]; then
    print_info "Showing service logs..."
    docker-compose -f $COMPOSE_FILE --env-file $ENV_FILE logs -f
    exit 0
fi

# Show status
if [ "$STATUS" = true ]; then
    print_info "Service Status:"
    docker-compose -f $COMPOSE_FILE --env-file $ENV_FILE ps
    echo ""
    print_info "Access URLs:"
    echo -e "   Frontend: ${WHITE}https://event.uog.edu.et${NC}"
    echo -e "   Backend API: ${WHITE}https://event.uog.edu.et/api${NC}"
    echo -e "   Admin Panel: ${WHITE}https://event.uog.edu.et/admin${NC}"
    echo -e "   Health Check: ${WHITE}https://event.uog.edu.et/health${NC}"
    exit 0
fi

# Restart services
if [ "$RESTART" = true ]; then
    print_warning "Restarting production services..."
    docker-compose -f $COMPOSE_FILE --env-file $ENV_FILE restart
    print_success "Services restarted."
    exit 0
fi

check_env

# Build and deploy
print_info "Building and deploying production environment..."

# Build images if requested
if [ "$BUILD" = true ]; then
    print_warning "Building Docker images..."
    docker-compose -f $COMPOSE_FILE --env-file $ENV_FILE build --no-cache
fi

# Start services
print_warning "Starting production services..."
docker-compose -f $COMPOSE_FILE --env-file $ENV_FILE up -d

# Wait for services to be ready
print_warning "Waiting for services to be ready..."
sleep 30

# Check service status
print_info "Checking service status..."
docker-compose -f $COMPOSE_FILE --env-file $ENV_FILE ps

# Test health endpoints
print_info "Testing health endpoints..."
if curl -s -o /dev/null -w "%{http_code}" "https://event.uog.edu.et/health/" | grep -q "200"; then
    print_success "Backend health check: PASSED"
else
    print_error "Backend health check: FAILED"
fi

if curl -s -o /dev/null -w "%{http_code}" "https://event.uog.edu.et/" | grep -q "200"; then
    print_success "Frontend health check: PASSED"
else
    print_error "Frontend health check: FAILED"
fi

echo ""
print_success "Production deployment completed!"
echo -e "${GREEN}============================================================${NC}"
print_info "Access URLs:"
echo -e "   Frontend: ${WHITE}https://event.uog.edu.et${NC}"
echo -e "   Backend API: ${WHITE}https://event.uog.edu.et/api${NC}"
echo -e "   Admin Panel: ${WHITE}https://event.uog.edu.et/admin${NC}"
echo -e "   Health Check: ${WHITE}https://event.uog.edu.et/health${NC}"
echo ""
print_info "Useful commands:"
echo -e "   View logs: ${WHITE}./deploy-production.sh --logs${NC}"
echo -e "   Check status: ${WHITE}./deploy-production.sh --status${NC}"
echo -e "   Restart: ${WHITE}./deploy-production.sh --restart${NC}"
echo -e "   Stop: ${WHITE}./deploy-production.sh --stop${NC}"
