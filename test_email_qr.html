<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Email QR Code Parsing</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-case {
            border: 1px solid #ddd;
            margin: 10px 0;
            padding: 15px;
            border-radius: 5px;
        }
        .success {
            background-color: #d4edda;
            border-color: #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            border-color: #f5c6cb;
        }
        code {
            background-color: #f8f9fa;
            padding: 2px 4px;
            border-radius: 3px;
            font-family: monospace;
        }
    </style>
</head>
<body>
    <h1>QR Code Email Parsing Test</h1>
    <p>This page tests the QR code parsing logic for email-based URLs.</p>
    
    <div id="results"></div>

    <script>
        function testQRParsing() {
            const testCases = [
                {
                    name: "Email URL (like the failing case)",
                    qrData: "https://event.uog.edu.et/events?email=<EMAIL>",
                    expectedIdentifier: "<EMAIL>",
                    expectedParam: "id"
                },
                {
                    name: "UUID URL",
                    qrData: "https://event.uog.edu.et/verify/123e4567-e89b-12d3-a456-************",
                    expectedIdentifier: "123e4567-e89b-12d3-a456-************",
                    expectedParam: "uuid"
                },
                {
                    name: "JSON with participant_id",
                    qrData: '{"participant_id": "123", "name": "Test User"}',
                    expectedIdentifier: "123",
                    expectedParam: "uuid"
                },
                {
                    name: "Direct email",
                    qrData: "<EMAIL>",
                    expectedIdentifier: "<EMAIL>",
                    expectedParam: "id"
                }
            ];

            const resultsDiv = document.getElementById('results');
            resultsDiv.innerHTML = '';

            testCases.forEach(testCase => {
                const result = parseQRCode(testCase.qrData);
                const success = result.identifier === testCase.expectedIdentifier && 
                               result.param === testCase.expectedParam;
                
                const div = document.createElement('div');
                div.className = `test-case ${success ? 'success' : 'error'}`;
                div.innerHTML = `
                    <h3>${testCase.name}</h3>
                    <p><strong>Input:</strong> <code>${testCase.qrData}</code></p>
                    <p><strong>Expected:</strong> identifier=<code>${testCase.expectedIdentifier}</code>, param=<code>${testCase.expectedParam}</code></p>
                    <p><strong>Actual:</strong> identifier=<code>${result.identifier}</code>, param=<code>${result.param}</code></p>
                    <p><strong>Result:</strong> ${success ? '✅ PASS' : '❌ FAIL'}</p>
                `;
                resultsDiv.appendChild(div);
            });
        }

        function parseQRCode(decodedText) {
            let qrData = null;
            let participantIdentifier = null;

            // Try to parse as JSON first (for structured QR codes)
            try {
                qrData = JSON.parse(decodedText);
                if (qrData.participant_id) {
                    participantIdentifier = qrData.participant_id;
                } else if (qrData.uuid) {
                    participantIdentifier = qrData.uuid;
                }
            } catch (jsonError) {
                // Not JSON, try URL parsing
                if (decodedText.includes('/verify/') || decodedText.includes('/participant/')) {
                    const urlParts = decodedText.split('/');
                    participantIdentifier = urlParts[urlParts.length - 1];
                } else if (decodedText.includes('?')) {
                    // Handle query parameter format
                    try {
                        const url = new URL(decodedText);
                        participantIdentifier = url.searchParams.get('email') || 
                                              url.searchParams.get('participant_id') || 
                                              url.searchParams.get('id') ||
                                              url.searchParams.get('uuid');
                    } catch (urlError) {
                        // If URL parsing fails, try manual parameter extraction
                        const urlParams = new URLSearchParams(decodedText.split('?')[1]);
                        participantIdentifier = urlParams.get('email') || 
                                              urlParams.get('participant_id') || 
                                              urlParams.get('id') ||
                                              urlParams.get('uuid');
                    }
                } else {
                    // Assume the entire string is the identifier
                    participantIdentifier = decodedText.trim();
                }
            }

            // Determine parameter type
            const param = participantIdentifier && participantIdentifier.includes('@') ? 'id' : 'uuid';

            return {
                identifier: participantIdentifier,
                param: param
            };
        }

        // Run tests when page loads
        window.onload = testQRParsing;
    </script>
</body>
</html>
