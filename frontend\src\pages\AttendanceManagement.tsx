import React, { useState, useEffect } from 'react';
import { Con<PERSON><PERSON>, <PERSON>, Col, <PERSON>, Button, Alert, Form, Modal, Badge, Table } from 'react-bootstrap';
import { useAuth } from '../contexts/AuthContext';
import api from '../services/api';

interface Event {
  id: number;
  name: string;
  start_date: string;
  end_date: string;
}

interface EventSchedule {
  id: number;
  title: string;
  start_time: string;
  end_time: string;
  event: number;
}

interface AttendanceRecord {
  id: number;
  participant_name: string;
  event_title: string;
  checked_in_at: string;
  checked_in_by: string;
  notes: string;
}

interface DailyReport {
  date: string;
  total_attendance: number;
  unique_participants: number;
  schedule_breakdown: Array<{
    event_schedule__title: string;
    event_schedule__start_time: string;
    event_schedule__event__name: string;
    attendance_count: number;
  }>;
  participant_type_breakdown: Array<{
    participant__participant_type__name: string;
    participant__participant_type__color: string;
    attendance_count: number;
  }>;
}

const AttendanceManagement: React.FC = () => {
  const { isAuthenticated } = useAuth();
  const [activeTab, setActiveTab] = useState('scanner');
  const [events, setEvents] = useState<Event[]>([]);
  const [schedules, setSchedules] = useState<EventSchedule[]>([]);
  const [selectedEvent, setSelectedEvent] = useState<string>('');
  const [selectedSchedule, setSelectedSchedule] = useState<string>('');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');

  // QR Scanner states
  const [qrInput, setQrInput] = useState('');
  const [checkedInBy, setCheckedInBy] = useState('');
  const [notes, setNotes] = useState('');

  // Reports states
  const [reportDate, setReportDate] = useState(new Date().toISOString().split('T')[0]);
  const [dailyReport, setDailyReport] = useState<DailyReport | null>(null);
  const [attendanceRecords, setAttendanceRecords] = useState<AttendanceRecord[]>([]);

  useEffect(() => {
    if (isAuthenticated) {
      fetchEvents();
    }
  }, [isAuthenticated]);

  useEffect(() => {
    if (selectedEvent) {
      fetchSchedules();
    }
  }, [selectedEvent]);

  const fetchEvents = async () => {
    try {
      const response = await api.get('/events/');
      setEvents(response.data.results || response.data);
    } catch (err) {
      setError('Failed to fetch events');
    }
  };

  const fetchSchedules = async () => {
    if (!selectedEvent) return;
    
    try {
      const response = await api.get(`/schedules/?event=${selectedEvent}`);
      setSchedules(response.data.results || response.data);
    } catch (err) {
      setError('Failed to fetch schedules');
    }
  };

  const handleQRScan = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!qrInput || !selectedSchedule) return;

    setLoading(true);
    try {
      // Extract UUID from QR code URL or use direct UUID
      const uuid = qrInput.includes('/verify/') 
        ? qrInput.split('/verify/')[1] 
        : qrInput;

      const response = await api.post('/attendance/check_in/', {
        uuid: uuid,
        event_schedule_id: selectedSchedule,
        checked_in_by: checkedInBy,
        notes: notes
      });

      setSuccess(`Successfully checked in: ${response.data.participant_name}`);
      setQrInput('');
      setNotes('');
    } catch (err: any) {
      setError(err.response?.data?.error || 'Failed to check in participant');
    } finally {
      setLoading(false);
    }
  };

  const fetchDailyReport = async () => {
    setLoading(true);
    try {
      const params = new URLSearchParams({
        date: reportDate,
        ...(selectedEvent && { event: selectedEvent })
      });

      const response = await api.get(`/attendance/daily_report/?${params}`);
      setDailyReport(response.data);
    } catch (err) {
      setError('Failed to fetch daily report');
    } finally {
      setLoading(false);
    }
  };

  const exportDailyReport = async () => {
    try {
      const params = new URLSearchParams({
        date: reportDate,
        ...(selectedEvent && { event: selectedEvent })
      });

      const response = await api.get(`/attendance/export_daily_report/?${params}`, {
        responseType: 'blob'
      });

      // Create download link
      const url = window.URL.createObjectURL(new Blob([response.data]));
      const link = document.createElement('a');
      link.href = url;
      link.setAttribute('download', `attendance_report_${reportDate}.csv`);
      document.body.appendChild(link);
      link.click();
      link.remove();
      window.URL.revokeObjectURL(url);

      setSuccess('Report exported successfully!');
    } catch (err) {
      setError('Failed to export report');
    }
  };

  const fetchSessionAttendance = async () => {
    if (!selectedSchedule) return;

    setLoading(true);
    try {
      const response = await api.get(`/attendance/session_attendance/?event_schedule_id=${selectedSchedule}`);
      setAttendanceRecords(response.data.attendance_records);
    } catch (err) {
      setError('Failed to fetch session attendance');
    } finally {
      setLoading(false);
    }
  };

  if (!isAuthenticated) {
    return (
      <Container className="py-5">
        <Alert variant="warning">Please log in to access attendance management.</Alert>
      </Container>
    );
  }

  return (
    <Container fluid className="py-4">
      <Row>
        <Col>
          <h2 className="mb-4">
            <i className="fas fa-qrcode me-2"></i>
            Attendance Management
          </h2>

          {error && (
            <Alert variant="danger" dismissible onClose={() => setError('')}>
              {error}
            </Alert>
          )}

          {success && (
            <Alert variant="success" dismissible onClose={() => setSuccess('')}>
              {success}
            </Alert>
          )}

          {/* Navigation Tabs */}
          <div className="mb-4">
            <Button
              variant={activeTab === 'scanner' ? 'primary' : 'outline-primary'}
              className="me-2 mb-2"
              onClick={() => setActiveTab('scanner')}
            >
              <i className="fas fa-qrcode me-1"></i>
              QR Scanner
            </Button>
            <Button
              variant={activeTab === 'reports' ? 'primary' : 'outline-primary'}
              className="me-2 mb-2"
              onClick={() => setActiveTab('reports')}
            >
              <i className="fas fa-chart-bar me-1"></i>
              Daily Reports
            </Button>
            <Button
              variant={activeTab === 'session' ? 'primary' : 'outline-primary'}
              className="me-2 mb-2"
              onClick={() => setActiveTab('session')}
            >
              <i className="fas fa-users me-1"></i>
              Session Attendance
            </Button>
          </div>

          {/* Event Selection */}
          <Card className="mb-4">
            <Card.Header>
              <h5 className="mb-0">Event & Session Selection</h5>
            </Card.Header>
            <Card.Body>
              <Row>
                <Col md={6}>
                  <Form.Group className="mb-3">
                    <Form.Label>Select Event</Form.Label>
                    <Form.Select
                      value={selectedEvent}
                      onChange={(e) => setSelectedEvent(e.target.value)}
                    >
                      <option value="">Choose an event...</option>
                      {events.map((event) => (
                        <option key={event.id} value={event.id}>
                          {event.name}
                        </option>
                      ))}
                    </Form.Select>
                  </Form.Group>
                </Col>
                <Col md={6}>
                  <Form.Group className="mb-3">
                    <Form.Label>Select Session</Form.Label>
                    <Form.Select
                      value={selectedSchedule}
                      onChange={(e) => setSelectedSchedule(e.target.value)}
                      disabled={!selectedEvent}
                    >
                      <option value="">Choose a session...</option>
                      {schedules.map((schedule) => (
                        <option key={schedule.id} value={schedule.id}>
                          {schedule.title} - {new Date(schedule.start_time).toLocaleString()}
                        </option>
                      ))}
                    </Form.Select>
                  </Form.Group>
                </Col>
              </Row>
            </Card.Body>
          </Card>

          {/* QR Scanner Tab */}
          {activeTab === 'scanner' && (
            <Card>
              <Card.Header>
                <h5 className="mb-0">QR Code Scanner</h5>
              </Card.Header>
              <Card.Body>
                <Form onSubmit={handleQRScan}>
                  <Row>
                    <Col md={6}>
                      <Form.Group className="mb-3">
                        <Form.Label>QR Code / Participant UUID</Form.Label>
                        <Form.Control
                          type="text"
                          value={qrInput}
                          onChange={(e) => setQrInput(e.target.value)}
                          placeholder="Scan QR code or enter participant UUID"
                          required
                        />
                        <Form.Text className="text-muted">
                          Scan the participant's QR code or manually enter their UUID
                        </Form.Text>
                      </Form.Group>
                    </Col>
                    <Col md={6}>
                      <Form.Group className="mb-3">
                        <Form.Label>Checked In By</Form.Label>
                        <Form.Control
                          type="text"
                          value={checkedInBy}
                          onChange={(e) => setCheckedInBy(e.target.value)}
                          placeholder="Your name"
                        />
                      </Form.Group>
                    </Col>
                  </Row>
                  <Form.Group className="mb-3">
                    <Form.Label>Notes (Optional)</Form.Label>
                    <Form.Control
                      as="textarea"
                      rows={2}
                      value={notes}
                      onChange={(e) => setNotes(e.target.value)}
                      placeholder="Any additional notes..."
                    />
                  </Form.Group>
                  <Button
                    type="submit"
                    variant="success"
                    disabled={loading || !selectedSchedule}
                    size="lg"
                  >
                    {loading ? (
                      <>
                        <div className="spinner-border spinner-border-sm me-2" role="status"></div>
                        Checking In...
                      </>
                    ) : (
                      <>
                        <i className="fas fa-check me-2"></i>
                        Check In Participant
                      </>
                    )}
                  </Button>
                </Form>
              </Card.Body>
            </Card>
          )}

          {/* Daily Reports Tab */}
          {activeTab === 'reports' && (
            <Card>
              <Card.Header className="d-flex justify-content-between align-items-center">
                <h5 className="mb-0">Daily Attendance Report</h5>
                <div>
                  <Button
                    variant="primary"
                    onClick={fetchDailyReport}
                    disabled={loading}
                    className="me-2"
                  >
                    <i className="fas fa-sync me-1"></i>
                    Generate Report
                  </Button>
                  <Button
                    variant="success"
                    onClick={exportDailyReport}
                    disabled={!dailyReport}
                  >
                    <i className="fas fa-download me-1"></i>
                    Export CSV
                  </Button>
                </div>
              </Card.Header>
              <Card.Body>
                <Row className="mb-3">
                  <Col md={4}>
                    <Form.Group>
                      <Form.Label>Report Date</Form.Label>
                      <Form.Control
                        type="date"
                        value={reportDate}
                        onChange={(e) => setReportDate(e.target.value)}
                      />
                    </Form.Group>
                  </Col>
                </Row>

                {loading ? (
                  <div className="text-center py-4">
                    <div className="spinner-border" role="status">
                      <span className="visually-hidden">Loading...</span>
                    </div>
                  </div>
                ) : dailyReport ? (
                  <>
                    {/* Summary Cards */}
                    <Row className="mb-4">
                      <Col md={4}>
                        <Card className="text-center">
                          <Card.Body>
                            <h3 className="text-primary">{dailyReport.total_attendance}</h3>
                            <p className="mb-0">Total Check-ins</p>
                          </Card.Body>
                        </Card>
                      </Col>
                      <Col md={4}>
                        <Card className="text-center">
                          <Card.Body>
                            <h3 className="text-success">{dailyReport.unique_participants}</h3>
                            <p className="mb-0">Unique Participants</p>
                          </Card.Body>
                        </Card>
                      </Col>
                      <Col md={4}>
                        <Card className="text-center">
                          <Card.Body>
                            <h3 className="text-info">{dailyReport.schedule_breakdown.length}</h3>
                            <p className="mb-0">Sessions</p>
                          </Card.Body>
                        </Card>
                      </Col>
                    </Row>

                    {/* Session Breakdown */}
                    <h6>Session Breakdown</h6>
                    <Table striped bordered hover className="mb-4">
                      <thead>
                        <tr>
                          <th>Session</th>
                          <th>Event</th>
                          <th>Time</th>
                          <th>Attendance</th>
                        </tr>
                      </thead>
                      <tbody>
                        {dailyReport.schedule_breakdown.map((session, index) => (
                          <tr key={index}>
                            <td>{session.event_schedule__title}</td>
                            <td>{session.event_schedule__event__name}</td>
                            <td>{new Date(session.event_schedule__start_time).toLocaleString()}</td>
                            <td>
                              <Badge bg="primary">{session.attendance_count}</Badge>
                            </td>
                          </tr>
                        ))}
                      </tbody>
                    </Table>

                    {/* Participant Type Breakdown */}
                    <h6>Participant Type Breakdown</h6>
                    <Table striped bordered hover>
                      <thead>
                        <tr>
                          <th>Participant Type</th>
                          <th>Attendance Count</th>
                        </tr>
                      </thead>
                      <tbody>
                        {dailyReport.participant_type_breakdown.map((type, index) => (
                          <tr key={index}>
                            <td>
                              <Badge 
                                style={{ backgroundColor: type.participant__participant_type__color }}
                                className="me-2"
                              >
                                {type.participant__participant_type__name}
                              </Badge>
                            </td>
                            <td>
                              <Badge bg="primary">{type.attendance_count}</Badge>
                            </td>
                          </tr>
                        ))}
                      </tbody>
                    </Table>
                  </>
                ) : (
                  <div className="text-center py-4">
                    <i className="fas fa-chart-bar fa-3x text-muted mb-3"></i>
                    <p className="text-muted">Click "Generate Report" to view attendance data</p>
                  </div>
                )}
              </Card.Body>
            </Card>
          )}

          {/* Session Attendance Tab */}
          {activeTab === 'session' && (
            <Card>
              <Card.Header className="d-flex justify-content-between align-items-center">
                <h5 className="mb-0">Session Attendance</h5>
                <Button
                  variant="primary"
                  onClick={fetchSessionAttendance}
                  disabled={loading || !selectedSchedule}
                >
                  <i className="fas fa-sync me-1"></i>
                  Load Attendance
                </Button>
              </Card.Header>
              <Card.Body>
                {!selectedSchedule ? (
                  <div className="text-center py-4">
                    <i className="fas fa-calendar fa-3x text-muted mb-3"></i>
                    <p className="text-muted">Please select a session to view attendance</p>
                  </div>
                ) : loading ? (
                  <div className="text-center py-4">
                    <div className="spinner-border" role="status">
                      <span className="visually-hidden">Loading...</span>
                    </div>
                  </div>
                ) : attendanceRecords.length === 0 ? (
                  <div className="text-center py-4">
                    <i className="fas fa-users fa-3x text-muted mb-3"></i>
                    <p className="text-muted">No attendance records found for this session</p>
                  </div>
                ) : (
                  <Table striped bordered hover>
                    <thead>
                      <tr>
                        <th>Participant</th>
                        <th>Check-in Time</th>
                        <th>Checked In By</th>
                        <th>Notes</th>
                      </tr>
                    </thead>
                    <tbody>
                      {attendanceRecords.map((record) => (
                        <tr key={record.id}>
                          <td>{record.participant_name}</td>
                          <td>{new Date(record.checked_in_at).toLocaleString()}</td>
                          <td>{record.checked_in_by || '-'}</td>
                          <td>{record.notes || '-'}</td>
                        </tr>
                      ))}
                    </tbody>
                  </Table>
                )}
              </Card.Body>
            </Card>
          )}
        </Col>
      </Row>
    </Container>
  );
};

export default AttendanceManagement;
