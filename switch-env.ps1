# PowerShell script to switch between different environment configurations
# Usage: .\switch-env.ps1 [localhost|ip|domain]

param(
    [Parameter(Mandatory=$true)]
    [ValidateSet("localhost", "ip", "domain")]
    [string]$Environment
)

$ErrorActionPreference = "Stop"

Write-Host "🔧 Switching to $Environment environment configuration..." -ForegroundColor Blue

# Define environment files
$envFiles = @{
    "localhost" = ".env.localhost"
    "ip" = ".env.ip"
    "domain" = ".env.domain"
}

$sourceFile = $envFiles[$Environment]

if (-not (Test-Path $sourceFile)) {
    Write-Host "❌ Environment file $sourceFile not found!" -ForegroundColor Red
    exit 1
}

# Backup current .env if it exists
if (Test-Path ".env") {
    $timestamp = Get-Date -Format "yyyyMMdd_HHmmss"
    $backupFile = ".env.backup.$timestamp"
    Copy-Item ".env" $backupFile
    Write-Host "📦 Backed up current .env to $backupFile" -ForegroundColor Yellow
}

# Copy the selected environment file to .env
Copy-Item $sourceFile ".env"

# Read the new configuration
$config = Get-Content ".env" | Where-Object { $_ -match "REACT_APP_" }

Write-Host "✅ Environment switched to: $Environment" -ForegroundColor Green
Write-Host "📋 Frontend API Configuration:" -ForegroundColor Cyan

foreach ($line in $config) {
    if ($line -match "^(REACT_APP_[^=]+)=(.+)$") {
        Write-Host "   $($matches[1]): $($matches[2])" -ForegroundColor White
    }
}

Write-Host ""
Write-Host "🔄 Next steps:" -ForegroundColor Yellow
Write-Host "   1. Restart Docker services: docker-compose -f docker-compose.local.yml down && docker-compose -f docker-compose.local.yml up -d" -ForegroundColor White
Write-Host "   2. Wait for services to start (2-3 minutes)" -ForegroundColor White

switch ($Environment) {
    "localhost" {
        Write-Host "   3. Access frontend: http://localhost:3000" -ForegroundColor White
        Write-Host "   4. Access backend: http://localhost:8000" -ForegroundColor White
    }
    "ip" {
        Write-Host "   3. Access frontend: http://************:3000" -ForegroundColor White
        Write-Host "   4. Access backend: http://************:8000" -ForegroundColor White
    }
    "domain" {
        Write-Host "   3. Access frontend: http://event.uog.edu.et:3000" -ForegroundColor White
        Write-Host "   4. Access backend: http://event.uog.edu.et:8000" -ForegroundColor White
    }
}

Write-Host ""
Write-Host "💡 Environment configurations available:" -ForegroundColor Magenta
Write-Host "   • localhost - For internal/same machine access" -ForegroundColor White
Write-Host "   • ip - For external access via IP (************)" -ForegroundColor White
Write-Host "   • domain - For domain access (event.uog.edu.et)" -ForegroundColor White
