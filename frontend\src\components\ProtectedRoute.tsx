import React from 'react';
import { Navigate, useLocation } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext';
import { Container, Spinner, Alert } from 'react-bootstrap';
import { UserPermissions } from '../services/auth';

interface ProtectedRouteProps {
  children: React.ReactNode;
  requireAdmin?: boolean;
  fallbackPath?: string;
}

const ProtectedRoute: React.FC<ProtectedRouteProps> = ({
  children,
  requireAdmin = false,
  fallbackPath = '/login'
}) => {
  const { isAuthenticated, user, loading } = useAuth();
  const location = useLocation();

  // Show loading spinner while checking authentication
  if (loading) {
    return (
      <Container className="d-flex justify-content-center align-items-center min-vh-100">
        <div className="text-center">
          <Spinner animation="border" variant="primary" style={{ width: '3rem', height: '3rem' }} />
          <p className="mt-3 text-muted">Loading...</p>
        </div>
      </Container>
    );
  }

  // Redirect to login if not authenticated
  if (!isAuthenticated) {
    return <Navigate to={fallbackPath} state={{ from: location }} replace />;
  }

  // Check admin access if required
  if (requireAdmin && user?.role_name !== 'administrator') {
    return (
      <Container className="py-5">
        <Alert variant="danger" className="text-center">
          <i className="fas fa-ban fa-3x mb-3 d-block"></i>
          <h4>Access Denied</h4>
          <p className="mb-0">
            You need administrator privileges to access this page.
          </p>
          <hr />
          <p className="mb-0">
            Your current role: <strong>{user?.role_display_name}</strong>
          </p>
        </Alert>
      </Container>
    );
  }

  // Render the protected content
  return <>{children}</>;
};

export default ProtectedRoute;
