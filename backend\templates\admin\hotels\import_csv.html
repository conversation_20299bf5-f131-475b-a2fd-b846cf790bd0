{% extends "admin/base_site.html" %}
{% load i18n admin_urls static admin_modify %}

{% block title %}Import Hotels from CSV{% endblock %}

{% block breadcrumbs %}
<div class="breadcrumbs">
    <a href="{% url 'admin:index' %}">Home</a>
    &rsaquo; <a href="{% url 'admin:app_list' app_label='hotels' %}">Hotels</a>
    &rsaquo; <a href="{% url 'admin:hotels_hotel_changelist' %}">Hotels</a>
    &rsaquo; Import from CSV
</div>
{% endblock %}

{% block content %}
<div class="module aligned">
    <h1>Import Hotels from CSV</h1>
    
    <div class="form-row">
        <div class="help">
            <h3>CSV Format Instructions:</h3>
            <p>Your CSV file should contain the following columns (header row required):</p>
            <ul>
                <li><strong>name</strong> - Hotel name (required)</li>
                <li><strong>address</strong> - Hotel address (required)</li>
                <li><strong>phone</strong> - Phone number (required)</li>
                <li><strong>email</strong> - Email address (required)</li>
                <li><strong>contact_person</strong> - Contact person name (required)</li>
                <li><strong>star_rating</strong> - Star rating 1-5 (optional)</li>
                <li><strong>website</strong> - Hotel website URL (optional)</li>
                <li><strong>description</strong> - Hotel description (optional)</li>
                <li><strong>latitude</strong> - GPS latitude (optional)</li>
                <li><strong>longitude</strong> - GPS longitude (optional)</li>
                <li><strong>event</strong> - Event name (required)</li>
                <li><strong>active</strong> - Active status: yes/no (optional, default: yes)</li>
            </ul>
            
            <h4>Sample CSV Content:</h4>
            <pre style="background: #f8f9fa; padding: 10px; border-radius: 5px; font-size: 12px;">
name,address,phone,email,contact_person,star_rating,website,description,latitude,longitude,event,active
Grand Hotel,123 Main Street Gujrat,+92-53-1234567,<EMAIL>,Ahmed Ali,4,https://grandhotel.com,Luxury hotel in city center,32.5734,74.0788,Annual Conference,yes
City Inn,456 University Road Gujrat,+92-53-7654321,<EMAIL>,Sara Khan,3,https://cityinn.com,Budget friendly hotel,32.5689,74.0812,Annual Conference,yes
            </pre>
        </div>
    </div>
    
    <form method="post" enctype="multipart/form-data" class="module aligned">
        {% csrf_token %}
        
        <div class="form-row">
            <div>
                <label for="csv_file" class="required">CSV File:</label>
                <input type="file" name="csv_file" id="csv_file" accept=".csv" required>
                <p class="help">Select a CSV file containing hotel information.</p>
            </div>
        </div>
        
        <div class="submit-row">
            <input type="submit" value="Import Hotels" class="default" name="_save">
            <a href="{% url 'admin:hotels_hotel_changelist' %}" class="button cancel-link">Cancel</a>
        </div>
    </form>
    
    <div class="form-row">
        <div class="help">
            <h3>Important Notes:</h3>
            <ul>
                <li>Make sure the event names in your CSV match existing events in the system</li>
                <li>Star rating should be a number between 1 and 5</li>
                <li>Latitude and longitude should be decimal numbers (e.g., 32.5734, 74.0788)</li>
                <li>Website URLs should include http:// or https://</li>
                <li>Email addresses will be validated during import</li>
                <li>Any errors during import will be displayed after processing</li>
                <li>You can add hotel rooms separately after importing hotels</li>
            </ul>
        </div>
    </div>
    
    <div class="form-row">
        <div class="help">
            <h3>After Import:</h3>
            <p>Once hotels are imported, you can:</p>
            <ul>
                <li>Add hotel rooms through the Hotel Rooms admin section</li>
                <li>Assign hotels to participants in the Participants admin section</li>
                <li>Export hotel data using the export action in the hotels list</li>
                <li>Update hotel information as needed</li>
            </ul>
        </div>
    </div>
</div>

<style>
.help {
    background: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 5px;
    padding: 15px;
    margin: 15px 0;
}

.help h3, .help h4 {
    color: #495057;
    margin-top: 0;
}

.help ul {
    margin: 10px 0;
    padding-left: 20px;
}

.help li {
    margin: 5px 0;
}

pre {
    overflow-x: auto;
    white-space: pre-wrap;
    word-wrap: break-word;
}

.cancel-link {
    margin-left: 10px;
    text-decoration: none;
    color: #6c757d;
    padding: 8px 15px;
    border: 1px solid #6c757d;
    border-radius: 4px;
}

.cancel-link:hover {
    background-color: #6c757d;
    color: white;
}
</style>
{% endblock %}
