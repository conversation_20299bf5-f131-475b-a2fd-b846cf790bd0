/* My Events Section Styles */
.myevents-section {
  padding: 5rem 0;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  position: relative;
}

.myevents-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="%23dee2e6" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
  opacity: 0.3;
  z-index: 1;
}

.myevents-section .container {
  position: relative;
  z-index: 2;
}

.participant-lookup-card {
  background: white;
  border-radius: 20px;
  padding: 3rem;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(0, 0, 0, 0.05);
  position: relative;
  overflow: hidden;
}

.participant-lookup-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #667eea, #764ba2, #667eea);
  background-size: 200% 100%;
  animation: gradientShift 3s ease-in-out infinite;
}

@keyframes gradientShift {
  0%, 100% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
}

.lookup-header {
  text-align: center;
  margin-bottom: 2.5rem;
}

.lookup-header i {
  font-size: 3rem;
  color: #667eea;
  margin-bottom: 1rem;
  display: block;
}

.lookup-header h3 {
  font-size: 2rem;
  font-weight: 700;
  color: #2c3e50;
  margin-bottom: 0.5rem;
}

.lookup-header p {
  color: #6c757d;
  font-size: 1.1rem;
  margin-bottom: 0;
}

.lookup-form {
  margin-bottom: 2rem;
}

.form-group {
  margin-bottom: 1.5rem;
}

.form-group label {
  font-weight: 600;
  color: #2c3e50;
  margin-bottom: 0.5rem;
  display: block;
}

.form-control {
  border: 2px solid #e9ecef;
  border-radius: 10px;
  padding: 0.8rem 1rem;
  font-size: 1rem;
  transition: all 0.3s ease;
  background: #f8f9fa;
}

.form-control:focus {
  border-color: #667eea;
  box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
  background: white;
  outline: none;
}

.lookup-results {
  margin-top: 2rem;
  padding-top: 2rem;
  border-top: 2px solid #e9ecef;
}

.results-header {
  text-align: center;
  margin-bottom: 2rem;
}

.results-header i {
  font-size: 2.5rem;
  margin-bottom: 0.5rem;
  display: block;
}

.results-header h4 {
  font-size: 1.5rem;
  font-weight: 600;
  color: #2c3e50;
  margin-bottom: 0;
}

.participant-info {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1.5rem;
}

.info-card {
  background: #f8f9fa;
  border-radius: 15px;
  padding: 1.5rem;
  border: 1px solid #e9ecef;
  transition: all 0.3s ease;
}

.info-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.info-card h5 {
  font-size: 1.1rem;
  font-weight: 600;
  color: #2c3e50;
  margin-bottom: 1rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.info-card h5 i {
  color: #667eea;
  font-size: 1.2rem;
}

.info-content p {
  margin-bottom: 0.5rem;
  color: #495057;
  font-size: 0.95rem;
}

.info-content p:last-child {
  margin-bottom: 0;
}

.info-content strong {
  color: #2c3e50;
  font-weight: 600;
  display: inline-block;
  min-width: 80px;
}

/* Button Styles */
.btn-primary {
  background: linear-gradient(45deg, #667eea, #764ba2);
  border: none;
  border-radius: 25px;
  padding: 0.8rem 2rem;
  font-weight: 600;
  font-size: 1rem;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

.btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
  background: linear-gradient(45deg, #5a6fd8, #6a4190);
}

.btn-lg {
  padding: 1rem 2.5rem;
  font-size: 1.1rem;
}

/* Loading Animation */
.loading-spinner {
  display: inline-block;
  width: 20px;
  height: 20px;
  border: 2px solid #ffffff;
  border-radius: 50%;
  border-top-color: transparent;
  animation: spin 1s ease-in-out infinite;
  margin-right: 0.5rem;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

/* Error States */
.error-message {
  background: #f8d7da;
  color: #721c24;
  padding: 1rem;
  border-radius: 10px;
  border: 1px solid #f5c6cb;
  margin-top: 1rem;
  text-align: center;
}

.success-message {
  background: #d4edda;
  color: #155724;
  padding: 1rem;
  border-radius: 10px;
  border: 1px solid #c3e6cb;
  margin-top: 1rem;
  text-align: center;
}

/* Responsive Design */
@media (max-width: 768px) {
  .participant-lookup-card {
    padding: 2rem 1.5rem;
  }
  
  .lookup-header h3 {
    font-size: 1.5rem;
  }
  
  .lookup-header p {
    font-size: 1rem;
  }
  
  .participant-info {
    grid-template-columns: 1fr;
    gap: 1rem;
  }
  
  .info-card {
    padding: 1rem;
  }
  
  .btn-lg {
    width: 100%;
    padding: 1rem;
  }
}

@media (max-width: 576px) {
  .myevents-section {
    padding: 3rem 0;
  }
  
  .participant-lookup-card {
    padding: 1.5rem 1rem;
  }
  
  .lookup-header i {
    font-size: 2rem;
  }
  
  .lookup-header h3 {
    font-size: 1.3rem;
  }
  
  .form-control {
    padding: 0.7rem;
  }
}

/* Animation for showing results */
.lookup-results.show {
  display: block !important;
  animation: slideDown 0.5s ease-out;
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Card hover effects */
.info-card {
  position: relative;
  overflow: hidden;
}

.info-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(102, 126, 234, 0.1), transparent);
  transition: left 0.5s ease;
}

.info-card:hover::before {
  left: 100%;
}
