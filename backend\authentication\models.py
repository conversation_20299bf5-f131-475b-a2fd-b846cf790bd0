from django.contrib.auth.models import AbstractUser
from django.db import models

class UserRole(models.Model):
    """User roles with specific permissions"""
    ADMIN = 'admin'
    ATTENDANCE_TAKER = 'attendance_taker'
    GALLERY_UPLOADER = 'gallery_uploader'
    EVENT_SCHEDULER = 'event_scheduler'
    EVENT_ORGANIZER = 'event_organizer'
    PARTICIPANT = 'participant'
    
    ROLE_CHOICES = [
        (ADMI<PERSON>, 'Administrator'),
        (ATTENDANCE_TAKER, 'Attendance Taker'),
        (GALLERY_UPLOADER, 'Gallery Uploader'),
        (EVENT_SCHEDULER, 'Event Scheduler'),
        (EVENT_ORGANIZER, 'Event Organizer'),
        (PARTICIPANT, 'Participant'),
    ]
    
    name = models.CharField(max_length=50, choices=ROLE_CHOICES, unique=True)
    display_name = models.CharField(max_length=100)
    description = models.TextField(blank=True)
    color = models.CharField(max_length=7, default='#6c757d')  # Hex color for UI
    
    # Permissions
    can_manage_users = models.BooleanField(default=False)
    can_manage_events = models.BooleanField(default=False)
    can_manage_participants = models.BooleanField(default=False)
    can_take_attendance = models.BooleanField(default=False)
    can_upload_gallery = models.BooleanField(default=False)
    can_manage_schedule = models.BooleanField(default=False)
    can_generate_badges = models.BooleanField(default=False)
    can_manage_hotels = models.BooleanField(default=False)
    can_manage_drivers = models.BooleanField(default=False)
    can_view_reports = models.BooleanField(default=False)
    can_export_data = models.BooleanField(default=False)
    
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        db_table = 'user_roles'
        verbose_name = 'User Role'
        verbose_name_plural = 'User Roles'
    
    def __str__(self):
        return self.display_name

class CustomUser(AbstractUser):
    """Extended user model with role-based permissions"""
    role = models.ForeignKey(UserRole, on_delete=models.SET_NULL, null=True, blank=True)
    phone = models.CharField(max_length=20, blank=True)
    position = models.CharField(max_length=100, blank=True)
    institution = models.CharField(max_length=200, blank=True)
    profile_picture = models.ImageField(upload_to='profile_pictures/', blank=True, null=True)
    is_active_staff = models.BooleanField(default=True)
    last_login_ip = models.GenericIPAddressField(null=True, blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        db_table = 'custom_users'
        verbose_name = 'User'
        verbose_name_plural = 'Users'
    
    def __str__(self):
        return f"{self.get_full_name()} ({self.username})"
    
    @property
    def role_name(self):
        return 'administrator' if self.is_superuser else 'user'

    @property
    def role_display_name(self):
        return 'Administrator' if self.is_superuser else 'User'

    @property
    def role_color(self):
        return '#dc3545' if self.is_superuser else '#6c757d'
    
    def has_permission(self, permission):
        """Check if user has specific permission - simplified to superuser only"""
        return self.is_superuser

    def can_manage_users(self):
        return self.is_superuser

    def can_manage_events(self):
        return self.is_superuser

    def can_manage_participants(self):
        return self.is_superuser

    def can_take_attendance(self):
        return self.is_superuser

    def can_upload_gallery(self):
        return self.is_superuser

    def can_manage_schedule(self):
        return self.is_superuser

    def can_generate_badges(self):
        return self.is_superuser

    def can_manage_hotels(self):
        return self.is_superuser

    def can_manage_drivers(self):
        return self.is_superuser

    def can_view_reports(self):
        return self.is_superuser

    def can_export_data(self):
        return self.is_superuser

class UserSession(models.Model):
    """Track user sessions for security"""
    user = models.ForeignKey(CustomUser, on_delete=models.CASCADE, related_name='sessions')
    session_key = models.CharField(max_length=40, unique=True)
    ip_address = models.GenericIPAddressField()
    user_agent = models.TextField()
    created_at = models.DateTimeField(auto_now_add=True)
    last_activity = models.DateTimeField(auto_now=True)
    is_active = models.BooleanField(default=True)
    
    class Meta:
        db_table = 'user_sessions'
        verbose_name = 'User Session'
        verbose_name_plural = 'User Sessions'
    
    def __str__(self):
        return f"{self.user.username} - {self.ip_address}"

class LoginAttempt(models.Model):
    """Track login attempts for security"""
    username = models.CharField(max_length=150)
    ip_address = models.GenericIPAddressField()
    user_agent = models.TextField()
    success = models.BooleanField(default=False)
    timestamp = models.DateTimeField(auto_now_add=True)
    
    class Meta:
        db_table = 'login_attempts'
        verbose_name = 'Login Attempt'
        verbose_name_plural = 'Login Attempts'
    
    def __str__(self):
        status = "Success" if self.success else "Failed"
        return f"{self.username} - {status} - {self.timestamp}"

class Developer(models.Model):
    """Developer information for the footer"""
    full_name = models.CharField(max_length=100)
    profession = models.CharField(max_length=100)
    linkedin_link = models.URLField(blank=True, null=True)
    photo = models.ImageField(upload_to='developers/', blank=True, null=True)
    bio = models.TextField(blank=True, null=True)
    order = models.PositiveIntegerField(default=0, help_text="Order of appearance")
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'developers'
        verbose_name = 'Developer'
        verbose_name_plural = 'Developers'
        ordering = ['order', 'full_name']

    def __str__(self):
        return f"{self.full_name} - {self.profession}"
