import React, { useState, useEffect } from 'react';
import { Con<PERSON><PERSON>, <PERSON>, Col, <PERSON>, But<PERSON>, Badge, Spinner, Form, InputGroup, Alert, Modal } from 'react-bootstrap';
import { Link } from 'react-router-dom';
import { eventService, Event, getMediaUrl } from '../services/api';

const EventList: React.FC = () => {
  const [events, setEvents] = useState<Event[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [filterStatus, setFilterStatus] = useState<string>('');
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [selectedEvent, setSelectedEvent] = useState<Event | null>(null);
  const [deleteLoading, setDeleteLoading] = useState(false);

  useEffect(() => {
    fetchEvents();
  }, []);

  const fetchEvents = async () => {
    try {
      setLoading(true);
      const response = await eventService.getEvents();
      // Ensure we have an array
      const eventsData = Array.isArray(response.data) ? response.data :
                        ((response.data as any)?.results ? (response.data as any).results : []);
      setEvents(eventsData);
      setError(null);
    } catch (error) {
      console.error('Error fetching events:', error);
      setError('Failed to load events. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const filteredEvents = events.filter(event => {
    const matchesSearch =
      event.name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      event.description?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      event.location?.toLowerCase().includes(searchTerm.toLowerCase());

    const matchesStatus = filterStatus === '' ||
      (filterStatus === 'active' && event.is_active) ||
      (filterStatus === 'inactive' && !event.is_active) ||
      (filterStatus === 'upcoming' && new Date(event.start_date) > new Date()) ||
      (filterStatus === 'ongoing' && new Date(event.start_date) <= new Date() && new Date(event.end_date) >= new Date()) ||
      (filterStatus === 'completed' && new Date(event.end_date) < new Date());

    return matchesSearch && matchesStatus;
  });

  const getEventStatus = (event: Event) => {
    const now = new Date();
    const startDate = new Date(event.start_date);
    const endDate = new Date(event.end_date);

    if (!event.is_active) {
      return <Badge bg="secondary">Inactive</Badge>;
    } else if (startDate > now) {
      return <Badge bg="info">Upcoming</Badge>;
    } else if (startDate <= now && endDate >= now) {
      return <Badge bg="success">Ongoing</Badge>;
    } else {
      return <Badge bg="warning">Completed</Badge>;
    }
  };

  const handleDeleteEvent = async () => {
    if (!selectedEvent) return;

    try {
      setDeleteLoading(true);
      await eventService.deleteEvent(selectedEvent.id);
      await fetchEvents(); // Refresh the list
      setShowDeleteModal(false);
      setSelectedEvent(null);
      setError(null);
    } catch (err) {
      console.error('Error deleting event:', err);
      setError('Failed to delete event. Please try again.');
    } finally {
      setDeleteLoading(false);
    }
  };

  const openDeleteModal = (event: Event) => {
    setSelectedEvent(event);
    setShowDeleteModal(true);
  };

  if (loading) {
    return (
      <Container className="text-center mt-5">
        <Spinner animation="border" role="status">
          <span className="visually-hidden">Loading...</span>
        </Spinner>
        <p className="mt-3">Loading events...</p>
      </Container>
    );
  }

  return (
    <Container className="py-4">
      <Row className="mb-4">
        <Col>
          <div className="d-flex justify-content-between align-items-center mb-4">
            <div>
              <h1 className="display-5 fw-bold text-primary">
                <i className="fas fa-calendar-alt me-3"></i>
                Event Management
              </h1>
              <p className="lead text-muted">Manage events, schedules, and registrations</p>
            </div>
            <Link to="/events/new" className="btn btn-primary btn-lg">
              <i className="fas fa-plus me-2"></i>
              Create New Event
            </Link>
          </div>

          {/* Search and Filter Controls */}
          <Row className="mb-4">
            <Col md={6}>
              <InputGroup>
                <InputGroup.Text>
                  <i className="fas fa-search"></i>
                </InputGroup.Text>
                <Form.Control
                  type="text"
                  placeholder="Search events by name, description, or location..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                />
              </InputGroup>
            </Col>
            <Col md={6}>
              <Form.Select
                value={filterStatus}
                onChange={(e) => setFilterStatus(e.target.value)}
              >
                <option value="">All Events</option>
                <option value="active">Active Events</option>
                <option value="inactive">Inactive Events</option>
                <option value="upcoming">Upcoming Events</option>
                <option value="ongoing">Ongoing Events</option>
                <option value="completed">Completed Events</option>
              </Form.Select>
            </Col>
          </Row>

          {error && (
            <Alert variant="danger" className="mb-4">
              <i className="fas fa-exclamation-triangle me-2"></i>
              {error}
            </Alert>
          )}
        </Col>
      </Row>

      {/* Events Grid */}
      <Row>
        {filteredEvents.length === 0 ? (
          <Col>
            <div className="text-center py-5">
              <i className="fas fa-calendar-times fa-3x text-muted mb-3"></i>
              <h5 className="text-muted">No events found</h5>
              <p className="text-muted">Try adjusting your search or filter criteria.</p>
              <Link to="/events/new" className="btn btn-primary">
                <i className="fas fa-plus me-2"></i>
                Create Your First Event
              </Link>
            </div>
          </Col>
        ) : (
          filteredEvents.map((event) => (
            <Col lg={4} md={6} key={event.id} className="mb-4">
              <Card className="h-100 shadow-sm hover-shadow">
                {event.banner && (
                  <Card.Img
                    variant="top"
                    src={event.banner}
                    style={{ height: '250px', objectFit: 'cover' }}
                  />
                )}
                <Card.Body className="d-flex flex-column">
                  <div className="d-flex justify-content-between align-items-start mb-2">
                    <h5 className="card-title mb-0">{event.name}</h5>
                    {getEventStatus(event)}
                  </div>

                  <p className="text-muted mb-2">
                    <i className="fas fa-map-marker-alt me-1"></i>
                    {event.location}
                  </p>
                  <p className="text-muted mb-2">
                    <i className="fas fa-calendar me-1"></i>
                    {new Date(event.start_date).toLocaleDateString()} - {new Date(event.end_date).toLocaleDateString()}
                  </p>

                  <Card.Text className="flex-grow-1 text-muted">
                    {event.description && event.description.length > 100
                      ? `${event.description.substring(0, 100)}...`
                      : event.description}
                  </Card.Text>

                  <div className="mt-auto">
                    <div className="d-flex justify-content-between align-items-center mb-3">
                      <small className="text-muted">
                        <i className="fas fa-users me-1"></i>
                        {event.participant_count} participants
                      </small>
                      <small className="text-muted">
                        <i className="fas fa-map-marker-alt me-1"></i>
                        {event.city}, {event.country}
                      </small>
                    </div>

                    <div className="d-flex gap-2">
                      <Link to={`/events/${event.id}`} className="btn btn-outline-primary btn-sm flex-fill">
                        <i className="fas fa-eye me-1"></i>
                        View
                      </Link>
                      <Link to={`/events/${event.id}/edit`} className="btn btn-outline-secondary btn-sm flex-fill">
                        <i className="fas fa-edit me-1"></i>
                        Edit
                      </Link>
                      <Button
                        variant="outline-danger"
                        size="sm"
                        onClick={() => openDeleteModal(event)}
                      >
                        <i className="fas fa-trash"></i>
                      </Button>
                    </div>
                  </div>
                </Card.Body>
              </Card>
            </Col>
          ))
        )}
      </Row>

      {/* Delete Confirmation Modal */}
      <Modal show={showDeleteModal} onHide={() => setShowDeleteModal(false)}>
        <Modal.Header closeButton>
          <Modal.Title className="text-danger">
            <i className="fas fa-exclamation-triangle me-2"></i>
            Confirm Deletion
          </Modal.Title>
        </Modal.Header>
        <Modal.Body>
          <p>Are you sure you want to delete the event <strong>{selectedEvent?.name}</strong>?</p>
          <Alert variant="warning">
            <i className="fas fa-warning me-2"></i>
            This action cannot be undone. All event data, including registrations and schedules, will be permanently deleted.
          </Alert>
        </Modal.Body>
        <Modal.Footer>
          <Button variant="secondary" onClick={() => setShowDeleteModal(false)}>
            Cancel
          </Button>
          <Button
            variant="danger"
            onClick={handleDeleteEvent}
            disabled={deleteLoading}
          >
            {deleteLoading ? (
              <>
                <Spinner animation="border" size="sm" className="me-2" />
                Deleting...
              </>
            ) : (
              <>
                <i className="fas fa-trash me-2"></i>
                Delete Event
              </>
            )}
          </Button>
        </Modal.Footer>
      </Modal>
    </Container>
  );
};

export default EventList;
