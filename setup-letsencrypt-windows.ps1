# Let's Encrypt SSL Setup for Windows Server
# University of Gondar Event Management System

Write-Host "🔐 University of Gondar Event Management System" -ForegroundColor Blue
Write-Host "🚀 Let's Encrypt SSL Certificate Setup for Windows" -ForegroundColor Blue
Write-Host "=================================================" -ForegroundColor Blue

# Configuration
$DOMAIN = "event.uog.edu.et"
$EMAIL = "<EMAIL>"  # Update this email address
$COMPOSE_FILE = "docker-compose.prod.yml"

# Check if running as Administrator
if (-NOT ([Security.Principal.WindowsPrincipal] [Security.Principal.WindowsIdentity]::GetCurrent()).IsInRole([Security.Principal.WindowsBuiltInRole] "Administrator")) {
    Write-Host "❌ This script must be run as Administrator" -ForegroundColor Red
    Write-Host "   Right-click PowerShell and select 'Run as Administrator'" -ForegroundColor Red
    exit 1
}

# Check if Docker is running
try {
    docker version | Out-Null
} catch {
    Write-Host "❌ Docker is not running or not installed" -ForegroundColor Red
    exit 1
}

Write-Host "`n🔍 Checking prerequisites..." -ForegroundColor Yellow

# Check if application is running on HTTP
try {
    $response = Invoke-WebRequest -Uri "http://localhost" -Method Head -TimeoutSec 10
    Write-Host "✅ Application is accessible on HTTP" -ForegroundColor Green
} catch {
    Write-Host "❌ Application is not accessible on HTTP" -ForegroundColor Red
    Write-Host "   Please ensure the application is running first" -ForegroundColor Red
    exit 1
}

# Create certificate directories
Write-Host "`n📁 Creating certificate directories..." -ForegroundColor Yellow
New-Item -ItemType Directory -Force -Path ".\certbot\conf" | Out-Null
New-Item -ItemType Directory -Force -Path ".\certbot\www" | Out-Null
New-Item -ItemType Directory -Force -Path ".\logs\certbot" | Out-Null

# Check if certificates already exist
if (Test-Path ".\certbot\conf\live\$DOMAIN") {
    Write-Host "`n⚠️  SSL certificates for $DOMAIN already exist" -ForegroundColor Yellow
    $renew = Read-Host "Do you want to renew them? (y/N)"
    if ($renew -ne "y" -and $renew -ne "Y") {
        Write-Host "ℹ️  Skipping certificate generation" -ForegroundColor Blue
        exit 0
    }
}

Write-Host "`n🔐 Generating Let's Encrypt SSL certificate..." -ForegroundColor Yellow
Write-Host "🔥 Using Let's Encrypt PRODUCTION environment" -ForegroundColor Green

# Generate SSL certificate using certbot
try {
    docker-compose -f $COMPOSE_FILE run --rm certbot certonly --webroot --webroot-path=/var/www/certbot --email $EMAIL --agree-tos --no-eff-email --force-renewal -d $DOMAIN -d "www.$DOMAIN"
    
    if (-not (Test-Path ".\certbot\conf\live\$DOMAIN\fullchain.pem")) {
        throw "Certificate files not found after generation"
    }
    
    Write-Host "✅ SSL certificate generated successfully!" -ForegroundColor Green
} catch {
    Write-Host "❌ SSL certificate generation failed" -ForegroundColor Red
    Write-Host "   Error: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "`n🔧 Troubleshooting steps:" -ForegroundColor Yellow
    Write-Host "   1. Ensure external DNS points to your public IP" -ForegroundColor Yellow
    Write-Host "   2. Check firewall allows port 80 from internet" -ForegroundColor Yellow
    Write-Host "   3. Verify port forwarding is configured" -ForegroundColor Yellow
    exit 1
}

# Create SSL-enabled nginx configuration
Write-Host "`n⚙️  Creating SSL-enabled nginx configuration..." -ForegroundColor Yellow

$sslConfig = @"
# Production Nginx Configuration with Let's Encrypt SSL

# HTTP Server - Redirects to HTTPS
server {
    listen 80;
    server_name $DOMAIN www.$DOMAIN;

    # Let's Encrypt ACME challenge
    location /.well-known/acme-challenge/ {
        root /var/www/certbot;
        try_files `$uri =404;
    }

    # Redirect all other HTTP traffic to HTTPS
    location / {
        return 301 https://`$server_name`$request_uri;
    }
}

# HTTPS Server Configuration with Let's Encrypt SSL
server {
    listen 443 ssl http2;
    server_name $DOMAIN www.$DOMAIN;

    # Let's Encrypt SSL Configuration
    ssl_certificate /etc/letsencrypt/live/$DOMAIN/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/$DOMAIN/privkey.pem;

    # SSL Security Settings
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512:ECDHE-RSA-AES256-GCM-SHA384:DHE-RSA-AES256-GCM-SHA384:ECDHE-RSA-AES256-SHA384;
    ssl_prefer_server_ciphers off;
    ssl_session_cache shared:SSL:10m;
    ssl_session_timeout 10m;
    ssl_session_tickets off;

    # OCSP Stapling
    ssl_stapling on;
    ssl_stapling_verify on;
    ssl_trusted_certificate /etc/letsencrypt/live/$DOMAIN/chain.pem;
    resolver ******* ******* valid=300s;
    resolver_timeout 5s;

    # Security Headers
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains; preload" always;
    add_header X-Frame-Options DENY always;
    add_header X-Content-Type-Options nosniff always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header Referrer-Policy "strict-origin-when-cross-origin" always;

    # Root directory for frontend
    root /usr/share/nginx/html;
    index index.html index.htm;

    # Logging
    access_log /var/log/nginx/access.log;
    error_log /var/log/nginx/error.log;

    # Backend API routes
    location /api/ {
        proxy_pass http://backend:8000;
        proxy_set_header Host `$host;
        proxy_set_header X-Real-IP `$remote_addr;
        proxy_set_header X-Forwarded-For `$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto `$scheme;
        proxy_connect_timeout 300s;
        proxy_read_timeout 300s;
        proxy_send_timeout 300s;
    }

    # Django Admin
    location /admin/ {
        proxy_pass http://backend:8000;
        proxy_set_header Host `$host;
        proxy_set_header X-Real-IP `$remote_addr;
        proxy_set_header X-Forwarded-For `$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto `$scheme;
        proxy_connect_timeout 300s;
        proxy_read_timeout 300s;
        proxy_send_timeout 300s;
    }

    # Health check endpoint
    location /health/ {
        proxy_pass http://backend:8000;
        proxy_set_header Host `$host;
        proxy_set_header X-Real-IP `$remote_addr;
        proxy_set_header X-Forwarded-For `$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto `$scheme;
        proxy_connect_timeout 300s;
        proxy_read_timeout 300s;
        proxy_send_timeout 300s;
    }

    # React static files
    location /static/ {
        root /usr/share/nginx/html;
        expires 1y;
        add_header Cache-Control "public, immutable";
    }

    # Django static files
    location /django-static/ {
        alias /var/www/static/;
        expires 1y;
        add_header Cache-Control "public, immutable";
    }

    # Media files
    location /media/ {
        alias /var/www/media/;
        expires 1M;
        add_header Cache-Control "public";
    }

    # Frontend application (React Router)
    location / {
        try_files `$uri `$uri/ /index.html;
    }
}
"@

# Backup current configuration
Copy-Item ".\nginx\conf.d\production.conf" ".\nginx\conf.d\production.conf.backup" -Force

# Write SSL configuration
$sslConfig | Out-File -FilePath ".\nginx\conf.d\production.conf" -Encoding UTF8

Write-Host "✅ SSL configuration created" -ForegroundColor Green

# Restart services with SSL
Write-Host "`n🚀 Restarting services with SSL enabled..." -ForegroundColor Yellow
docker-compose -f $COMPOSE_FILE down
docker-compose -f $COMPOSE_FILE up -d

# Wait for services to start
Write-Host "⏳ Waiting for services to start..." -ForegroundColor Yellow
Start-Sleep -Seconds 30

# Test HTTPS connection
Write-Host "`n🧪 Testing HTTPS connection..." -ForegroundColor Yellow
try {
    $response = Invoke-WebRequest -Uri "https://$DOMAIN" -Method Head -TimeoutSec 15
    Write-Host "✅ HTTPS is working correctly!" -ForegroundColor Green
} catch {
    Write-Host "⚠️  HTTPS test failed, but this might be normal during startup" -ForegroundColor Yellow
    Write-Host "   Please wait a few minutes and test manually" -ForegroundColor Yellow
}

# Display certificate information
Write-Host "`n📋 Certificate Information:" -ForegroundColor Blue
docker-compose -f $COMPOSE_FILE run --rm certbot certificates

Write-Host "`n🎉 Let's Encrypt SSL setup completed!" -ForegroundColor Green
Write-Host "=================================================" -ForegroundColor Green
Write-Host "✅ Your University of Gondar Event Management System is now secured with SSL" -ForegroundColor Green
Write-Host "🌐 Access your application at: https://$DOMAIN" -ForegroundColor Green
Write-Host "🔒 SSL certificate will auto-renew every 12 hours" -ForegroundColor Green
Write-Host "📧 Certificate notifications will be sent to: $EMAIL" -ForegroundColor Green

Write-Host "`nℹ️  Important Notes:" -ForegroundColor Blue
Write-Host "   • Internal DNS: Add A record for $DOMAIN -> ************ on your AD server" -ForegroundColor Blue
Write-Host "   • External DNS: Ensure A record for $DOMAIN -> [Your Public IP] with registrar" -ForegroundColor Blue
Write-Host "   • Firewall: Allow ports 80 and 443 from internet" -ForegroundColor Blue
Write-Host "   • Auto-renewal: Configured via the certbot container" -ForegroundColor Blue

Write-Host "`n🔐 SSL setup complete! 🔐" -ForegroundColor Green
