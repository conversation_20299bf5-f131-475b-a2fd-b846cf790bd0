from django.contrib import admin
from django.utils.html import format_html
from django.urls import reverse
from django.utils.safestring import mark_safe
from .models import EventFeedback, SessionFeedback, FeedbackTemplate


class SessionFeedbackInline(admin.TabularInline):
    model = SessionFeedback
    extra = 0
    readonly_fields = ['average_rating_display', 'created_at']

    def average_rating_display(self, obj):
        """Display average rating for session"""
        if obj.pk:
            try:
                avg = float(obj.average_rating)
                return f"{avg:.1f}/5"
            except (ValueError, TypeError):
                return "N/A"
        return "-"
    average_rating_display.short_description = 'Avg Rating'


@admin.register(EventFeedback)
class EventFeedbackAdmin(admin.ModelAdmin):
    inlines = [SessionFeedbackInline]
    list_display = [
        'event', 'participant_display_name', 'overall_satisfaction_stars',
        'average_rating_display', 'met_expectations', 'consent_given', 'created_at'
    ]
    list_filter = [
        'event', 'overall_satisfaction', 'met_expectations', 'session_relevance',
        'speaker_quality', 'consent_given', 'is_anonymous', 'created_at'
    ]
    search_fields = [
        'event__name', 'participant__full_name', 'participant_name', 
        'participant_email', 'institution_name', 'most_valuable_aspect'
    ]
    readonly_fields = [
        'created_at', 'updated_at', 'ip_address', 'user_agent', 'average_rating_display'
    ]
    
    fieldsets = (
        ('Event & Participant Information', {
            'fields': (
                'event', 'participant', 'participant_name', 'participant_email',
                'institution_name', 'position_title', 'is_anonymous'
            )
        }),
        ('Overall Experience', {
            'fields': (
                'overall_satisfaction', 'met_expectations', 'most_valuable_aspect',
                'improvement_suggestions'
            )
        }),
        ('Sessions & Content', {
            'fields': (
                'session_relevance', 'most_valuable_sessions', 'future_topics_suggestions'
            )
        }),
        ('Speakers & Moderators', {
            'fields': (
                'speaker_quality', 'speaker_comments'
            )
        }),
        ('Logistics & Organization', {
            'fields': (
                'venue_facilities', 'technical_setup', 'time_management',
                'transportation_accessibility', 'pre_event_communication', 'logistics_comments'
            )
        }),
        ('Networking & Future', {
            'fields': (
                'sufficient_networking', 'networking_improvements',
                'future_topics', 'additional_feedback'
            )
        }),
        ('Files & Consent', {
            'fields': (
                'uploaded_file', 'consent_given'
            )
        }),
        ('Metadata', {
            'fields': (
                'ip_address', 'user_agent', 'created_at', 'updated_at', 'average_rating_display'
            ),
            'classes': ('collapse',)
        }),
    )
    
    def overall_satisfaction_stars(self, obj):
        """Display overall satisfaction as stars"""
        stars = '⭐' * obj.overall_satisfaction
        return format_html('<span title="{}/5">{}</span>', obj.overall_satisfaction, stars)
    overall_satisfaction_stars.short_description = 'Overall Rating'
    
    def average_rating_display(self, obj):
        """Display average rating with color coding"""
        try:
            avg = float(obj.average_rating)
            if avg >= 4.5:
                color = 'green'
            elif avg >= 3.5:
                color = 'orange'
            else:
                color = 'red'
            return format_html(
                '<span style="color: {}; font-weight: bold;">{:.1f}/5</span>',
                color, avg
            )
        except (ValueError, TypeError):
            return format_html('<span style="color: red;">N/A</span>')
    average_rating_display.short_description = 'Avg Rating'
    
    def participant_display_name(self, obj):
        """Display participant name with link if available"""
        if obj.participant:
            try:
                url = reverse('admin:participants_participant_change', args=[obj.participant.pk])
                return format_html('<a href="{}">{}</a>', url, obj.participant.full_name)
            except:
                # Fallback if URL reverse fails
                return obj.participant.full_name
        elif obj.participant_name:
            return obj.participant_name
        else:
            return mark_safe('<em>Anonymous</em>')
    participant_display_name.short_description = 'Participant'
    
    def get_queryset(self, request):
        return super().get_queryset(request).select_related('event', 'participant')


@admin.register(SessionFeedback)
class SessionFeedbackAdmin(admin.ModelAdmin):
    list_display = [
        'session', 'event_feedback', 'content_quality_stars', 
        'speaker_effectiveness_stars', 'average_rating_display', 'created_at'
    ]
    list_filter = [
        'session__event', 'content_quality', 'speaker_effectiveness',
        'session_organization', 'audience_engagement', 'created_at'
    ]
    search_fields = [
        'session__title', 'session__speaker', 'event_feedback__participant__full_name',
        'what_worked_well', 'improvement_suggestions'
    ]
    readonly_fields = ['created_at', 'updated_at', 'average_rating_display']
    
    fieldsets = (
        ('Session Information', {
            'fields': ('event_feedback', 'session')
        }),
        ('Ratings', {
            'fields': (
                'content_quality', 'speaker_effectiveness', 
                'session_organization', 'audience_engagement'
            )
        }),
        ('Comments', {
            'fields': (
                'what_worked_well', 'improvement_suggestions', 'additional_comments'
            )
        }),
        ('Metadata', {
            'fields': ('created_at', 'updated_at', 'average_rating_display'),
            'classes': ('collapse',)
        }),
    )
    
    def content_quality_stars(self, obj):
        """Display content quality as stars"""
        stars = '⭐' * obj.content_quality
        return format_html('<span title="{}/5">{}</span>', obj.content_quality, stars)
    content_quality_stars.short_description = 'Content'
    
    def speaker_effectiveness_stars(self, obj):
        """Display speaker effectiveness as stars"""
        stars = '⭐' * obj.speaker_effectiveness
        return format_html('<span title="{}/5">{}</span>', obj.speaker_effectiveness, stars)
    speaker_effectiveness_stars.short_description = 'Speaker'
    
    def average_rating_display(self, obj):
        """Display average rating with color coding"""
        try:
            avg = float(obj.average_rating)
            if avg >= 4.5:
                color = 'green'
            elif avg >= 3.5:
                color = 'orange'
            else:
                color = 'red'
            return format_html(
                '<span style="color: {}; font-weight: bold;">{:.1f}/5</span>',
                color, avg
            )
        except (ValueError, TypeError):
            return format_html('<span style="color: red;">N/A</span>')
    average_rating_display.short_description = 'Avg Rating'


@admin.register(FeedbackTemplate)
class FeedbackTemplateAdmin(admin.ModelAdmin):
    list_display = ['name', 'template_type', 'is_active', 'is_default', 'created_at']
    list_filter = ['template_type', 'is_active', 'is_default', 'created_at']
    search_fields = ['name', 'description']
    readonly_fields = ['created_at', 'updated_at']
    
    fieldsets = (
        ('Template Information', {
            'fields': ('name', 'template_type', 'description')
        }),
        ('Configuration', {
            'fields': ('form_config',),
            'description': 'JSON configuration for the feedback form'
        }),
        ('Settings', {
            'fields': ('is_active', 'is_default')
        }),
        ('Metadata', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )
    
    def save_model(self, request, obj, form, change):
        # If this template is set as default, unset other defaults for the same type
        if obj.is_default:
            FeedbackTemplate.objects.filter(
                template_type=obj.template_type,
                is_default=True
            ).exclude(pk=obj.pk).update(is_default=False)
        super().save_model(request, obj, form, change)
