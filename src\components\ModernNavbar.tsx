import React, { useState, useEffect } from 'react';
import { Link, useLocation } from 'react-router-dom';
import { useBranding } from '../hooks/useBranding';
import { getMediaUrl } from '../services/api';

const ModernNavbar: React.FC = () => {
  const location = useLocation();
  const { organization } = useBranding();
  const [isScrolled, setIsScrolled] = useState(false);
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const isHomePage = location.pathname === '/';

  useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 50);
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  const handleSectionClick = (sectionId: string) => {
    if (isHomePage) {
      const element = document.getElementById(sectionId);
      if (element) {
        element.scrollIntoView({ behavior: 'smooth' });
      }
    } else {
      window.location.href = `/#${sectionId}`;
    }
    setIsMobileMenuOpen(false);
  };

  const navItems = [
    { id: 'home', label: 'Home', icon: 'fas fa-home', path: '/' },
    { id: 'events', label: 'Events', icon: 'fas fa-calendar-alt', section: 'events' },
    { id: 'about', label: 'About', icon: 'fas fa-info-circle', section: 'about' },
    { id: 'gallery', label: 'Gallery', icon: 'fas fa-images', section: 'gallery' },
    { id: 'contact', label: 'Contact', icon: 'fas fa-envelope', section: 'contact' },
  ];

  return (
    <>
      <style>{`
        .modern-navbar {
          position: fixed;
          top: 0;
          left: 0;
          right: 0;
          z-index: 1000;
          transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
          backdrop-filter: blur(20px);
          -webkit-backdrop-filter: blur(20px);
        }

        .modern-navbar.scrolled {
          background: rgba(255, 255, 255, 0.95);
          box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
          border-bottom: 1px solid rgba(255, 255, 255, 0.2);
        }

        .modern-navbar.not-scrolled {
          background: rgba(255, 255, 255, 0.1);
          border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }

        .navbar-brand-modern {
          display: flex;
          align-items: center;
          text-decoration: none;
          transition: all 0.3s ease;
          padding: 0.5rem 0;
        }

        .navbar-brand-modern:hover {
          transform: translateY(-2px);
          text-decoration: none;
        }

        .brand-logo-modern {
          width: 50px;
          height: 50px;
          border-radius: 12px;
          object-fit: cover;
          margin-right: 1rem;
          transition: all 0.3s ease;
          box-shadow: 0 4px 20px rgba(0, 123, 255, 0.3);
        }

        .brand-text-modern {
          display: flex;
          flex-direction: column;
        }

        .brand-title-modern {
          font-size: 1.4rem;
          font-weight: 800;
          margin: 0;
          line-height: 1.2;
          color: #007bff;
          text-shadow: 1px 1px 3px rgba(0, 0, 0, 0.4);
          transition: all 0.4s ease;
        }

        .modern-navbar.scrolled .brand-title-modern {
          color: #b28705;
          text-shadow: 1px 1px 3px rgba(0, 0, 0, 0.2);
        }

        .brand-subtitle-modern {
          font-size: 0.75rem;
          color: #bf9107;
          margin: 0;
          line-height: 1;
          transition: all 0.4s ease;
          font-weight: 500;
        }

        .modern-navbar.scrolled .brand-subtitle-modern {
          color: #007bff;
          text-shadow: none;
        }

        .nav-menu-modern {
          display: flex;
          align-items: center;
          gap: 0.5rem;
          margin: 0;
          padding: 0;
          list-style: none;
        }

        .nav-item-modern {
          position: relative;
        }

        .nav-link-modern {
          display: flex;
          align-items: center;
          gap: 0.5rem;
          padding: 0.75rem 1.25rem;
          border-radius: 12px;
          text-decoration: none;
          color: #495057;
          font-weight: 500;
          font-size: 0.95rem;
          transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
          position: relative;
          overflow: hidden;
        }

        .nav-link-modern::before {
          content: '';
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          background: linear-gradient(135deg, rgba(0, 123, 255, 0.1), rgba(0, 86, 179, 0.1));
          opacity: 0;
          transition: opacity 0.3s ease;
          border-radius: 12px;
        }

        .nav-link-modern:hover::before,
        .nav-link-modern.active::before {
          opacity: 1;
        }

        .nav-link-modern:hover {
          color: #007bff;
          transform: translateY(-2px);
          box-shadow: 0 8px 25px rgba(0, 123, 255, 0.2);
          text-decoration: none;
        }

        .nav-link-modern.active {
          color: #007bff;
          background: rgba(0, 123, 255, 0.1);
          box-shadow: 0 4px 15px rgba(0, 123, 255, 0.2);
        }

        .nav-icon-modern {
          font-size: 1rem;
          transition: all 0.3s ease;
        }

        .nav-link-modern:hover .nav-icon-modern {
          transform: scale(1.1);
        }

        .mobile-toggle-modern {
          display: none;
          flex-direction: column;
          gap: 4px;
          background: none;
          border: none;
          padding: 0.5rem;
          cursor: pointer;
          border-radius: 8px;
          transition: all 0.3s ease;
        }

        .mobile-toggle-modern:hover {
          background: rgba(0, 123, 255, 0.1);
        }

        .toggle-line {
          width: 25px;
          height: 3px;
          background: #495057;
          border-radius: 2px;
          transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .mobile-toggle-modern.active .toggle-line:nth-child(1) {
          transform: rotate(45deg) translate(6px, 6px);
        }

        .mobile-toggle-modern.active .toggle-line:nth-child(2) {
          opacity: 0;
        }

        .mobile-toggle-modern.active .toggle-line:nth-child(3) {
          transform: rotate(-45deg) translate(6px, -6px);
        }

        .mobile-menu-modern {
          position: absolute;
          top: 100%;
          left: 0;
          right: 0;
          background: rgba(255, 255, 255, 0.98);
          backdrop-filter: blur(20px);
          border-radius: 0 0 20px 20px;
          box-shadow: 0 10px 40px rgba(0, 0, 0, 0.1);
          padding: 1rem;
          transform: translateY(-20px);
          opacity: 0;
          visibility: hidden;
          transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .mobile-menu-modern.open {
          transform: translateY(0);
          opacity: 1;
          visibility: visible;
        }

        .mobile-nav-menu {
          display: flex;
          flex-direction: column;
          gap: 0.5rem;
        }

        @media (max-width: 768px) {
          .nav-menu-modern {
            display: none;
          }

          .mobile-toggle-modern {
            display: flex;
          }

          .brand-title-modern {
            font-size: 1.2rem;
          }

          .brand-subtitle-modern {
            font-size: 0.7rem;
          }
        }

        .cta-button-modern {
          background: linear-gradient(135deg, #007bff, #0056b3);
          color: white;
          padding: 0.75rem 1.5rem;
          border-radius: 12px;
          text-decoration: none;
          font-weight: 600;
          transition: all 0.3s ease;
          box-shadow: 0 4px 15px rgba(0, 123, 255, 0.3);
          margin-left: 1rem;
        }

        .cta-button-modern:hover {
          transform: translateY(-2px);
          box-shadow: 0 8px 25px rgba(0, 123, 255, 0.4);
          color: white;
          text-decoration: none;
        }
      `}</style>

      <nav className={`modern-navbar ${isScrolled ? 'scrolled' : 'not-scrolled'}`}>
        <div className="container">
          <div className="d-flex align-items-center justify-content-between py-2">
            {/* Brand */}
            <Link to="/" className="navbar-brand-modern">
              {organization?.logo ? (
                <img
                  src={getMediaUrl(organization.logo)}
                  alt={organization.name}
                  className="brand-logo-modern"
                />
              ) : (
                <div className="brand-logo-modern bg-primary d-flex align-items-center justify-content-center">
                  <i className="fas fa-university text-white" style={{ fontSize: '1.5rem' }}></i>
                </div>
              )}
              <div className="brand-text-modern">
                <h1 className="brand-title-modern">
                  {organization?.short_name || 'UoG'} Events
                </h1>
                <p className="brand-subtitle-modern">
                  {organization?.name || 'University of Gondar'}
                </p>
              </div>
            </Link>

            {/* Desktop Navigation */}
            <ul className="nav-menu-modern">
              {navItems.map((item) => (
                <li key={item.id} className="nav-item-modern">
                  {item.path ? (
                    item.external ? (
                      <a
                        href={item.path}
                        target="_blank"
                        rel="noopener noreferrer"
                        className={`nav-link-modern ${location.pathname === item.path ? 'active' : ''}`}
                      >
                        <i className={`${item.icon} nav-icon-modern`}></i>
                        <span>{item.label}</span>
                      </a>
                    ) : (
                      <Link
                        to={item.path}
                        className={`nav-link-modern ${location.pathname === item.path ? 'active' : ''}`}
                      >
                        <i className={`${item.icon} nav-icon-modern`}></i>
                        <span>{item.label}</span>
                      </Link>
                    )
                  ) : (
                    <button
                      onClick={() => handleSectionClick(item.section!)}
                      className="nav-link-modern"
                      style={{ background: 'none', border: 'none' }}
                    >
                      <i className={`${item.icon} nav-icon-modern`}></i>
                      <span>{item.label}</span>
                    </button>
                  )}
                </li>
              ))}
            </ul>

            {/* CTA Button */}
            <Link to="/register" className="cta-button-modern d-none d-md-inline-block">
              <i className="fas fa-rocket me-2"></i>
              Register Now
            </Link>

            {/* Mobile Toggle */}
            <button
              className={`mobile-toggle-modern ${isMobileMenuOpen ? 'active' : ''}`}
              onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
            >
              <div className="toggle-line"></div>
              <div className="toggle-line"></div>
              <div className="toggle-line"></div>
            </button>
          </div>

          {/* Mobile Menu */}
          <div className={`mobile-menu-modern ${isMobileMenuOpen ? 'open' : ''}`}>
            <ul className="mobile-nav-menu">
              {navItems.map((item) => (
                <li key={item.id}>
                  {item.path ? (
                    item.external ? (
                      <a
                        href={item.path}
                        target="_blank"
                        rel="noopener noreferrer"
                        className={`nav-link-modern ${location.pathname === item.path ? 'active' : ''}`}
                        onClick={() => setIsMobileMenuOpen(false)}
                      >
                        <i className={`${item.icon} nav-icon-modern`}></i>
                        <span>{item.label}</span>
                      </a>
                    ) : (
                      <Link
                        to={item.path}
                        className={`nav-link-modern ${location.pathname === item.path ? 'active' : ''}`}
                        onClick={() => setIsMobileMenuOpen(false)}
                      >
                        <i className={`${item.icon} nav-icon-modern`}></i>
                        <span>{item.label}</span>
                      </Link>
                    )
                  ) : (
                    <button
                      onClick={() => handleSectionClick(item.section!)}
                      className="nav-link-modern w-100 text-start"
                      style={{ background: 'none', border: 'none' }}
                    >
                      <i className={`${item.icon} nav-icon-modern`}></i>
                      <span>{item.label}</span>
                    </button>
                  )}
                </li>
              ))}
              <li>
                <Link to="/register" className="cta-button-modern d-inline-block mt-2" onClick={() => setIsMobileMenuOpen(false)}>
                  <i className="fas fa-rocket me-2"></i>
                  Register Now
                </Link>
              </li>
            </ul>
          </div>
        </div>
      </nav>
    </>
  );
};

export default ModernNavbar;
