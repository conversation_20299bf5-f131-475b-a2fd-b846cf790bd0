#!/usr/bin/env python
"""
Check participant photos
"""
import os
import sys
import django

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'event_management.settings')
django.setup()

from participants.models import Participant

def check_photos():
    """Check participant photos"""
    print("🔍 CHECKING PARTICIPANT PHOTOS")
    print("=" * 50)
    
    participants = Participant.objects.all()
    print(f"Total participants: {participants.count()}")
    
    for p in participants[:5]:
        print(f"\n👤 {p.full_name}")
        print(f"   Profile photo: {p.profile_photo}")
        if p.profile_photo:
            print(f"   Photo path: {p.profile_photo.path}")
            print(f"   Photo exists: {os.path.exists(p.profile_photo.path) if p.profile_photo else False}")
        else:
            print(f"   No photo uploaded")

if __name__ == "__main__":
    check_photos()
