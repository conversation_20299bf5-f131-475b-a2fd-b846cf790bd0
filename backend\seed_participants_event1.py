#!/usr/bin/env python
"""
Script to seed participants, drivers, hotels, and participant types for Event ID 1
"""
import os
import sys
import django
from datetime import datetime, timedelta
from django.utils import timezone

# Setup Django
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'event_management.settings')
django.setup()

from events.models import Event
from participants.models import Participant, ParticipantType
from hotels.models import Hotel, HotelRoom
from drivers.models import Driver
from contact_persons.models import Contact<PERSON>erson


def seed_data_for_event_1():
    print("🌱 Seeding data for Event ID 1...")
    
    # Get Event ID 1
    try:
        event = Event.objects.get(id=1)
        print(f"✓ Found event: {event.name}")
    except Event.DoesNotExist:
        print("❌ Event with ID 1 not found. Please create an event first.")
        return
    
    # Create Participant Types
    participant_types_data = [
        {'name': 'Speaker', 'description': 'Event speakers and presenters', 'color': '#dc3545'},
        {'name': 'VIP Guest', 'description': 'VIP guests and dignitaries', 'color': '#ffc107'},
        {'name': 'Attendee', 'description': 'Regular event attendees', 'color': '#007bff'},
    ]
    
    created_participant_types = []
    for pt_data in participant_types_data:
        pt, created = ParticipantType.objects.get_or_create(
            name=pt_data['name'],
            defaults={
                'description': pt_data['description'],
                'color': pt_data['color']
            }
        )
        created_participant_types.append(pt)
        if created:
            print(f"✓ Created participant type: {pt.name}")
        else:
            print(f"✓ Participant type already exists: {pt.name}")
    
    # Create Hotels for Event 1
    hotels_data = [
        {
            'name': 'Grand Hotel Gujrat',
            'address': '123 Main Street, Gujrat, Punjab, Pakistan',
            'phone': '+92-53-1234567',
            'email': '<EMAIL>',
            'contact_person': 'Ahmed Ali Khan',
            'star_rating': 4,
            'website': 'https://grandhotelgujrat.com',
            'description': 'A luxury hotel in the heart of Gujrat city with modern amenities.',
            'latitude': 32.5734,
            'longitude': 74.0788,
        },
        {
            'name': 'City Inn Gujrat',
            'address': '456 University Road, Gujrat, Punjab, Pakistan',
            'phone': '+92-53-7654321',
            'email': '<EMAIL>',
            'contact_person': 'Sara Khan',
            'star_rating': 3,
            'website': 'https://cityinngujrat.com',
            'description': 'Budget-friendly hotel near the university campus.',
            'latitude': 32.5689,
            'longitude': 74.0812,
        },
        {
            'name': 'Royal Palace Hotel',
            'address': '789 GT Road, Gujrat, Punjab, Pakistan',
            'phone': '+92-53-9876543',
            'email': '<EMAIL>',
            'contact_person': 'Muhammad Hassan',
            'star_rating': 5,
            'website': 'https://royalpalacegujrat.com',
            'description': 'Premium 5-star hotel with world-class facilities.',
            'latitude': 32.5756,
            'longitude': 74.0756,
        }
    ]
    
    created_hotels = []
    for hotel_data in hotels_data:
        hotel, created = Hotel.objects.get_or_create(
            name=hotel_data['name'],
            event=event,
            defaults={
                **hotel_data,
                'is_active': True
            }
        )
        created_hotels.append(hotel)
        if created:
            print(f"✓ Created hotel: {hotel.name}")
            
            # Create sample rooms for each hotel
            room_types = ['Single', 'Double', 'Suite', 'Deluxe']
            for i in range(1, 6):  # 5 rooms per hotel
                room_type = room_types[i % len(room_types)]
                capacity = 1 if room_type == 'Single' else 2 if room_type == 'Double' else 4
                price = 5000 if room_type == 'Single' else 8000 if room_type == 'Double' else 15000 if room_type == 'Suite' else 12000
                
                room, room_created = HotelRoom.objects.get_or_create(
                    hotel=hotel,
                    room_number=f"{hotel.name[:3].upper()}-{i:03d}",
                    defaults={
                        'room_type': room_type,
                        'capacity': capacity,
                        'price_per_night': price,
                        'amenities': f'{room_type} room with modern amenities, AC, WiFi, TV',
                        'is_available': True
                    }
                )
                if room_created:
                    print(f"  ✓ Created room: {room.room_number}")
        else:
            print(f"✓ Hotel already exists: {hotel.name}")
    
    # Create Drivers for Event 1
    drivers_data = [
        {
            'name': 'Muhammad Tariq',
            'phone': '+92-300-1234567',
            'email': '<EMAIL>',
            'car_plate': 'GJT-123',
            'car_code': 'CAR001',
            'car_model': 'Toyota Corolla',
            'car_color': 'White',
            'license_number': 'DL123456789',
            'notes': 'Experienced driver with 10+ years of service. Excellent knowledge of local routes.'
        },
        {
            'name': 'Ali Hassan',
            'phone': '+92-301-7654321',
            'email': '<EMAIL>',
            'car_plate': 'GJT-456',
            'car_code': 'CAR002',
            'car_model': 'Honda Civic',
            'car_color': 'Blue',
            'license_number': 'DL987654321',
            'notes': 'Reliable driver specializing in airport transfers.'
        },
        {
            'name': 'Fatima Sheikh',
            'phone': '+92-302-9876543',
            'email': '<EMAIL>',
            'car_plate': 'GJT-789',
            'car_code': 'CAR003',
            'car_model': 'Suzuki Cultus',
            'car_color': 'Red',
            'license_number': 'DL456789123',
            'notes': 'Female driver available for female passengers.'
        }
    ]
    
    created_drivers = []
    for driver_data in drivers_data:
        driver, created = Driver.objects.get_or_create(
            car_plate=driver_data['car_plate'],
            event=event,
            defaults={
                **driver_data,
                'is_available': True
            }
        )
        created_drivers.append(driver)
        if created:
            print(f"✓ Created driver: {driver.name} ({driver.car_plate})")
        else:
            print(f"✓ Driver already exists: {driver.name} ({driver.car_plate})")
    
    # Create Contact Persons (if the model exists)
    try:
        contact_persons_data = [
            {
                'name': 'Dr. Sarah Ahmed',
                'position': 'Event Coordinator',
                'phone': '+92-53-1111111',
                'email': '<EMAIL>',
                'department': 'Event Management',
                'notes': 'Primary contact for event coordination and logistics.'
            },
            {
                'name': 'Mr. Hassan Ali',
                'position': 'Protocol Officer',
                'phone': '+92-53-2222222',
                'email': '<EMAIL>',
                'department': 'Protocol',
                'notes': 'Handles VIP guest protocol and arrangements.'
            }
        ]
        
        created_contact_persons = []
        for cp_data in contact_persons_data:
            cp, created = ContactPerson.objects.get_or_create(
                email=cp_data['email'],
                event=event,
                defaults={
                    **cp_data,
                    'is_active': True
                }
            )
            created_contact_persons.append(cp)
            if created:
                print(f"✓ Created contact person: {cp.name}")
            else:
                print(f"✓ Contact person already exists: {cp.name}")
    except Exception as e:
        print(f"⚠️ Could not create contact persons: {e}")
        created_contact_persons = []
    
    # Create Participants with specific emails
    participants_data = [
        {
            'first_name': 'Tewodros',
            'last_name': 'Abebaw',
            'email': '<EMAIL>',
            'phone': '+251-911-123456',
            'institution_name': 'University of Gujrat',
            'position': 'Senior Lecturer',
            'participant_type': created_participant_types[0],  # Speaker
            'arrival_date': timezone.now() + timedelta(days=1),
            'departure_date': timezone.now() + timedelta(days=4),
            'remarks': 'Keynote speaker for the opening ceremony'
        },
        {
            'first_name': 'Tewodros',
            'last_name': 'Abebaw',
            'email': '<EMAIL>',
            'phone': '+251-911-654321',
            'institution_name': 'Ethiopian Institute of Technology',
            'position': 'Research Fellow',
            'participant_type': created_participant_types[1],  # VIP Guest
            'arrival_date': timezone.now() + timedelta(days=1),
            'departure_date': timezone.now() + timedelta(days=3),
            'remarks': 'VIP guest and technology expert'
        },
        {
            'first_name': 'Mestesh',
            'last_name': 'Ale',
            'email': '<EMAIL>',
            'phone': '+251-911-789012',
            'institution_name': 'Addis Ababa University',
            'position': 'Assistant Professor',
            'participant_type': created_participant_types[2],  # Attendee
            'arrival_date': timezone.now() + timedelta(days=2),
            'departure_date': timezone.now() + timedelta(days=4),
            'remarks': 'Academic participant interested in research collaboration'
        },
        {
            'first_name': 'Mebratu',
            'last_name': 'Aragaw',
            'email': '<EMAIL>',
            'phone': '+251-911-345678',
            'institution_name': 'Bahir Dar University',
            'position': 'Department Head',
            'participant_type': created_participant_types[1],  # VIP Guest
            'arrival_date': timezone.now() + timedelta(days=1),
            'departure_date': timezone.now() + timedelta(days=5),
            'remarks': 'Department head and distinguished academic'
        }
    ]
    
    created_participants = []
    for i, participant_data in enumerate(participants_data):
        participant, created = Participant.objects.get_or_create(
            email=participant_data['email'],
            defaults={
                **participant_data,
                'event': event,
                'is_confirmed': True,
                'status': 'approved',
                # Assign hotels and drivers cyclically
                'assigned_hotel': created_hotels[i % len(created_hotels)] if created_hotels else None,
                'assigned_driver': created_drivers[i % len(created_drivers)] if created_drivers else None,
                'assigned_contact_person': created_contact_persons[i % len(created_contact_persons)] if created_contact_persons else None,
            }
        )
        created_participants.append(participant)
        if created:
            print(f"✓ Created participant: {participant.first_name} {participant.last_name} ({participant.email})")
            if participant.assigned_hotel:
                print(f"  → Assigned to hotel: {participant.assigned_hotel.name}")
            if participant.assigned_driver:
                print(f"  → Assigned driver: {participant.assigned_driver.name}")
        else:
            print(f"✓ Participant already exists: {participant.first_name} {participant.last_name} ({participant.email})")
    
    print("\n🎉 Seeding completed for Event ID 1!")
    print(f"📊 Summary for Event '{event.name}':")
    print(f"   - Participant Types: {len(created_participant_types)}")
    print(f"   - Hotels: {len(created_hotels)}")
    print(f"   - Drivers: {len(created_drivers)}")
    print(f"   - Contact Persons: {len(created_contact_persons)}")
    print(f"   - Participants: {len(created_participants)}")
    
    # Show participant assignments
    print(f"\n👥 Participant Assignments:")
    for participant in created_participants:
        print(f"   - {participant.first_name} {participant.last_name} ({participant.email})")
        print(f"     Type: {participant.participant_type.name}")
        print(f"     Hotel: {participant.assigned_hotel.name if participant.assigned_hotel else 'Not assigned'}")
        print(f"     Driver: {participant.assigned_driver.name if participant.assigned_driver else 'Not assigned'}")
        print(f"     Contact: {participant.assigned_contact_person.name if participant.assigned_contact_person else 'Not assigned'}")
        print()


if __name__ == '__main__':
    seed_data_for_event_1()
