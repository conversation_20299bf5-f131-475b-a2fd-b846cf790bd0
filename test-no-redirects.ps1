# Test No HTTP to HTTPS Redirects
# University of Gondar Event Management System

Write-Host "Testing HTTP-Only Configuration (No Redirects)" -ForegroundColor Blue
Write-Host "===============================================" -ForegroundColor Blue

Write-Host "`nTesting for HTTP to HTTPS redirects..." -ForegroundColor Yellow

# Test localhost with no redirects allowed
Write-Host "Testing localhost (no redirects allowed)..." -ForegroundColor Cyan
try {
    $response = Invoke-WebRequest -Uri "http://localhost" -Method Head -MaximumRedirection 0 -TimeoutSec 10
    Write-Host "SUCCESS: No redirects detected (Status: $($response.StatusCode))" -ForegroundColor Green
    
    # Check for any HTTPS-related headers
    $httpsHeaders = @()
    if ($response.Headers["Location"]) {
        $location = $response.Headers["Location"]
        if ($location -like "https://*") {
            $httpsHeaders += "Location: $location"
        }
    }
    if ($response.Headers["Strict-Transport-Security"]) {
        $httpsHeaders += "HSTS: $($response.Headers["Strict-Transport-Security"])"
    }
    
    if ($httpsHeaders.Count -eq 0) {
        Write-Host "SUCCESS: No HTTPS-related headers found" -ForegroundColor Green
    } else {
        Write-Host "WARNING: Found HTTPS-related headers:" -ForegroundColor Yellow
        foreach ($header in $httpsHeaders) {
            Write-Host "  $header" -ForegroundColor Yellow
        }
    }
    
} catch {
    if ($_.Exception.Message -like "*redirect*" -or $_.Exception.Message -like "*301*" -or $_.Exception.Message -like "*302*") {
        Write-Host "ERROR: HTTP to HTTPS redirect detected!" -ForegroundColor Red
        Write-Host "Error: $($_.Exception.Message)" -ForegroundColor Red
    } else {
        Write-Host "INFO: Request completed without redirects" -ForegroundColor Green
        Write-Host "Note: $($_.Exception.Message)" -ForegroundColor Cyan
    }
}

# Test with curl if available
Write-Host "`nTesting with curl (if available)..." -ForegroundColor Cyan
try {
    $curlOutput = curl -I -s -o /dev/null -w "%{http_code} %{redirect_url}" http://localhost 2>$null
    if ($curlOutput) {
        Write-Host "Curl response: $curlOutput" -ForegroundColor White
        if ($curlOutput -like "*https://*") {
            Write-Host "WARNING: Redirect to HTTPS detected by curl" -ForegroundColor Yellow
        } else {
            Write-Host "SUCCESS: No HTTPS redirect detected by curl" -ForegroundColor Green
        }
    }
} catch {
    Write-Host "INFO: Curl not available or failed" -ForegroundColor Cyan
}

# Check Docker services
Write-Host "`nChecking Docker services..." -ForegroundColor Yellow
try {
    $services = docker-compose -f docker-compose.prod.yml ps --format "table {{.Name}}\t{{.Status}}"
    Write-Host "Services Status:" -ForegroundColor Green
    Write-Host $services -ForegroundColor White
} catch {
    Write-Host "ERROR: Cannot check Docker services" -ForegroundColor Red
}

# Check nginx configuration for redirects
Write-Host "`nChecking nginx configuration..." -ForegroundColor Yellow
$nginxConfig = Get-Content "nginx/conf.d/production.conf" -Raw
if ($nginxConfig -match "return 301|return 302|redirect.*https") {
    Write-Host "WARNING: Found redirect rules in nginx configuration" -ForegroundColor Yellow
} else {
    Write-Host "SUCCESS: No redirect rules found in nginx configuration" -ForegroundColor Green
}

# Check docker-compose environment variables
Write-Host "`nChecking backend environment variables..." -ForegroundColor Yellow
$dockerConfig = Get-Content "docker-compose.prod.yml" -Raw
if ($dockerConfig -match "SECURE_SSL_REDIRECT=True") {
    Write-Host "WARNING: SECURE_SSL_REDIRECT is set to True" -ForegroundColor Yellow
} elseif ($dockerConfig -match "SECURE_SSL_REDIRECT=False") {
    Write-Host "SUCCESS: SECURE_SSL_REDIRECT is set to False" -ForegroundColor Green
} else {
    Write-Host "INFO: SECURE_SSL_REDIRECT not found in configuration" -ForegroundColor Cyan
}

Write-Host "`nConfiguration Summary:" -ForegroundColor Blue
Write-Host "=====================" -ForegroundColor Blue
Write-Host "HTTP-Only Mode: ACTIVE" -ForegroundColor Green
Write-Host "SSL Redirects: DISABLED" -ForegroundColor Green
Write-Host "HTTPS: NOT CONFIGURED" -ForegroundColor Yellow

Write-Host "`nAccess URLs (HTTP Only):" -ForegroundColor Yellow
Write-Host "========================" -ForegroundColor Yellow
Write-Host "Localhost: http://localhost" -ForegroundColor White
Write-Host "Internal IP: http://************" -ForegroundColor White
Write-Host "Domain: http://event.uog.edu.et" -ForegroundColor White

Write-Host "`nTest Complete!" -ForegroundColor Green
Write-Host "Your application is configured for HTTP-only access with no redirects." -ForegroundColor Green
